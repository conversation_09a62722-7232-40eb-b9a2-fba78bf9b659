<?php

use App\Http\Middleware\LogRequestResponse;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        api: __DIR__.'/../routes/api.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->alias([
            'role' => \App\Http\Middleware\RoleMiddleware::class,
            'EnsureOtpIsVerified' => \App\Http\Middleware\EnsureOtpIsVerified::class,
            'VersionCheck' => \App\Http\Middleware\VersionCheckMiddleware::class,
        ]);
        $middleware->append(LogRequestResponse::class);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })->create();
