<?php


namespace App\Http\Requests\Admin;
use Closure;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;

class MisApprovelUpdateRequest extends FormRequest
{
    
    
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {        
        return [
            'selected_roster_id' => ['required'],
            'trip_approve_status' => 'required',
        ];
    }

    public function messages(): array
    {
        return [
            'selected_roster_id.required' => 'select Atleast One RouteId',            
        ];
    }
   

    public function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json([
            'success' => false,
            'validation_error' => true,
            'message' => 'Validation errors',
            'data' => $validator->errors()
        ]));
    }
   
}
