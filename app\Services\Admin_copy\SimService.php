<?php

namespace App\Services\Admin;

use App\Helpers\CommonFunction;
use App\Helpers\MyHelper;
use Carbon\Carbon;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use App\Models\Sim;

class SimService
{
    protected CommonFunction $commonFunction;
    protected ?Branch $currentBranch = null;

    public function __construct(CommonFunction $commonFunction)
    {
        $this->commonFunction = $commonFunction;
    }
	
	 public function indexSim(): FoundationApplication|Response|ResponseFactory
    {
        $authUser = Auth::user();

        try 
		{
            $sim = Sim::select('sim.SIM_ID',
                'sim.SIM_MOBILE_NO',
                'sim.SIM_SERIAL_NO',
                'sim.SIM_PROVIDER',
                'sim.SIM_MAP_STATUS',
                'sim.SIM_REMARKS','O.NAME as ORG_NAME','O.LOCATION'
            )
                ->where('sim.ACTIVE', MyHelper::$RS_ACTIVE)
               ->where('sim.ORG_ID', "=","O.ORGANIZATIONID")
			    ->join("branch", "branch.BRANCH_ID", "=", DB::Raw($authUser->BRANCH_ID))
                ->Join("organization as O", "O.ORGANIZATIONID", "=", "branch.ORG_ID")
                ->get();

            return response([
                'success' => true,
                'status' => 3,
                'employees' => $sim,
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Sim Index Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ]);
        }
    }

	

    public function storeSim($request): FoundationApplication|Response|ResponseFactory
    {

        try {

            DB::beginTransaction();
            $auth_user = Auth::user();

            $simData = $this->prepareSimData($request, $auth_user, MyHelper::$RS_ACTIVE, $auth_user->id);
            $simResult = Sim::create($simData);



            return response([
                'success' => true,
                'status' => 3,
                'message' => 'Sim Created Successfully',

            ]);

        } catch (\Throwable|\Exception $e) 
		{
            DB::rollBack();
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'validation_controller' => true,
                'status' => 4,
                'message' => 'Sim Created UnSuccessfully',
                'errorM' => $e->getMessage(),
            ],500);
        } finally {
            DB::commit();
        }


    }
	
    /**
     * @throws ConnectionException
     */
    private function prepareSimData($request, $auth_user, $active, $userId): array
    {
        $date = Carbon::now();

        return [
            "SIM_MOBILE_NO" => $request->SIM_MOBILE_NO,
            "SIM_SERIAL_NO" => $request->SIM_SERIAL_NO,
            "SIM_PROVIDER" => $request->SIM_PROVIDER,
            "SIM_MAP_STATUS" => $active,
            "SIM_REMARKS" => $request->SIM_REMARKS,
            "ACTIVE" => $active,
            "CREATED_BY" => $auth_user->id,
            "CREATED_DATE" => $date->format("Y-m-d H:i:s"),
          
        ];
    }

    public function deleteSim($request, $simAutoIdCrypt): FoundationApplication|Response|ResponseFactory
    {
        $authUser = Auth::user();
       
        try {
            DB::beginTransaction();

            $simId = Crypt::decryptString($simAutoIdCrypt);

            $sim = sim::where('ACTIVE', MyHelper::$RS_ACTIVE)
               // ->where('BRANCH_ID', $authUser->BRANCH_ID)
                ->findOrFail($simId);

            $sim->update([
                'REMARKS' => $request->input('remark'),
                'ACTIVE' => MyHelper::$RS_INACTIVE,
                'UPDATED_BY' => Auth::user()->id,
            ]);

            DB::commit();

            return response([
                'success' => true,
                'status' => 3,
                'message' => 'Sim Deleted Successfully',
            ]);
        } catch (\Throwable|\Exception $e) {
            DB::rollBack();
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Sim Delete Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ]);
        }
    }

   

    public function paginationSim($request, bool $active = true): FoundationApplication|Response|ResponseFactory
    {
        try {
            $perPage = $request->input('per_page', MyHelper::$PAGINATION_PER_PAGE);
            $branch_id = Auth::user()->BRANCH_ID;
            $activeStatus = $active ? MyHelper::$RS_ACTIVE : MyHelper::$RS_INACTIVE;

            $authUser = Auth::user();

         $sim = Sim::select('sim.SIM_ID',
                'sim.SIM_MOBILE_NO',
                'sim.SIM_SERIAL_NO',
                'sim.SIM_PROVIDER',
                'sim.SIM_MAP_STATUS',
                'sim.SIM_REMARKS','O.NAME as ORG_NAME','O.LOCATION'
            )
                ->where('sim.ACTIVE', MyHelper::$RS_ACTIVE)
                ->where('sim.ORG_ID', "=","O.ORGANIZATIONID")
			    ->join("branch", "branch.BRANCH_ID", "=", DB::Raw($authUser->BRANCH_ID))
                ->Join("organization as O", "O.ORGANIZATIONID", "=", "branch.ORG_ID");
               
            $filterModel = $request->input('filterModel');
            if ($filterModel) {
                foreach ($filterModel as $field => $filter) {
                    if (isset($filter['filter']) && $filter['filter'] !== '') {
                        $value = $filter['filter'];
                        $type = $filter['type'];

                        switch ($field) {
                            case 'SIM_MOBILE_NO':
                                $sim->where('sim.SIM_MOBILE_NO', 'like', "%{$value}%");
                                break;
                        }
                    }
                }
            }


            if ($request->has('orderBy') && !empty($request->input('orderBy'))) {
                $orderBy = $request->input('orderBy');
                $order = $request->input('order', 'asc');
                $sim->orderBy($orderBy, $order);
            } else {
                $sim->orderBy('sim.CREATED_DATE', 'desc');
            }


            if ($perPage === MyHelper::$PAGINATION_PER_PAGE_ALL) {
                $paginatedSim = $sim->paginate($sim->count());
            } else {
                $perPage = is_numeric($perPage) ? (int)$perPage : MyHelper::$PAGINATION_PER_PAGE;
                $paginatedSim = $sim->paginate($perPage);
            }

            return response([
                'success' => true,
                'status' => 3,
                'employees' => $paginatedSim,
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'Sim Pagination Unsuccessful' : 'Sim Pagination Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ]);
        }
    } 

    public function editSim($simAutoIdCrypt): FoundationApplication|Response|ResponseFactory
    {
        $authUser = Auth::user();

        try {
            $simAutoId = Crypt::decryptString($simAutoIdCrypt);
            $sim = Sim::where('sim.ACTIVE', MyHelper::$RS_ACTIVE)
                 ->where('sim.ORG_ID', "=","O.ORGANIZATIONID")
				 ->where('sim.SIM_ID', $simAutoId)
			   
                ->select(
                    'sim.SIM_ID',
                    'sim.SIM_MOBILE_NO',
                    'sim.SIM_SERIAL_NO',
                    'sim.SIM_PROVIDER',
                )
				->join("branch", "branch.BRANCH_ID", "=", DB::Raw($authUser->BRANCH_ID))
                ->Join("organization as O", "O.ORGANIZATIONID", "=", "branch.ORG_ID")
                ->firstOrFail();

            return response([
                'success' => true,
                'status' => 3,
                'sim' => $sim,
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Sim Edit Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ]);
        }
    }

    public function updateSim($request, $simAutoIdCrypt): FoundationApplication|Response|ResponseFactory
    {
        $authUser = Auth::user();
        try {
            DB::beginTransaction();
            $simAutoId = Crypt::decryptString($simAutoIdCrypt);
            $sim = Sim::findOrFail($simAutoId);
            $auth_user = Auth::user();

            $simData = $this->prepareSimDataForUpdate($request, $auth_user, $sim);
            $sim->update($simData);

            DB::commit();


            return response([
                'success' => true,
                'status' => 3,
                'message' => 'Sim Updated Successfully',
            ]);

        } catch (\Throwable|\Exception $e) {
            DB::rollBack();
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'message' => 'Employee Update Unsuccessful',
                'status' => 4,
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * @throws ConnectionException
     */
    private function prepareSimDataForUpdate($request, $auth_user, $sim): array
    {
        $date = Carbon::now();

        return [
            "SIM_MOBILE_NO" => $request->SIM_MOBILE_NO,
            "SIM_SERIAL_NO" => $request->SIM_SERIAL_NO,
            "SIM_PROVIDER" => $request->SIM_PROVIDER,
            "SIM_REMARKS" => $request->SIM_REMARKS,
            "UPDATED_BY" => $auth_user->id,
            "UPDATED_AT" => $date->format("Y-m-d H:i:s"),
        ];
    }

    

    public function dataForCreateEmployee(): FoundationApplication|Response|ResponseFactory
    {

        try {
            $user = Auth::user();
            $rsActive = MyHelper::$RS_ACTIVE;
            $branchId = $user->BRANCH_ID;

            $branch = DB::table('branch')
                ->select('BRANCH_ID', 'DIVISION_ID')
                ->where('ACTIVE', $rsActive)
                ->where('BRANCH_ID', $branchId)
                ->first();

            $location = DB::table('locations')
                ->select('LOCATION_ID', 'LOCATION_NAME')
                ->where('ACTIVE', $rsActive)
                ->where('DIVISION_ID', $branch->DIVISION_ID)
                ->get();

            $branchList = DB::table('branch')
                ->select('BRANCH_ID', 'BRANCH_NAME')
                ->where('ACTIVE', $rsActive)
                ->where('BRANCH_ID', $branchId)
                ->get();


            return response([
                'success' => true,
                'status' => 3,
                'locations' => $location,
                'branch_list' => $branchList,
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Data For Create for Employee  Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ]);
        }
    }

}
