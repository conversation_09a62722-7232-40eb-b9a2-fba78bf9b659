<?php
namespace App\Http\Requests\Admin;

use App\Helpers\CommonFunction;
use Closure;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;

class EscortRequest extends FormRequest
{
    protected CommonFunction $commonFunction;

    public function __construct(CommonFunction $commonFunction)
    {
        
    }

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        
        return [
            'ESCORT_NAME' => 'required',
            'ESCORT_MOBILE' => ['required','numeric',Rule::unique('escorts', 'ESCORT_MOBILE')],
            'ESCORT_BATCH_NUMBER' => 'required'
        ];
    }
   

    public function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json([
            'success' => false,
            'validation_error' => true,
            'message' => 'Validation errors',
            'data' => $validator->errors()
        ]));
    }

    private function checkDeactivatedEscort($branchId): Closure
    {
        return function (string $attribute, mixed $value, Closure $fail) use ($branchId) {
            $deactivatedEscort = DB::table("escorts")
                ->where("ESCORT_ID", $value)
                ->where("ACTIVE", 2)
                ->exists();

            if ($deactivatedEscort) {
                $fail("Escort already exists and is deactivated.");
            }
        };
    }

   
}
