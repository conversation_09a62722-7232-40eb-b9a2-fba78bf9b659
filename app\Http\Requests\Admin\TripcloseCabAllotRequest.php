<?php

//namespace App\Http\Requests\Admin;
namespace App\Http\Requests\Admin;

use App\Helpers\CommonFunction;
use Closure;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;

class TripcloseCabAllotRequest extends FormRequest
{
    protected CommonFunction $commonFunction;

    public function __construct(CommonFunction $commonFunction)
    {
        
    }

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        
        return [
            
            'roster_id' => ['required','numeric'],
            'cabid' => ['required','numeric'],
            'billable_confirm' => ['nullable','string'],
            'vendor_id' => ['required','numeric'],
            'chk_first_pickup' => ['required','string'],
            'get_trip_type' => ['required','string'],
            
            
        ];
    }

    public function messages(): array
    {
        return [
            'cabid.required' => 'cabid field is required',
            'vendor_id.required' => 'vendor_id field is required',
            'roster_id.required' => 'roster_id field is required',
            'chk_first_pickup.required' => 'chk_first_pickup field is required',
            'get_trip_type.required' => 'get_trip_type field is required',
            
        ];
    }
   

    public function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json([
            'success' => false,
            'validation_error' => true,
            'message' => 'Validation errors',
            'data' => $validator->errors()
        ]));
    }
   
}
