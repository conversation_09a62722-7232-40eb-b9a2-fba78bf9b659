<?php

namespace App\Services\Admin;


use App\Helpers\CommonFunction;
use App\Helpers\MyHelper;
use App\Models\Roster;
use App\Models\RosterPassenger;
use App\Models\AlertSignalFailure;
use App\Models\Cab;
use App\Models\CabAttendance;
use App\Models\EmployeesRosterRequest;
use App\Models\Sms;
use App\Models\Employee;
use App\Models\PanicAlert;
use App\Models\EmptyCabKm;
use App\Models\MaskCallLogs;
use App\Models\MaskTransaction;
use Carbon\Carbon;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use App\Models\Vendor;
use App\Http\Controllers\ElasticController;

class ReportService
{
    protected CommonFunction $commonFunction;


    public function __construct(CommonFunction $commonFunction)
    {
        $this->commonFunction = $commonFunction;
    }


    public function fetch_OverAllReport_Details($request, bool $active = true): FoundationApplication|Response|ResponseFactory
    {
        try {
            $perPage = $request->input('per_page', MyHelper::$PAGINATION_PER_PAGE);
            $branch_id = Auth::user()->BRANCH_ID;
            $user_type = Auth::user()->user_type;
            $vendor_id = Auth::user()->vendor_id;

            if ($user_type == MyHelper::$ADMIN) {
                $vendorid = "";
            } else {
                $vendorid = $vendor_id;
            }


            $triptype_id = $request->triptype_id;

            if ($triptype_id == 'ALL' || $triptype_id == 'all') {
                $triptype = ['P', 'D'];
            } else {
                $triptype = [$triptype_id];
            }



             $from_date = Carbon::parse($request->from_date)->startOfDay()->toDateTimeString();
             $to_date = Carbon::parse($request->to_date)->endOfDay()->toDateTimeString();


            $RS_ACTIVE = MyHelper::$RS_ACTIVE;

            $RPS_CABDELAY = MyHelper::$RPS_CABDELAY;
            $RPS_NOSHOW =  MyHelper::$RPS_NOSHOW;
            $RPS_MANUALOTP =  MyHelper::$RPS_MANUALOTP;
            $RPS_EMPLOYEEDELAY =  MyHelper::$RPS_EMPLOYEEDELAY;
            $RS_DELAYROUTES =  MyHelper::$RS_DELAYROUTES;


            $comments = '';


            $data = Roster::query()
                ->select(
                    'rosters.ROUTE_ID',
                    'rosters.TRIP_TYPE',
                    'V.VEHICLE_REG_NO',
                    'VM.MODEL',
                    'VM.CAPACITY',
                    'VE.NAME as VENDOR_NAME',
                    'E.NAME as EMPLOYEE_NAME',
                    'RP.EMPLOYEE_ID',
                    'E.GENDER as GENDER',
                    'L.LOCATION_NAME',
                    'rosters.ESTIMATE_END_TIME',
                    'rosters.ESTIMATE_START_TIME', 
                    DB::raw("if(rosters.TRIP_TYPE = 'P',DATE(rosters.ESTIMATE_END_TIME),DATE(rosters.ESTIMATE_START_TIME)) as LOGIN_DATE"),
                    DB::raw("if(rosters.TRIP_TYPE = 'P',TIME(rosters.ESTIMATE_END_TIME),TIME(rosters.ESTIMATE_START_TIME)) as LOGIN_TIME"),
                    DB::raw("if(rosters.TRIP_TYPE = 'P',rosters.ESTIMATE_START_TIME,DATE_SUB(rosters.ESTIMATE_START_TIME, INTERVAL 30 MINUTE)) as SCHEDULE_CABALLOT"),
                    'rosters.CAB_ALLOT_TIME',
                    DB::raw("'0' AS DELAYALLOTTIME"),
                     'RP.ESTIMATE_START_TIME as rp_estimate_start_time',
                    DB::raw("if(rosters.TRIP_TYPE = 'P',RP.DRIVER_ARRIVAL_TIME,rosters.ACTUAL_START_TIME) as DRIVER_ARRIVAL_TIME,'0' AS CAB_DELAY"),
                    'RP.ACTUAL_START_TIME',
                    DB::raw("'0' AS EMP_DELAY"),
                    'rosters.ACTUAL_END_TIME',
                    'RP.ACTUAL_END_TIME as EMP_DROP_TIME',
                    DB::raw("TIMEDIFF(RP.ACTUAL_END_TIME,RP.ACTUAL_START_TIME) as TRAVEL_DURATION"),
                    DB::raw("(select ROUTE_ESCORT_ID from route_escorts re where re.ROSTER_ID = rosters.ROSTER_ID AND re.STATUS not in(5,6) limit 1 ) as ROUTE_ESCORT_ID"),
                    DB::raw("if(RP.ACTIVE=3,RM.REASON,'--')as REASON $comments"),
                    DB::raw("if(rosters.ROSTER_STATUS & $RS_DELAYROUTES,'Delay','Ontime') AS DELAYROUTES"),
                    DB::raw(" '--' AS ALLSTATUS"),
                    DB::raw("if(rosters.TRIP_TYPE = 'P',TIMEDIFF(RP.DRIVER_ARRIVAL_TIME,RP.ESTIMATE_START_TIME),TIMEDIFF(rosters.ACTUAL_START_TIME,RP.ESTIMATE_START_TIME)) AS CABDELAY"),
                    DB::raw("if(rosters.TRIP_TYPE = 'P',TIMEDIFF(RP.ACTUAL_START_TIME,if(RP.ESTIMATE_START_TIME > RP.DRIVER_ARRIVAL_TIME,RP.ESTIMATE_START_TIME,RP.DRIVER_ARRIVAL_TIME)),TIMEDIFF(RP.ACTUAL_START_TIME,if(RP.ESTIMATE_START_TIME > rosters.ACTUAL_START_TIME,RP.ESTIMATE_START_TIME,rosters.ACTUAL_START_TIME))) AS EMPDELAY"),
                    DB::raw("if(RP.ROSTER_PASSENGER_STATUS & $RPS_CABDELAY,1,0) as CABSTATUS,if(RP.ROSTER_PASSENGER_STATUS & $RPS_NOSHOW,1,0) as NOSHOWSTATUS"),
                    DB::raw("if(RP.ROSTER_PASSENGER_STATUS & $RPS_EMPLOYEEDELAY,1,0) as EMPSTATUS,if(RP.ROSTER_PASSENGER_STATUS & $RPS_MANUALOTP,1,0) as MANUALOTPSTATUS"),
                    DB::raw("if(rosters.TRIP_TYPE = 'P',TIMEDIFF(rosters.CAB_ALLOT_TIME,rosters.ESTIMATE_START_TIME),TIMEDIFF(rosters.CAB_ALLOT_TIME,DATE_SUB(rosters.ESTIMATE_START_TIME, INTERVAL 30 MINUTE))) AS DELAYALLOT_TIME"),
                    'RP.ROSTER_PASSENGER_STATUS',
                    'rosters.ROSTER_STATUS',
                    'rosters.CAB_ID'
                )


                ->join('roster_passengers as RP', function ($join) {
                    $join->on('RP.ROSTER_ID', '=', 'rosters.ROSTER_ID')
                        ->whereIn('RP.ACTIVE', ['1', '3']);
                })


                 

                ->Join('branch as  B', 'B.BRANCH_ID', '=', 'rosters.BRANCH_ID')
                ->Join('vendors as  VE', 'VE.VENDOR_ID', '=', 'rosters.VENDOR_ID')
                ->leftJoin('cab as  C', 'C.CAB_ID', '=', 'rosters.CAB_ID')
                ->leftJoin('vehicles as V', 'V.VEHICLE_ID', '=', 'C.VEHICLE_ID')
                ->leftJoin('vehicle_models as VM', 'VM.VEHICLE_MODEL_ID', '=', 'V.VEHICLE_MODEL_ID')
                ->join('employees as E', function ($join) use ($branch_id) {
                    $join->on('E.EMPLOYEES_ID', '=', 'RP.EMPLOYEE_ID')
                        ->where('E.BRANCH_ID',  '=', $branch_id);
                })
                ->leftJoin('locations as  L', 'L.LOCATION_ID', '=', 'RP.LOCATION_ID')
                ->leftJoin('reason_log as   RL', 'RL.ROSTER_PASSENGER_ID', '=', 'RP.ROSTER_PASSENGER_ID')
                ->leftJoin('reason_master as RM', 'RM.REASON_ID', '=', 'RL.REASON_ID')
                ->where('rosters.ACTIVE', $RS_ACTIVE)
                ->whereIn('rosters.TRIP_TYPE', $triptype)
                ->where('rosters.BRANCH_ID', $branch_id)
                ->when($vendorid, function ($query, $vendorid) {
                    return $query->where('rosters.VENDOR_ID', $vendorid);
                })
                 ->havingRaw("(rosters.ESTIMATE_END_TIME BETWEEN  '$from_date' AND '$to_date' OR  rosters.ESTIMATE_START_TIME BETWEEN  '$from_date' AND '$to_date')");


            $filterModel = $request->input('filterModel');
            if ($filterModel) {
                foreach ($filterModel as $field => $filter) {
                    if (isset($filter['filter']) && $filter['filter'] !== '') {
                        $value = $filter['filter'];
                        $type = $filter['type'];

                        switch ($field) {                            

                           case 'ROUTE_ID':
                                $data->where('rosters.ROUTE_ID', 'like', "%{$value}%");
                                break;

                           case 'TRIP_TYPE':
                                $data->where('rosters.ROUTE_ID', 'like', "%{$value}%");
                                break;
                           case 'VEHICLE_REG_NO':
                                $data->where('V.VEHICLE_REG_NO', 'like', "%{$value}%");
                                break;
                          case 'MODEL':
                                $data->where('VM.VEHICLE_REG_NO', 'like', "%{$value}%");
                                break;

                          case 'CAPACITY':
                                $data->where('rosters.CAPACITY', 'like', "%{$value}%");
                                break;

                         case 'VENDOR_NAME':
                                $data->where('VE.NAME', 'like', "%{$value}%");
                                break;



                        }
                    }
                }
            }


        
       /* { field: 'EMPLOYEE_NAME', headerName: 'Associate Name' },
        { field: 'EMPLOYEE_ID', headerName: 'Associate-Id' },
        { field: 'GENDER', headerName: 'Gender' },
        { field: 'LOCATION_NAME', headerName: 'Location' },
        { field: 'LOGIN_DATE', headerName: 'Login/Logout Date' },
        { field: 'LOGIN_TIME', headerName: 'Login/Logout Time' },
        { field: 'SCHEDULE_CABALLOT', headerName: 'Schedule Cab Allot Time' },
        { field: 'CAB_ALLOT_TIME', headerName: 'Actual Cab Allot Time' },
        { field: 'DELAYALLOT_TIME', headerName: 'Delay Allot Time' },
        { field: 'ESTIMATE_START_TIME', headerName: 'Schedule Pickup/Drop Time' },
        { field: 'RIVER_ARRIVAL_TIME', headerName: 'Cab Arrived Time' },
        { field: 'LOGIN_TIME', headerName: 'Cab Delay Time' },
        { field: 'rp_estimate_start_time', headerName: 'Associate Pickup/Drop Time' },
        { field: 'EMPDELAY', headerName: 'Associate Delay Time' },
        { field: 'ACTUAL_END_TIME', headerName: 'TripClose Time' },
        { field: 'EMP_DROP_TIME', headerName: 'Employee Drop Time' },
        { field: 'TRAVEL_DURATION', headerName: 'Travel Duration' },
        { field: 'ROUTE_ESCORT_ID', headerName: 'Escort Status' },
        { field: 'REASON', headerName: 'Reason Remark' },
        { field: 'tripstatus', headerName: 'Cab Status' },
        { field: 'passenger_status', headerName: 'Employee Status'},*/


            if ($request->has('orderBy') && !empty($request->input('orderBy'))) {
                $orderBy = $request->input('orderBy');
                $order = $request->input('order', 'DESC');
                $data->orderBy($orderBy, $order);
            } else {
                $data->orderBy('RP.ROSTER_PASSENGER_ID', 'DESC');
            }

            if ($perPage === MyHelper::$PAGINATION_PER_PAGE_ALL) {
                $paginateddata = $data->paginate($data->count());              
            } else {
                $paginateddata = $data->paginate($data->count());

                $statusCounts = $paginateddata->getCollection()
                    ->groupBy('passenger_status')
                    ->map(function ($group) {
                        return $group->count();
                    });

                $statusCount_data = json_decode($statusCounts, true);

                // Replace spaces with underscores in keys
                $newData = [];
                foreach ($statusCount_data as $key => $value) {
                    $newKey = str_replace(' ', '_', $key); // Replace spaces with underscores
                    $newData[$newKey] = $value;
                }

                // Encode the updated array back to JSON
                $newStatusCount_data = $newData; 
           

                $perPage = is_numeric($perPage) ? (int)$perPage : MyHelper::$PAGINATION_PER_PAGE;
                $paginateddata = $data->paginate($perPage);
               
            }

            

           

            return response([
                'success' => true,
                'status' => 3,
                'overall_report' => $paginateddata,
                'statusCounts' =>$newStatusCount_data, 

            ]);
        } catch (\Throwable | \Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'OverAllReport Pagination Unsuccessful' : 'OverAllReport Pagination Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ], 500);
        }
    }


    public function fetch_EmployeesWise_OverAllReport_Details($request, bool $active = true): FoundationApplication|Response|ResponseFactory
    {
        try {
            $perPage = $request->input('per_page', MyHelper::$PAGINATION_PER_PAGE);
            $branch_id = Auth::user()->BRANCH_ID;
            $user_type = Auth::user()->user_type;
            $vendor_id = Auth::user()->vendor_id;

            if ($user_type == MyHelper::$ADMIN) {
                $vendorid = "";
            } else {
                $vendorid = $vendor_id;
            }

            $employee_id = $request->employee_id;

            if ($employee_id == 'ALL' ||  $employee_id == 'all') {
                $employeeid = '';
            } else {
                $employeeid =  $employee_id;
            }



            $triptype_id = $request->triptype_id;

            if ($triptype_id == 'ALL' || $triptype_id == 'all') {
                $triptype = ['P', 'D'];
            } else {
                $triptype = [$triptype_id];
            }

             $from_date = Carbon::parse($request->from_date)->startOfDay()->toDateTimeString();
             $to_date = Carbon::parse($request->to_date)->endOfDay()->toDateTimeString();


            $RS_ACTIVE = MyHelper::$RS_ACTIVE;

            $RPS_CABDELAY = MyHelper::$RPS_CABDELAY;
            $RPS_NOSHOW =  MyHelper::$RPS_NOSHOW;
            $RPS_MANUALOTP =  MyHelper::$RPS_MANUALOTP;
            $RPS_EMPLOYEEDELAY =  MyHelper::$RPS_EMPLOYEEDELAY;
            $RS_DELAYROUTES =  MyHelper::$RS_DELAYROUTES;


            $comments = '';


            $data = Roster::query()
                ->select(
                    'rosters.ROUTE_ID',
                    'rosters.TRIP_TYPE',
                    'V.VEHICLE_REG_NO',
                    'VM.MODEL',
                    'VM.CAPACITY',
                    'VE.NAME as VENDOR_NAME',
                    'E.NAME as EMPLOYEE_NAME',
                    'RP.EMPLOYEE_ID',
                    'E.GENDER as GENDER',
                    'L.LOCATION_NAME',
                    'rosters.ESTIMATE_END_TIME',
                    'rosters.ESTIMATE_START_TIME', 
                    DB::raw("if(rosters.TRIP_TYPE = 'P',DATE(rosters.ESTIMATE_END_TIME),DATE(rosters.ESTIMATE_START_TIME)) as LOGIN_DATE"),
                    DB::raw("if(rosters.TRIP_TYPE = 'P',TIME(rosters.ESTIMATE_END_TIME),TIME(rosters.ESTIMATE_START_TIME)) as LOGIN_TIME"),
                    DB::raw("if(rosters.TRIP_TYPE = 'P',rosters.ESTIMATE_START_TIME,DATE_SUB(rosters.ESTIMATE_START_TIME, INTERVAL 30 MINUTE)) as SCHEDULE_CABALLOT"),
                    'rosters.CAB_ALLOT_TIME',
                    DB::raw("'0' AS DELAYALLOTTIME"),
                     'RP.ESTIMATE_START_TIME as rp_estimate_start_time',
                    DB::raw("if(rosters.TRIP_TYPE = 'P',RP.DRIVER_ARRIVAL_TIME,rosters.ACTUAL_START_TIME) as DRIVER_ARRIVAL_TIME,'0' AS CAB_DELAY"),
                    'RP.ACTUAL_START_TIME',
                    DB::raw("'0' AS EMP_DELAY"),
                    'rosters.ACTUAL_END_TIME',
                    'RP.ACTUAL_END_TIME as EMP_DROP_TIME',
                    DB::raw("TIMEDIFF(RP.ACTUAL_END_TIME,RP.ACTUAL_START_TIME) as TRAVEL_DURATION"),
                    DB::raw("(select ROUTE_ESCORT_ID from route_escorts re where re.ROSTER_ID = rosters.ROSTER_ID AND re.STATUS not in(5,6) limit 1 ) as ROUTE_ESCORT_ID"),
                    DB::raw("if(RP.ACTIVE=3,RM.REASON,'--')as REASON $comments"),
                    DB::raw("if(rosters.ROSTER_STATUS & $RS_DELAYROUTES,'Delay','Ontime') AS DELAYROUTES"),
                    DB::raw(" '--' AS ALLSTATUS"),
                    DB::raw("if(rosters.TRIP_TYPE = 'P',TIMEDIFF(RP.DRIVER_ARRIVAL_TIME,RP.ESTIMATE_START_TIME),TIMEDIFF(rosters.ACTUAL_START_TIME,RP.ESTIMATE_START_TIME)) AS CABDELAY"),
                    DB::raw("if(rosters.TRIP_TYPE = 'P',TIMEDIFF(RP.ACTUAL_START_TIME,if(RP.ESTIMATE_START_TIME > RP.DRIVER_ARRIVAL_TIME,RP.ESTIMATE_START_TIME,RP.DRIVER_ARRIVAL_TIME)),TIMEDIFF(RP.ACTUAL_START_TIME,if(RP.ESTIMATE_START_TIME > rosters.ACTUAL_START_TIME,RP.ESTIMATE_START_TIME,rosters.ACTUAL_START_TIME))) AS EMPDELAY"),
                    DB::raw("if(RP.ROSTER_PASSENGER_STATUS & $RPS_CABDELAY,1,0) as CABSTATUS,if(RP.ROSTER_PASSENGER_STATUS & $RPS_NOSHOW,1,0) as NOSHOWSTATUS"),
                    DB::raw("if(RP.ROSTER_PASSENGER_STATUS & $RPS_EMPLOYEEDELAY,1,0) as EMPSTATUS,if(RP.ROSTER_PASSENGER_STATUS & $RPS_MANUALOTP,1,0) as MANUALOTPSTATUS"),
                    DB::raw("if(rosters.TRIP_TYPE = 'P',TIMEDIFF(rosters.CAB_ALLOT_TIME,rosters.ESTIMATE_START_TIME),TIMEDIFF(rosters.CAB_ALLOT_TIME,DATE_SUB(rosters.ESTIMATE_START_TIME, INTERVAL 30 MINUTE))) AS DELAYALLOT_TIME"),
                    'RP.ROSTER_PASSENGER_STATUS',
                    'rosters.ROSTER_STATUS',
                    'rosters.CAB_ID'
                )
                ->join('roster_passengers as RP', function ($join) use ($employeeid) {

                    if ($employeeid == '') {
                        $join->on('RP.ROSTER_ID', '=', 'rosters.ROSTER_ID')
                            ->whereIn('RP.ACTIVE', ['1', '3']);
                    } else {

                        $join->on('RP.ROSTER_ID', '=', 'rosters.ROSTER_ID')
                            ->where('RP.EMPLOYEE_ID', $employeeid)
                            ->whereIn('RP.ACTIVE', ['1', '3']);
                    }
                })
                

                ->Join('branch as  B', 'B.BRANCH_ID', '=', 'rosters.BRANCH_ID')
                ->Join('vendors as  VE', 'VE.VENDOR_ID', '=', 'rosters.VENDOR_ID')
                ->leftJoin('cab as  C', 'C.CAB_ID', '=', 'rosters.CAB_ID')
                ->leftJoin('vehicles as V', 'V.VEHICLE_ID', '=', 'C.VEHICLE_ID')
                ->leftJoin('vehicle_models as VM', 'VM.VEHICLE_MODEL_ID', '=', 'V.VEHICLE_MODEL_ID')
                ->join('employees as E', function ($join) use ($branch_id) {
                    $join->on('E.EMPLOYEES_ID', '=', 'RP.EMPLOYEE_ID')
                        ->where('E.BRANCH_ID',  '=', $branch_id);
                })
                ->leftJoin('locations as  L', 'L.LOCATION_ID', '=', 'RP.LOCATION_ID')
                ->leftJoin('reason_log as   RL', 'RL.ROSTER_PASSENGER_ID', '=', 'RP.ROSTER_PASSENGER_ID')
                ->leftJoin('reason_master as RM', 'RM.REASON_ID', '=', 'RL.REASON_ID')

                ->where('rosters.ACTIVE', $RS_ACTIVE)
                ->whereIn('rosters.TRIP_TYPE', $triptype)
                ->where('rosters.BRANCH_ID', $branch_id)

                ->when($vendorid, function ($query, $vendorid) {
                    return $query->where('rosters.VENDOR_ID', $vendorid);
                })
                ->havingRaw("(rosters.ESTIMATE_END_TIME BETWEEN  '$from_date' AND '$to_date' OR  rosters.ESTIMATE_START_TIME BETWEEN  '$from_date' AND '$to_date')"); 


            $filterModel = $request->input('filterModel');
            if ($filterModel) {
                foreach ($filterModel as $field => $filter) {
                    if (isset($filter['filter']) && $filter['filter'] !== '') {
                        $value = $filter['filter'];
                        $type = $filter['type'];

                        switch ($field) {
                            case 'VEHICLE_REG_NO':
                                $data->where('V.VEHICLE_REG_NO', 'like', "%{$value}%");
                                break;
                        }
                    }
                }
            }


            if ($request->has('orderBy') && !empty($request->input('orderBy'))) {
                $orderBy = $request->input('orderBy');
                $order = $request->input('order', 'DESC');
                $data->orderBy($orderBy, $order);
            } else {
                $data->orderBy('RP.ROSTER_PASSENGER_ID', 'DESC');
            }


            if ($perPage === MyHelper::$PAGINATION_PER_PAGE_ALL) {
                $paginateddata = $data->paginate($data->count());
            } else {
           
                $paginateddata = $data->paginate($data->count());

                $statusCounts = $paginateddata->getCollection()
                    ->groupBy('passenger_status')
                    ->map(function ($group) {
                        return $group->count();
                    });

                $statusCount_data = json_decode($statusCounts, true);

                // Replace spaces with underscores in keys
                $newData = [];
                foreach ($statusCount_data as $key => $value) {
                    $newKey = str_replace(' ', '_', $key); // Replace spaces with underscores
                    $newData[$newKey] = $value;
                }

                // Encode the updated array back to JSON
                $newStatusCount_data = $newData; 
           

                $perPage = is_numeric($perPage) ? (int)$perPage : MyHelper::$PAGINATION_PER_PAGE;
                $paginateddata = $data->paginate($perPage);
            }

            return response([
                'success' => true,
                'status' => 3,
                'employee_overall_report' => $paginateddata,
                'statusCounts' => $newStatusCount_data,
            ]);
        } catch (\Throwable | \Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'Employee_OverAllReport Pagination Unsuccessful' : 'Employee_OverAllReport Pagination Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ], 500);
        }
    }



    public function fetch_VehicleWise_OverAllReport_Details($request, bool $active = true): FoundationApplication|Response|ResponseFactory
    {
        try {
            $perPage = $request->input('per_page', MyHelper::$PAGINATION_PER_PAGE);
            $branch_id = Auth::user()->BRANCH_ID;
            $user_type = Auth::user()->user_type;
            $vendor_id = Auth::user()->vendor_id;

            if ($user_type == MyHelper::$ADMIN) {
                $vendorid = "";
            } else {
                $vendorid = $vendor_id;
            }

            $vehicle_id = $request->vehicle_id;

            if ($vehicle_id  == 'ALL' ||  $vehicle_id  == 'all') {
                $vehicleid  = '';
            } else {
                $vehicleid =  $vehicle_id;
            }


            $triptype_id = $request->triptype_id;

            if ($triptype_id == 'ALL' || $triptype_id == 'all') {
                $triptype = ['P', 'D'];
            } else {
                $triptype = [$triptype_id];
            }



             $from_date = Carbon::parse($request->from_date)->startOfDay()->toDateTimeString();
             $to_date = Carbon::parse($request->to_date)->endOfDay()->toDateTimeString();

            $RS_ACTIVE = MyHelper::$RS_ACTIVE;

            $RPS_CABDELAY = MyHelper::$RPS_CABDELAY;
            $RPS_NOSHOW =  MyHelper::$RPS_NOSHOW;
            $RPS_MANUALOTP =  MyHelper::$RPS_MANUALOTP;
            $RPS_EMPLOYEEDELAY =  MyHelper::$RPS_EMPLOYEEDELAY;
            $RS_DELAYROUTES =  MyHelper::$RS_DELAYROUTES;


            $comments = '';


            $data = Roster::query()
                ->select(
                    'rosters.ROUTE_ID',
                    'rosters.TRIP_TYPE',
                    'V.VEHICLE_REG_NO',
                    'VM.MODEL',
                    'VM.CAPACITY',
                    'VE.NAME as VENDOR_NAME',
                    'E.NAME as EMPLOYEE_NAME',
                    'RP.EMPLOYEE_ID',
                    'E.GENDER as GENDER',
                    'L.LOCATION_NAME',
                    'rosters.ESTIMATE_END_TIME',
                    'rosters.ESTIMATE_START_TIME', 
                    DB::raw("if(rosters.TRIP_TYPE = 'P',DATE(rosters.ESTIMATE_END_TIME),DATE(rosters.ESTIMATE_START_TIME)) as LOGIN_DATE"),
                    DB::raw("if(rosters.TRIP_TYPE = 'P',TIME(rosters.ESTIMATE_END_TIME),TIME(rosters.ESTIMATE_START_TIME)) as LOGIN_TIME"),
                    DB::raw("if(rosters.TRIP_TYPE = 'P',rosters.ESTIMATE_START_TIME,DATE_SUB(rosters.ESTIMATE_START_TIME, INTERVAL 30 MINUTE)) as SCHEDULE_CABALLOT"),
                    'rosters.CAB_ALLOT_TIME',
                    DB::raw("'0' AS DELAYALLOTTIME"),
                     'RP.ESTIMATE_START_TIME as rp_estimate_start_time',
                    DB::raw("if(rosters.TRIP_TYPE = 'P',RP.DRIVER_ARRIVAL_TIME,rosters.ACTUAL_START_TIME) as DRIVER_ARRIVAL_TIME,'0' AS CAB_DELAY"),
                    'RP.ACTUAL_START_TIME',
                    DB::raw("'0' AS EMP_DELAY"),
                    'rosters.ACTUAL_END_TIME',
                    'RP.ACTUAL_END_TIME as EMP_DROP_TIME',
                    DB::raw("TIMEDIFF(RP.ACTUAL_END_TIME,RP.ACTUAL_START_TIME) as TRAVEL_DURATION"),
                    DB::raw("(select ROUTE_ESCORT_ID from route_escorts re where re.ROSTER_ID = rosters.ROSTER_ID AND re.STATUS not in(5,6) limit 1 ) as ROUTE_ESCORT_ID"),
                    DB::raw("if(RP.ACTIVE=3,RM.REASON,'--')as REASON $comments"),
                    DB::raw("if(rosters.ROSTER_STATUS & $RS_DELAYROUTES,'Delay','Ontime') AS DELAYROUTES"),
                    DB::raw(" '--' AS ALLSTATUS"),
                    DB::raw("if(rosters.TRIP_TYPE = 'P',TIMEDIFF(RP.DRIVER_ARRIVAL_TIME,RP.ESTIMATE_START_TIME),TIMEDIFF(rosters.ACTUAL_START_TIME,RP.ESTIMATE_START_TIME)) AS CABDELAY"),
                    DB::raw("if(rosters.TRIP_TYPE = 'P',TIMEDIFF(RP.ACTUAL_START_TIME,if(RP.ESTIMATE_START_TIME > RP.DRIVER_ARRIVAL_TIME,RP.ESTIMATE_START_TIME,RP.DRIVER_ARRIVAL_TIME)),TIMEDIFF(RP.ACTUAL_START_TIME,if(RP.ESTIMATE_START_TIME > rosters.ACTUAL_START_TIME,RP.ESTIMATE_START_TIME,rosters.ACTUAL_START_TIME))) AS EMPDELAY"),
                    DB::raw("if(RP.ROSTER_PASSENGER_STATUS & $RPS_CABDELAY,1,0) as CABSTATUS,if(RP.ROSTER_PASSENGER_STATUS & $RPS_NOSHOW,1,0) as NOSHOWSTATUS"),
                    DB::raw("if(RP.ROSTER_PASSENGER_STATUS & $RPS_EMPLOYEEDELAY,1,0) as EMPSTATUS,if(RP.ROSTER_PASSENGER_STATUS & $RPS_MANUALOTP,1,0) as MANUALOTPSTATUS"),
                    DB::raw("if(rosters.TRIP_TYPE = 'P',TIMEDIFF(rosters.CAB_ALLOT_TIME,rosters.ESTIMATE_START_TIME),TIMEDIFF(rosters.CAB_ALLOT_TIME,DATE_SUB(rosters.ESTIMATE_START_TIME, INTERVAL 30 MINUTE))) AS DELAYALLOT_TIME"),
                    'RP.ROSTER_PASSENGER_STATUS',
                    'rosters.ROSTER_STATUS',
                    'rosters.CAB_ID'
                )
                ->join('roster_passengers as RP', function ($join) {
                    $join->on('RP.ROSTER_ID', '=', 'rosters.ROSTER_ID')
                        ->whereIn('RP.ACTIVE', ['1', '3']);
                })
               


               

                ->Join('branch as  B', 'B.BRANCH_ID', '=', 'rosters.BRANCH_ID')
                ->Join('vendors as  VE', 'VE.VENDOR_ID', '=', 'rosters.VENDOR_ID')
                ->leftJoin('cab as  C', 'C.CAB_ID', '=', 'rosters.CAB_ID')
                ->leftJoin('vehicles as V', 'V.VEHICLE_ID', '=', 'C.VEHICLE_ID')
                ->leftJoin('vehicle_models as VM', 'VM.VEHICLE_MODEL_ID', '=', 'V.VEHICLE_MODEL_ID')
                ->join('employees as E', function ($join) use ($branch_id) {
                    $join->on('E.EMPLOYEES_ID', '=', 'RP.EMPLOYEE_ID')
                        ->where('E.BRANCH_ID',  '=', $branch_id);
                })
                ->leftJoin('locations as  L', 'L.LOCATION_ID', '=', 'RP.LOCATION_ID')
                ->leftJoin('reason_log as   RL', 'RL.ROSTER_PASSENGER_ID', '=', 'RP.ROSTER_PASSENGER_ID')
                ->leftJoin('reason_master as RM', 'RM.REASON_ID', '=', 'RL.REASON_ID')

                ->where('rosters.ACTIVE', $RS_ACTIVE)
                ->whereIn('rosters.TRIP_TYPE', $triptype)
                ->where('rosters.BRANCH_ID', $branch_id)

                ->when($vendorid, function ($query, $vendorid) {
                    return $query->where('rosters.VENDOR_ID', $vendorid);
                })
                ->where(function ($query) use ($vehicleid) {
                    if ($vehicleid != '') {
                        $query->where('rosters.CAB_ID', $vehicleid);
                    }
                })
                
               ->havingRaw("(rosters.ESTIMATE_END_TIME BETWEEN  '$from_date' AND '$to_date' OR  rosters.ESTIMATE_START_TIME BETWEEN  '$from_date' AND '$to_date')"); 


            $filterModel = $request->input('filterModel');
            if ($filterModel) {
                foreach ($filterModel as $field => $filter) {
                    if (isset($filter['filter']) && $filter['filter'] !== '') {
                        $value = $filter['filter'];
                        $type = $filter['type'];

                        switch ($field) {
                            case 'VEHICLE_REG_NO':
                                $data->where('V.VEHICLE_REG_NO', 'like', "%{$value}%");
                                break;
                        }
                    }
                }
            }


            if ($request->has('orderBy') && !empty($request->input('orderBy'))) {
                $orderBy = $request->input('orderBy');
                $order = $request->input('order', 'DESC');
                $data->orderBy($orderBy, $order);
            } else {
                $data->orderBy('RP.ROSTER_PASSENGER_ID', 'DESC');
            }


            if ($perPage === MyHelper::$PAGINATION_PER_PAGE_ALL) {
                $paginateddata = $data->paginate($data->count());
            } else {
                $paginateddata = $data->paginate($data->count());

                $statusCounts = $paginateddata->getCollection()
                    ->groupBy('passenger_status')
                    ->map(function ($group) {
                        return $group->count();
                    });

                $statusCount_data = json_decode($statusCounts, true);

                // Replace spaces with underscores in keys
                $newData = [];
                foreach ($statusCount_data as $key => $value) {
                    $newKey = str_replace(' ', '_', $key); // Replace spaces with underscores
                    $newData[$newKey] = $value;
                }

                // Encode the updated array back to JSON
                $newStatusCount_data = $newData; 
           

                $perPage = is_numeric($perPage) ? (int)$perPage : MyHelper::$PAGINATION_PER_PAGE;
                $paginateddata = $data->paginate($perPage);
            }

            return response([
                'success' => true,
                'status' => 3,
                'vehicle_overall_report' => $paginateddata,
                'statusCounts' => $newStatusCount_data,
            ]);
        } catch (\Throwable | \Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'Vehicle_OverAllReport Pagination Unsuccessful' : 'Vehicle_OverAllReport Pagination Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ], 500);
        }
    }

    public function fetch_Associate_Noshow_Details($request, bool $active = true): FoundationApplication|Response|ResponseFactory
    {
        try {
            $perPage = $request->input('per_page', MyHelper::$PAGINATION_PER_PAGE);
            $branch_id = Auth::user()->BRANCH_ID;
            $user_type = Auth::user()->user_type;
            $vendor_id = Auth::user()->vendor_id;

            if ($user_type == MyHelper::$ADMIN) {
                $vendorid = "";
            } else {
                $vendorid = $vendor_id;
            }

            $triptype_id = $request->triptype_id;

            if ($triptype_id == 'ALL' || $triptype_id == 'all') {
                $triptype = ['P', 'D'];
            } else {
                $triptype = [$triptype_id];
            }


             $from_date = Carbon::parse($request->from_date)->startOfDay()->toDateTimeString();
             $to_date = Carbon::parse($request->to_date)->endOfDay()->toDateTimeString();


            $RS_ACTIVE = MyHelper::$RS_ACTIVE;
            $RPS_NOSHOW =  MyHelper::$RPS_NOSHOW;


            $data = RosterPassenger::query()
                ->select(
                    'R.ROUTE_ID',
                    'R.TRIP_TYPE',
                    'V.VEHICLE_REG_NO',
                    'VM.MODEL',
                    'VM.CAPACITY',
                    'VE.NAME AS VENDORNAME',
                    'E.EMPLOYEES_ID',
                    'E.NAME AS EMPNAME',
                    'E.MOBILE',
                    'L.LOCATION_NAME',
                    'R.ESTIMATE_END_TIME',
                    'R.ESTIMATE_START_TIME',
                    DB::raw("if(R.TRIP_TYPE = 'P',DATE(R.ESTIMATE_END_TIME),DATE(R.ESTIMATE_START_TIME)) as INOUT_DATE"),
                    DB::raw("if(R.TRIP_TYPE = 'P',TIME(R.ESTIMATE_END_TIME),TIME(R.ESTIMATE_START_TIME)) AS INOUT_TIME"),
                    DB::raw("if(roster_passengers.REMARKS is NULL,RM.REASON,roster_passengers.REMARKS) AS REASON_REMARKS"),
                    'roster_passengers.ROSTER_PASSENGER_ID'
                )
                ->Join('rosters as R', 'R.ROSTER_ID', '=', 'roster_passengers.ROSTER_ID')
                ->Join('branch as  B', 'B.BRANCH_ID', '=', 'R.BRANCH_ID')
                ->Join('vendors as  VE', 'VE.VENDOR_ID', '=', 'R.VENDOR_ID')
                ->leftJoin('cab as  C', 'C.CAB_ID', '=', 'R.CAB_ID')
                ->leftJoin('vehicles as V', 'V.VEHICLE_ID', '=', 'C.VEHICLE_ID')
                ->leftJoin('vehicle_models as VM', 'VM.VEHICLE_MODEL_ID', '=', 'V.VEHICLE_MODEL_ID')
                ->join('employees as E', function ($join) use ($branch_id) {
                    $join->on('E.EMPLOYEES_ID', '=', 'roster_passengers.EMPLOYEE_ID')
                        ->where('E.BRANCH_ID',  '=', $branch_id);
                })
                ->leftJoin('locations as  L', 'L.LOCATION_ID', '=', 'roster_passengers.LOCATION_ID')
                ->leftJoin('reason_log as   RL', 'RL.ROSTER_PASSENGER_ID', '=', 'roster_passengers.ROSTER_PASSENGER_ID')
                ->leftJoin('reason_master as RM', 'RM.REASON_ID', '=', 'RL.REASON_ID')
                ->where('R.ACTIVE', $RS_ACTIVE)
                ->whereIn('R.TRIP_TYPE', $triptype)
                ->where('R.BRANCH_ID', $branch_id)
                ->when($vendorid, function ($query, $vendorid) {
                    return $query->where('R.VENDOR_ID', $vendorid);
                })
                ->whereRaw("roster_passengers.ROSTER_PASSENGER_STATUS & $RPS_NOSHOW")
               ->havingRaw("(R.ESTIMATE_END_TIME BETWEEN  '$from_date' AND '$to_date' OR  R.ESTIMATE_START_TIME BETWEEN  '$from_date' AND '$to_date')");


            $filterModel = $request->input('filterModel');
            if ($filterModel) {
                foreach ($filterModel as $field => $filter) {
                    if (isset($filter['filter']) && $filter['filter'] !== '') {
                        $value = $filter['filter'];
                        $type = $filter['type'];

                        switch ($field) {
                            case 'VEHICLE_REG_NO':
                                $data->where('V.VEHICLE_REG_NO', 'like', "%{$value}%");
                                break;
                        }
                    }
                }
            }


            if ($request->has('orderBy') && !empty($request->input('orderBy'))) {
                $orderBy = $request->input('orderBy');
                $order = $request->input('order', 'DESC');
                $data->orderBy($orderBy, $order);
            } else {
                $data->orderBy('roster_passengers.ROSTER_PASSENGER_ID', 'DESC');
            }


            if ($perPage === MyHelper::$PAGINATION_PER_PAGE_ALL) {
                $paginateddata = $data->paginate($data->count());
            } else {
                $perPage = is_numeric($perPage) ? (int)$perPage : MyHelper::$PAGINATION_PER_PAGE;
                $paginateddata = $data->paginate($perPage);
            }

            return response([
                'success' => true,
                'status' => 3,
                'associate_noshow_report' => $paginateddata,
            ]);
        } catch (\Throwable | \Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'Associate Noshow Report Pagination Unsuccessful' : 'Associate Noshow Report Pagination Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ], 500);
        }
    }


    
     public function fetch_Ontime_Report_Details($request, bool $active = true): FoundationApplication|Response|ResponseFactory
    {
        try {
            $perPage = $request->input('per_page', MyHelper::$PAGINATION_PER_PAGE);
            $branch_id = Auth::user()->BRANCH_ID;
            $user_type = Auth::user()->user_type;
            $vendor_id = Auth::user()->vendor_id;

            if ($user_type == MyHelper::$ADMIN) {
                $vendorid = "";
            } else {
                $vendorid = $vendor_id;
            }

            $triptype_id = $request->triptype_id;

            if ($triptype_id == 'ALL' || $triptype_id == 'all') {
                $triptype = ['P', 'D'];
            } else {
                $triptype = [$triptype_id];
            }


            $from_date = Carbon::parse($request->from_date)->startOfDay()->toDateTimeString();
            $to_date = Carbon::parse($request->to_date)->endOfDay()->toDateTimeString();

            $RS_ACTIVE = MyHelper::$RS_ACTIVE;
            $RS_DELAYROUTES = MyHelper::$RS_DELAYROUTES;
            $RS_MANUALTRIPCLOSE = MyHelper::$RS_MANUALTRIPCLOSE;
            $RS_TRIPCLOSE = MyHelper::$RS_TRIPCLOSE;

            $data = Roster::query()
                ->select(
                    'rosters.ROUTE_ID',
                    'RS.TTLEMPCNT',
                    'RS.TRAVELEMPCNT',
                    'RS.EMPNOSHOWCNT',
                    'rosters.TRIP_TYPE',
                    'V.VEHICLE_REG_NO',
                    'VM.MODEL',
                    'VM.CAPACITY',
                    'VE.NAME AS VENDORNAME',
                    'rosters.START_LOCATION',
                    'rosters.END_LOCATION',
                    'rosters.ESTIMATE_END_TIME',
                    'rosters.ESTIMATE_START_TIME',
                    DB::raw("if(rosters.TRIP_TYPE = 'P',DATE(rosters.ESTIMATE_END_TIME),DATE(rosters.ESTIMATE_START_TIME)) as INOUT_DATE"),
                    DB::raw("if(rosters.TRIP_TYPE = 'P',TIME(rosters.ESTIMATE_END_TIME),TIME(rosters.ESTIMATE_START_TIME)) AS INOUT_TIME"),
                    'rosters.ACTUAL_END_TIME',
                     DB::raw(" '0'  AS TIMEDELAY"),
                    DB::raw("TIME_FORMAT(TIMEDIFF(rosters.ACTUAL_END_TIME,CONCAT(if(rosters.TRIP_TYPE = 'P',DATE(rosters.ESTIMATE_END_TIME),DATE(rosters.ESTIMATE_START_TIME)),'',if(rosters.TRIP_TYPE = 'P',TIME(rosters.ESTIMATE_END_TIME),TIME(rosters.ESTIMATE_START_TIME)))),'%H:%i:%s') AS TIMESDELAY"),
                    DB::raw("case 
                            when rosters.TRIP_TYPE='P' then if(TIMEDIFF(CONCAT(if(rosters.TRIP_TYPE = 'P',DATE(rosters.ESTIMATE_END_TIME),DATE(rosters.ESTIMATE_START_TIME)),' ',if(rosters.TRIP_TYPE = 'P',TIME(rosters.ESTIMATE_END_TIME),TIME(rosters.ESTIMATE_START_TIME))),rosters.ACTUAL_END_TIME)>'0','OntimePickup','pickupDelay')
                            else (select if(TIMEDIFF(ADDTIME(RR.ESTIMATE_START_TIME,'00:15:00'),RR.ACTUAL_START_TIME)>'0','OnTimeDrop','DropDelay') from roster_passengers RR 
                            where RR.ROSTER_ID= rosters.ROSTER_ID and RR.ACTIVE=1 GROUP BY RR.ROSTER_ID ORDER BY MAX(RR.ROUTE_ORDER))
                            end as delay_routes,
                            
                            case
                            
                            when rosters.TRIP_TYPE='P' then rosters.ACTUAL_END_TIME
                            else  (select max(RR.ACTUAL_START_TIME) from roster_passengers RR 
                            where RR.ROSTER_ID= rosters.ROSTER_ID and RR.ACTIVE=1 GROUP BY RR.ROSTER_ID ORDER BY MAX(RR.ROUTE_ORDER))
                            end as MANUAL_TRIPCLOSE,
                            
                            case 
                            when rosters.TRIP_TYPE='P' then if(TIMEDIFF(rosters.ESTIMATE_END_TIME,rosters.ACTUAL_END_TIME)>'0','Ontime','Delay')
                            else (select if(TIMEDIFF(ADDTIME(RR.ESTIMATE_START_TIME,'00:15:00'),RR.ACTUAL_START_TIME)>'0','OnTime','Delay') from roster_passengers RR 
                            where RR.ROSTER_ID= rosters.ROSTER_ID and RR.ACTIVE=1 GROUP BY RR.ROSTER_ID ORDER BY MAX(RR.ROUTE_ORDER))
                            end as STSVAL"),

                    DB::raw("if(rosters.ROSTER_STATUS & $RS_DELAYROUTES,1,0) AS DELAYROUTES,0 as p_d_time"),
                    DB::raw("if(rosters.ROSTER_STATUS & $RS_MANUALTRIPCLOSE,1,0) AS MANUALTRIPCLOSE"),
                    'rosters.ROSTER_ID'
                )

                ->leftJoin(
                    DB::raw("(SELECT ROSTER_ID, 
                COUNT(*) AS TTLEMPCNT,
                SUM(IF(ROSTER_PASSENGER_STATUS & 16, 1, 0)) AS EMPNOSHOWCNT,
                SUM(IF((ROSTER_PASSENGER_STATUS & 32 OR ROSTER_PASSENGER_STATUS & 128), 1, 0)) AS TRAVELEMPCNT
                FROM roster_passengers 
                WHERE ACTIVE = $RS_ACTIVE 
                AND (
                    ESTIMATE_END_TIME BETWEEN  '$from_date' AND  '$to_date' 
                    OR ESTIMATE_START_TIME BETWEEN  '$from_date' AND  '$to_date'
                )
                GROUP BY ROSTER_ID
                     ) as RS"),
                    'RS.ROSTER_ID',
                    '=',
                    'rosters.ROSTER_ID'
                )
                ->Join('branch as  B', 'B.BRANCH_ID', '=', 'rosters.BRANCH_ID')
                ->Join('vendors as  VE', 'VE.VENDOR_ID', '=', 'rosters.VENDOR_ID')
                ->leftJoin('cab as  C', 'C.CAB_ID', '=', 'rosters.CAB_ID')
                ->leftJoin('vehicles as V', 'V.VEHICLE_ID', '=', 'C.VEHICLE_ID')
                ->leftJoin('vehicle_models as VM', 'VM.VEHICLE_MODEL_ID', '=', 'V.VEHICLE_MODEL_ID')

                ->where('rosters.ACTIVE', $RS_ACTIVE)
                ->whereIn('rosters.TRIP_TYPE', $triptype)
                ->where('rosters.BRANCH_ID', $branch_id)
                ->when($vendorid, function ($query, $vendorid) {
                    return $query->where('rosters.VENDOR_ID', $vendorid);
                })
                ->whereRaw("(rosters.ROSTER_STATUS & $RS_TRIPCLOSE OR rosters.ROSTER_STATUS & $RS_MANUALTRIPCLOSE)")
              ->havingRaw("(rosters.ESTIMATE_END_TIME BETWEEN  '$from_date' AND '$to_date' OR  rosters.ESTIMATE_START_TIME BETWEEN  '$from_date' AND '$to_date')");


            $filterModel = $request->input('filterModel');
            if ($filterModel) {
                foreach ($filterModel as $field => $filter) {
                    if (isset($filter['filter']) && $filter['filter'] !== '') {
                        $value = $filter['filter'];
                        $type = $filter['type'];

                        switch ($field) {
                            case 'VEHICLE_REG_NO':
                                $data->where('V.VEHICLE_REG_NO', 'like', "%{$value}%");
                                break;
                        }
                    }
                }
            }


            if ($request->has('orderBy') && !empty($request->input('orderBy'))) {
                $orderBy = $request->input('orderBy');
                $order = $request->input('order', 'DESC');
                $data->orderBy($orderBy, $order);
            } else {
                $data->orderBy('rosters.ROSTER_ID', 'DESC');
            }


            if ($perPage === MyHelper::$PAGINATION_PER_PAGE_ALL) {
                $paginateddata = $data->paginate($data->count());
            } else {
                $perPage = is_numeric($perPage) ? (int)$perPage : MyHelper::$PAGINATION_PER_PAGE;
                $paginateddata = $data->paginate($perPage);
            }

            return response([
                'success' => true,
                'status' => 3,
                'Ontime_report' => $paginateddata,
            ]);
        } catch (\Throwable | \Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'Ontime Report Pagination Unsuccessful' : 'Ontime Report Pagination Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ], 500);
        }
    }


    public function fetch_Cancel_Report_Details($request, bool $active = true): FoundationApplication|Response|ResponseFactory
    {
        try {
            $perPage = $request->input('per_page', MyHelper::$PAGINATION_PER_PAGE);
            $branch_id = Auth::user()->BRANCH_ID;
            $user_type = Auth::user()->user_type;
            $vendor_id = Auth::user()->vendor_id;

            if ($user_type == MyHelper::$ADMIN) {
                $vendorid = "";
            } else {
                $vendorid = $vendor_id;
            }

            $triptype_id = $request->triptype_id;

            if ($triptype_id == 'ALL' || $triptype_id == 'all') {
                $triptype = ['P', 'D'];
            } else {
                $triptype = [$triptype_id];
            }


            $from_date = Carbon::parse($request->from_date)->startOfDay()->toDateTimeString();
            $to_date = Carbon::parse($request->to_date)->endOfDay()->toDateTimeString();

            $RS_ACTIVE = MyHelper::$RS_ACTIVE;
            $RS_AUTOCANCEL =  MyHelper::$RS_AUTOCANCEL;


            $data = Roster::query()
                ->select(
                    'rosters.ROUTE_ID',
                    'RS.TTLEMPCNT',
                    'RS.TRAVELEMPCNT',
                    'RS.EMPNOSHOWCNT',
                    'rosters.TRIP_TYPE',
                    'V.VEHICLE_REG_NO',
                    'VM.MODEL',
                    'VM.CAPACITY',
                    'VE.NAME as VENDORNAME',
                    'rosters.START_LOCATION',
                    'rosters.END_LOCATION',
                    'rosters.ESTIMATE_END_TIME',
                    'rosters.ESTIMATE_START_TIME',
                    DB::raw("if(rosters.TRIP_TYPE = 'P',DATE(rosters.ESTIMATE_END_TIME),DATE(rosters.ESTIMATE_START_TIME)) as INOUT_DATE"),
                    DB::raw("if(rosters.TRIP_TYPE = 'P',TIME(rosters.ESTIMATE_END_TIME),TIME(rosters.ESTIMATE_START_TIME)) AS INOUT_TIME"),
                    'rosters.ROSTER_STATUS'
                )


                ->leftJoin(
                    DB::raw("(SELECT EMPLOYEE_ID, LOCATION_ID, ROSTER_ID, 
                COUNT(*) AS TTLEMPCNT,
                SUM(IF(ROSTER_PASSENGER_STATUS & 16, 1, 0)) AS EMPNOSHOWCNT,
                SUM(IF((ROSTER_PASSENGER_STATUS & 32 OR ROSTER_PASSENGER_STATUS & 128), 1, 0)) AS TRAVELEMPCNT
                FROM roster_passengers 
                WHERE ACTIVE = $RS_ACTIVE 
                AND (
                    DATE(ESTIMATE_END_TIME) BETWEEN  '$from_date' AND  '$to_date' 
                    OR DATE(ESTIMATE_START_TIME) BETWEEN  '$from_date' AND  '$to_date'
                )
                GROUP BY ROSTER_ID
                     ) as RS"),
                    'RS.ROSTER_ID',
                    '=',
                    'rosters.ROSTER_ID'
                )

                ->Join('branch as  B', 'B.BRANCH_ID', '=', 'rosters.BRANCH_ID')
                ->Join('vendors as  VE', 'VE.VENDOR_ID', '=', 'rosters.VENDOR_ID')
                ->leftJoin('cab as  C', 'C.CAB_ID', '=', 'rosters.CAB_ID')
                ->leftJoin('vehicles as V', 'V.VEHICLE_ID', '=', 'C.VEHICLE_ID')
                ->leftJoin('vehicle_models as VM', 'VM.VEHICLE_MODEL_ID', '=', 'V.VEHICLE_MODEL_ID')
                ->leftJoin('employees as E', function ($join) use ($branch_id) {
                    $join->on('E.EMPLOYEES_ID', '=', 'RS.EMPLOYEE_ID')
                        ->where('E.BRANCH_ID',  '=', $branch_id);
                })
                ->leftJoin('locations as  L', 'L.LOCATION_ID', '=', 'RS.LOCATION_ID')

                ->where('rosters.ACTIVE', $RS_ACTIVE)
                ->whereIn('rosters.TRIP_TYPE', $triptype)
                ->where('rosters.BRANCH_ID', $branch_id)
                ->when($vendorid, function ($query, $vendorid) {
                    return $query->where('rosters.VENDOR_ID', $vendorid);
                })
                ->whereRaw("rosters.ROSTER_STATUS & $RS_AUTOCANCEL")

                ->havingRaw("(rosters.ESTIMATE_END_TIME BETWEEN  '$from_date' AND '$to_date' OR  rosters.ESTIMATE_START_TIME BETWEEN  '$from_date' AND '$to_date')");


            $filterModel = $request->input('filterModel');
            if ($filterModel) {
                foreach ($filterModel as $field => $filter) {
                    if (isset($filter['filter']) && $filter['filter'] !== '') {
                        $value = $filter['filter'];
                        $type = $filter['type'];

                        switch ($field) {
                            case 'VEHICLE_REG_NO':
                                $data->where('V.VEHICLE_REG_NO', 'like', "%{$value}%");
                                break;
                        }
                    }
                }
            }


            if ($request->has('orderBy') && !empty($request->input('orderBy'))) {
                $orderBy = $request->input('orderBy');
                $order = $request->input('order', 'DESC');
                $data->orderBy($orderBy, $order);
            } else {
                $data->orderBy('rosters.ROSTER_ID', 'DESC');
            }


            if ($perPage === MyHelper::$PAGINATION_PER_PAGE_ALL) {
                $paginateddata = $data->paginate($data->count());
            } else {
                $perPage = is_numeric($perPage) ? (int)$perPage : MyHelper::$PAGINATION_PER_PAGE;
                $paginateddata = $data->paginate($perPage);
            }

            return response([
                'success' => true,
                'status' => 3,
                'cancel_report' => $paginateddata,
            ]);
        } catch (\Throwable | \Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'Cancel Report Pagination Unsuccessful' : 'Cancel Report Pagination Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ], 500);
        }
    }


    public function fetch_Escort_Report_Details($request, bool $active = true): FoundationApplication|Response|ResponseFactory
    {
        try {
            $perPage = $request->input('per_page', MyHelper::$PAGINATION_PER_PAGE);
            $branch_id = Auth::user()->BRANCH_ID;
            $user_type = Auth::user()->user_type;
            $vendor_id = Auth::user()->vendor_id;

            if ($user_type == MyHelper::$ADMIN) {
                $vendorid = "";
            } else {
                $vendorid = $vendor_id;
            }

            $triptype_id = $request->triptype_id;

            if ($triptype_id == 'ALL' || $triptype_id == 'all') {
                $triptype = ['P', 'D'];
            } else {
                $triptype = [$triptype_id];
            }


            $from_date = Carbon::parse($request->from_date)->startOfDay()->toDateTimeString();
            $to_date = Carbon::parse($request->to_date)->endOfDay()->toDateTimeString();

            $RS_ACTIVE = MyHelper::$RS_ACTIVE;

            $data = Roster::query()
                ->select(
                    'rosters.ROUTE_ID',
                    'rosters.TRIP_TYPE',
                     DB::raw("if(RE.STATUS=1,'Not Allot',ES.ESCORT_NAME) as escort_sts"),
                    'RE.ESCORT_ID',
                    'ES.ESCORT_NAME',
                    'rosters.ESTIMATE_END_TIME',
                    'rosters.ESTIMATE_START_TIME',
                    DB::raw("if(rosters.TRIP_TYPE='P',rosters.ESTIMATE_END_TIME,rosters.ESTIMATE_START_TIME) as in_out")                   
                )
               
                ->Join('route_escorts as RE', function ($join) {
                    $join->on('RE.ROSTER_ID', '=', 'rosters.ROSTER_ID')
                        ->whereNotIn('RE.STATUS', ['5','6']);
                })               
                ->Join('escorts as  ES', 'ES.ESCORT_ID', '=', 'RE.ESCORT_ID')
                ->Join('vendors as  VE', 'VE.VENDOR_ID', '=', 'rosters.VENDOR_ID')
                ->where('rosters.ACTIVE', $RS_ACTIVE)
                ->whereIn('rosters.TRIP_TYPE', $triptype)
                ->where('rosters.BRANCH_ID', $branch_id)
                ->when($vendorid, function ($query, $vendorid) {
                    return $query->where('rosters.VENDOR_ID', $vendorid);
                }) 
              
               ->havingRaw("(rosters.ESTIMATE_END_TIME BETWEEN  '$from_date' AND '$to_date' OR  rosters.ESTIMATE_START_TIME BETWEEN  '$from_date' AND '$to_date')");


            $filterModel = $request->input('filterModel');
            if ($filterModel) {
                foreach ($filterModel as $field => $filter) {
                    if (isset($filter['filter']) && $filter['filter'] !== '') {
                        $value = $filter['filter'];
                        $type = $filter['type'];

                        switch ($field) {
                            case 'VEHICLE_REG_NO':
                                $data->where('V.VEHICLE_REG_NO', 'like', "%{$value}%");
                                break;
                        }
                    }
                }
            }


            if ($request->has('orderBy') && !empty($request->input('orderBy'))) {
                $orderBy = $request->input('orderBy');
                $order = $request->input('order', 'DESC');
                $data->orderBy($orderBy, $order);
            } else {
                $data->orderBy('rosters.ROSTER_ID', 'DESC');
            }


            if ($perPage === MyHelper::$PAGINATION_PER_PAGE_ALL) {
                $paginateddata = $data->paginate($data->count());
            } else {
                $perPage = is_numeric($perPage) ? (int)$perPage : MyHelper::$PAGINATION_PER_PAGE;
                $paginateddata = $data->paginate($perPage);
            }

            return response([
                'success' => true,
                'status' => 3,
                'escort_report' => $paginateddata,
            ]);
        } catch (\Throwable | \Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'Escort Report Pagination Unsuccessful' : 'Escort Report Pagination Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ], 500);
        }
    }


    public function fetch_ASF_Report_Details($request, bool $active = true): FoundationApplication|Response|ResponseFactory
    {
        try {
            $perPage = $request->input('per_page', MyHelper::$PAGINATION_PER_PAGE);
            $branch_id = Auth::user()->BRANCH_ID;
            $user_type = Auth::user()->user_type;
            $vendor_id = Auth::user()->vendor_id;

            if ($user_type == MyHelper::$ADMIN) {
                $vendorid = "";
            } else {
                $vendorid = $vendor_id;
            }
                       
            $from_date = Carbon::parse($request->from_date)->startOfDay()->toDateTimeString();
            $to_date = Carbon::parse($request->to_date)->endOfDay()->toDateTimeString();

            $data = alertsignalfailure::query()
                ->select(
                    'alertsignalfailure.ROSTER_ID',
                    'alertsignalfailure.CAB_ID',
                    'alertsignalfailure.CAB_NO',
                    'alertsignalfailure.GPS_DATE',
                    'alertsignalfailure.SPEED',
                    'alertsignalfailure.LOCATION',
                                       
                )              
               
                ->where('alertsignalfailure.BRANCH_ID', $branch_id)
                ->when($vendorid, function ($query, $vendorid) {
                    return $query->where('alertsignalfailure.VENDOR_ID', $vendorid);
                }) 
             ->havingRaw("(alertsignalfailure.GPS_DATE BETWEEN  '$from_date' AND '$to_date')");

            $filterModel = $request->input('filterModel');
            if ($filterModel) {
                foreach ($filterModel as $field => $filter) {
                    if (isset($filter['filter']) && $filter['filter'] !== '') {
                        $value = $filter['filter'];
                        $type = $filter['type'];

                        switch ($field) {
                            case 'CAB_NO':
                                $data->where('alertsignalfailure.CAB_NO', 'like', "%{$value}%");
                                break;
                        }
                    }
                }
            }


            if ($request->has('orderBy') && !empty($request->input('orderBy'))) {
                $orderBy = $request->input('orderBy');
                $order = $request->input('order', 'DESC');
                $data->orderBy($orderBy, $order);
            } else {
                $data->orderBy('alertsignalfailure.ALERT_ID', 'DESC');
            }


            if ($perPage === MyHelper::$PAGINATION_PER_PAGE_ALL) {
                $paginateddata = $data->paginate($data->count());
            } else {
                $perPage = is_numeric($perPage) ? (int)$perPage : MyHelper::$PAGINATION_PER_PAGE;
                $paginateddata = $data->paginate($perPage);
            }

            return response([
                'success' => true,
                'status' => 3,
                'asf_report' => $paginateddata,
            ]);
        } catch (\Throwable | \Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'ASF Report Pagination Unsuccessful' : 'ASF Report Pagination Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ], 500);
        }
    }


     public function fetch_AcceptReject_Report_Details($request, bool $active = true): FoundationApplication|Response|ResponseFactory
    {
        try {
            $perPage = $request->input('per_page', MyHelper::$PAGINATION_PER_PAGE);
            $branch_id = Auth::user()->BRANCH_ID;
            $user_type = Auth::user()->user_type;
            $vendor_id = Auth::user()->vendor_id;

            if ($user_type == MyHelper::$ADMIN) {
                $vendorid = "";
            } else {
                $vendorid = $vendor_id;
            }

            $triptype_id = $request->triptype_id;

            if ($triptype_id == 'ALL' || $triptype_id == 'all') {
                $triptype = ['P', 'D'];
            } else {
                $triptype = [$triptype_id];
            }


            $from_date = Carbon::parse($request->from_date)->startOfDay()->toDateTimeString();
            $to_date = Carbon::parse($request->to_date)->endOfDay()->toDateTimeString();

            $RS_ACTIVE = MyHelper::$RS_ACTIVE;

            $RS_TOTALALLOT = MyHelper::$RS_TOTALALLOT;
            $RS_TOTALACCEPT = MyHelper::$RS_TOTALACCEPT;
            $RS_TOTALREJECT = MyHelper::$RS_TOTALREJECT;
            $RS_TOTALEXECUTE = MyHelper::$RS_TOTALEXECUTE;
            $NORESPONSE = MyHelper::$RS_Noresponse_sts;
            $ACCEPTED = MyHelper::$RS_ACCEPTed_sts;
            $REJECTED = MyHelper::$RS_Rejected_sts;
            $EXECUTED = MyHelper::$RS_Execute_sts;
            $TRIPCLOSED = MyHelper::$RS_Closed_sts;
            $RS_AUTOCANCEL = MyHelper::$RS_AUTOCANCEL;

            $data = Roster::query()
                ->select(                    
                    'rosters.ROUTE_ID',
                    'RS.TTLEMPCNT',
                    'RS.TRAVELEMPCNT',
                    'RS.EMPNOSHOWCNT',
                    'rosters.TRIP_TYPE',
                    'V.VEHICLE_REG_NO',
                    'VM.MODEL',
                    'VM.CAPACITY',
                    'VE.NAME as VENDORNAME',
                    'rosters.START_LOCATION',
                    'rosters.END_LOCATION',
                    'rosters.ESTIMATE_END_TIME',
                    'rosters.ESTIMATE_START_TIME', 
                    DB::raw("if(rosters.TRIP_TYPE = 'P',DATE(rosters.ESTIMATE_END_TIME),DATE(rosters.ESTIMATE_START_TIME)) as INOUT_DATE"),
                    DB::raw("if(rosters.TRIP_TYPE = 'P',TIME(rosters.ESTIMATE_END_TIME),TIME(rosters.ESTIMATE_START_TIME)) AS INOUT_TIME"),
                    DB::raw("CASE 
                                WHEN rosters.ROSTER_STATUS in ($RS_TOTALALLOT) THEN '$NORESPONSE'
                                WHEN rosters.ROSTER_STATUS in ($RS_TOTALACCEPT) THEN '$ACCEPTED'
                                WHEN rosters.ROSTER_STATUS in ($RS_TOTALREJECT) THEN '$REJECTED'
                                WHEN rosters.ROSTER_STATUS in ($RS_TOTALEXECUTE) THEN '$EXECUTED'
                                WHEN (rosters.ROSTER_STATUS & 256 OR rosters.ROSTER_STATUS & 1024 OR rosters.ROSTER_STATUS & 16384) THEN '$TRIPCLOSED' 
                                END as TRIPSTATUS"),
                    'rosters.ROSTER_ID',
                )

                ->Join(
                    DB::raw("(SELECT ROSTER_ID,COUNT(*) AS TTLEMPCNT,
                               sum(if(ROSTER_PASSENGER_STATUS & 16,1,0)) AS EMPNOSHOWCNT,
                               SUM(if((ROSTER_PASSENGER_STATUS & 32 OR ROSTER_PASSENGER_STATUS & 128),1,0)) AS TRAVELEMPCNT
                               FROM roster_passengers 
                WHERE ACTIVE = $RS_ACTIVE 
                AND (
                    DATE(ESTIMATE_END_TIME) BETWEEN  '$from_date' AND  '$to_date' 
                    OR DATE(ESTIMATE_START_TIME) BETWEEN  '$from_date' AND  '$to_date'
                )
                GROUP BY ROSTER_ID
                     ) as RS"),
                    'RS.ROSTER_ID',
                    '=',
                    'rosters.ROSTER_ID'
                )
                ->Join('branch as  B', 'B.BRANCH_ID', '=', 'rosters.BRANCH_ID')
                ->Join('vendors as  VE', 'VE.VENDOR_ID', '=', 'rosters.VENDOR_ID')
                ->Join('cab as  C', 'C.CAB_ID', '=', 'rosters.CAB_ID')
                ->Join('vehicles as V', 'V.VEHICLE_ID', '=', 'C.VEHICLE_ID')
                ->Join('vehicle_models as VM', 'VM.VEHICLE_MODEL_ID', '=', 'V.VEHICLE_MODEL_ID')
                ->Join('reason_master as RM', 'RM.REASON_ID', '=', 'C.TARIFF_TYPE')

                ->where('rosters.ACTIVE', $RS_ACTIVE)
                ->whereIn('rosters.TRIP_TYPE', $triptype)
                ->where('rosters.BRANCH_ID', $branch_id)
                ->whereRaw("~rosters.ROSTER_STATUS & $RS_AUTOCANCEL")
                ->when($vendorid, function ($query, $vendorid) {
                    return $query->where('rosters.VENDOR_ID', $vendorid);
                })
               ->havingRaw("(rosters.ESTIMATE_END_TIME BETWEEN  '$from_date' AND '$to_date' OR  rosters.ESTIMATE_START_TIME BETWEEN  '$from_date' AND '$to_date')");

            $filterModel = $request->input('filterModel');
            if ($filterModel) {
                foreach ($filterModel as $field => $filter) {
                    if (isset($filter['filter']) && $filter['filter'] !== '') {
                        $value = $filter['filter'];
                        $type = $filter['type'];

                        switch ($field) {
                            case 'ROUTE_ID':
                                $data->where('rosters.ROUTE_ID', 'like', "%{$value}%");
                                break;
                        }
                    }
                }
            }


            if ($request->has('orderBy') && !empty($request->input('orderBy'))) {
                $orderBy = $request->input('orderBy');
                $order = $request->input('order', 'DESC');
                $data->orderBy($orderBy, $order);
            } else {
                $data->orderBy('rosters.ROSTER_ID', 'DESC');
            }


            if ($perPage === MyHelper::$PAGINATION_PER_PAGE_ALL) {
                $paginateddata = $data->paginate($data->count());
            } else {
                $perPage = is_numeric($perPage) ? (int)$perPage : MyHelper::$PAGINATION_PER_PAGE;
                $paginateddata = $data->paginate($perPage);
            }

            return response([
                'success' => true,
                'status' => 3,
                'accept_reject_report' => $paginateddata,
            ]);
        } catch (\Throwable | \Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'AcceptReject Report Pagination Unsuccessful' : 'AcceptReject Report Pagination Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ], 500);
        }
    }

    public function fetch_Toll_Report_Details($request, bool $active = true): FoundationApplication|Response|ResponseFactory
    {
        try {
            $perPage = $request->input('per_page', MyHelper::$PAGINATION_PER_PAGE);
            $branch_id = Auth::user()->BRANCH_ID;
            $user_type = Auth::user()->user_type;
            $vendor_id = Auth::user()->vendor_id;

            if ($user_type == MyHelper::$ADMIN) {
                $vendorid = "";
            } else {
                $vendorid = $vendor_id;
            }

            $triptype_id = $request->triptype_id;

            if ($triptype_id == 'ALL' || $triptype_id == 'all') {
                $triptype = ['P', 'D'];
            } else {
                $triptype = [$triptype_id];
            }


            $from_date = Carbon::parse($request->from_date)->startOfDay()->toDateTimeString();
            $to_date = Carbon::parse($request->to_date)->endOfDay()->toDateTimeString();

            $RS_ACTIVE = MyHelper::$RS_ACTIVE;

            $data = Roster::query()
                ->select(                    
                    'rosters.ROSTER_ID',
                    'rosters.ROUTE_ID',
                    'rosters.TRIP_TYPE',
                    'VE.NAME as VENDORNAME',
                    'V.VEHICLE_REG_NO',
                    'VM.MODEL',
                    'VM.CAPACITY',
                    'rosters.START_LOCATION',
                    'rosters.END_LOCATION',
                    'rosters.ESTIMATE_END_TIME',
                    'rosters.ESTIMATE_START_TIME',
                    DB::raw("if(rosters.TRIP_TYPE = 'P',DATE(rosters.ESTIMATE_END_TIME),DATE(rosters.ESTIMATE_START_TIME)) as INOUT_DATE"),
                    DB::raw("if(rosters.TRIP_TYPE = 'P',TIME(rosters.ESTIMATE_END_TIME),TIME(rosters.ESTIMATE_START_TIME)) AS INOUT_TIME"),
                    DB::raw("SUM(TP.TOLL_CHARGE) as TOLLAMOUNT"),
                )

                ->Join('branch as  B', 'B.BRANCH_ID', '=', 'rosters.BRANCH_ID')
                ->Join('vendors as  VE', 'VE.VENDOR_ID', '=', 'rosters.VENDOR_ID')
                ->Join('cab as  C', 'C.CAB_ID', '=', 'rosters.CAB_ID')
                ->Join('vehicles as V', 'V.VEHICLE_ID', '=', 'C.VEHICLE_ID')
                ->Join('vehicle_models as VM', 'VM.VEHICLE_MODEL_ID', '=', 'V.VEHICLE_MODEL_ID')
                ->Join('toll_payment as TP', 'TP.ROSTER_ID', '=', 'rosters.ROSTER_ID')
                ->where('rosters.ACTIVE', $RS_ACTIVE)
                ->whereIn('rosters.TRIP_TYPE', $triptype)
                ->where('rosters.BRANCH_ID', $branch_id)
                ->when($vendorid, function ($query, $vendorid) {
                    return $query->where('rosters.VENDOR_ID', $vendorid);
                })
                 
                ->groupBy('TP.ROSTER_ID')                 
                ->havingRaw("(rosters.ESTIMATE_END_TIME BETWEEN  '$from_date' AND '$to_date' OR  rosters.ESTIMATE_START_TIME BETWEEN  '$from_date' AND '$to_date')");



            $filterModel = $request->input('filterModel');
            if ($filterModel) {
                foreach ($filterModel as $field => $filter) {
                    if (isset($filter['filter']) && $filter['filter'] !== '') {
                        $value = $filter['filter'];
                        $type = $filter['type'];

                        switch ($field) {
                            case 'VEHICLE_REG_NO':
                                $data->where('V.VEHICLE_REG_NO', 'like', "%{$value}%");
                                break;
                        }
                    }
                }
            }


            if ($request->has('orderBy') && !empty($request->input('orderBy'))) {
                $orderBy = $request->input('orderBy');
                $order = $request->input('order', 'DESC');
                $data->orderBy($orderBy, $order);
            } else {
                $data->orderBy('rosters.ROSTER_ID', 'DESC');
            }


            if ($perPage === MyHelper::$PAGINATION_PER_PAGE_ALL) {
                $paginateddata = $data->paginate($data->count());
            } else {
                $perPage = is_numeric($perPage) ? (int)$perPage : MyHelper::$PAGINATION_PER_PAGE;
                $paginateddata = $data->paginate($perPage);
            }

            return response([
                'success' => true,
                'status' => 3,
                'toll_report' => $paginateddata,
            ]);
        } catch (\Throwable | \Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'Toll Report Pagination Unsuccessful' : 'Toll Report Pagination Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ], 500);
        }
    }

    public function fetch_Vehicle_Report_Details($request, bool $active = true): FoundationApplication|Response|ResponseFactory
    {
        try {
            $perPage = $request->input('per_page', MyHelper::$PAGINATION_PER_PAGE);
            $branch_id = Auth::user()->BRANCH_ID;
            $user_type = Auth::user()->user_type;
            $vendor_id = Auth::user()->vendor_id;

            if ($user_type == MyHelper::$ADMIN) {
                $vendorid = "";
            } else {
                $vendorid = $vendor_id;
            }


            $RS_ACTIVE = MyHelper::$RS_ACTIVE;
            $RP_ACTIVESTS =  MyHelper::$RP_ACTIVESTS;
            $RP_INACTIVESTS =  MyHelper::$RP_INACTIVESTS;

            $data = Cab::query()
                ->select(
                    'VE.NAME AS VENDORNAME',
                     'V.VEHICLE_REG_NO',
                    'VM.MODEL',
                    'V.PERMIT_EXPIRY',
                    'V.INSURANCE_EXPIRY',
                    'V.FC_EXPIRY',
                    'V.TAX_EXPIRY',
                    'D.DRIVERS_NAME',
                    'D.DRIVER_MOBILE',
                    'D.DRIVERS_ADRESS',
                    'D.DRIVER_LICENSE',
                    'D.LICENCE_EXPIRY',
                    'D.BADGE_EXPIRY',
                    'D.BADGE_NUMBER',
                    'D.SHIFT_IN_TIME',
                    'D.SHIFT_OUT_TIME',
                    'DE.DEVICE_MODEL',
                    'DE.IMEI_NO_1',
                    'DE.IMEI_NO_2',
                    'S.SIM_MOBILE_NO',
                    'S.SIM_PROVIDER',
                    'S.SIM_SERIAL_NO',
                   DB::raw("if(cab.ACTIVE = 1,'$RP_ACTIVESTS','$RP_INACTIVESTS') AS ACTSTSE"),
                )

                ->Join('branch as  B', 'B.BRANCH_ID', '=', 'cab.BRANCH_ID')
                ->Join('vendors as  VE', 'VE.VENDOR_ID', '=', 'cab.VENDOR_ID')
                ->Join('vehicles as V', 'V.VEHICLE_ID', '=', 'cab.VEHICLE_ID')
                ->Join('vehicle_models as VM', 'VM.VEHICLE_MODEL_ID', '=', 'V.VEHICLE_MODEL_ID')
                ->Join('drivers as D', 'D.DRIVERS_ID', '=', 'cab.DRIVER_ID')
                ->Join('devices as DE', 'DE.DEVICE_ID', '=', 'cab.DEVICE_ID')
                ->Join('sim as S', 'S.SIM_ID', '=', 'cab.SIM_ID')               
               
                ->where('cab.ACTIVE', $RS_ACTIVE)              
                ->where('cab.BRANCH_ID', $branch_id)
                ->when($vendorid, function ($query, $vendorid) {
                    return $query->where('cab.VENDOR_ID', $vendorid);
                });

            $filterModel = $request->input('filterModel');
            if ($filterModel) {
                foreach ($filterModel as $field => $filter) {
                    if (isset($filter['filter']) && $filter['filter'] !== '') {
                        $value = $filter['filter'];
                        $type = $filter['type'];

                        switch ($field) {
                            case 'VEHICLE_REG_NO':
                                $data->where('V.VEHICLE_REG_NO', 'like', "%{$value}%");
                                break;
                        }
                    }
                }
            }


            if ($request->has('orderBy') && !empty($request->input('orderBy'))) {
                $orderBy = $request->input('orderBy');
                $order = $request->input('order', 'DESC');
                $data->orderBy($orderBy, $order);
            } else {
                $data->orderBy('cab.CAB_ID', 'DESC');
            }


            if ($perPage === MyHelper::$PAGINATION_PER_PAGE_ALL) {
                $paginateddata = $data->paginate($data->count());
            } else {
                $perPage = is_numeric($perPage) ? (int)$perPage : MyHelper::$PAGINATION_PER_PAGE;
                $paginateddata = $data->paginate($perPage);
            }

            return response([
                'success' => true,
                'status' => 3,
                'vehicle_report' => $paginateddata,
            ]);
        } catch (\Throwable | \Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'Vehicle Report Pagination Unsuccessful' : 'Vehicle Report Pagination Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ], 500);
        }
    }

    public function fetch_Breakdown_Report_Details($request, bool $active = true): FoundationApplication|Response|ResponseFactory
    {
        try {
            $perPage = $request->input('per_page', MyHelper::$PAGINATION_PER_PAGE);
            $branch_id = Auth::user()->BRANCH_ID;
            $user_type = Auth::user()->user_type;
            $vendor_id = Auth::user()->vendor_id;

            if ($user_type == MyHelper::$ADMIN) {
                $vendorid = "";
            } else {
                $vendorid = $vendor_id;
            }

            $triptype_id = $request->triptype_id;

            if ($triptype_id == 'ALL' || $triptype_id == 'all') {
                $triptype = ['P', 'D'];
            } else {
                $triptype = [$triptype_id];
            }


            $from_date = Carbon::parse($request->from_date)->startOfDay()->toDateTimeString();
            $to_date = Carbon::parse($request->to_date)->endOfDay()->toDateTimeString();

            $RS_ACTIVE = MyHelper::$RS_ACTIVE;
            $RS_INACTIVE = MyHelper::$RS_INACTIVE;   
            $RS_TOTALBREAKDOWN = MyHelper::$RS_TOTALBREAKDOWN;

             $data = Roster::query()
                ->select(

                    'rosters.ROUTE_ID',
                    'rosters.TRIP_TYPE',
                    'V.VEHICLE_REG_NO',
                    'VM.MODEL',
                    'VM.CAPACITY',
                    'D.DRIVERS_NAME',
                    'D.DRIVER_MOBILE',
                    'VE.NAME AS VENDORNAME',
                    'rosters.START_LOCATION',
                    'rosters.END_LOCATION',
                    'rosters.ESTIMATE_END_TIME',
                    'rosters.ESTIMATE_START_TIME', 
                    DB::raw("if(rosters.TRIP_TYPE = 'P',DATE(rosters.ESTIMATE_END_TIME),DATE(rosters.ESTIMATE_START_TIME)) as INOUT_DATE"),
                    DB::raw("if(rosters.TRIP_TYPE = 'P',TIME(rosters.ESTIMATE_END_TIME),TIME(rosters.ESTIMATE_START_TIME)) AS INOUT_TIME"),
                    'CA.ACTION',
                    'RM.REASON',
                    'rosters.updated_at',
                )

                ->Join('cab as  C', 'C.CAB_ID', '=', 'rosters.CAB_ID')
                ->Join('vehicles as V', 'V.VEHICLE_ID', '=', 'C.VEHICLE_ID')
                ->Join('branch as  B', 'B.BRANCH_ID', '=', 'rosters.BRANCH_ID')
                ->Join('vendors as  VE', 'VE.VENDOR_ID', '=', 'rosters.VENDOR_ID')
                ->Join('vehicle_models as VM', 'VM.VEHICLE_MODEL_ID', '=', 'V.VEHICLE_MODEL_ID')
                ->Join('drivers as D', 'D.DRIVERS_ID', '=', 'C.DRIVER_ID')               
                ->Join('cab_allocation as CA', function ($join)  use ($RS_TOTALBREAKDOWN) {
                    $join->on('CA.ROSTER_ID', '=', 'rosters.ROSTER_ID')
                        ->whereIn('CA.ACCEPTANCE_REJECT_STATE', explode(',', $RS_TOTALBREAKDOWN));
                })
                ->Join('reason_master as RM', 'RM.REASON_ID', '=', 'CA.REJECT_REASON_ID')

                ->where('rosters.ACTIVE', $RS_INACTIVE)
                ->whereIn('rosters.TRIP_TYPE', $triptype)
                ->where('rosters.BRANCH_ID', $branch_id)
                ->whereIn('rosters.ROSTER_STATUS',explode(',', $RS_TOTALBREAKDOWN))
                ->when($vendorid, function ($query, $vendorid) {
                    return $query->where('rosters.VENDOR_ID', $vendorid);
                })
                
               ->havingRaw("(rosters.ESTIMATE_END_TIME BETWEEN  '$from_date' AND '$to_date' OR  rosters.ESTIMATE_START_TIME BETWEEN  '$from_date' AND '$to_date')");
            

            $filterModel = $request->input('filterModel');
            if ($filterModel) {
                foreach ($filterModel as $field => $filter) {
                    if (isset($filter['filter']) && $filter['filter'] !== '') {
                        $value = $filter['filter'];
                        $type = $filter['type'];

                        switch ($field) {
                            case 'VEHICLE_REG_NO':
                                $data->where('V.VEHICLE_REG_NO', 'like', "%{$value}%");
                                break;
                        }
                    }
                }
            }


            if ($request->has('orderBy') && !empty($request->input('orderBy'))) {
                $orderBy = $request->input('orderBy');
                $order = $request->input('order', 'DESC');
                $data->orderBy($orderBy, $order);
            } else {
                $data->orderBy('rosters.ROSTER_ID', 'DESC');
            }


            if ($perPage === MyHelper::$PAGINATION_PER_PAGE_ALL) {
                $paginateddata = $data->paginate($data->count());
            } else {
                $perPage = is_numeric($perPage) ? (int)$perPage : MyHelper::$PAGINATION_PER_PAGE;
                $paginateddata = $data->paginate($perPage);
            }

            return response([
                'success' => true,
                'status' => 3,
                'breakdown_report' => $paginateddata,
            ]);
        } catch (\Throwable | \Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'Toll Report Pagination Unsuccessful' : 'Toll Report Pagination Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ], 500);
        }
    }

     public function fetch_Vendorchange_Report_Details($request, bool $active = true): FoundationApplication|Response|ResponseFactory
    {
        try {
            $perPage = $request->input('per_page', MyHelper::$PAGINATION_PER_PAGE);
            $branch_id = Auth::user()->BRANCH_ID;
            $user_type = Auth::user()->user_type;
            $vendor_id = Auth::user()->vendor_id;

            if ($user_type == MyHelper::$ADMIN) {
                $vendorid = "";
            } else {
                $vendorid = $vendor_id;
            }


            $triptype_id = $request->triptype_id;

            if ($triptype_id == 'ALL' || $triptype_id == 'all') {
                $triptype = ['P', 'D'];
            } else {
                $triptype = [$triptype_id];
            }


             $from_date = Carbon::parse($request->from_date)->startOfDay()->toDateTimeString();
             $to_date = Carbon::parse($request->to_date)->endOfDay()->toDateTimeString();


            $RS_VENDORCHANGE = MyHelper::$RS_VENDORCHANGE;
            $RS_INACTIVE = MyHelper::$RS_INACTIVE; 

            $data = Roster::query()
                ->select(                  
                    'rosters.ROUTE_ID',
                    'rosters.TRIP_TYPE',
                    DB::raw("substring_index ( substring_index ( rosters.REMARKS,',',1 ), ',', -1) as OLDVENDOR"),
                    'rosters.START_LOCATION',
                    'rosters.END_LOCATION',
                    'rosters.ESTIMATE_END_TIME',
                    'rosters.ESTIMATE_START_TIME',
                    DB::raw("if(rosters.TRIP_TYPE = 'P',DATE(rosters.ESTIMATE_END_TIME),DATE(rosters.ESTIMATE_START_TIME)) as INOUT_DATE"),
                    DB::raw("if(rosters.TRIP_TYPE = 'P',TIME(rosters.ESTIMATE_END_TIME),TIME(rosters.ESTIMATE_START_TIME)) AS INOUT_TIME"),
                    'VE.NAME AS VENDORNAME',
                    DB::raw("substring_index ( substring_index ( rosters.REMARKS,',',2 ), ',', -1) as REMARKS"),

                )

                ->Join('branch as  B', 'B.BRANCH_ID', '=', 'rosters.BRANCH_ID')
                ->Join('vendors as  VE', 'VE.VENDOR_ID', '=', 'rosters.VENDOR_ID')
              
                ->where('rosters.ACTIVE', $RS_INACTIVE)
                ->whereIn('rosters.TRIP_TYPE', $triptype)
                ->where('rosters.BRANCH_ID', $branch_id)
                ->whereRaw("rosters.ROSTER_STATUS & $RS_VENDORCHANGE")
                ->when($vendorid, function ($query, $vendorid) {
                    return $query->where('rosters.VENDOR_ID', $vendorid);
                })

               ->havingRaw("(rosters.ESTIMATE_END_TIME BETWEEN  '$from_date' AND '$to_date' OR  rosters.ESTIMATE_START_TIME BETWEEN  '$from_date' AND '$to_date')");


                /*->where(function ($query) use ($from_date, $to_date) {
                    $query->whereBetween('rosters.ESTIMATE_END_TIME', ["'$from_date'", "'$to_date'"])
                          ->orWhereBetween('rosters.ESTIMATE_START_TIME', ["'$from_date'","'$to_date'"]);
                });*/


            $filterModel = $request->input('filterModel');
            if ($filterModel) {
                foreach ($filterModel as $field => $filter) {
                    if (isset($filter['filter']) && $filter['filter'] !== '') {
                        $value = $filter['filter'];
                        $type = $filter['type'];

                        switch ($field) {
                            case 'ROUTE_ID':
                                $data->where('rosters.ROUTE_ID', 'like', "%{$value}%");
                                break;
                        }
                    }
                }
            }


            if ($request->has('orderBy') && !empty($request->input('orderBy'))) {
                $orderBy = $request->input('orderBy');
                $order = $request->input('order', 'DESC');
                $data->orderBy($orderBy, $order);
            } else {
                $data->orderBy('rosters.ROSTER_ID', 'DESC');
            }


            if ($perPage === MyHelper::$PAGINATION_PER_PAGE_ALL) {
                $paginateddata = $data->paginate($data->count());
            } else {
                $perPage = is_numeric($perPage) ? (int)$perPage : MyHelper::$PAGINATION_PER_PAGE;
                $paginateddata = $data->paginate($perPage);
            }

            return response([
                'success' => true,
                'status' => 3,
                'vendor_change_report' => $paginateddata,
            ]);
        } catch (\Throwable | \Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'VendorChange Report Pagination Unsuccessful' : 'VendorChange Report Pagination Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ], 500);
        }
    }

    public function fetch_Feedback_Report_Details($request, bool $active = true): FoundationApplication|Response|ResponseFactory
    {
        try {
            $perPage = $request->input('per_page', MyHelper::$PAGINATION_PER_PAGE);
            $branch_id = Auth::user()->BRANCH_ID;
            $user_type = Auth::user()->user_type;
            $vendor_id = Auth::user()->vendor_id;

            if ($user_type == MyHelper::$ADMIN) {
                $vendorid = "";
            } else {
                $vendorid = $vendor_id;
            }

             $triptype_id = $request->triptype_id;

            if ($triptype_id == 'ALL' || $triptype_id == 'all') {
                $triptype = ['P', 'D'];
            } else {
                $triptype = [$triptype_id];
            }



            $from_date = Carbon::parse($request->from_date)->startOfDay()->toDateTimeString();
            $to_date = Carbon::parse($request->to_date)->endOfDay()->toDateTimeString();

            $RS_MANUALTRIPCLOSE = MyHelper::$RS_MANUALTRIPCLOSE;
            $RS_TRIPCLOSE = MyHelper::$RS_TRIPCLOSE;
            $RS_ACTIVE = MyHelper::$RS_ACTIVE; 

            $data = Roster::query()
                ->select(                  
                    'rosters.ROUTE_ID',
                    'rosters.TRIP_TYPE',
                    'E.NAME AS EMPNAME',
                    'E.MOBILE',
                    'V.VEHICLE_REG_NO',
                    'VM.MODEL',
                    'rosters.CAB_CAPACITY_COUNT',
                    'VE.NAME as VENDORNAME',
                    'rosters.START_LOCATION',
                    'rosters.END_LOCATION',
                    'rosters.ESTIMATE_END_TIME',
                    'rosters.ESTIMATE_START_TIME',
                    DB::raw("if(rosters.TRIP_TYPE = 'P',DATE(rosters.ESTIMATE_END_TIME),DATE(rosters.ESTIMATE_START_TIME)) as LOGIN_DATE"), 
                    DB::raw("if(rosters.TRIP_TYPE = 'P',TIME(rosters.ESTIMATE_END_TIME),TIME(rosters.ESTIMATE_START_TIME)) as LOGIN_TIME"),
                    'FL.FEEDBACK1',
                    'FL.FEEDBACK2',
                    'FL.FEEDBACK3',
                    'FL.FEEDBACK4',
                    'FL.COMMENTS',
                    DB::raw("'0%' AS AVGVAL"),  
                )

                ->Join('feedback_log as FL', 'FL.ROSTER_ID', '=', 'rosters.ROSTER_ID')
                ->leftJoin('employees as E', function ($join) use ($branch_id) {
                    $join->on('E.EMPLOYEES_ID', '=', 'FL.EMPLOYEE_ID')
                        ->where('E.BRANCH_ID',  '=', $branch_id);
                })               
                ->Join('cab as C', 'C.CAB_ID', '=', 'rosters.CAB_ID')
                ->Join('vehicles as V', 'V.VEHICLE_ID', '=', 'C.VEHICLE_ID')
                ->Join('vehicle_models as VM', 'VM.VEHICLE_MODEL_ID', '=', 'V.VEHICLE_MODEL_ID')
                ->Join('branch as  B', 'B.BRANCH_ID', '=', 'rosters.BRANCH_ID')
                ->Join('vendors as  VE', 'VE.VENDOR_ID', '=', 'rosters.VENDOR_ID')
              
                ->where('rosters.ACTIVE', $RS_ACTIVE)
                ->whereIn('rosters.TRIP_TYPE', $triptype)
                ->where('rosters.BRANCH_ID', $branch_id)

                ->whereRaw("(rosters.ROSTER_STATUS & $RS_TRIPCLOSE OR rosters.ROSTER_STATUS & $RS_MANUALTRIPCLOSE)")              

                ->when($vendorid, function ($query, $vendorid) {
                    return $query->where('rosters.VENDOR_ID', $vendorid);
                })

                ->havingRaw("(rosters.ESTIMATE_END_TIME BETWEEN  '$from_date' AND '$to_date' OR  rosters.ESTIMATE_START_TIME BETWEEN  '$from_date' AND '$to_date')");


            $filterModel = $request->input('filterModel');
            if ($filterModel) {
                foreach ($filterModel as $field => $filter) {
                    if (isset($filter['filter']) && $filter['filter'] !== '') {
                        $value = $filter['filter'];
                        $type = $filter['type'];

                        switch ($field) {
                            case 'ROUTE_ID':
                                $data->where('rosters.ROUTE_ID', 'like', "%{$value}%");
                                break;
                        }
                    }
                }
            }


            if ($request->has('orderBy') && !empty($request->input('orderBy'))) {
                $orderBy = $request->input('orderBy');
                $order = $request->input('order', 'DESC');
                $data->orderBy($orderBy, $order);
            } else {
                $data->orderBy('rosters.ROSTER_ID', 'DESC');
            }


            if ($perPage === MyHelper::$PAGINATION_PER_PAGE_ALL) {
                $paginateddata = $data->paginate($data->count());
            } else {
                $perPage = is_numeric($perPage) ? (int)$perPage : MyHelper::$PAGINATION_PER_PAGE;
                $paginateddata = $data->paginate($perPage);
            }

            return response([
                'success' => true,
                'status' => 3,
                'feedback_report' => $paginateddata,
            ]);
        } catch (\Throwable | \Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'Feedback Report Pagination Unsuccessful' : 'Feedback Report Pagination Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ], 500);
        }
    }

     public function fetch_OverTime_Report_Details($request, bool $active = true): FoundationApplication|Response|ResponseFactory
    {
        try {
            $perPage = $request->input('per_page', MyHelper::$PAGINATION_PER_PAGE);
            $branch_id = Auth::user()->BRANCH_ID;
            $user_type = Auth::user()->user_type;
            $vendor_id = Auth::user()->vendor_id;

            if ($user_type == MyHelper::$ADMIN) {
                $vendorid = "";
            } else {
                $vendorid = $vendor_id;
            }

            $from_date = Carbon::parse($request->from_date)->startOfDay()->toDateTimeString();
            $to_date = Carbon::parse($request->to_date)->endOfDay()->toDateTimeString();

            $data = CabAttendance::query()
                ->select(
                    'VE.NAME as vendorname',
                    'D.DRIVERS_NAME',
                    'D.DRIVER_MOBILE',
                    'D.DRIVERS_ADRESS',
                    'V.VEHICLE_REG_NO',
                    'VM.MODEL',
                    DB::raw("MIN(cab_attandance.LOGIN_TIME) as intime,MAX(cab_attandance.LOGOUT_TIME) as outtime"),
                    DB::raw("IF(TIMEDIFF(MAX(cab_attandance.LOGOUT_TIME),MIN(cab_attandance.LOGIN_TIME)) > '12:00:00'"),
                    DB::raw("TIMEDIFF(MAX(cab_attandance.LOGOUT_TIME),MIN(cab_attandance.LOGIN_TIME)),0) as total_time"),
                )
                ->Join('cab as C', function ($join) use ($branch_id,$vendorid) {
                    $join->on('C.CAB_ID', '=', 'cab_attandance.CAB_ID')
                        ->where('C.BRANCH_ID',  $branch_id)
                        ->when($vendorid, function ($query, $vendorid) {
                            return $query->where('C.VENDOR_ID', $vendorid);
                        });
                })
                 ->Join('vehicles as V', 'V.VEHICLE_ID', '=', 'C.VEHICLE_ID')
                 ->Join('vehicle_models as VM', 'VM.VEHICLE_MODEL_ID', '=', 'V.VEHICLE_MODEL_ID')
                 ->Join('branch as B', 'B.BRANCH_ID', '=', 'cab_attandance.BRANCH_ID')
                 ->leftJoin('vendors as VE', 'VE.VENDOR_ID', '=', 'C.VENDOR_ID')
                ->Join('drivers as D', 'D.DRIVERS_ID', '=', 'C.DRIVER_ID')

                ->where('cab_attandance.BRANCH_ID', $branch_id)
                ->whereRaw("(cab_attandance.LOGIN_TIME BETWEEN  '$from_date' AND '$to_date')")
                ->groupBy('cab_attandance.CAB_ID')
                ->havingRaw("total_time > 0");

            $filterModel = $request->input('filterModel');
            if ($filterModel) {
                foreach ($filterModel as $field => $filter) {
                    if (isset($filter['filter']) && $filter['filter'] !== '') {
                        $value = $filter['filter'];
                        $type = $filter['type'];

                        switch ($field) {
                            case 'CAB_NO':
                                $data->where('cab_attandance.CAB_NO', 'like', "%{$value}%");
                                break;
                        }
                    }
                }
            }


            if ($request->has('orderBy') && !empty($request->input('orderBy'))) {
                $orderBy = $request->input('orderBy');
                $order = $request->input('order', 'DESC');
                $data->orderBy($orderBy, $order);
            } else {
                $data->orderBy('cab_attandance.ATTANDANCE_ID', 'DESC');
            }


            if ($perPage === MyHelper::$PAGINATION_PER_PAGE_ALL) {
                $paginateddata = $data->paginate($data->count());
            } else {
                $perPage = is_numeric($perPage) ? (int)$perPage : MyHelper::$PAGINATION_PER_PAGE;
                $paginateddata = $data->paginate($perPage);
            }

            return response([
                'success' => true,
                'status' => 3,
                'over_time_report' => $paginateddata,
            ]);
        } catch (\Throwable | \Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'OverTime Report Pagination Unsuccessful' : 'OverTime Report Pagination Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ], 500);
        }
    }

   public function fetch_Adhoc_Report_Details($request, bool $active = true): FoundationApplication|Response|ResponseFactory
    {
        try {
            $perPage = $request->input('per_page', MyHelper::$PAGINATION_PER_PAGE);
            $branch_id = Auth::user()->BRANCH_ID;
            $user_type = Auth::user()->user_type;
            $vendor_id = Auth::user()->vendor_id;

            if ($user_type == MyHelper::$ADMIN) {
                $vendorid = "";
            } else {
                $vendorid = $vendor_id;
            }

            $triptype_id = $request->triptype_id;

            if ($triptype_id == 'ALL' || $triptype_id == 'all') {
                $triptype = ['P', 'D'];
            } else {
                $triptype = [$triptype_id];
            }



            $from_date = Carbon::parse($request->from_date)->startOfDay()->toDateTimeString();
            $to_date = Carbon::parse($request->to_date)->endOfDay()->toDateTimeString();

            $data = EmployeesRosterRequest::query()
                ->select(
                    'employees_roster_request.EMPLOYEE_ID',
                    'E.NAME as EMPNAME',
                    'employees_roster_request.TRIP_TYPE',
                    'employees_roster_request.START_LOCATION',
                    'employees_roster_request.END_LOCATION',
                    'employees_roster_request.ESTIMATE_START_TIME',
                    'employees_roster_request.ADHOC_OTHER_REASON',
                    DB::raw("case 
                            when employees_roster_request.STATUS=1 then 'Created'
                            when employees_roster_request.STATUS=6 then 'Cancelled'
                            when employees_roster_request.STATUS=3 then 'Alloted'
                            end status"),
                )
                ->Join('branch as B', 'B.BRANCH_ID', '=', 'employees_roster_request.BRANCH_ID')

                ->Join('employees as E', function ($join) use ($branch_id) {
                    $join->on('E.EMPLOYEES_ID', '=', 'employees_roster_request.EMPLOYEE_ID')
                        ->where('E.BRANCH_ID',  '=', $branch_id);
                })
                ->where('employees_roster_request.BRANCH_ID', $branch_id)
                ->whereIn('employees_roster_request.TRIP_TYPE', $triptype)
                ->where('employees_roster_request.CATEGORY', 'adhoc')
                ->whereRaw("(employees_roster_request.ESTIMATE_START_TIME BETWEEN  '$from_date' AND '$to_date')");

            $filterModel = $request->input('filterModel');
            if ($filterModel) {
                foreach ($filterModel as $field => $filter) {
                    if (isset($filter['filter']) && $filter['filter'] !== '') {
                        $value = $filter['filter'];
                        $type = $filter['type'];

                        switch ($field) {
                            case 'EMPLOYEE_ID':
                                $data->where('employees_roster_request.EMPLOYEE_ID', 'like', "%{$value}%");
                                break;
                        }
                    }
                }
            }


            if ($request->has('orderBy') && !empty($request->input('orderBy'))) {
                $orderBy = $request->input('orderBy');
                $order = $request->input('order', 'DESC');
                $data->orderBy($orderBy, $order);
            } else {
                $data->orderBy('employees_roster_request.ROSTER_REQ_ID', 'DESC');
            }


            if ($perPage === MyHelper::$PAGINATION_PER_PAGE_ALL) {
                $paginateddata = $data->paginate($data->count());
            } else {
                $perPage = is_numeric($perPage) ? (int)$perPage : MyHelper::$PAGINATION_PER_PAGE;
                $paginateddata = $data->paginate($perPage);
            }

            return response([
                'success' => true,
                'status' => 3,
                'Adhoc_report' => $paginateddata,
            ]);
        } catch (\Throwable | \Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'Adhoc Report Pagination Unsuccessful' : 'Adhoc Report Pagination Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ], 500);
        }
    }

   public function fetch_SMS_Report_Details($request, bool $active = true): FoundationApplication|Response|ResponseFactory
    {
        try {
            $perPage = $request->input('per_page', MyHelper::$PAGINATION_PER_PAGE);
            $branch_id = Auth::user()->BRANCH_ID;
            $user_type = Auth::user()->user_type;
            $vendor_id = Auth::user()->vendor_id;

            if ($user_type == MyHelper::$ADMIN) {
                $vendorid = "";
            } else {
                $vendorid = $vendor_id;
            }


            $from_date = Carbon::parse($request->from_date)->startOfDay()->toDateTimeString();
            $to_date = Carbon::parse($request->to_date)->endOfDay()->toDateTimeString();

            $data = Sms::query()
                ->select(
                    'sms.ORIGINATOR',
                    'sms.RECIPIENT',
                    'sms.MESSAGE',
                    'sms.STATUS',
                    'sms.CREATED_DATE',
                    'sms.REF_NO',
                    'sms.UPDATED_DATE',
                    'sms.SENT_DATE',
                    
                )             
                ->where('sms.BRANCH_ID', $branch_id)
                ->whereRaw("(sms.CREATED_DATE BETWEEN  '$from_date' AND '$to_date')");

            $filterModel = $request->input('filterModel');
            if ($filterModel) {
                foreach ($filterModel as $field => $filter) {
                    if (isset($filter['filter']) && $filter['filter'] !== '') {
                        $value = $filter['filter'];
                        $type = $filter['type'];

                        switch ($field) {
                            case 'RECIPIENT':
                                $data->where('sms.RECIPIENT', 'like', "%{$value}%");
                                break;
                        }
                    }
                }
            }


            if ($request->has('orderBy') && !empty($request->input('orderBy'))) {
                $orderBy = $request->input('orderBy');
                $order = $request->input('order', 'DESC');
                $data->orderBy($orderBy, $order);
            } else {
                $data->orderBy('sms.CREATED_DATE', 'DESC');
            }


            if ($perPage === MyHelper::$PAGINATION_PER_PAGE_ALL) {
                $paginateddata = $data->paginate($data->count());
            } else {
                $perPage = is_numeric($perPage) ? (int)$perPage : MyHelper::$PAGINATION_PER_PAGE;
                $paginateddata = $data->paginate($perPage);
            }

            return response([
                'success' => true,
                'status' => 3,
                'sms_report' => $paginateddata,
            ]);
        } catch (\Throwable | \Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'SMS Report Pagination Unsuccessful' : 'SMS Report Pagination Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ], 500);
        }
    }

    public function fetch_WeeklyRoster_Report_Details($request, bool $active = true): FoundationApplication|Response|ResponseFactory
    {
        try {
            $perPage = $request->input('per_page', MyHelper::$PAGINATION_PER_PAGE);
            $branch_id = Auth::user()->BRANCH_ID;
            $user_type = Auth::user()->user_type;
            $vendor_id = Auth::user()->vendor_id;

            if ($user_type == MyHelper::$ADMIN) {
                $vendorid = "";
            } else {
                $vendorid = $vendor_id;
            }

            $triptype_id = $request->triptype_id;

            if ($triptype_id == 'ALL' || $triptype_id == 'all') {
                $triptype = ['P', 'D'];
            } else {
                $triptype = [$triptype_id];
            }



            $from_date = Carbon::parse($request->from_date)->startOfDay()->toDateTimeString();
            $to_date = Carbon::parse($request->to_date)->endOfDay()->toDateTimeString();

            $data = EmployeesRosterRequest::query()
                ->select(
                    'employees_roster_request.EMPLOYEE_ID',                   
                    'employees_roster_request.TRIP_TYPE',
                    'employees_roster_request.START_LOCATION',
                    'employees_roster_request.END_LOCATION',
                    'employees_roster_request.ESTIMATE_START_TIME',  
                    'E.NAME as EMPNAME',
                    'E.MOBILE',
                    DB::raw("case 
                            when employees_roster_request.STATUS=1 then 'Created'
                            when employees_roster_request.STATUS=6 then 'Cancelled'
                            when employees_roster_request.STATUS=3 then 'Alloted'
                            end status"),
                )
                ->Join('branch as B', 'B.BRANCH_ID', '=', 'employees_roster_request.BRANCH_ID')
                ->Join('employees as E', function ($join) use ($branch_id) {
                    $join->on('E.EMPLOYEES_ID', '=', 'employees_roster_request.EMPLOYEE_ID')
                        ->where('E.BRANCH_ID',  '=', $branch_id)
                        ->where('E.ACTIVE',  '=', 1); 
                })
                ->where('employees_roster_request.BRANCH_ID', $branch_id)
                ->whereIn('employees_roster_request.TRIP_TYPE', $triptype)
                ->where('employees_roster_request.CATEGORY', 'roster')

                ->whereRaw("(employees_roster_request.ESTIMATE_START_TIME BETWEEN  '$from_date' AND '$to_date')");

            $filterModel = $request->input('filterModel');
            if ($filterModel) {
                foreach ($filterModel as $field => $filter) {
                    if (isset($filter['filter']) && $filter['filter'] !== '') {
                        $value = $filter['filter'];
                        $type = $filter['type'];

                        switch ($field) {
                            case 'EMPLOYEE_ID':
                                $data->where('employees_roster_request.EMPLOYEE_ID', 'like', "%{$value}%");
                                break;
                        }
                    }
                }
            }


            if ($request->has('orderBy') && !empty($request->input('orderBy'))) {
                $orderBy = $request->input('orderBy');
                $order = $request->input('order', 'DESC');
                $data->orderBy($orderBy, $order);
            } else {
                $data->orderBy('employees_roster_request.ROSTER_REQ_ID', 'DESC');
            }


            if ($perPage === MyHelper::$PAGINATION_PER_PAGE_ALL) {
                $paginateddata = $data->paginate($data->count());
            } else {
                $perPage = is_numeric($perPage) ? (int)$perPage : MyHelper::$PAGINATION_PER_PAGE;
                $paginateddata = $data->paginate($perPage);
            }

            return response([
                'success' => true,
                'status' => 3,
                'weekly_roster_report' => $paginateddata,
            ]);
        } catch (\Throwable | \Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'Weekly Roster Report Pagination Unsuccessful' : 'Weekly Roster Report Pagination Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ], 500);
        }
    }

   public function fetch_OtpReport_Details($request, bool $active = true): FoundationApplication|Response|ResponseFactory
    {
        try {
            $perPage = $request->input('per_page', MyHelper::$PAGINATION_PER_PAGE);
            $branch_id = Auth::user()->BRANCH_ID;
            $user_type = Auth::user()->user_type;
            $vendor_id = Auth::user()->vendor_id;

            if ($user_type == MyHelper::$ADMIN) {
                $vendorid = "";
            } else {
                $vendorid = $vendor_id;
            }

            $triptype_id = $request->triptype_id;


            $RS_ACTIVE = MyHelper::$RS_ACTIVE;
            $RPSYSOTP =   MyHelper::$RPSYSOTP;
            $RPS_TTLCABSYSTEMOTP =  MyHelper::$RPS_TTLCABSYSTEMOTP;
            $RPS_TTLSYSTEMOTP =  MyHelper::$RPS_TTLSYSTEMOTP . ',' . $RPS_TTLCABSYSTEMOTP;
            $RPS_TTLEMPLOYEEDELAY_SYSTEMOTP =  MyHelper::$RPS_TTLEMPLOYEEDELAY_SYSTEMOTP;
            $RPS_TTLCABEMPLOYEEDELAY =    MyHelper::$RPS_TTLCABEMPLOYEEDELAY;
            $RPS_TTLMANUALOTP =   MyHelper::$RPS_TTLMANUALOTP;


            if ($triptype_id == $RPSYSOTP) {
                $RPS_OTP = $RPS_TTLSYSTEMOTP . ',' . $RPS_TTLEMPLOYEEDELAY_SYSTEMOTP . ',' . $RPS_TTLCABEMPLOYEEDELAY;
            } else {
                $RPS_OTP = $RPS_TTLMANUALOTP;
            }


            $from_date = Carbon::parse($request->from_date)->startOfDay()->toDateTimeString();
            $to_date = Carbon::parse($request->to_date)->endOfDay()->toDateTimeString();



            $data = RosterPassenger::query()
                ->select(
                    'R.ROUTE_ID',
                    'E.EMPLOYEES_ID as EMPID',
                    'E.NAME as EMPNAME',
                    'E.MOBILE',
                    'R.TRIP_TYPE',
                    'V.VEHICLE_REG_NO',
                    'VM.MODEL',
                    'VM.CAPACITY',
                    'VE.NAME as VENDORNAME',
                    'R.START_LOCATION',
                    'R.END_LOCATION',
                    'R.ESTIMATE_END_TIME',
                    'R.ESTIMATE_START_TIME',
                    DB::raw("if(R.TRIP_TYPE = 'P',DATE(R.ESTIMATE_END_TIME),DATE(R.ESTIMATE_START_TIME)) as INOUT_DATE"),
                    DB::raw("if(R.TRIP_TYPE = 'P',TIME(R.ESTIMATE_END_TIME),TIME(R.ESTIMATE_START_TIME)) AS INOUT_TIME,TIME(R.ACTUAL_END_TIME) AS INTIME"),
                    DB::raw("IF(roster_passengers.REMARKS IS NULL,'--',roster_passengers.REMARKS) AS REMARK"),
                )

                ->Join('rosters as R', 'R.ROSTER_ID', '=', 'roster_passengers.ROSTER_ID')
                ->Join('branch as  B', 'B.BRANCH_ID', '=', 'R.BRANCH_ID')
                ->Join('vendors as  VE', 'VE.VENDOR_ID', '=', 'R.VENDOR_ID')
                ->leftJoin('cab as  C', 'C.CAB_ID', '=', 'R.CAB_ID')
                ->leftJoin('vehicles as V', 'V.VEHICLE_ID', '=', 'C.VEHICLE_ID')
                ->leftJoin('vehicle_models as VM', 'VM.VEHICLE_MODEL_ID', '=', 'V.VEHICLE_MODEL_ID')
                ->join('employees as E', function ($join) use ($branch_id) {
                    $join->on('E.EMPLOYEES_ID', '=', 'roster_passengers.EMPLOYEE_ID')
                        ->where('E.BRANCH_ID',  '=', $branch_id);
                })
                ->leftJoin('locations as  L', 'L.LOCATION_ID', '=', 'roster_passengers.LOCATION_ID')

                ->where('R.ACTIVE', $RS_ACTIVE)
                ->whereIn('roster_passengers.ROSTER_PASSENGER_STATUS',  explode(',', $RPS_OTP))
                ->where('R.BRANCH_ID', $branch_id)
                ->when($vendorid, function ($query, $vendorid) {
                    return $query->where('R.VENDOR_ID', $vendorid);
                })               
            
          ->havingRaw("(R.ESTIMATE_END_TIME BETWEEN  '$from_date' AND '$to_date' OR  R.ESTIMATE_START_TIME BETWEEN  '$from_date' AND '$to_date')");


            $filterModel = $request->input('filterModel');
            if ($filterModel) {
                foreach ($filterModel as $field => $filter) {
                    if (isset($filter['filter']) && $filter['filter'] !== '') {
                        $value = $filter['filter'];
                        $type = $filter['type'];

                        switch ($field) {
                            case 'VEHICLE_REG_NO':
                                $data->where('V.VEHICLE_REG_NO', 'like', "%{$value}%");
                                break;
                        }
                    }
                }
            }


            if ($request->has('orderBy') && !empty($request->input('orderBy'))) {
                $orderBy = $request->input('orderBy');
                $order = $request->input('order', 'DESC');
                $data->orderBy($orderBy, $order);
            } else {
                $data->orderBy('R.ROSTER_ID', 'DESC');
            }


            if ($perPage === MyHelper::$PAGINATION_PER_PAGE_ALL) {
                $paginateddata = $data->paginate($data->count());
            } else {
                $perPage = is_numeric($perPage) ? (int)$perPage : MyHelper::$PAGINATION_PER_PAGE;
                $paginateddata = $data->paginate($perPage);
            }

            return response([
                'success' => true,
                'status' => 3,
                'otp_report' => $paginateddata,
            ]);
        } catch (\Throwable | \Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'Otp Report Pagination Unsuccessful' : 'Otp Report Pagination Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ], 500);
        }
    }


   public function fetch_PanicReport_Details($request, bool $active = true): FoundationApplication|Response|ResponseFactory
    {
        try {
            $perPage = $request->input('per_page', MyHelper::$PAGINATION_PER_PAGE);
            $branch_id = Auth::user()->BRANCH_ID;
            $user_type = Auth::user()->user_type;
            $vendor_id = Auth::user()->vendor_id;

            if ($user_type == MyHelper::$ADMIN) {
                $vendorid = "";
            } else {
                $vendorid = $vendor_id;
            }

            $triptype_id = $request->triptype_id;



            $RP_EMPLOYEEPANIC = MyHelper::$RP_EMPLOYEEPANIC;
            $RS_ACTIVE = MyHelper::$RS_ACTIVE;

            $from_date = Carbon::parse($request->from_date)->startOfDay()->toDateTimeString();
            $to_date = Carbon::parse($request->to_date)->endOfDay()->toDateTimeString();


            if ($triptype_id == $RP_EMPLOYEEPANIC) {

                $data = PanicAlert::query()
                    ->select(
                        'R.ROUTE_ID',
                        'R.TRIP_TYPE',
                        'panic_alert.EMPLOYEE_ID',
                        'E.NAME as EMPNAME',
                        'E.MOBILE',
                        DB::raw("CONCAT(L.LOCATION_NAME,'-',panic_alert.ADDRESS) as ADDRESSVAL"),
                        'V.VEHICLE_REG_NO',
                        'VM.MODEL',
                        'D.NAME as vendor_name',
                        'R.START_LOCATION',
                        'R.END_LOCATION',
                        DB::raw("if(R.TRIP_TYPE = 'P',DATE(R.ESTIMATE_END_TIME),DATE(R.ESTIMATE_START_TIME)) as INOUT_DATE"),
                        DB::raw("if(R.TRIP_TYPE = 'P',TIME(R.ESTIMATE_END_TIME),TIME(R.ESTIMATE_START_TIME)) AS INOUT_TIME"),
                        'panic_alert.created_at',
                        'panic_alert.ACTION_TAKEN',

                    )

                    ->leftJoin('employees as E', function ($join) use ($branch_id) {
                        $join->on('E.EMPLOYEES_ID', '=', 'panic_alert.EMPLOYEE_ID')
                            ->where('E.BRANCH_ID',  '=', $branch_id);
                    })
                    ->leftJoin('locations as L', 'L.LOCATION_ID', '=', 'E.LOCATION_ID')
                    ->leftJoin('rosters as R', function ($join) use ($RS_ACTIVE) {
                        $join->on('R.ROSTER_ID', '=', 'panic_alert.ROSTER_ID')
                            ->where('R.ACTIVE',  '=', $RS_ACTIVE);
                    })
                    ->leftJoin('cab as C', 'C.CAB_ID', '=', 'panic_alert.CAB_ID')
                    ->leftJoin('drivers as DR', 'DR.DRIVERS_ID', '=', 'C.DRIVER_ID')
                    ->leftJoin('vehicles as V', 'V.VEHICLE_ID', '=', 'C.VEHICLE_ID')
                    ->leftJoin('vehicle_models as VM', 'VM.VEHICLE_MODEL_ID', '=', 'V.VEHICLE_MODEL_ID')
                    ->leftJoin('branch as B', 'B.BRANCH_ID', '=', 'R.BRANCH_ID')
                    ->leftJoin('vendors as  D', 'D.VENDOR_ID', '=', 'C.VENDOR_ID')                    
                    ->where('panic_alert.BRANCH_ID', $branch_id)
                    ->when($vendorid, function ($query, $vendorid) {
                        return $query->where('R.VENDOR_ID', $vendorid);
                    })
                    ->where('panic_alert.EMPLOYEE_ID', '!=', '')
                    ->whereRaw("panic_alert.created_at BETWEEN  '$from_date' AND '$to_date'");
            } else {


                $data = PanicAlert::query()
                    ->select(
                        'R.ROUTE_ID',
                        'R.TRIP_TYPE',
                        'DR.DRIVERS_NAME',
                        'DR.DRIVER_MOBILE',
                        'panic_alert.ADDRESS as ADDRESSVAL',
                        'V.VEHICLE_REG_NO',
                        'VM.MODEL',
                        'VM.CAPACITY',
                        'D.NAME as vendor_name',
                        'R.START_LOCATION',
                        'R.END_LOCATION',
                        DB::raw("if(R.TRIP_TYPE = 'P',DATE(R.ESTIMATE_END_TIME),DATE(R.ESTIMATE_START_TIME)) as INOUT_DATE"),
                        DB::raw("if(R.TRIP_TYPE = 'P',TIME(R.ESTIMATE_END_TIME),TIME(R.ESTIMATE_START_TIME)) AS INOUT_TIME"),
                        'panic_alert.created_at',
                        'panic_alert.ACTION_TAKEN',
                    )

                    ->leftJoin('rosters as R', 'R.ROSTER_ID', '=', 'panic_alert.ROSTER_ID')
                    ->leftJoin('cab as C', 'C.CAB_ID', '=', 'panic_alert.CAB_ID')
                    ->leftJoin('drivers as DR', 'DR.DRIVERS_ID', '=', 'C.DRIVER_ID')
                    ->leftJoin('vehicles as V', 'V.VEHICLE_ID', '=', 'C.VEHICLE_ID')
                    ->leftJoin('vehicle_models as VM', 'VM.VEHICLE_MODEL_ID', '=', 'V.VEHICLE_MODEL_ID')
                    ->leftJoin('branch as B', 'B.BRANCH_ID', '=', 'R.BRANCH_ID')
                    ->leftJoin('vendors as  D', 'D.VENDOR_ID', '=', 'C.VENDOR_ID')
                    ->where('R.ACTIVE', $RS_ACTIVE)
                    ->where('R.BRANCH_ID', $branch_id)
                    ->when($vendorid, function ($query, $vendorid) {
                        return $query->where('R.VENDOR_ID', $vendorid);
                    })
                     ->where('panic_alert.EMPLOYEE_ID', '=', null)
                    ->whereRaw("panic_alert.created_at BETWEEN  '$from_date' AND '$to_date'");
            }


            $filterModel = $request->input('filterModel');
            if ($filterModel) {
                foreach ($filterModel as $field => $filter) {
                    if (isset($filter['filter']) && $filter['filter'] !== '') {
                        $value = $filter['filter'];
                        $type = $filter['type'];

                        switch ($field) {
                            case 'VEHICLE_REG_NO':
                                $data->where('V.VEHICLE_REG_NO', 'like', "%{$value}%");
                                break;
                        }
                    }
                }
            }


            if ($request->has('orderBy') && !empty($request->input('orderBy'))) {
                $orderBy = $request->input('orderBy');
                $order = $request->input('order', 'DESC');
                $data->orderBy($orderBy, $order);
            } else {
                $data->orderBy('R.ROSTER_ID', 'DESC');
            }


            if ($perPage === MyHelper::$PAGINATION_PER_PAGE_ALL) {
                $paginateddata = $data->paginate($data->count());
            } else {
                $perPage = is_numeric($perPage) ? (int)$perPage : MyHelper::$PAGINATION_PER_PAGE;
                $paginateddata = $data->paginate($perPage);
            }

            return response([
                'success' => true,
                'status' => 3,
                'panic_report' => $paginateddata,
            ]);
        } catch (\Throwable | \Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'Panic Report Pagination Unsuccessful' : 'Panic Report Pagination Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ], 500);
        }
    }

    public function dataForCreateEmployees(): FoundationApplication|Response|ResponseFactory
    {
        $branch_id = Auth::user()->BRANCH_ID;
        $RS_ACTIVE = MyHelper::$RS_ACTIVE;
       

        try 
		{
            $employees = Employee::select(
                'employees.id',
                'employees.EMPLOYEES_ID',
                'employees.NAME as emp_name'
                
              )
                ->where('employees.ACTIVE', $RS_ACTIVE)
                ->where('employees.BRANCH_ID', $branch_id)
                ->get();

            return response([
                'success' => true,
                'status' => 3,
                'employees' => $employees,
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'employees Index Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ],500);
        }
    }
    


    public function dataForCreateVehicles(): FoundationApplication|Response|ResponseFactory
    {
        $branch_id = Auth::user()->BRANCH_ID;
        $RS_ACTIVE = MyHelper::$RS_ACTIVE;
       

        try 
		{
                $vehicles = cab::select(
                    'cab.CAB_ID',                
                    'VH.VEHICLE_REG_NO'
                )
                ->join("vehicles as VH", "VH.VEHICLE_ID", "=", 'cab.VEHICLE_ID')
                ->where('cab.ACTIVE', $RS_ACTIVE)
                ->where('cab.BRANCH_ID', $branch_id)
                ->get();

            return response([
                'success' => true,
                'status' => 3,
                'vehicles' => $vehicles,
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'vehicles Index Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ],500);
        }
    }

    public function fetch_Deactive_Route_Employee_Details($request, bool $active = true): FoundationApplication|Response|ResponseFactory
    {
        try {
            $perPage = $request->input('per_page', MyHelper::$PAGINATION_PER_PAGE);
            $branch_id = Auth::user()->BRANCH_ID;
            $user_type = Auth::user()->user_type;
            $vendor_id = Auth::user()->vendor_id;
           

            
            $from_date = date('Y-m-d');
            $to_date  = date('Y-m-d');

            $data = RosterPassenger::query()
                ->select(
                    'R.ROSTER_ID',
                    'R.BRANCH_ID',
                    'R.ROUTE_ID',
                    'R.START_LOCATION',
                    'R.END_LOCATION',
                    'R.TRIP_TYPE',
                     DB::raw("if(R.TRIP_TYPE='P',R.ESTIMATE_END_TIME,R.ESTIMATE_START_TIME) as LOGIN_TIME"),
                    'R.CAB_ID',
                    'roster_passengers.EMPLOYEE_ID',
                    'E.NAME',
                    'E.MOBILE',
                    'L.LOCATION_NAME',
                    'V.VEHICLE_REG_NO',
                    'R.ESTIMATE_END_TIME',
                    'R.ESTIMATE_START_TIME',
                )

                ->Join('rosters as R', 'R.ROSTER_ID', '=', 'roster_passengers.ROSTER_ID')
				->Join('employees as  E', 'E.EMPLOYEES_ID', '=', 'roster_passengers.EMPLOYEE_ID')
				->Join('locations as L', 'L.LOCATION_ID', '=', 'roster_passengers.LOCATION_ID')
				->leftJoin('cab as C', 'C.CAB_ID', '=', 'R.CAB_ID')
				->leftJoin('vehicles as V', 'V.VEHICLE_ID', '=', 'C.VEHICLE_ID')
                ->where('R.BRANCH_ID', $branch_id)
                ->where('roster_passengers.ACTIVE', 3)
                ->groupBy('R.ROSTER_ID')
                ->groupBy('roster_passengers.EMPLOYEE_ID')
                ->havingRaw("(date(R.ESTIMATE_END_TIME) BETWEEN  '$from_date' AND '$to_date' OR  date(R.ESTIMATE_START_TIME) BETWEEN  '$from_date' AND '$to_date')");

            $filterModel = $request->input('filterModel');
            if ($filterModel) {
                foreach ($filterModel as $field => $filter) {
                    if (isset($filter['filter']) && $filter['filter'] !== '') {
                        $value = $filter['filter'];
                        $type = $filter['type'];

                        switch ($field) {
                            case 'CAB_NO':
                                $data->where('roster_passengers.CAB_NO', 'like', "%{$value}%");
                                break;
                        }
                    }
                }
            }


            if ($request->has('orderBy') && !empty($request->input('orderBy'))) {
                $orderBy = $request->input('orderBy');
                $order = $request->input('order', 'DESC');
                $data->orderBy($orderBy, $order);
            } else {
                $data->orderBy('roster_passengers.ROSTER_ID', 'DESC');
            }


            if ($perPage === MyHelper::$PAGINATION_PER_PAGE_ALL) {
                $paginateddata = $data->paginate($data->count());
            } else {
                $perPage = is_numeric($perPage) ? (int)$perPage : MyHelper::$PAGINATION_PER_PAGE;
                $paginateddata = $data->paginate($perPage);
            }

            return response([
                'success' => true,
                'status' => 3,
                'deactive_employee_report' => $paginateddata,
            ]);
        } catch (\Throwable | \Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'deactive_employee Report Pagination Unsuccessful' : 'deactive_employee Report Pagination Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ], 500);
        }
    }


    public function getCabOverspeedReport($request, bool $active = true): FoundationApplication|Response|ResponseFactory
    {
        try {
            $perPage = $request->input('per_page', MyHelper::$PAGINATION_PER_PAGE);            

            $from_date = $request->from_date;
            $to_date = $request->to_date;

            $ElasticController = new ElasticController();
            $CabSpeedCount = $ElasticController->getCabOverspeedReport($from_date,$to_date);
            $results = array();
            
            if( $CabSpeedCount == 'No Record'){

                return response([
                    'success' => true,
                    'status' => 3,
                    'cab_overspeed_report' => array("data" => [], "total" => 0),
                ]);

            }else{
                for ($k = 0; $k < count($CabSpeedCount); $k++) {
                    $CAB_ID = $CabSpeedCount[$k]['CAB_ID'];
                    $VENDOR_ID = $CabSpeedCount[$k]['VENDOR_ID'];
                    $SpeedCount = $CabSpeedCount[$k]['ALERT_STATUS'];
                    $ACTION_REMARK = $CabSpeedCount[$k]['ACTION_REMARK'];
                    $GPS_DATE = $CabSpeedCount[$k]['START_GPS_DATE'];
                    $ADDRESS = $CabSpeedCount[$k]['START_LOCATION'];
    
                    $cabdetails = $this->cabdetails($CAB_ID);
                    $j = 0;
                    $VEHICLE_REG_NO = $cabdetails[$j]->VEHICLE_REG_NO;
                    $MODEL = $cabdetails[$j]->MODEL;
                    $DRIVERS_NAME = $cabdetails[$j]->DRIVERS_NAME;
                    $DRIVER_MOBILE = $cabdetails[$j]->DRIVER_MOBILE;
                    $vendorname = $cabdetails[$j]->vendorname;
    
                    $results[] = array(
                        "VENDORNAME" => $vendorname,
                        "DRIVERNAME" => $DRIVERS_NAME,
                        "DRIVER_MOBILE" => $DRIVER_MOBILE,
                        "CABNO" => $VEHICLE_REG_NO,
                        "MODEL" => $MODEL,
                        "ADDRESS" => $ADDRESS,
                        "GPS_DATE" => $GPS_DATE,
                        "SPEED_COUNT" => $SpeedCount,
                        "ACTION_REMARK" => $ACTION_REMARK,
                        "CAB_ID" => $CAB_ID
                    );
                }

                return response([
                    'success' => true,
                    'status' => 3,
                    'cab_overspeed_report' => array("data" => $results, "total" => count($results)),
                ]);

            }

          
           
        } catch (\Throwable | \Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'Cab OverSpeed Report Pagination Unsuccessful' : 'Cab OverSpeed Report Pagination Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ], 500);
        }
    }
    public function getWrongLocationReport($request, bool $active = true): FoundationApplication|Response|ResponseFactory
    {
        try {

            $branch_id = Auth::user()->BRANCH_ID;
            $vendor_id = Auth::user()->vendor_id;
            $perPage = $request->input('per_page', MyHelper::$PAGINATION_PER_PAGE);            

            $from_date = $request->from_date;
            $to_date = $request->to_date;
           
            $ElasticController = new ElasticController();
            $emp_id = '';
            $results = array();
            $wrong_otp_arr = $ElasticController->getDistanceAlert($branch_id, $vendor_id, $from_date, $to_date);
            /*  echo "<pre>";							
                                         print_r($wrong_otp_arr);
                                         exit; */
            if (is_array($wrong_otp_arr)) {
                for ($i = 0; $i < count($wrong_otp_arr); $i++) {
                    $eid = $wrong_otp_arr[$i]['EMP_ID'];
                    $cabid = $wrong_otp_arr[$i]['CAB_ID'];
                    $emp_position = $wrong_otp_arr[$i]['COM_EMP_POSITION'];
                    $distance = $wrong_otp_arr[$i]['DISTANCE'];
                    $cab_current_position = $wrong_otp_arr[$i]['CURRENT_POSITION'];
                    $remarks = $wrong_otp_arr[$i]['ACTION_REMARK'];
                    $process_date = str_replace("T", " ", $wrong_otp_arr[$i]['PROCESS_DATE']);

                    if ($eid != 0) {
                        $val = strpos($eid, "'");
                        /* echo "test ".$val;
                                                      exit; */
                        if ($val == NULL) {
                            $sql = "select EMPLOYEE_ID from roster_passengers where ROSTER_PASSENGER_ID in(" . $eid . ")";
                            $res = DB::select($sql);
                            if ($res)
                                $emp_id = $res[0]->EMPLOYEE_ID;
                        } else {
                            $emp_id = $eid;
                        }
                    } else {
                        $emp_id = $eid;
                    }
                    $emp_arr_val = array(
                        "EMP_ID" => $emp_id,
                        "CAB_ID" => $cabid,
                        "DISTANCE" => $distance,
                        "DRIVER_CURRENT_POSITION" => $cab_current_position,
                        "COMPANY_EMPLOYEE_POSITION" => $emp_position,
                        "DRIVER_ACTION" => $remarks,
                        "PROCESS_DATE" => $process_date
                    );
                    $results[$i] = array_replace($emp_arr_val);
                }
            }

                return response([
                    'success' => true,
                    'status' => 3,
                    'wrong_location_report' => array("data" => $results, "total" => count($results)),
                ]);

            

          
           
        } catch (\Throwable | \Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'Cab wronglocation Report Pagination Unsuccessful' : 'Cab wronglocation Report Pagination Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ], 500);
        }
    }

    public function cabdetails($cabid)
    {
        try {
            $rsActive = MyHelper::$RS_ACTIVE;
            $branchId = Auth::user()->BRANCH_ID;
            $dbname = Auth::user()->dbname;

            // Build the query using Laravel's Query Builder
            $result = DB::connection($dbname)->table('cab as C')
                ->select(
                    'C.VENDOR_ID',
                    'C.CAB_ID',
                    'V.VEHICLE_REG_NO',
                    'VM.MODEL',
                    'D.DRIVERS_NAME',
                    'D.DRIVER_MOBILE',
                    'VD.NAME as vendorname'
                )
                ->join('vehicles as V', 'V.VEHICLE_ID', '=', 'C.VEHICLE_ID')
                ->join('vehicle_models as VM', 'VM.VEHICLE_MODEL_ID', '=', 'V.VEHICLE_MODEL_ID')
                ->join('drivers as D', 'D.DRIVERS_ID', '=', 'C.DRIVER_ID')
                ->join('vendors as VD', 'VD.VENDOR_ID', '=', 'C.VENDOR_ID')
                ->where('C.BRANCH_ID', $branchId)
                ->where('C.CAB_ID', $cabid)
                ->get();
            return $result;
        } catch (\Throwable | \Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'No data',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }

   public function fetch_Adhoc_WeeklyRoster_Report_Details($request, bool $active = true): FoundationApplication|Response|ResponseFactory
    {
        try {
            $perPage = $request->input('per_page', MyHelper::$PAGINATION_PER_PAGE);
            $branch_id = Auth::user()->BRANCH_ID;
            $user_type = Auth::user()->user_type;
            $vendor_id = Auth::user()->vendor_id;

            if ($user_type == MyHelper::$ADMIN) {
                $vendorid = "";
            } else {
                $vendorid = $vendor_id;
            }

            $triptype_id = $request->triptype_id;

            if ($triptype_id == 'ALL' || $triptype_id == 'all') {
                $triptype = ['P', 'D'];
            } else {
                $triptype = [$triptype_id];
            }

            $reporttype_id = $request->reporttype_id;
            if ($reporttype_id == 'ALL' || $triptype_id == 'all') {
                $reporttype =['adhoc', 'roster'];'';
            }else{
                $reporttype =[$reporttype_id];
            }

            $bookingstatus_id = $request->bookingstatus_id;
            if ($bookingstatus_id == 'ALL' || $bookingstatus_id == 'all') {
                $bookingstatus = ['S', 'F'];
            } else {
                $bookingstatus = [$bookingstatus_id];
            }


            $from_date = Carbon::parse($request->from_date)->startOfDay()->toDateTimeString();
            $to_date = Carbon::parse($request->to_date)->endOfDay()->toDateTimeString();

            $data = EmployeesRosterRequest::query()
                ->select(
                    'employees_roster_request.EMPLOYEE_ID',                   
                    'employees_roster_request.TRIP_TYPE',
                    'employees_roster_request.START_LOCATION',
                    'employees_roster_request.END_LOCATION',
                    'employees_roster_request.ESTIMATE_START_TIME',  
                    'E.NAME as EMPNAME',
                    'E.MOBILE',
                    'E.GENDER',
                    'E.ADDRESS',
                    'E.DISTANCE',
                    'employees_roster_request.created_at',
                    'employees_roster_request.BOOKING_STATUS',
                    'employees_roster_request.ADHOC_OTHER_REASON',
                    DB::raw("case 
                            when employees_roster_request.STATUS=1 then 'Created'
                            when employees_roster_request.STATUS=6 then 'Cancelled'
                            when employees_roster_request.STATUS=3 then 'Alloted'
                            when employees_roster_request.STATUS=2 then 'Roster Moved'
                            end status"),
                )
                ->Join('branch as B', 'B.BRANCH_ID', '=', 'employees_roster_request.BRANCH_ID')
                ->Join('employees as E', function ($join) use ($branch_id) {
                    $join->on('E.EMPLOYEES_ID', '=', 'employees_roster_request.EMPLOYEE_ID')
                        ->where('E.BRANCH_ID',  '=', $branch_id)
                        ->where('E.ACTIVE',  '=', 1); 
                })
                ->where('employees_roster_request.BRANCH_ID', $branch_id)
                ->whereIn('employees_roster_request.TRIP_TYPE', $triptype)
                ->whereIn('employees_roster_request.CATEGORY', $reporttype)
                ->whereIn('employees_roster_request.BOOKING_STATUS', $bookingstatus)

                ->whereRaw("(employees_roster_request.ESTIMATE_START_TIME BETWEEN  '$from_date' AND '$to_date')");

            $filterModel = $request->input('filterModel');
            if ($filterModel) {
                foreach ($filterModel as $field => $filter) {
                    if (isset($filter['filter']) && $filter['filter'] !== '') {
                        $value = $filter['filter'];
                        $type = $filter['type'];

                        switch ($field) {
                            case 'EMPLOYEE_ID':
                                $data->where('employees_roster_request.EMPLOYEE_ID', 'like', "%{$value}%");
                                break;
                        }
                    }
                }
            }


            if ($request->has('orderBy') && !empty($request->input('orderBy'))) {
                $orderBy = $request->input('orderBy');
                $order = $request->input('order', 'DESC');
                $data->orderBy($orderBy, $order);
            } else {
                $data->orderBy('employees_roster_request.ROSTER_REQ_ID', 'DESC');
            }


            if ($perPage === MyHelper::$PAGINATION_PER_PAGE_ALL) {
                $paginateddata = $data->paginate($data->count());
            } else {
                $perPage = is_numeric($perPage) ? (int)$perPage : MyHelper::$PAGINATION_PER_PAGE;
                $paginateddata = $data->paginate($perPage);
            }

            return response([
                'success' => true,
                'status' => 3,
                'adhoc_weeklyroster_report' => $paginateddata,
            ]);
        } catch (\Throwable | \Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'Adhoc & Weekly Roster Report Pagination Unsuccessful' : 'Adhoc & Weekly Roster Report Pagination Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ], 500);
        }
    }


    public function fetch_FWMIS_Report_Details($request, bool $active = true): FoundationApplication|Response|ResponseFactory
    {
        try {
            $perPage = $request->input('per_page', MyHelper::$PAGINATION_PER_PAGE);
            $branch_id = Auth::user()->BRANCH_ID;
            $user_type = Auth::user()->user_type;
           

             $vendor_id = $request->vendor_id;   

            if ($vendor_id == 'ALL' || $vendor_id== 'all' ) {
                $vendorid = "";
            } else {
                $vendorid = $vendor_id;
            }

            $triptype_id = $request->triptype_id;

            if ($triptype_id == 'ALL' || $triptype_id == 'all') {
                $triptype = ['P', 'D'];
            } else {
                $triptype = [$triptype_id];
            }


            $from_date = Carbon::parse($request->from_date)->startOfDay()->toDateTimeString();
            $to_date = Carbon::parse($request->to_date)->endOfDay()->toDateTimeString();

            $RS_ACTIVE = MyHelper::$RS_ACTIVE;

            $data = Roster::query()
                ->select(
                    DB::raw("if(rosters.TRIP_TYPE='P',time(rosters.ESTIMATE_END_TIME),time(rosters.ESTIMATE_START_TIME)) as login_time"),
                    DB::raw("if(rosters.TRIP_TYPE='P',date(rosters.ESTIMATE_END_TIME),date(rosters.ESTIMATE_START_TIME)) as login_date"),
                    'rosters.*',
                    'E.EMPLOYEES_ID',
                    'E.GENDER',
                    'E.NAME as EMP_NAME',
                    'E.DISTANCE',
                    'V.NAME as VENDOR_NAME',
                    'VH.VEHICLE_REG_NO',
                    'VM.MODEL',
                    'T.PACKAGE_PRICE',
                    DB::raw("if(rosters.TRIP_TYPE='D',RP.ACTUAL_START_TIME,RP.ESTIMATE_START_TIME) as EMP_ACTUAL_START_TIME"),
                    'RP.DRIVER_ARRIVAL_TIME',
                    DB::raw("if(rosters.TRIP_TYPE='D',RP.ACTUAL_END_TIME,RP.ACTUAL_START_TIME) as EMP_ACTUAL_END_TIME"),
                    'L.LOCATION_NAME',
                    'RP.ROUTE_ORDER',
                    'RE.ESCORT_ID',
                    DB::raw("if(RP.ROSTER_PASSENGER_STATUS & 32 || RP.ROSTER_PASSENGER_STATUS & 64 || RP.ROSTER_PASSENGER_STATUS & 128,'Show',if(RP.ROSTER_PASSENGER_STATUS=1,'Created','Noshow') ) as show_sts"),
                    DB::raw("case 
                             WHEN TIME_TO_SEC(TIMEDIFF(rosters.ACTUAL_END_TIME,rosters.ESTIMATE_END_TIME)) > 900 THEN 'Delay'
                             WHEN TIME_TO_SEC(TIMEDIFF(rosters.ACTUAL_END_TIME,rosters.ESTIMATE_END_TIME)) BETWEEN 0 AND 900 THEN 'On Time'
                             ELSE 'On Time'
                             end as Dealy_Status"),
                    DB::raw("CASE 
                              WHEN TIME_TO_SEC(TIMEDIFF(rosters.ACTUAL_END_TIME,rosters.ESTIMATE_END_TIME)) > 900 THEN TIMEDIFF(rosters.ACTUAL_END_TIME,rosters.ESTIMATE_END_TIME)
                              WHEN TIME_TO_SEC(TIMEDIFF(rosters.ACTUAL_END_TIME,rosters.ESTIMATE_END_TIME)) BETWEEN 0 AND 900 THEN ''
                              ELSE ''
                              END as Delay_Time"),
  

                   
                    
                )

                ->join('roster_passengers as RP', function ($join) {
                    $join->on('RP.ROSTER_ID', '=', 'rosters.ROSTER_ID')
                        ->whereIn('RP.ACTIVE', ['1']);
                })
              
               ->Join('employees as E', function ($join) use ($branch_id) {
                    $join->on('E.EMPLOYEES_ID', '=', 'RP.EMPLOYEE_ID')
                         ->where('E.BRANCH_ID', '=',$branch_id)
                         ->where('E.ACTIVE', '=', '1');
                })

                ->Join('locations as L', 'L.LOCATION_ID', '=', 'RP.LOCATION_ID')
                ->Join('vendors as V', 'V.VENDOR_ID', '=', 'rosters.VENDOR_ID')
                ->Join('cab as C', 'C.CAB_ID', '=', 'rosters.CAB_ID')
                ->Join('vehicles as VH', 'VH.VEHICLE_ID', '=', 'C.VEHICLE_ID')
                ->Join('vehicle_models as VM', 'VM.VEHICLE_MODEL_ID', '=', 'VH.VEHICLE_MODEL_ID')
                ->leftJoin('route_escorts as RE', 'RE.ROSTER_ID', '=', 'rosters.ROSTER_ID')
                ->leftJoin('tariff_km_slap as T', function ($join)  use ($branch_id,$vendorid) {
                     $join->on('T.PACKAGE_START_KM', '<=', 'rosters.TRIP_APPROVED_KM')
                         ->where('T.PACKAGE_END_KM', '>=', 'rosters.TRIP_APPROVED_KM')
                         ->where('T.VENDOR_ID', '=', $vendorid)
                        ->where('T.VEHICLE_MODEL_ID', '=', 'VH.VEHICLE_MODEL_ID')
                        ->where('T.BRANCH_ID', '=', $branch_id);
                })

                ->where('rosters.ACTIVE', $RS_ACTIVE)
                ->where('rosters.BRANCH_ID', $branch_id)
                ->when($vendorid, function ($query, $vendorid) {
                    return $query->where('rosters.VENDOR_ID', $vendorid);
                })
                ->whereIn('rosters.TRIP_TYPE', $triptype)
                ->whereNotIn('rosters.ROSTER_STATUS', ['8193', '8205', '8283', '8221', '8281', '8201', '8225', '8285', '8195'])
                ->whereRaw("(rosters.ROSTER_STATUS & 256 OR rosters.ROSTER_STATUS & 1024 OR rosters.ROSTER_STATUS & 512)")
                ->havingRaw("(rosters.ESTIMATE_END_TIME BETWEEN  '$from_date' AND '$to_date' OR  rosters.ESTIMATE_START_TIME BETWEEN  '$from_date' AND '$to_date')");


            $filterModel = $request->input('filterModel');
            if ($filterModel) {
                foreach ($filterModel as $field => $filter) {
                    if (isset($filter['filter']) && $filter['filter'] !== '') {
                        $value = $filter['filter'];
                        $type = $filter['type'];

                        switch ($field) {
                            case 'VEHICLE_REG_NO':
                                $data->where('V.VEHICLE_REG_NO', 'like', "%{$value}%");
                                break;
                        }
                    }
                }
            }


            if ($request->has('orderBy') && !empty($request->input('orderBy'))) {
                $orderBy = $request->input('orderBy');
                $order = $request->input('order', 'DESC');
                $data->orderBy($orderBy, $order);
            } else {
                $data->orderBy('RP.ROUTE_ORDER', 'DESC');
            }


            if ($perPage === MyHelper::$PAGINATION_PER_PAGE_ALL) {
                $paginateddata = $data->paginate($data->count());
            } else {
                $perPage = is_numeric($perPage) ? (int)$perPage : MyHelper::$PAGINATION_PER_PAGE;
                $paginateddata = $data->paginate($perPage);
            }

            return response([
                'success' => true,
                'status' => 3,
                'fw_mis_report' => $paginateddata,
            ]);
        } catch (\Throwable | \Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'MIS Report Pagination Unsuccessful' : 'MIS Report Pagination Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ], 500);
        }
    }


      public function misreport_date_wise_shiftlogin($request): FoundationApplication|Response|ResponseFactory
    {

        try {
            $user = Auth::user();
            $RS_ACTIVE = MyHelper::$RS_ACTIVE;
            $branchId = $user->BRANCH_ID;
            $vendor_id = $request->vendor_id;
            if ($vendor_id == 'ALL') {
                $vendorid = "";
            } else {
                $vendorid = $vendor_id;
            }

            $triptype_id = $request->triptype_id;

            if ($triptype_id == 'ALL' || $triptype_id == 'all') {
                $triptype = ['P', 'D'];
            } else {
                $triptype = [$triptype_id];
            }

            $selected_date = $request->selected_date;

            $logintime = DB::table('rosters')
                ->select(                   
                    DB::raw("CASE 
                                 WHEN TRIP_TYPE = 'P' THEN TIME(ESTIMATE_END_TIME) 
                                 WHEN TRIP_TYPE = 'D' THEN TIME(ESTIMATE_START_TIME) 
                                  END as logintime")
                )
                ->where(function ($query) use ($selected_date) {
                    $query->whereDate('ESTIMATE_END_TIME', $selected_date)
                        ->orWhereDate('ESTIMATE_START_TIME',  $selected_date);
                })
                ->where('ACTIVE', 1)
                ->where('BRANCH_ID', $branchId)
                ->when($vendorid, function ($query, $vendorid) {
                    return $query->where('VENDOR_ID', $vendorid);
                })
                ->whereIn('TRIP_TYPE', $triptype)
                ->groupBy('logintime')
                ->get();

            return response([
                'success' => true,
                'status' => 3,
                'logintime' => $logintime,
            ]);
        } catch (\Throwable | \Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Datewise shift time MisReport  Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }


    public function fetch_FWMIS_AcceptApprovelReport_Details($request, bool $active = true): FoundationApplication|Response|ResponseFactory
    {
        try {
            $perPage = $request->input('per_page', MyHelper::$PAGINATION_PER_PAGE);
            $branch_id = Auth::user()->BRANCH_ID;
            $vendor_id = Auth::user()->vendor_id;

            $vendor_id = $request->vendor_id;
            if ($vendor_id == 'ALL') {
                $vendorid = "";
            } else {
                $vendorid = $vendor_id;
            }

            $triptype_id = $request->triptype_id;

            if ($triptype_id == 'ALL' || $triptype_id == 'all') {
                $triptype = ['P', 'D'];
            } else {
                $triptype = [$triptype_id];
            }


            $login_datetime = $request->selectedDate . ' ' . $request->selectedTime;
             $isNonBillable = $request->isNonBillable;

            $RS_ACTIVE = MyHelper::$RS_ACTIVE;

            $data = Roster::query()
                ->select(
                    DB::raw("if(rosters.TRIP_TYPE='P',time(rosters.ESTIMATE_END_TIME),time(rosters.ESTIMATE_START_TIME)) as login_time"),
                    DB::raw("if(rosters.TRIP_TYPE='P',date(rosters.ESTIMATE_END_TIME),date(rosters.ESTIMATE_START_TIME)) as login_date"),
                    'rosters.*',
                    DB::raw("GROUP_CONCAT(E.EMPLOYEES_ID) as EMPLOYEES_ID"),
                    DB::raw("GROUP_CONCAT(E.GENDER) as GENDER"),
                    DB::raw("GROUP_CONCAT(E.`NAME` separator '|,') as NAME"),
                    DB::raw("GROUP_CONCAT(L.LOCATION_NAME separator '|,') as LOCATION_NAME"),
                    DB::raw("GROUP_CONCAT(E.DISTANCE separator '|,') as DISTANCE"),
                    'V.NAME as VENDOR_NAME',
                    'VH.VEHICLE_REG_NO',
                    'VM.MODEL',
                    'T.PACKAGE_PRICE',
                    'L.LOCATION_NAME as LongestArea',
                    DB::raw("if(rosters.TRIP_TYPE='D',RP.ACTUAL_START_TIME,RP.ESTIMATE_START_TIME) as EMP_ACTUAL_START_TIME"),
                    'RP.DRIVER_ARRIVAL_TIME',
                    DB::raw("if(rosters.TRIP_TYPE='D',RP.ACTUAL_END_TIME,RP.ACTUAL_START_TIME) as EMP_ACTUAL_END_TIME"),
                   
                    'RP.ROUTE_ORDER',
                    'RE.ESCORT_ID',
                    //  DB::raw("if(RP.ROSTER_PASSENGER_STATUS & 32 || RP.ROSTER_PASSENGER_STATUS & 64 || RP.ROSTER_PASSENGER_STATUS & 128,'Show',if(RP.ROSTER_PASSENGER_STATUS=1,'Created','Noshow') ) as show_sts"),
                    DB::raw("GROUP_CONCAT(RP.ROSTER_PASSENGER_STATUS) as ROSTER_PASSENGER_STATUS"),
                    


                )

                ->join('roster_passengers as RP', function ($join) {
                    $join->on('RP.ROSTER_ID', '=', 'rosters.ROSTER_ID')
                        ->whereIn('RP.ACTIVE', ['1']);
                })
              
                ->Join('employees as E', function ($join) use ($branch_id) {
                    $join->on('E.EMPLOYEES_ID', '=', 'RP.EMPLOYEE_ID')
                         ->where('E.BRANCH_ID', '=',$branch_id)
                         ->where('E.ACTIVE', '=', '1');
                })

                ->Join('locations as L', 'L.LOCATION_ID', '=', 'RP.LOCATION_ID')
                ->Join('vendors as V', 'V.VENDOR_ID', '=', 'rosters.VENDOR_ID')
                ->Join('cab as C', 'C.CAB_ID', '=', 'rosters.CAB_ID')
                ->Join('vehicles as VH', 'VH.VEHICLE_ID', '=', 'C.VEHICLE_ID')
                ->Join('vehicle_models as VM', 'VM.VEHICLE_MODEL_ID', '=', 'VH.VEHICLE_MODEL_ID')
                ->leftJoin('route_escorts as RE', 'RE.ROSTER_ID', '=', 'rosters.ROSTER_ID')
                ->leftJoin('tariff_km_slap as T', function ($join)  use ($branch_id,$vendorid) {
                     $join->on('T.PACKAGE_START_KM', '<=', 'rosters.TRIP_APPROVED_KM')
                         ->where('T.PACKAGE_END_KM', '>=', 'rosters.TRIP_APPROVED_KM')
                         ->where('T.VENDOR_ID', '=', $vendorid)
                        ->where('T.VEHICLE_MODEL_ID', '=', 'VH.VEHICLE_MODEL_ID')
                        ->where('T.BRANCH_ID', '=', $branch_id);
                })

                ->where('rosters.ACTIVE', $RS_ACTIVE)
                ->where('rosters.BRANCH_ID', $branch_id)
                ->where('rosters.isNonBillable', '=', $isNonBillable)
                ->when($vendorid, function ($query, $vendorid) {
                    return $query->where('rosters.VENDOR_ID', $vendorid);
                })
                ->whereIn('rosters.TRIP_TYPE', $triptype)
                ->whereNotIn('rosters.ROSTER_STATUS', ['8193', '8205', '8283', '8221', '8281', '8201', '8225', '8285', '8195'])
                ->whereRaw("(rosters.ROSTER_STATUS & 256 OR rosters.ROSTER_STATUS & 1024 OR rosters.ROSTER_STATUS & 512)")
                ->where(function ($query) use ($login_datetime, $triptype) {
                    if (in_array('D', $triptype)) {
                        $query->where('rosters.ESTIMATE_START_TIME', $login_datetime);
                    }
                    if (in_array('P', $triptype)) {
                        $query->where('rosters.ESTIMATE_END_TIME', $login_datetime);
                    }
                })
              ->groupBy('rosters.ROSTER_ID');

            $filterModel = $request->input('filterModel');
            if ($filterModel) {
                foreach ($filterModel as $field => $filter) {
                    if (isset($filter['filter']) && $filter['filter'] !== '') {
                        $value = $filter['filter'];
                        $type = $filter['type'];

                        switch ($field) {
                            case 'VEHICLE_REG_NO':
                                $data->where('V.VEHICLE_REG_NO', 'like', "%{$value}%");
                                break;
                        }
                    }
                }
            }


            if ($request->has('orderBy') && !empty($request->input('orderBy'))) {
                $orderBy = $request->input('orderBy');
                $order = $request->input('order', 'DESC');
                $data->orderBy($orderBy, $order);
            } else {
                 $data->orderBy('RP.ROUTE_ORDER', 'DESC');
            }


            if ($perPage === MyHelper::$PAGINATION_PER_PAGE_ALL) {
                $paginateddata = $data->paginate($data->count());
            } else {
                $perPage = is_numeric($perPage) ? (int)$perPage : MyHelper::$PAGINATION_PER_PAGE;
                $paginateddata = $data->paginate($perPage);
            }

            return response([
                'success' => true,
                'status' => 3,
                'fw_mis_acceptapprovel_report' => $paginateddata,
            ]);
        } catch (\Throwable | \Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'MIS Report Pagination Unsuccessful' : 'MIS Report Pagination Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ], 500);
        }
    }

   public function bill_Approvel($request): FoundationApplication|Response|ResponseFactory
    {
        $date = Carbon::now();
        $authUser = Auth::user();
        $userid = Auth::user()->id;
        try {
            DB::beginTransaction();

            $billApproved_count = 0;
            $failedIds = [];

            foreach ($request->selected_roster_id as $roster_id) {
                try {
                   
                    $arr = array("isNonBillable" => $request->trip_approve_status, "UPDATED_BY" => $userid, "updated_at" => $date->format("Y-m-d H:i:s"));
                    Roster::where("ACTIVE", "=", MyHelper::$RS_ACTIVE)
                        ->where("BRANCH_ID","=", $authUser->BRANCH_ID)
                        ->where("ROSTER_ID","=", $roster_id)
                        ->update($arr);

                    $billApproved_count++;

                } catch (\Exception $e) {
                    Log::error("Failed to Bill Approvel route: " . $e->getMessage());
                    $failedIds[] = $roster_id;
                }
            }

            DB::commit();

            $message = $billApproved_count > 0
                ? "$billApproved_count routes(s) Bill Approved successfully."
                : "No routes were Bill Approved.";

            if (!empty($failedIds)) {
                $message .= " Failed to Bill Approvel " . count($failedIds) . " routes(s).";
            }

            return response([
                'success' => true,
                'message' => $message,
                'status' => 3,
                'billApproved_count' => $billApproved_count,
                'failed_ids' => $failedIds,
            ]);
        } catch (\Throwable | \Exception $e) {
            DB::rollBack();
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'validation_controller' => true,
                'message' => 'Bill Approved process failed',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

   public function fetch_EmptyKmApprovel_Report_Details($request, bool $active = true): FoundationApplication|Response|ResponseFactory
    {
        try {
            $perPage = $request->input('per_page', MyHelper::$PAGINATION_PER_PAGE);
            $branch_id = Auth::user()->BRANCH_ID;
            $user_type = Auth::user()->user_type;
            $vendor_id = Auth::user()->vendor_id;

            if ($user_type == MyHelper::$ADMIN) {
                $vendorid = "";
            } else {
                $vendorid = $vendor_id;
            }

            $RS_ACTIVE = MyHelper::$RS_ACTIVE;

            $from_date = Carbon::parse($request->from_date)->startOfDay()->toDateTimeString();
            $to_date = Carbon::parse($request->to_date)->endOfDay()->toDateTimeString();

            $data = EmptyCabKm::query()
    ->select(
        'empty_cab_km.BRANCH_ID',
        'empty_cab_km.CAB_ID',
        'empty_cab_km.APPROVE_STATUS',
        'empty_cab_km.EMPTY_ID',
        'empty_cab_km.EMPTY_KM',
        'VH.VEHICLE_REG_NO',
        'empty_cab_km.IN_OUT_TIME',
        DB::raw("DATE(empty_cab_km.IN_OUT_TIME) as IN_OUT_DATE"),
        'empty_cab_km.TRIP_TYPE',
        'V.NAME as VENDOR_NAME',
        DB::raw("CASE 
                     WHEN empty_cab_km.APPROVE_STATUS = 1 THEN 'Created'
                     WHEN empty_cab_km.APPROVE_STATUS = 2 THEN 'Approved'
                     WHEN empty_cab_km.APPROVE_STATUS = 6 THEN 'Rejected'
                 END as STS"),
        'VM.MODEL',
        'T.PACKAGE_PRICE'
    )
    ->join('cab as C', 'C.CAB_ID', '=', 'empty_cab_km.CAB_ID')
    ->join('vendors as V', 'V.VENDOR_ID', '=', 'empty_cab_km.VENDOR_ID')
    ->join('vehicles as VH', 'VH.VEHICLE_ID', '=', 'C.VEHICLE_ID')
    ->join('vehicle_models as VM', 'VM.VEHICLE_MODEL_ID', '=', 'VH.VEHICLE_MODEL_ID')
    ->leftJoin('tariff as T', function ($join) use ($branch_id, $RS_ACTIVE) {
       $join->on('T.TARIFF_TYPE', '=', 'C.TARIFF_TYPE')
         ->whereColumn('T.VEHICLE_MODEL_ID', '=', 'VH.VEHICLE_MODEL_ID')
         ->where('T.BRANCH_ID', '=', $branch_id)
         ->where('T.ACTIVE', '=', $RS_ACTIVE);
})
    ->where('empty_cab_km.BRANCH_ID', $branch_id)
    ->whereBetween('empty_cab_km.IN_OUT_TIME', [$from_date, $to_date])
    ->groupBy('empty_cab_km.EMPTY_ID', 'T.VEHICLE_MODEL_ID');

            $filterModel = $request->input('filterModel');
            if ($filterModel) {
                foreach ($filterModel as $field => $filter) {
                    if (isset($filter['filter']) && $filter['filter'] !== '') {
                        $value = $filter['filter'];
                        $type = $filter['type'];

                        switch ($field) {
                            case 'VEHICLE_REG_NO':
                                $data->where('V.VEHICLE_REG_NO', 'like', "%{$value}%");
                                break;
                        }
                    }
                }
            }


            if ($request->has('orderBy') && !empty($request->input('orderBy'))) {
                $orderBy = $request->input('orderBy');
                $order = $request->input('order', 'DESC');
                $data->orderBy($orderBy, $order);
            } else {
                $data->orderBy('empty_cab_km.IN_OUT_TIME', 'DESC');
            }


            if ($perPage === MyHelper::$PAGINATION_PER_PAGE_ALL) {
                $paginateddata = $data->paginate($data->count());
            } else {
                $perPage = is_numeric($perPage) ? (int)$perPage : MyHelper::$PAGINATION_PER_PAGE;
                $paginateddata = $data->paginate($perPage);
            }

            return response([
                'success' => true,
                'status' => 3,
                'emptykm_approval_report' => $paginateddata,
            ]);
        } catch (\Throwable | \Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'EmptyKm Approvel Report Pagination Unsuccessful' : 'EmptyKm Approvel Report Pagination Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ], 500);
        }
    }

   public function fetch_Mask_CallLogs_Report_Details($request, bool $active = true): FoundationApplication|Response|ResponseFactory
    {
        try {
            $perPage = $request->input('per_page', MyHelper::$PAGINATION_PER_PAGE);
            $branch_id = Auth::user()->BRANCH_ID;
            $user_type = Auth::user()->user_type;
            $vendor_id = Auth::user()->vendor_id;

            if ($user_type == MyHelper::$ADMIN) {
                $vendorid = "";
            } else {
                $vendorid = $vendor_id;
            }


            $from_date = Carbon::parse($request->from_date)->startOfDay()->toDateTimeString();
            $to_date = Carbon::parse($request->to_date)->endOfDay()->toDateTimeString();

            $data = MaskCallLogs::query()
                ->select(
                    'MASK_CALL_LOG.ROSTER_ID',
                    'MASK_CALL_LOG.EMPLOYEE_ID',
                    'MASK_CALL_LOG.CALL_DATE',
                    'MASK_CALL_LOG.ANSWER_TIME',
                    'MASK_CALL_LOG.END_TIME',
                    'MASK_CALL_LOG.CUSTOMER_NUMBER',
                    'MASK_CALL_LOG.DRIVER_NUMBER',
                    'MASK_CALL_LOG.CALL_TYPE',
                    'MASK_CALL_LOG.DURATION',
                    'MASK_CALL_LOG.RECORDING_URL',
                    'MASK_CALL_LOG.STATUS',

                )
                ->leftJoin('MASK_TRANSACTION as MT', 'MT.ROSTER_ID', '=', 'MASK_CALL_LOG.ROSTER_ID') 
                ->where('MT.BRANCH_ID', $branch_id)
                ->whereRaw("(MASK_CALL_LOG.CALL_DATE BETWEEN  '$from_date' AND '$to_date')")
                ->groupBy('MASK_CALL_LOG.CALL_LOG_ID');

            $filterModel = $request->input('filterModel');
            if ($filterModel) {
                foreach ($filterModel as $field => $filter) {
                    if (isset($filter['filter']) && $filter['filter'] !== '') {
                        $value = $filter['filter'];
                        $type = $filter['type'];

                         switch ($field) {
                            case 'ROSTER_ID':
                                $data->where('MASK_CALL_LOG.ROSTER_ID', 'like', "%{$value}%");
                                break;
                            case 'CALL_DATE':
                                $data->where('MASK_CALL_LOG.CALL_DATE', 'like', "%{$value}%");
                                break;

                            case 'END_TIME':
                                $data->where('MASK_CALL_LOG.END_TIME', 'like', "%{$value}%");
                                break;

                            case 'CUSTOMER_NUMBER':
                                $data->where('MASK_CALL_LOG.CUSTOMER_NUMBER', 'like', "%{$value}%");
                                break;

                            case 'DRIVER_NUMBER':
                                $data->where('MASK_CALL_LOG.DRIVER_NUMBER', 'like', "%{$value}%");
                                break;
                            case 'CALL_TYPE':
                                $data->where('MASK_CALL_LOG.CALL_TYPE', 'like', "%{$value}%");
                                break;
                            case 'DURATION':
                                $data->where('MASK_CALL_LOG.DURATION', 'like', "%{$value}%");
                                break;
                            case 'RECORDING_URL':
                                $data->where('MASK_CALL_LOG.RECORDING_URL', 'like', "%{$value}%");
                                break;

                            case 'STATUS':
                                $data->where('MASK_CALL_LOG.STATUS', 'like', "%{$value}%");
                                break;
                        }
                    }
                }
            }


            if ($request->has('orderBy') && !empty($request->input('orderBy'))) {
                $orderBy = $request->input('orderBy');
                $order = $request->input('order', 'DESC');
                $data->orderBy($orderBy, $order);
            } else {
                $data->orderBy('MASK_CALL_LOG.CALL_DATE', 'DESC');
            }


            if ($perPage === MyHelper::$PAGINATION_PER_PAGE_ALL) {
                $paginateddata = $data->paginate($data->count());
            } else {
                $perPage = is_numeric($perPage) ? (int)$perPage : MyHelper::$PAGINATION_PER_PAGE;
                $paginateddata = $data->paginate($perPage);
            }

            return response([
                'success' => true,
                'status' => 3,
                'mask_call_log_report' => $paginateddata,
            ]);
        } catch (\Throwable | \Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'Mask CallLogs Report Pagination Unsuccessful' : 'Mask CallLogs Report Pagination Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ], 500);
        }
    }

   public function fetch_Mask_Transaction_Report_Details($request, bool $active = true): FoundationApplication|Response|ResponseFactory
    {
        try {
            $perPage = $request->input('per_page', MyHelper::$PAGINATION_PER_PAGE);
            $branch_id = Auth::user()->BRANCH_ID;
            $user_type = Auth::user()->user_type;
            $vendor_id = Auth::user()->vendor_id;


            $triptype_id = $request->triptype_id;

            if ($triptype_id == 'ALL' || $triptype_id == 'all') {
                $triptype = ['P', 'D'];
            } else {
                $triptype = [$triptype_id];
            }

            $login_datetime = $request->selectedDate . ' ' . $request->selectedTime;
            $date = Carbon::now();


            $mask_dids = DB::table('MASK_DID_MASTER')
            ->select(DB::raw("SUM(if(AVAILABLITY_STATUS=1,1,0)) as free_count"),
            DB::raw("SUM(if(AVAILABLITY_STATUS=2,1,0)) as occupaied_count"))
            ->where('STATUS', 1)
            ->get();


            $mask_did_lists = DB::table('MASK_DID_MASTER')
            ->select('DID_NUMBER','AVAILABLITY_STATUS')
            ->where('STATUS', 1)
            ->get();

            $channel_histroy = DB::table('CHANNEL_RESPONSE')
            ->select('*')
            ->whereDate('CREATED_AT',$date->format("Y-m-d"))
            ->orderBy('CREATED_AT', 'DESC')
            ->get();

            $data = MaskTransaction::query()
                ->select(
                    'MASK_TRANSACTION.*',
                    DB::raw("IF(TIMEDIFF(MASK_TRANSACTION.END_TIME, '{$date->format("Y-m-d H:i:s")}') > 0, 1, 0) AS diff"),
                    'E.NAME AS EMPLOYEE_NAME',
                    'E.MOBILE AS EMPLOYEES_MOBILE',
                    'E.EMPLOYEES_ID',
                    'D.DRIVER_MOBILE',
                    'D.DRIVERS_NAME',
                    'VH.VEHICLE_REG_NO',
                    DB::raw("IF(R.TRIP_TYPE = 'P', R.ESTIMATE_END_TIME, R.ESTIMATE_START_TIME) AS login_date_time"),
                    'MC.CALL_DATE',
                    'MC.END_TIME AS CALL_CLOSE',
                    'MC.RECORDING_URL',
                    'MC.DURATION',
                    'MC.STATUS AS mask_status'
                )
                ->join('rosters AS R', 'R.ROSTER_ID', '=', 'MASK_TRANSACTION.ROSTER_ID')
                ->join('roster_passengers AS RP', function ($join) {
                    $join->on('RP.ROSTER_ID', '=', 'R.ROSTER_ID')
                        ->whereIn('RP.ACTIVE', [1]); // Ensure array contains integers
                })
                ->join('employees AS E', function ($join) use ($branch_id) {
                    $join->on('E.EMPLOYEES_ID', '=', 'RP.EMPLOYEE_ID')
                        ->where('E.BRANCH_ID', '=', $branch_id)
                        ->where('E.ACTIVE', '=', 1);
                })
                ->join('locations AS L', 'L.LOCATION_ID', '=', 'RP.LOCATION_ID')
                ->join('cab AS C', 'C.CAB_ID', '=', 'R.CAB_ID')
                ->join('vehicles AS VH', 'VH.VEHICLE_ID', '=', 'C.VEHICLE_ID')
                ->join('drivers AS D', 'D.DRIVERS_ID', '=', 'C.DRIVER_ID')
                ->join('vehicle_models AS VM', 'VM.VEHICLE_MODEL_ID', '=', 'VH.VEHICLE_MODEL_ID')
                ->join('MASK_CALL_LOG AS MC', function ($join) {
                    $join->on('MC.ROSTER_ID', '=', 'MASK_TRANSACTION.ROSTER_ID')
                        ->on('MC.EMPLOYEE_ID', '=', 'RP.EMPLOYEE_ID');
                })
                ->whereIn('R.TRIP_TYPE', $triptype)
                ->where('R.ACTIVE', '=', 1)
 
                ->where(function ($query) use ($login_datetime, $triptype) {
                    if (in_array('D', $triptype)) {
                        $query->orWhere('R.ESTIMATE_START_TIME', '=', $login_datetime);
                    }
                    if (in_array('P', $triptype)) {
                        $query->orWhere('R.ESTIMATE_END_TIME', '=', $login_datetime); // Fixed typo
                    }
                })
                ->groupBy('MC.CALL_LOG_ID');


            $filterModel = $request->input('filterModel');
            if ($filterModel) {
                foreach ($filterModel as $field => $filter) {
                    if (isset($filter['filter']) && $filter['filter'] !== '') {
                        $value = $filter['filter'];
                        $type = $filter['type'];

                        switch ($field) {
                            case 'ROSTER_ID':
                                $data->where('R.ROSTER_ID', 'like', "%{$value}%");
                                break;
                        }
                    }
                }
            }


            if ($request->has('orderBy') && !empty($request->input('orderBy'))) {
                $orderBy = $request->input('orderBy');
                $order = $request->input('order', 'DESC');
                $data->orderBy($orderBy, $order);
            } else {
                $data->orderBy('MC.CALL_DATE', 'DESC');
            }


            if ($perPage === MyHelper::$PAGINATION_PER_PAGE_ALL) {
                $paginateddata = $data->paginate($data->count());
            } else {
                $perPage = is_numeric($perPage) ? (int)$perPage : MyHelper::$PAGINATION_PER_PAGE;
                $paginateddata = $data->paginate($perPage);
            }

            return response([
                'success' => true,
                'status' => 3,
                'mask_transaction_report' => $paginateddata,
                'mask_dids'=>array("data" => $mask_dids),
                'mask_did_lists'=>array("data" => $mask_did_lists),                
                'channel_histroy' =>array("data" =>$channel_histroy),   
            ]);
        } catch (\Throwable | \Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'Mask CallLogs Report Pagination Unsuccessful' : 'Mask CallLogs Report Pagination Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ], 500);
        }
    }

    public function EmptyRoute()
    {
        try {
            $RS_ACTIVE = MyHelper::$RS_ACTIVE;
            $branch_id = Auth::user()->BRANCH_ID;
            $org_id = $this->commonFunction->getOrgId($branch_id);

            $cab_data = DB::table('cab as C')->select('C.CAB_ID', 'V.VEHICLE_ID', 'C.CREATED_BY', 'C.CREATED_DATE', 'V.VEHICLE_REG_NO')
                                ->join('vehicles as V', 'V.VEHICLE_ID', '=', 'C.VEHICLE_ID')
                                ->where("C.ACTIVE", "=", $RS_ACTIVE)
                                ->where("C.BRANCH_ID", "=", $branch_id)
                                ->orderBy('C.updated_at','DESC')
                                ->get();

            $vendor=vendor::where("ACTIVE","=",1)->where("BRANCH_ID","=",$branch_id)->get();

            return response([
                'success' => true,
                'status' => 1,
                'cab_data' => $cab_data,
                'vendor' => $vendor,
                'message' => "Empty Route Details Get Successfully!!!"
            ]);
        } catch (\Throwable | \Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'validation_controller' => true,
                'message' => 'Empty Route Details Get Unsuccessful',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function EmptyRouteVendorChange($request)
    {
        try {
            $RS_ACTIVE = MyHelper::$RS_ACTIVE;

            $branch_id = Auth::user()->BRANCH_ID;
            
            $cab_data = DB::table('cab as C')->select('C.CAB_ID', 'V.VEHICLE_ID', 'C.CREATED_BY', 'C.CREATED_DATE', 'V.VEHICLE_REG_NO')
                ->join('vehicles as V', 'V.VEHICLE_ID', '=', 'C.VEHICLE_ID')
                ->where("C.ACTIVE", "=", $RS_ACTIVE)
                ->where("C.BRANCH_ID", "=", $branch_id)
                ->where("C.VENDOR_ID", "=", $request->input('vendor_id'))
                ->orderBy('C.updated_at','DESC')->get();

            return response([
                'success' => true,
                'status' => 1,
                'cab_data' => $cab_data,
                'message' => "Empty Route Vendor Change Get Successfully!!!"
            ]);
        }  catch (\Throwable | \Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'validation_controller' => true,
                'message' => 'Empty Route Vendor Change Get Unsuccessful',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
    
    public function TripTypeChange($request)
    {
        try {
            $branch_id=Auth::user()->BRANCH_ID;
            $type=$request->trip_type_change;
            $current_date = date("Y-m-d");
            
            if($type=='P'){
                $query="select CONCAT('$current_date', ' ', IN_TIME) AS IN_TIME,CATEGORY from shift_time where BRANCH_ID='".$branch_id."' and Active=1 ";
            }else{
                $query="select CONCAT('$current_date', ' ', OUT_TIME) AS OUT_TIME,CATEGORY from shift_time where BRANCH_ID='".$branch_id."' and Active=1 and OUT_TIME is not null ";
            }

            $result = DB::select($query);

            return response([
                'success' => true,
                'status' => 1,
                'shift_time' => $result,
                'message' => "Empty Route Details Get Successfully!!!"
            ]);
        } catch (\Throwable | \Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'validation_controller' => true,
                'message' => 'Trip Type Change Details Unsuccessful',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function EmptyVehicle($request)
    {
        try {
            $result=$this->empty_vehicle($request);
            $trip_type=$request->trip_type;
            //dd($result);
            if(count($result)>0)
            {
                $site_lat_long=$result[0]->site_lat_long;
                $driver_lat_long=$result[0]->driver_lat_long;

                if($trip_type=='P'){
                    $site_distance=$this->commonFunction->getKmTwoPointMasterEmp($driver_lat_long,$site_lat_long);
                } else {
                    $site_distance=$this->commonFunction->getKmTwoPointMasterEmp($site_lat_long,$driver_lat_long);
                }

                $distance_split2=explode("-",$site_distance);
                $distance=$distance_split2[0]/1000;

                return response([
                    'success' => true,
                    'status' => 1,
                    'distance' => $distance,
                    'message' => "Distance Get Successfully!!!"
                ]);
            }
        } catch (\Throwable | \Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'validation_controller' => true,
                'message' => 'Trip Type Change Details Unsuccessful',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function EmptyRouteAdd($request)
    {
        try {
            $vendor_id=$request->vendor_name;
            $trip_type=$request->trip_type;
            $in_out=$request->in_out;
            $vehicle_no=$request->vehicle_no;
            $remarks=$request->remarks;
            $empty_km=$request->empty_km;
            $branch_id=Auth::user()->BRANCH_ID;
            $id = Auth::user()->id;

            DB::beginTransaction();
            $insert = "INSERT INTO `empty_cab_km` (`CAB_ID`, `VENDOR_ID`, `BRANCH_ID`, `TRIP_TYPE`, `IN_OUT_TIME`,EMPTY_KM, `APPROVE_STATUS`,`REMARKS`,`CREATED_BY`,CREATED_DATE) VALUES ('".$vehicle_no."','".$vendor_id."','".$branch_id."','".$trip_type."','".$in_out."','5',1,'".$remarks."',".$id.",'".date("Y-m-d H:i:s")."')";
            $result = DB::insert($insert);
            if($result==1){
                return response([
                    'success' => true,
                    'status' => 1,
                    'message' => "Empty KM Added Successfully!!!"
                ]);
            } else{
                DB::rollBack();
                return response([
                    'success' => true,
                    'status' => 0,
                    'message' => "Empty KM Added Unsuccessful!!!"
                ]);
            }
        } catch (\Throwable | \Exception $e) {
            DB::rollBack();
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'validation_controller' => true,
                'message' => 'Trip Type Change Details Unsuccessful',
                'error' => $e->getMessage(),
            ], 500);
        } finally {
            DB::commit();
        }
    }

    public function empty_vehicle($request)
    {
        $branch_id=Auth::user()->BRANCH_ID;
		$trip_type=$request->trip_type;
		$veh=$request->vehicle_id;
		
		if($trip_type=='P')
		{
			$query="SELECT C.CAB_ID,D.DRIVERS_ADRESS,concat(D.DRIVERS_ADDR_LAT,',',D.DRIVERS_ADDR_LONG) as driver_lat_long,B.BRANCH_ID,B.BRANCH_NAME,concat(B.LAT,',',B.`LONG`) as site_lat_long from cab C
			inner join drivers D on D.DRIVERS_ID=C.DRIVER_ID
			inner join branch B on B.BRANCH_ID=".$branch_id."

			WHERE C.BRANCH_ID=".$branch_id." and C.CAB_ID=".$veh." ";
		}
		else
		{
			$query="SELECT C.CAB_ID,D.DRIVERS_ADRESS,concat(D.DRIVERS_ADDR_LAT,',',D.DRIVERS_ADDR_LONG) as driver_lat_long,B.BRANCH_ID,B.BRANCH_NAME,concat(B.LAT,',',B.`LONG`) as site_lat_long from cab C
			inner join drivers D on D.DRIVERS_ID=C.DRIVER_ID
			inner join branch B on B.BRANCH_ID=".$branch_id."
			WHERE C.BRANCH_ID=".$branch_id." and C.CAB_ID=".$veh." ";
		} 
		return DB::select($query);
    }
   
    public function GetFilterEmpty($request)
    {
        try {
            $branch_id=Auth::user()->BRANCH_ID;

            $query="SELECT EK.BRANCH_ID,EK.CAB_ID,EK.APPROVE_STATUS,EK.EMPTY_ID,EK.EMPTY_KM,V.VEHICLE_REG_NO,EK.IN_OUT_TIME,date(EK.IN_OUT_TIME) as IN_OUT_DATE,EK.TRIP_TYPE,VE.NAME as VENDOR_NAME,
	    ROUND(EK.EMPTY_KM * T.PACKAGE_PRICE,2) AS AMOUNT,
            ROUND(EK.EMPTY_KM * T.PACKAGE_PRICE * 1.05,2) AS AMOUNT_WITH_TAX,
            case 
            when EK.APPROVE_STATUS=1 then 'Created'
            when EK.APPROVE_STATUS=2 then 'Approved'
            when EK.APPROVE_STATUS=6 then 'Rejected'
            end STS,VM.MODEL,T.PACKAGE_PRICE
            from empty_cab_km EK
            INNER join cab C on C.CAB_ID=EK.CAB_ID
            INNER JOIN vehicles V on V.VEHICLE_ID=C.VEHICLE_ID
            INNER JOIN vehicle_models VM on VM.VEHICLE_MODEL_ID=V.VEHICLE_MODEL_ID
            inner join vendors VE on VE.VENDOR_ID=EK.VENDOR_ID
            inner join (select * from tariff where BRANCH_ID=".$branch_id." and ACTIVE=1 group by VEHICLE_MODEL_ID) T on
            T.TARIFF_TYPE=C.TARIFF_TYPE and T.BRANCH_ID=".$branch_id." AND T.VEHICLE_MODEL_ID = VM.VEHICLE_MODEL_ID
            where date(EK.IN_OUT_TIME) BETWEEN '".$request->from_date."' and '".$request->to_date."' and EK.BRANCH_ID=".$branch_id." ";

            $result = DB::select($query);

            return response([
                'success' => true,
                'status' => 1,
                'empty_approvel_list' => $result,
                'message' => "Empty Approvel Details Fetch Successfully!!!"
            ]); 
        } catch (\Throwable | \Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'validation_controller' => true,
                'message' => 'Empty Approvel Details Fetch Unsuccessful',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function ApproveSubmitEmpty($request)
    {
        try {
            $USERID = Auth::user()->id;
            DB::beginTransaction();
            $update="update empty_cab_km set APPROVE_STATUS=".$request->approve_type.",UPDATED_BY='".$USERID."',UPDATED_DATE_TIME='".date("Y-m-d H:i:s")."' where EMPTY_ID='".$request->empty_id."' ";
            $result = DB::update($update);

            return response([
                'success' => true,
                'status' => 1,
                'message' => "Empty Approvel Details Updated Successfully!!!"
            ]); 
        } catch (\Throwable | \Exception $e) {
            DB::rollBack();
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'validation_controller' => true,
                'message' => 'Empty Approvel Details Updated Unsuccessful',
                'error' => $e->getMessage(),
            ], 500);
        } finally {
            DB::commit();
        }
    }
	
	 public function getMisReport($request, bool $active = true): FoundationApplication|Response|ResponseFactory
    {
        try {
            $ElasticController = new ElasticController();


            $perPage = $request->input('per_page', MyHelper::$PAGINATION_PER_PAGE);
            $branch_id = Auth::user()->BRANCH_ID;
            $user_type = Auth::user()->user_type;
            $vendor_id = Auth::user()->vendor_id;


            $trip_type = $request->trip_type;
            $from_date = $request->from_date;
            $to_date = $request->to_date;


            $vendorCondition = '';
            if ($user_type !== 'ADMIN') {
                $vendorCondition = "AND R.VENDOR_ID = $vendor_id";
            }

            $triptypeCondition = ($trip_type === 'ALL')
                ? "AND R.TRIP_TYPE IN ('P','D')"
                : "AND R.TRIP_TYPE = '$trip_type'";

            $SWINGSTART_TIME = $this->commonFunction->GetPropertyValue('SWING START TIME');
            $SWINGEND_TIME = $this->commonFunction->GetPropertyValue('SWING END TIME');
            $taxdetect = $this->commonFunction->GetPropertyValue('TAX');


            $subRS = DB::table(DB::raw('(SELECT
                RR.ROSTER_ID,
                RR.EMPLOYEE_ID,
                RR.ROUTE_ORDER,
                RR.ROSTER_PASSENGER_STATUS,
                RR.START_LAT,
                RR.START_LONG,
                RR.END_LAT,
                RR.END_LONG,
                DATE(RR.ESTIMATE_END_TIME)   AS end_date,
                DATE(RR.ESTIMATE_START_TIME) AS start_date
              FROM rosters RU
              INNER JOIN roster_passengers RR ON RR.ROSTER_ID = RU.ROSTER_ID
              WHERE RU.BRANCH_ID = ' . $branch_id . '
                AND RU.ACTIVE = 1
                AND RR.ACTIVE = 1
                AND (
                     DATE(RR.ESTIMATE_END_TIME) BETWEEN "' . $from_date . '" AND "' . $to_date . '"
                     OR DATE(RR.ESTIMATE_START_TIME) BETWEEN "' . $from_date . '" AND "' . $to_date . '"
                    )
        ) AS EM'))
                ->selectRaw('
            EM.ROSTER_ID,
            COUNT(*) AS TTLEMPCNT,
            SUM(IF(EM.ROSTER_PASSENGER_STATUS & 16, 1, 0)) AS EMPNOSHOWCNT,
            SUM(
                IF(
                    (EM.ROSTER_PASSENGER_STATUS & 32 OR EM.ROSTER_PASSENGER_STATUS & 128),
                    1,
                    0
                )
            ) AS TRAVELEMPCNT,
            EM.START_LAT,
            EM.START_LONG,
            EM.END_LAT,
            EM.END_LONG
        ')
                ->groupBy('EM.ROSTER_ID');

            $subTP = DB::table('toll_payment')
                ->selectRaw('ROSTER_ID, SUM(TOLL_CHARGE) as ttlcost')
                ->whereBetween(DB::raw('DATE(PAYMENT_DATE)'), [$from_date, $to_date])
                ->groupBy('ROSTER_ID');

            $subPY = DB::table('route_penalty')
                ->selectRaw('ROSTER_ID, SUM(AMOUNT) AS TTLAMOUNT')
                ->whereBetween('LOGIN_DATE', [$from_date, $to_date])
                ->groupBy('ROSTER_ID');


            $useTariffKmSlab = in_array($branch_id, [48, 61]);


            $query = Roster::query()
                ->from('rosters AS R')
                ->selectRaw("
                R.ROSTER_ID,
                R.ROUTE_ID,
                RS.TTLEMPCNT,
                RS.TRAVELEMPCNT,
                RS.EMPNOSHOWCNT,
                R.TRIP_TYPE,
                V.VEHICLE_REG_NO,
                VM.MODEL,
                VM.CAPACITY,
                VE.NAME AS VENDORNAME,
                R.START_LOCATION,
                R.END_LOCATION,
                '--' AS OTPLOCATION,
                IF(R.TRIP_TYPE = 'P', DATE(R.ESTIMATE_END_TIME), DATE(R.ESTIMATE_START_TIME)) AS INOUT_DATE,
                IF(R.TRIP_TYPE = 'P', TIME(R.ESTIMATE_END_TIME),  TIME(R.ESTIMATE_START_TIME)) AS INOUT_TIME,
                RM.REASON,
                dbs.GOOGLE_KM,
                dbs.TRIP_STATUS,
                R.TOTAL_KM,
                R.TRIP_APPROVED_KM,
                IF(
                    TIME(R.ESTIMATE_END_TIME) <= '$SWINGSTART_TIME'
                       OR TIME(R.ESTIMATE_END_TIME) >= '$SWINGEND_TIME',
                    (RS.TRAVELEMPCNT - 1),
                    0
                ) AS SWINGKM,
                '0' AS TTLKMS,
                IF(TP.ttlcost IS NULL, 0, TP.ttlcost)   AS TOLLCOST,
                IF(PY.TTLAMOUNT IS NULL, 0, PY.TTLAMOUNT) AS PENALTYCOST,
                '--' AS ALLSTATUS,
                R.ROSTER_STATUS,
                IF(RE.ROSTER_ID IS NULL, '--', CONCAT(ES.ESCORT_ID,'-',ES.ESCORT_NAME)) AS ESCORTROUTE,
                RS.START_LAT,
                RS.START_LONG,
                RS.END_LAT,
                RS.END_LONG
                " . ($useTariffKmSlab
                        ? ", T.PACKAGE_PRICE AS TARIFF, 0 AS PACKAGE_KMS, 0 AS EXTRA_KMS_CHARGE "
                        : ", TF.PACKAGE_PRICE AS TARIFF, TF.PACKAGE_KMS, TF.EXTRA_KMS_CHARGE "
                    ) . "
            ")
                ->joinSub($subRS, 'RS', function ($join) {
                    $join->on('RS.ROSTER_ID', '=', 'R.ROSTER_ID');
                })
                ->leftJoin('driver_billing_summary AS dbs', function ($join) {
                    $join->on('dbs.ROSTER_ID', '=', 'R.ROSTER_ID')
                         ->where('dbs.ACTIVE', '=', 1);
                })
                ->join('branch AS B', 'B.BRANCH_ID', '=', 'R.BRANCH_ID')
                ->join('vendors AS VE', 'VE.VENDOR_ID', '=', 'R.VENDOR_ID')
                ->join('cab AS C', 'C.CAB_ID', '=', 'R.CAB_ID')
                ->join('vehicles AS V', 'V.VEHICLE_ID', '=', 'C.VEHICLE_ID')
                ->join('vehicle_models AS VM', 'VM.VEHICLE_MODEL_ID', '=', 'V.VEHICLE_MODEL_ID')
                ->join('reason_master AS RM', 'RM.REASON_ID', '=', 'C.TARIFF_TYPE')
                ->when($useTariffKmSlab, function ($q) use ($branch_id) {
                    return $q->leftJoin('tariff_km_slap AS T', function ($join) use ($branch_id) {
                        $join->on('T.VENDOR_ID', '=', 'R.VENDOR_ID')
                            ->on('T.VEHICLE_MODEL_ID', '=', 'V.VEHICLE_MODEL_ID')
                            ->where('T.BRANCH_ID', '=', $branch_id)
                            ->whereRaw('T.PACKAGE_START_KM <= R.TRIP_APPROVED_KM')
                            ->whereRaw('T.PACKAGE_END_KM >= R.TRIP_APPROVED_KM');
                    });
                }, function ($q) use ($branch_id) {
                    return $q->leftJoin('tariff AS TF', function ($join) use ($branch_id) {
                        $join->on('TF.TARIFF_TYPE', '=', 'C.TARIFF_TYPE')
                            ->on('TF.BRANCH_ID', '=', 'C.BRANCH_ID')
                            ->on('TF.VENDOR_ID', '=', 'C.VENDOR_ID')
                            ->on('TF.VEHICLE_MODEL_ID', '=', 'V.VEHICLE_MODEL_ID');
                    });
                })
                ->leftJoinSub($subTP, 'TP', function ($join) {
                    $join->on('TP.ROSTER_ID', '=', 'R.ROSTER_ID');
                })
                ->leftJoinSub($subPY, 'PY', function ($join) {
                    $join->on('PY.ROSTER_ID', '=', 'R.ROSTER_ID');
                })
                ->leftJoin('route_escorts AS RE', function ($join) {
                    $join->on('RE.ROSTER_ID', '=', 'R.ROSTER_ID')
                        ->on('RE.BRANCH_ID', '=', 'R.BRANCH_ID')
                        ->whereNotIn('RE.STATUS', [5, 6]);
                })
                ->leftJoin('escorts AS ES', 'ES.ESCORT_ID', '=', 'RE.ESCORT_ID')
                ->where('R.BRANCH_ID', $branch_id)
                ->where('R.ACTIVE', 1)
                ->where(function ($q) use ($from_date, $to_date) {
                    $q->whereBetween(DB::raw('DATE(R.ESTIMATE_END_TIME)'), [$from_date, $to_date])
                        ->orWhereBetween(DB::raw('DATE(R.ESTIMATE_START_TIME)'), [$from_date, $to_date]);
                })
                ->where(function ($q) {
                    // (R.ROSTER_STATUS & 256 OR R.ROSTER_STATUS & 1024)
                    $q->whereRaw('(R.ROSTER_STATUS & 256) OR (R.ROSTER_STATUS & 1024)');
                })
                ->whereRaw('1=1 ' . $triptypeCondition . ' ' . $vendorCondition);


            $filterModel = $request->input('filterModel');
            if ($filterModel) {
                foreach ($filterModel as $field => $filter) {
                    if (isset($filter['filter']) && $filter['filter'] !== '') {
                        $value = $filter['filter'];

                        switch ($field) {
                            case 'ROSTER_ID':
                                $query->where('R.ROSTER_ID', 'like', "%{$value}%");
                                break;
                            case 'TRIP_TYPE':
                                $query->where('R.TRIP_TYPE', 'like', "%{$value}%");
                                break;
                            case 'VENDORNAME':
                                $query->where('VE.NAME', 'like', "%{$value}%");
                                break;
                            case 'VEHICLE_REG_NO':
                                $query->where('V.VEHICLE_REG_NO', 'like', "%{$value}%");
                                break;
                            case 'MODEL':
                                $query->where('VM.MODEL', 'like', "%{$value}%");
                                break;

                        }
                    }
                }
            }


            if ($request->has('orderBy') && !empty($request->input('orderBy'))) {
                $orderBy = $request->input('orderBy');
                $order = $request->input('order', 'asc');
                $query->orderBy($orderBy, $order);
            } else {
                $query->orderBy('R.ROSTER_ID','asc');
            }


            if ($perPage === MyHelper::$PAGINATION_PER_PAGE_ALL) {
                $paginatedRosters = $query->paginate($query->count());
            } else {
                $perPage = is_numeric($perPage) ? (int)$perPage : MyHelper::$PAGINATION_PER_PAGE;
                $paginatedRosters = $query->paginate($perPage);
            }


            $paginatedRosters->getCollection()->transform(function ($row) use (
                $ElasticController,
                $branch_id,
                $SWINGSTART_TIME,
                $SWINGEND_TIME,
                $taxdetect
            ) {

                $OPTLOCATION = '--';
                if ($row->TRIP_TYPE === 'P') {
                    if (!empty($row->START_LAT)) {
                        $dataotp = $ElasticController->getGpsAddress($row->START_LAT, $row->START_LONG);
                        $OPTLOCATION = ($dataotp !== 'No Record') ? $dataotp[0]['ADDRESS'] : '--';
                    }
                } else {
                    if (!empty($row->END_LAT)) {
                        $dataotp = $ElasticController->getGpsAddress($row->END_LAT, $row->END_LONG);
                        $OPTLOCATION = ($dataotp !== 'No Record') ? $dataotp[0]['ADDRESS'] : '--';
                    }
                }
                $row->OTPLOCATION = $OPTLOCATION;


                $RS_STATUS = $this->commonFunction->TripStatus($row->ROSTER_STATUS);
                $row->ALLSTATUS = $RS_STATUS;


                $TRIP_TYPE = $row->TRIP_TYPE;
                $TOTAL_KM = (float)$row->TOTAL_KM;
                $TRIP_APPROVED_KM = (float)$row->TRIP_APPROVED_KM;
                $SWINGKM = (float)$row->SWINGKM;
                if ($SWINGKM < 0) {
                    $SWINGKM = 0;
                }
                $TRAVELEMPCNT = (int)$row->TRAVELEMPCNT;
                $CAPACITY = (int)$row->CAPACITY;
                $TOLLCOST = (float)$row->TOLLCOST;
                $PENALTYCOST = (float)$row->PENALTYCOST;
                $TARIFF = (float)$row->TARIFF;
                $PACKAGE_KMS = (float)($row->PACKAGE_KMS ?? 0);
                $EXTRA_KMS_CHARGE = (float)($row->EXTRA_KMS_CHARGE ?? 0);

                $TTLKM = 0.0;
                $FINALCOST = 0.0;


                if ($branch_id == 27 || $branch_id == 23) {
                    $TTLKM = $TRIP_APPROVED_KM + $TOTAL_KM + $SWINGKM;
                    if ($TTLKM < $PACKAGE_KMS) {
                        $FINALCOST = $TARIFF;
                    } else {
                        $remainKm = $TTLKM - $PACKAGE_KMS;
                        $FINALCOST = $TARIFF + ($remainKm * $EXTRA_KMS_CHARGE);
                    }
                } else if ($branch_id == 25) {

                    $TTLDIST = round($this->GetRosterDistance($row->ROSTER_ID, $TRIP_TYPE));
                    $TTLKM = (2 * $TTLDIST) + (2 * $TRAVELEMPCNT);
                    $FINALCOST = $TARIFF * $TTLKM;
                } else if ($branch_id == 48) {
                    $TTLKM = $TRIP_APPROVED_KM;
                    $FINALCOST = $TARIFF;
                } else if ($branch_id == 57) {

                    if ($TRAVELEMPCNT < $CAPACITY) {

                        if ($TRAVELEMPCNT > 0 && $TRAVELEMPCNT <= 4) {
                            $FINALCOST = 820;
                        } elseif ($TRAVELEMPCNT > 4 && $TRAVELEMPCNT < 8) {
                            $FINALCOST = 1055;
                        }
                    } else {
                        $FINALCOST = $TARIFF;
                    }
                    $TTLKM = $TRIP_APPROVED_KM + $TOTAL_KM + $SWINGKM;
                } else {

                    $TTLKM = $TRIP_APPROVED_KM + $SWINGKM;
                    $FINALCOST = $TARIFF * $TTLKM;
                }

                $FINALCOST = $FINALCOST + $TOLLCOST - $PENALTYCOST;

                if ($RS_STATUS === 'Tripsheet Cancelled') {
                    $FINALCOST = 0;
                }

                $TtlTax = 0.0;
                if ($FINALCOST != 0) {
                    $TtlTax = ($FINALCOST * $taxdetect) / 100;
                }

                if ($branch_id == 48) {
                    $row->TAX = $TtlTax;
                }

                $row->TOTALAMT = $FINALCOST;
                $row->TTLKMS = $TTLKM;

                return $row;
            });


            return response([
                'success' => true,
                'status' => 3,
                'mis_report' => $paginatedRosters
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            $message = 'MIS Report Pagination Unsuccessful';

            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }
	
     /* public function getMisReport($request, bool $active = true): FoundationApplication|Response|ResponseFactory
    {
        try 
		{
            $ElasticController = new ElasticController();
            $perPage = $request->input('per_page', MyHelper::$PAGINATION_PER_PAGE);
            $branch_id = Auth::user()->BRANCH_ID;
            $user_type = Auth::user()->user_type;
            $vendor_id = Auth::user()->vendor_id;


            $trip_type = $request->trip_type;
            $from_date = $request->from_date;
            $to_date = $request->to_date;
		
		$dbname = Auth::user()->dbname;
        if( $user_type=='ADMIN'){
            $vendor = "";
        }
        else{
            $vendor = "AND R.VENDOR_ID=$vendor_id";
        }
        if($trip_type == 'ALL')
        {   
           // $RPTTLTYPE = explode(',', $this->TTLTYPE);
            $triptype = "AND R.TRIP_TYPE IN ('P','D')";
        }
        else
        {
            $triptype = "AND R.TRIP_TYPE ='$trip_type'";
        }
        $SWINGSTART_TIME = $this->commonFunction->GetPropertyValue('SWING START TIME');
        $SWINGEND_TIME = $this->commonFunction->GetPropertyValue('SWING END TIME');
		
        if($branch_id != '25'){
			
			if($branch_id == '48' || $branch_id == '61') {
            $kmquery = '';
            $queryttlamt = "'0' AS TAX,'0' AS TOTALAMT,";
            $querykm = ",R.TOTAL_KM,R.TRIP_APPROVED_KM,
            IF(TIME(R.ESTIMATE_END_TIME) <= '$SWINGSTART_TIME' OR TIME(R.ESTIMATE_END_TIME) >= '$SWINGEND_TIME',(RS.TRAVELEMPCNT-1),0) AS SWINGKM";
			
			$tariff="LEFT JOIN tariff_km_slap  T ON   T.PACKAGE_START_KM <= R.TRIP_APPROVED_KM AND T.PACKAGE_END_KM >= R.TRIP_APPROVED_KM AND T.VENDOR_ID = R.VENDOR_ID AND T.VEHICLE_MODEL_ID = V.VEHICLE_MODEL_ID AND T.BRANCH_ID = $branch_id";
			$tariff_select=" T.PACKAGE_PRICE AS TARIFF,0 as PACKAGE_KMS,0 as EXTRA_KMS_CHARGE";
			
           }else{           			   
		
					$kmquery = "R.TOTAL_KM,R.TRIP_APPROVED_KM,
					IF(TIME(R.ESTIMATE_END_TIME) <= '$SWINGSTART_TIME' OR TIME(R.ESTIMATE_END_TIME) >= '$SWINGEND_TIME',(RS.TRAVELEMPCNT-1),0) AS SWINGKM,";
					$querykm = '';
					$queryttlamt = "'0' AS TOTALAMT,";
					$tariff='LEFT JOIN tariff TF ON TF.TARIFF_TYPE = C.TARIFF_TYPE AND TF.BRANCH_ID = C.BRANCH_ID 
				AND TF.VENDOR_ID = C.VENDOR_ID AND TF.VEHICLE_MODEL_ID = V.VEHICLE_MODEL_ID';
                          $tariff_select=" TF.PACKAGE_PRICE AS TARIFF,TF.PACKAGE_KMS,TF.EXTRA_KMS_CHARGE";


                if(in_array($branch_id,[18,62,63,64])){  
                    $kmquery = "R.TOTAL_KM,R.TRIP_APPROVED_KM,dbs.GOOGLE_KM,dbs.TRIP_STATUS,
                    IF(TIME(R.ESTIMATE_END_TIME) <= '$SWINGSTART_TIME' OR TIME(R.ESTIMATE_END_TIME) >= '$SWINGEND_TIME',(RS.TRAVELEMPCNT-1),0) AS SWINGKM,";
                }

		   }
        }else{
                $kmquery = '2 * R.TOTAL_KM,';
                $queryttlamt = '';
                $querykm = ",R.TOTAL_KM,R.TRIP_APPROVED_KM,
            IF(TIME(R.ESTIMATE_END_TIME) <= '$SWINGSTART_TIME' OR TIME(R.ESTIMATE_END_TIME) >= '$SWINGEND_TIME',(RS.TRAVELEMPCNT-1),0) AS SWINGKM";
			$tariff='LEFT JOIN tariff TF ON TF.TARIFF_TYPE = C.TARIFF_TYPE AND TF.BRANCH_ID = C.BRANCH_ID 
        AND TF.VENDOR_ID = C.VENDOR_ID AND TF.VEHICLE_MODEL_ID = V.VEHICLE_MODEL_ID';
		$tariff_select=" TF.PACKAGE_PRICE AS TARIFF,TF.PACKAGE_KMS,TF.EXTRA_KMS_CHARGE";
			
        }

		$sql = "SELECT R.ROSTER_ID,R.ROUTE_ID,RS.TTLEMPCNT,RS.TRAVELEMPCNT,RS.EMPNOSHOWCNT,R.TRIP_TYPE,V.VEHICLE_REG_NO,VM.MODEL,VM.CAPACITY,
        VE.`NAME` VENDORNAME,R.START_LOCATION,R.END_LOCATION,'--' as OTPLOCATION,
        if(R.TRIP_TYPE = 'P',DATE(R.ESTIMATE_END_TIME),DATE(R.ESTIMATE_START_TIME)) as INOUT_DATE,
        if(R.TRIP_TYPE = 'P',TIME(R.ESTIMATE_END_TIME),TIME(R.ESTIMATE_START_TIME)) AS INOUT_TIME,RM.REASON,$kmquery
        '0' AS TTLKMS,if(TP.ttlcost is NULL,0,TP.ttlcost) AS TOLLCOST,if(PY.TTLAMOUNT is NULL,0,PY.TTLAMOUNT) AS PENALTYCOST,$queryttlamt
        if(RE.ROSTER_ID IS NULL,'--',CONCAT(ES.ESCORT_ID,'-',ES.ESCORT_NAME)) ESCORTROUTE,'--' AS ALLSTATUS,
		$tariff_select
       ,R.ROSTER_STATUS
         ,RS.START_LAT,RS.START_LONG,RS.END_LAT,RS.END_LONG $querykm 

         FROM rosters R
        INNER JOIN (SELECT EM.*,COUNT(*) AS TTLEMPCNT,
        sum(if(EM.ROSTER_PASSENGER_STATUS & 16,1,0)) AS EMPNOSHOWCNT,
        SUM(if((EM.ROSTER_PASSENGER_STATUS & 32 OR EM.ROSTER_PASSENGER_STATUS & 128),1,0)) AS TRAVELEMPCNT  
        FROM (SELECT RR.ROSTER_ID,RR.EMPLOYEE_ID,RR.ROUTE_ORDER,RR.ROSTER_PASSENGER_STATUS,RR.START_LAT,RR.START_LONG,RR.END_LAT,RR.END_LONG
        FROM rosters RU
        INNER JOIN roster_passengers RR ON RR.ROSTER_ID = RU.ROSTER_ID 
        WHERE RU.BRANCH_ID=$branch_id AND RU.ACTIVE = 1 AND RR.ACTIVE = 1 AND (DATE(RR.ESTIMATE_END_TIME) BETWEEN '$from_date' AND '$to_date'
        OR DATE(RR.ESTIMATE_START_TIME) BETWEEN '$from_date' AND '$to_date') ORDER BY RR.ROUTE_ORDER DESC)
        EM GROUP BY EM.ROSTER_ID) RS ON RS.ROSTER_ID = R.ROSTER_ID
        LEFT JOIN driver_billing_summary dbs ON dbs.ROSTER_ID = R.ROSTER_ID
        INNER JOIN branch B ON B.BRANCH_ID = R.BRANCH_ID
        INNER JOIN vendors VE ON VE.VENDOR_ID = R.VENDOR_ID
        INNER JOIN cab C ON C.CAB_ID = R.CAB_ID
        INNER JOIN vehicles V ON V.VEHICLE_ID = C.VEHICLE_ID
        INNER JOIN vehicle_models VM ON VM.VEHICLE_MODEL_ID = V.VEHICLE_MODEL_ID
        INNER JOIN reason_master RM ON RM.REASON_ID = C.TARIFF_TYPE
		$tariff
        
        LEFT JOIN (SELECT ROSTER_ID,SUM(TOLL_CHARGE) as ttlcost FROM toll_payment WHERE DATE(PAYMENT_DATE)
        BETWEEN '$from_date' AND '$to_date' GROUP BY ROSTER_ID) TP ON TP.ROSTER_ID = R.ROSTER_ID 
        LEFT JOIN (SELECT ROSTER_ID,SUM(AMOUNT) AS TTLAMOUNT FROM route_penalty 
        WHERE LOGIN_DATE BETWEEN '$from_date' AND '$to_date' GROUP BY ROSTER_ID) PY ON PY.ROSTER_ID = R.ROSTER_ID 
        LEFT JOIN route_escorts RE ON RE.ROSTER_ID = R.ROSTER_ID AND RE.BRANCH_ID = R.BRANCH_ID AND RE.STATUS NOT IN (5,6)
        LEFT JOIN escorts ES ON ES.ESCORT_ID = RE.ESCORT_ID
        WHERE R.BRANCH_ID=$branch_id AND R.ACTIVE = 1 $vendor AND (DATE(R.ESTIMATE_END_TIME) BETWEEN '$from_date' AND '$to_date' 
        OR DATE(R.ESTIMATE_START_TIME) BETWEEN '$from_date' AND '$to_date') and (R.ROSTER_STATUS &256 or R.ROSTER_STATUS &1024) $triptype order by R.ROSTER_ID DESC
		";
        $normal_array=DB::select($sql);
        //print_r($normal_array);exit;
        $taxdetect = $this->commonFunction->GetPropertyValue('TAX');
        for ($i = 0; $i < count($normal_array); $i++) {
            // echo '<pre>';
            // print_r($normal_array);
            $REMAINKM = 0;
            $ROSTER_ID = $normal_array[$i]->ROSTER_ID;
          
            $TRIP_TYPE = $normal_array[$i]->TRIP_TYPE;
            $TOTAL_KM = $normal_array[$i]->TOTAL_KM;
            if ($normal_array[$i]->SWINGKM < 0) {
                $SWINGKM = 0;
            } else {
                $SWINGKM = $normal_array[$i]->SWINGKM;
            }
            $TRAVELEMPCNT = $normal_array[$i]->TRAVELEMPCNT;
            $CAPACITY = $normal_array[$i]->CAPACITY;
            $TRIP_APPROVED_KM = $normal_array[$i]->TRIP_APPROVED_KM;
            $TARIFF = $normal_array[$i]->TARIFF;
            $TOLLCOST = $normal_array[$i]->TOLLCOST;
            $PENALTYCOST = $normal_array[$i]->PENALTYCOST;
            $PACKAGE_KMS = $normal_array[$i]->PACKAGE_KMS;
            $EXTRA_KMS_CHARGE = $normal_array[$i]->EXTRA_KMS_CHARGE;
            $ROSTER_STATUS = $normal_array[$i]->ROSTER_STATUS;
            $TRIP_TYPE = $normal_array[$i]->TRIP_TYPE;
            $START_LAT = $normal_array[$i]->START_LAT;
            $START_LONG = $normal_array[$i]->START_LONG;
            $END_LAT = $normal_array[$i]->END_LAT;
            $END_LONG = $normal_array[$i]->END_LONG;

            if ($TRIP_TYPE == 'P') {
                if ($START_LAT != '' || $START_LAT != NULL) {
                    $dataotp = $ElasticController->getGpsAddress($START_LAT, $START_LONG);
                    if ($dataotp != 'No Record') {
                        $OPTLOCATION = $dataotp[0]['ADDRESS'];
                    } else {
                        $OPTLOCATION = '--';
                    }

                } else {
                    $OPTLOCATION = '--';
                }

            } else {
                if ($END_LAT != '' || $END_LAT != NULL) {
                    $dataotp = $ElasticController->getGpsAddress($END_LAT, $END_LONG);
                    if ($dataotp != 'No Record') {
                        $OPTLOCATION = $dataotp[0]['ADDRESS'];
                    } else {
                        $OPTLOCATION = '--';
                    }
                } else {
                    $OPTLOCATION = '--';
                }
            }
            $RS_STATUS = $this->commonFunction->TripStatus($ROSTER_STATUS);

            if ($branch_id == '27' || $branch_id == '23') {
                $TTLKM = $TRIP_APPROVED_KM + $TOTAL_KM + $SWINGKM;
                if ($TTLKM < $PACKAGE_KMS) {
                    $TTLCOST = $TARIFF;
                } else {
                    $REMAINKM = $TTLKM - $PACKAGE_KMS;
                    $TTLCOST = $TARIFF + ($REMAINKM * $EXTRA_KMS_CHARGE);
                }
            } else if ($branch_id == '25') {

                // $TTLDIST = round($this->GetRosterDistance($ROSTER_ID,$TRIP_TYPE,$MASTERDIST));
                $TTLDIST = round($this->GetRosterDistance($ROSTER_ID, $TRIP_TYPE));
                //                        $TTLKM = (2 * $TRIP_APPROVED_KM) + (5 * $TRAVELEMPCNT);
                $TTLKM = (2 * $TTLDIST) + (2 * $TRAVELEMPCNT);
                $TTLCOST = $TARIFF * $TTLKM;
            } else if ($branch_id == '48') {
                $TTLKM = $TRIP_APPROVED_KM;
                //$TTLKM = $TRIP_APPROVED_KM + $TOTAL_KM + $SWINGKM;
                $TTLCOST = $TARIFF;
            } else if ($branch_id == '57') {
                if ($TRAVELEMPCNT < $CAPACITY) {
                    if ($TRAVELEMPCNT > 0 && $TRAVELEMPCNT <= 4) {
                        $TTLCOST = 820;
                    } elseif ($TRAVELEMPCNT > 4 && $TRAVELEMPCNT < 8) {
                        $TTLCOST = 1055;
                    }
                } else {
                    $TTLCOST = $TARIFF;
                }
                $TTLKM = $TRIP_APPROVED_KM + $TOTAL_KM + $SWINGKM;

            } else {
                $TTLKM = $TRIP_APPROVED_KM + $SWINGKM;
                $TTLCOST = $TARIFF * $TTLKM;
            }

            $FINALCOST = $TTLCOST + $TOLLCOST - $PENALTYCOST;
            if ($RS_STATUS == 'Tripsheet Cancelled') {
                $FINALCOST = 0;
            }
            if ($FINALCOST != 0) {
                $TtlTax = ($FINALCOST * $taxdetect) / 100;
            } else {
                $TtlTax = 0;
            }
            if ($branch_id == '48') {
                $emp_arr_val = array(
                    "OTPLOCATION" => $OPTLOCATION,
                    "SWINGKM" => $SWINGKM,
                    "TAX" => $TtlTax,
                    "TOTALAMT" => $FINALCOST,
                    "ALLSTATUS" => $RS_STATUS,
                    "TTLKMS" => $TTLKM
                );
            } else {
                $emp_arr_val = array(
                    "OTPLOCATION" => $OPTLOCATION,
                    "SWINGKM" => $SWINGKM,
                    "TOTALAMT" => $FINALCOST,
                    "ALLSTATUS" => $RS_STATUS,
                    "TTLKMS" => $TTLKM
                );
            }

            if (isset($normal_array[$i]->ROUTE_ID) && $normal_array[$i]->ROUTE_ID != '') {

                $startsWith = strpos($normal_array[$i]->ROUTE_ID, "LD") === 0;
                if ($startsWith) {

                    if ($normal_array[$i]->TRIP_APPROVED_KM == '' || $normal_array[$i]->TRIP_APPROVED_KM == null) {
                        $normal_array[$i]->TRIP_APPROVED_KM = isset($normal_array[$i]->GOOGLE_KM) ? $normal_array[$i]->GOOGLE_KM : '';
                    }
                }

            }

            $results[$i] = array_replace((array)$normal_array[$i], (array)$emp_arr_val);
        }

    //    print_r($results);
    //    exit;



           return response([
                'success' => true,
                'status' => 3,
                'mis_report' =>  array("data" => $results, "total" => count($results)),

                  
            ]);
        } catch (\Throwable | \Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'MIS Report Pagination Unsuccessful' : 'MIS Report Pagination Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ], 500);
        }
    } */

}
