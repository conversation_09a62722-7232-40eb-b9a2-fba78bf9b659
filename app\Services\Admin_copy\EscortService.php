<?php

namespace App\Services\Admin;

use App\Helpers\CommonFunction;
use App\Helpers\MyHelper;
use Carbon\Carbon;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use App\Models\Escort;

class EscortService
{
    protected CommonFunction $commonFunction;
    protected ?Branch $currentBranch = null;

    public function __construct(CommonFunction $commonFunction)
    {
        $this->commonFunction = $commonFunction;
    }
	
	 public function indexEscort(): FoundationApplication|Response|ResponseFactory
    {
        $authUser = Auth::user();

        try 
		{
            $escort_data = Escort::select('escorts.ESCORT_ID',
                'escorts.ESCORT_NAME',
                'escorts.ESCORT_MOBILE',
                'escorts.ESCORT_BATCH_NUMBER',
                'escorts.ESCORT_PHOTO',
                'escorts.ESCORT_TYPE',
                'escorts.ACTIVE',
                'escorts.CREATED_DATE'
               
            )
              
			  //  ->join("branch", "branch.BRANCH_ID", "=", DB::Raw($authUser->BRANCH_ID))
               
                ->where('escorts.ACTIVE', MyHelper::$RS_ACTIVE)
                ->where("escorts.BRANCH_ID",DB::Raw($authUser->BRANCH_ID))
                ->get();

            return response([
                'success' => true,
                'status' => 3,
                'escorts' => $escort_data,
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Escort Index Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ]);
        }
    }

	

    public function storeEscort($request): FoundationApplication|Response|ResponseFactory
    {

        try {

            DB::beginTransaction();
            $auth_user = Auth::user();

            $escortData = $this->prepareEscortData($request, $auth_user, MyHelper::$RS_ACTIVE, $auth_user->id);
            $escortResult = Escort::create($escortData);



            return response([
                'success' => true,
                'status' => 3,
                'message' => 'Escort Created Successfully',

            ]);

        } catch (\Throwable|\Exception $e) 
		{
            DB::rollBack();
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'validation_controller' => true,
                'status' => 4,
                'message' => 'Escort Created UnSuccessfully',
                'errorM' => $e->getMessage(),
            ],500);
        } finally {
            DB::commit();
        }


    }
	
    /**
     * @throws ConnectionException
     */
    private function prepareEscortData($request, $auth_user, $active, $userId): array
    {
        $date = Carbon::now();

        return [
		
			'ESCORT_NAME' => $request->ESCORT_NAME,
            'ESCORT_MOBILE' => $request->ESCORT_MOBILE,
            'ESCORT_BATCH_NUMBER' => $request->ESCORT_BATCH_NUMBER,
            'ESCORT_PHOTO' => $request->ESCORT_PHOTO,
            "ACTIVE" => $active,
			"BRANCH_ID" => $auth_user->BRANCH_ID,
			"CREATED_BY" => $auth_user->id,
            "CREATED_DATE" => $date->format("Y-m-d H:i:s"),
		             
        ];
    }

    public function deleteEscort($request, $escortAutoIdCrypt): FoundationApplication|Response|ResponseFactory
    {
        $authUser = Auth::user();
         $date = Carbon::now();

        try {
            DB::beginTransaction();

            $escortId = Crypt::decryptString($escortAutoIdCrypt);

            $escort = Escort::where('ACTIVE', MyHelper::$RS_ACTIVE)
               // ->where('BRANCH_ID', $authUser->BRANCH_ID)
                ->findOrFail($escortId);

            $escort->update([
                'REMARKS' => $request->input('remark'),
                'ACTIVE' => MyHelper::$RS_INACTIVE,
                'UPDATED_BY' => Auth::user()->id,
                'updated_at' =>$date->format("Y-m-d H:i:s"),
            ]);

            DB::commit();

            return response([
                'success' => true,
                'status' => 3,
                'message' => 'Escort Deleted Successfully',
            ]);
        } catch (\Throwable|\Exception $e) {
            DB::rollBack();
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Escort Delete Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ]);
        }
    }



    public function editEscort($escortAutoIdCrypt): FoundationApplication|Response|ResponseFactory
    {
        $authUser = Auth::user();

        try {
            $escortAutoId = Crypt::decryptString($escortAutoIdCrypt);

            $escort = Escort::where('escorts.ACTIVE', MyHelper::$RS_ACTIVE)
                ->where("escorts.ESCORT_ID",$escortAutoId)
                ->where("escorts.BRANCH_ID",DB::Raw($authUser->BRANCH_ID))
                ->firstOrFail();

            return response([
                'success' => true,
                'status' => 3,
                'escort' => $escort,
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Escort Edit Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ]);
        }
    }

    public function updateEscort($request, $escortAutoIdCrypt): FoundationApplication|Response|ResponseFactory
    {
        $authUser = Auth::user();
        try {
            DB::beginTransaction();
            $escortAutoId = Crypt::decryptString($escortAutoIdCrypt);
            $escort = Escort::findOrFail($escortAutoId);
           
            $auth_user = Auth::user();

            $escortData = $this->prepareEscortDataForUpdate($request, $auth_user, $escort);
            $escort->update($escortData);

            DB::commit();


            return response([
                'success' => true,
                'status' => 3,
                'message' => 'Escort Updated Successfully',
            ]);

        } catch (\Throwable|\Exception $e) {
            DB::rollBack();
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'message' => 'Escort Update Unsuccessful',
                'status' => 4,
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * @throws ConnectionException
     */
    private function prepareEscortDataForUpdate($request, $auth_user, $escort): array
    {
        $date = Carbon::now();

        return [
            'ESCORT_NAME' => $request->ESCORT_NAME,
            'ESCORT_MOBILE' => $request->ESCORT_MOBILE,
            'BRANCH_ID' =>  $auth_user->BRANCH_ID,
            'ESCORT_BATCH_NUMBER' => $request->ESCORT_BATCH_NUMBER,
            'ESCORT_PHOTO' => $request->ESCORT_PHOTO,
            "UPDATED_BY" => $auth_user->id,
            "updated_at" => $date->format("Y-m-d H:i:s"),
        ];
    }


    public function dataForCreateEmployee(): FoundationApplication|Response|ResponseFactory
    {

        try {
            $user = Auth::user();
            $rsActive = MyHelper::$RS_ACTIVE;
            $branchId = $user->BRANCH_ID;

            $branch = DB::table('branch')
                ->select('BRANCH_ID', 'DIVISION_ID')
                ->where('ACTIVE', $rsActive)
                ->get();

           
            return response([
                'success' => true,
                'status' => 3,
                'branch' => $branch,
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Data For Create for branch  Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ]);
        }
    }
	
  

    public function paginationEscort($request, bool $active = true): FoundationApplication|Response|ResponseFactory
    {
        try {
           
            $perPage = $request->input('per_page', MyHelper::$PAGINATION_PER_PAGE);
            $branch_id = Auth::user()->BRANCH_ID;
            $activeStatus = $active ? MyHelper::$RS_ACTIVE : MyHelper::$RS_INACTIVE;
            $authUser = Auth::user();
          $escorts = Escort::select('escorts.ESCORT_ID',
                'escorts.ESCORT_NAME',
                'escorts.ESCORT_MOBILE',
                'escorts.ESCORT_BATCH_NUMBER',
                'escorts.ESCORT_PHOTO',
                'escorts.ESCORT_TYPE',
                'escorts.ACTIVE',
                'escorts.CREATED_DATE'
               
            )
              
			  //  ->join("branch", "branch.BRANCH_ID", "=", DB::Raw($authUser->BRANCH_ID))
               
                ->where('escorts.ACTIVE', MyHelper::$RS_ACTIVE)
                ->where("escorts.BRANCH_ID",DB::Raw($authUser->BRANCH_ID));
                

            $filterModel = $request->input('filterModel');
            if ($filterModel) {
                foreach ($filterModel as $field => $filter) {
                    if (isset($filter['filter']) && $filter['filter'] !== '') {
                        $value = $filter['filter'];
                        $type = $filter['type'];

                        switch ($field) {
                            case 'ESCORT_NAME':
                                $escorts->where('escorts.ESCORT_NAME', 'like', "%{$value}%");
                                break;
                            case 'ESCORT_MOBILE':
                                $escorts->where('escorts.ESCORT_MOBILE', 'like', "%{$value}%");
                                break;
                            
                        }
                    }
                }
            }


            if ($request->has('orderBy') && !empty($request->input('orderBy'))) 
            {
                $orderBy = $request->input('orderBy');
                $order = $request->input('order', 'asc');
                $escorts->orderBy($orderBy, $order);
            } else {
                $escorts->orderBy('escorts.CREATED_DATE', 'desc');
            }
// echo $driver->count();exit;
            if ($perPage === MyHelper::$PAGINATION_PER_PAGE_ALL) {
                $paginatedEscort = $escorts->paginate($escorts->count());
            } else {
                $perPage = is_numeric($perPage) ? (int)$perPage : MyHelper::$PAGINATION_PER_PAGE;
                $paginatedEscort = $escorts->paginate($perPage);
            }
           // print_r($paginatedDriver);exit;
            return response([
                'success' => true,
                'status' => 3,
                'datass' => $paginatedEscort,
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'Escort Pagination Unsuccessful' : 'Deactivate Escort Pagination Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ]);
        }
    } 

}
