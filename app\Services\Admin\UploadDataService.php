<?php
namespace App\Services\Admin;

use App\Helpers\CommonFunction;
use App\Helpers\MyHelper;
use App\Models\Branch;
use App\Models\Employee;
use App\Models\Employee_input_data;
use App\Models\Location;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;


class UploadDataService
{
    protected CommonFunction $commonFunction;
    //protected  RosterUploadService $rosteruploadservice;
    protected ?Branch $currentBranch = null;

    public function __construct(CommonFunction $commonFunction, RosterUploadService $rosteruploadservice)
    {
        $this->commonFunction = $commonFunction;
        //$this->rosteruploadservice = $rosteruploadservice;
    }

    public function GetEmpLocationUpdate(): FoundationApplication|Response|ResponseFactory
    {
        try {
            $dbname = Auth::user()->dbname;
            $branch_id = Auth::user()->BRANCH_ID;
            $DIVISION_ID = $this->commonFunction->getDivisionId($branch_id);
            
            $input_list = Employee_input_data::on("$dbname")->where([['BRANCH_ID', '=', $branch_id], ['STATUS', '=', 0],])->get();
            $location_list_Arr = array();
            $location_rs_list = Location::on("$dbname")->where([['ACTIVE', '=', 1], ['DIVISION_ID', '=', $DIVISION_ID]])->get();
            foreach ($location_rs_list as $location_list) {
                $location_list_Arr[$location_list->LOCATION_NAME] = $location_list->LOCATION_NAME;
            }

            return response([
                'success' => true,
                'status' => 1,
                'input_datas' => $input_list,
                'location_lists' => $location_list_Arr,
                'message' => 'Employee Location Update Fetch Successfully!',
            ], 200);
        } catch (\Throwable|\Exception $e) {
            DB::rollBack();
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'message' => 'Employee Location Update Fetch Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function PostEmpLocationUpdate($request): FoundationApplication|Response|ResponseFactory
    {
        try {
            
            $dbname = Auth::user()->dbname;
            $branch_id = Auth::user()->BRANCH_ID; //get session branch id 
            // if ($request->new_location == '') {
            //     Session::flash('error', 'location required');
            // } else {
            //     Employee_input_data::on("$dbname")->where('INPUT_ID', '=', $request->input_id)
            //             ->update(array("LOCATION" => $request->new_location, 'STATUS' => 1));
            //     Session::flash('error', '');
            // }
            Employee_input_data::on("$dbname")->where('INPUT_ID', '=', $request->input_id)
                        ->update(array("LOCATION" => $request->new_location, 'STATUS' => 1));

            //$input_list = Employee_input_data::on("$dbname")->where([['BRANCH_ID', '=', $branch_id], ['STATUS', '=', 0],])->get();
            //$location_list_Arr = array();
            //$location_rs_list = location::on("$dbname")->where([['ACTIVE', '=', 1],])->get();
            //foreach ($location_rs_list as $location_list) {
            //    $location_list_Arr[$location_list->LOCATION_NAME] = $location_list->LOCATION_NAME;
            //}
            //if (count($input_list) < 1) {
            //    $this->employee_input_datas();
            //}

            return response([
                'success' => true,
                'status' => 1,
                //'input_datas' => $input_list,
                //'location_lists' => $location_list_Arr,
                'message' => 'Employee Location Update Successfully!',
            ], 200);
        } catch (\Throwable|\Exception $e) {
            DB::rollBack();
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'message' => 'Employee Location Update Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function employee_input_datas() {
        ini_set('max_execution_time', 0);
        $dbname = Auth::user()->dbname;
        //uploadrunemp
        $BRANCH_ID = Auth::user()->BRANCH_ID;
        $user_id = Auth::user()->id;

        $property_roster_request = DB::table("properties")->select("PROPERTIE_NAME", "PROPERTIE_VALUE")->where("BRANCH_ID", "=", $BRANCH_ID)->where("PROPERTIE_NAME", "=", "EMP REQUEST LOGIN")->get();
        $roster_request_value = $property_roster_request[0]->PROPERTIE_VALUE;

        $branch_lat = DB::connection("$dbname")->table("branch")->select("BRANCH_ID", "LAT", "LONG", "DIVISION_ID")->where("BRANCH_ID", "=", $BRANCH_ID)->get();
        $cmp_lat = $branch_lat[0]->LAT;
        $cmp_long = $branch_lat[0]->LONG;
        $division_id = $branch_lat[0]->DIVISION_ID;
        $RS_ACTIVE = env('RS_ACTIVE');
        
        $sql = "SELECT BRANCH_ID,LOCATION,EMPLOYEE_ID,EMPLOYEE_NAME,GENDER,EMPLOYEE_MOBILE,PROJECT_NAME,ADDRESS,
			LATITUDE,LONGITUDE,MAIL_ID,EMERGENCY_CONTACT_NO,STATUS,INPUT_ID,RFID_CARD FROM `employee_input_datas` where STATUS='" . $RS_ACTIVE . "' and BRANCH_ID = '" . $BRANCH_ID . "' ";
        $datas = DB::connection("$dbname")->select($sql);
        $location_select = "select LOCATION_ID,LOCATION_NAME,LATITUDE,LONGITUDE from locations where ACTIVE=1 and DIVISION_ID='" . $division_id . "'";
        $location_id_res = DB::connection("$dbname")->select($location_select);
        $checklocarray = array();

        foreach ($location_id_res as $val) {
            $checklocarray[$val->LOCATION_ID] = $val->LOCATION_NAME;
        }

        if (count($datas) > 0) {
            $arr = array();
            $arr2 = array();
            $user_arr = array();
            $input_id_list = array();
            $i = 0;
            $location_id = 0;
            $distance = 0;
            foreach ($datas as $val) {
                $EMPLOYEE_NAME = trim($val->EMPLOYEE_NAME);
                $GENDER = trim($val->GENDER);
                $EMPLOYEE_ID = trim($val->EMPLOYEE_ID);
                $LOCATION = trim($val->LOCATION);
                $BRANCH_ID = $val->BRANCH_ID;
                $EMPLOYEE_MOBILE = trim($val->EMPLOYEE_MOBILE);
                $PROJECT_NAME = trim($val->PROJECT_NAME);
                $ADDRESS = trim($val->ADDRESS);
                $EMP_LATITUDE = trim($val->LATITUDE);
                $EMP_LONGITUDE = trim($val->LONGITUDE);
                $MAIL_ID = trim($val->MAIL_ID);
                $EMERGENCY_CONTACT_NO = trim($val->EMERGENCY_CONTACT_NO);
                $STATUS = $val->STATUS;
                $input_id = $val->INPUT_ID;
                $RFID_CARD = $val->RFID_CARD;

                $location_id = array_search($LOCATION, $checklocarray);
                $emp_name = $this->commonFunction->AES_ENCRYPT($EMPLOYEE_NAME, env('AES_ENCRYPT_KEY'));
                $mobile_no = $this->commonFunction->AES_ENCRYPT($EMPLOYEE_MOBILE, env('AES_ENCRYPT_KEY'));
                $email_id = $this->commonFunction->AES_ENCRYPT($MAIL_ID, env('AES_ENCRYPT_KEY'));
                $password = $this->commonFunction->AES_ENCRYPT($EMPLOYEE_MOBILE, env('AES_ENCRYPT_KEY'));

                $arr[] = array("EMPLOYEES_ID" => $EMPLOYEE_ID,
                    "BRANCH_ID" => $BRANCH_ID,
                    "NAME" => $emp_name,
                    'PASSWORD' => $password,
                    "MOBILE" => $mobile_no,
                    "GENDER" => $GENDER,
                    "PROJECT_NAME" => $PROJECT_NAME,
                    "LOCATION_ID" => $location_id,
                    "ADDRESS" => $ADDRESS,
                    "ACTIVE" => 1,
                    "LATITUDE" => $EMP_LATITUDE,
                    "LONGITUDE" => $EMP_LONGITUDE,
                    "DISTANCE" => $distance,
                    "EMAIL" => $email_id,
                    "RFID_CARD" => $RFID_CARD,
                    "CREATED_BY" => $user_id,
                    "CREATED_DATE" => date("Y-m-d H:i:s"),
                    "updated_at" => date("Y-m-d H:i:s"),
                    "CATEGORY" => 'Emp');
                if ($roster_request_value == 'Y') {
                    $user_arr[] = array("BRANCH_ID" => $BRANCH_ID, "name" => $EMPLOYEE_MOBILE, "user_type" => "EMPLOYEE", "vendor_id" => '0', "active_status" => 1, "CREATED_BY" => $user_id, "created_at" => date("Y-m-d H:i:s"), "updated_at" => date("Y-m-d H:i:s"), "login_category" => "ZINGO");
                }
                
                $input_id_list[$i] = $input_id;
                $i++;
            }
            /*  echo "<pre>";
              print_r($arr);exit; */
            $res = Employee::on("$dbname")->insert($arr);
            $user_res = User::insert($user_arr);
        
            if ($res == true) {
                foreach ($input_id_list as $input_id) {
                    Employee_input_data::on("$dbname")->where('INPUT_ID', '=', $input_id)
                            ->update(array('STATUS' => 2));
                }
            }
        }
    }
}