<?php

namespace App\Models;

use App\Helpers\CommonFunction;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AlertSignalFailure extends Model
{
    use HasFactory;

    protected $table = 'alertsignalfailure';
    protected $primaryKey = 'ALERT_ID';

    public $timestamps = false;

    protected $fillable = [

                'ROSTER_ID',
                'BRANCH_ID',
                'VENDOR_ID',
                'CAB_NO',
                'CAB_ID',
                'POSITION',
                'SPEED',
                'GPS_DATE',
                'LOCATION',
                'GPS_STATUS',
                'BATTERY_STATUS',
                'LOGIN_STATUS',
                'ROUTE_STATUS',
                'PROCESS_DATE',
                'created_at',
                'REMARKS',
                'UPDATE_BY',
                'updated_at',
    ];
	

}
