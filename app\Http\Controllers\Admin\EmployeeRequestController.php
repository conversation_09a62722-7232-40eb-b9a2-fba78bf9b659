<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;

use App\Services\Admin\EmployeeRequestService;
use App\Http\Requests\Admin\GetDateWebRequest;
use App\Http\Requests\Admin\RemoveWebEmpRequestRequest;



use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class EmployeeRequestController extends Controller
{
    protected EmployeeRequestService $EmployeeRequestService;

    public function __construct(EmployeeRequestService $EmployeeRequestService)
    {
        $this->EmployeeRequestService = $EmployeeRequestService;
    }
    
    public function dataForCreate(): FoundationApplication|Response|ResponseFactory
    {
        return $this->EmployeeRequestService->dataForCreate();
    }
    public function getdate_webemprequest(GetDateWebRequest $request): FoundationApplication|Response|ResponseFactory
    {

        return $this->EmployeeRequestService->getdate_webemprequest($request);
    }
    
    public function getfilter_trip_history(TripHistoryRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->triphistoryService->getfilter_trip_history($request);
    }
   
    public function weekRoster(Request $request): Application|Response|ResponseFactory
    {
        return $this->EmployeeRequestService->weekRoster($request);
    }

    public function RosterEmpWeekReq(Request $request): Application|Response|ResponseFactory
    {
        return $this->EmployeeRequestService->RosterEmpWeekReq($request->emp_id,$request->pickdata,$request->dropdata);
    }

    public function PostUpdatePassword(Request $request): Application|Response|ResponseFactory
    {
        return $this->EmployeeRequestService->postUpdatePassword($request);
    }

    public function RemoveWebEmpRequest(RemoveWebEmpRequestRequest $request): Application|Response|ResponseFactory
    {
        return $this->EmployeeRequestService->RemoveWebEmpRequest($request);
    }

    public function WeekrosterAdmin(): Application|Response|ResponseFactory
    {
        return $this->EmployeeRequestService->WeekrosterAdmin();
    }

    public function WeekRosterAdminData(Request $request): Application|Response|ResponseFactory
    {
        return $this->EmployeeRequestService->WeekRosterAdminData($request);
    }

}
