<?php


namespace App\Http\Requests\Admin;
use Closure;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;

class TollRequest extends FormRequest
{
    
    
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {        
        return [
            'TICKET_NO' => 'required',
            'TOLL_CHARGES' => 'required',
        ];
    }

    public function messages(): array
    {
        return [
            'TICKET_NO.required' => 'ticket no is required',
            'TOLL_CHARGES.required' => 'toll charges is required',
        ];
    }
   

    public function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json([
            'success' => false,
            'validation_error' => true,
            'message' => 'Validation errors',
            'data' => $validator->errors()
        ]));
    }
   
}
