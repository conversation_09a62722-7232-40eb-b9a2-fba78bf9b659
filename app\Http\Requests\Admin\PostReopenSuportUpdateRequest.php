<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Contracts\Validation\Validator;

class PostReopenSuportUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'reopen_support_id' => 'required',
            'reopen_emp_id' => 'required',
            'reopen_remarks' => 'required',
        ];
    }

    public function messages(): array
    {
        return [
           'reopen_support_id.required' => 'Reopen Support ID is required.',
           'reopen_emp_id.required' => 'Reopen Employee ID is required.',
           'reopen_remarks.required' => 'Reopen Remarks is required.',
        ];
    }

    public function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json([
            'success' => false,
            'validation_error' => true,
           'message' => 'Validation errors',
            'data' => $validator->errors(),
        ]));
    }
}
