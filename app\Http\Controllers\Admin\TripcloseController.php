<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;

use App\Services\Admin\TripcloseService;
use App\Http\Requests\Admin\TripcloseRequest;
use App\Http\Requests\Admin\TripstatusRequest;
use App\Http\Requests\Admin\Tripclose_date_wise_shiftlogin_Request;
use App\Http\Requests\Admin\TotalPassengerListRequest;
use App\Http\Requests\Admin\TripcloseBoardedNoshowRequest;
use App\Http\Requests\Admin\ManualTripcloseRequest;
use App\Http\Requests\Admin\TripcloseCabAllotRequest;
use App\Http\Requests\Admin\TripcloseCabAllotVehicleListRequest;
use App\Http\Requests\Admin\BillableTripApproveRequest;

use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class TripcloseController extends Controller
{
    protected TripcloseService $tripcloseService;

    public function __construct(TripcloseService $tripcloseService)
    {
        $this->tripcloseService = $tripcloseService;
    }
    public function index(): ResponseFactory|Application|Response
    {
        return $this->tripcloseService->indexTripclose();
    }
    public function activeTripclosePagination(Request $request): ResponseFactory|Application|Response
    {
        return $this->tripcloseService->paginationTripclose($request);
    }
    public function dataForCreate(): FoundationApplication|Response|ResponseFactory
    {
        return $this->tripcloseService->dataForCreateTripclose();
    }
    public function get_tripclose_details(TripcloseRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->tripcloseService->get_tripclose_details($request);
    }
    public function trip_status_update(TripstatusRequest $request): FoundationApplication|Response|ResponseFactory
    {
       
        return $this->tripcloseService->trip_status_update($request);
    }
    public function tripclose_date_wise_shiftlogin(Tripclose_date_wise_shiftlogin_Request $request): FoundationApplication|Response|ResponseFactory
    {
       
        return $this->tripcloseService->tripclose_date_wise_shiftlogin($request);
    }
    public function tripclose_boarded_noshow(TripcloseBoardedNoshowRequest $request): FoundationApplication|Response|ResponseFactory
    {
       
        return $this->tripcloseService->tripclose_boarded_noshow($request);
    }
    public function total_passenger_list(TotalPassengerListRequest $request): FoundationApplication|Response|ResponseFactory
    {
       
        return $this->tripcloseService->total_passenger_list($request);
    }
    public function manualtripclose(ManualTripcloseRequest $request): FoundationApplication|Response|ResponseFactory
    {
       
        return $this->tripcloseService->manualtripclose($request);
    }
    public function tripclose_cab_allot(TripcloseCabAllotRequest $request): FoundationApplication|Response|ResponseFactory
    {
       
        return $this->tripcloseService->tripclose_cab_allot($request);
    }
    public function tripclose_cab_allot_vehicle_list(TripcloseCabAllotVehicleListRequest $request): FoundationApplication|Response|ResponseFactory
    {
       
        return $this->tripcloseService->tripclose_cab_allot_vehicle_list($request);
    }

    public function billable_trip_Approve(BillableTripApproveRequest $request): FoundationApplication|Response|ResponseFactory
    {
       
        return $this->tripcloseService->billable_trip_Approve($request);
    }



 
}
