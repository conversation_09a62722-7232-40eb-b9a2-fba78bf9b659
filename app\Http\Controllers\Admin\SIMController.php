<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\SimRequest;
use App\Http\Requests\Admin\SimDeleteRequest;
use App\Http\Requests\Admin\SimUpdateRequest;
use App\Services\Admin\SimService;


use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class SIMController extends Controller
{
    protected SIMService $simService;

    public function __construct(SimService $simService)
    {
        $this->simService = $simService;
    }

    public function index(): ResponseFactory|Application|Response
    {
        return $this->simService->indexSim();
    }

   public function store(SimRequest $request): ResponseFactory|Application|Response
    {
        return $this->simService->storeSim($request);
    }
    public function delete(SimDeleteRequest $request, $simAutoIdCrypt): FoundationApplication|Response|ResponseFactory
    {
        return $this->simService->deleteSim($request, $simAutoIdCrypt);
    }
	
	 public function edit($simAutoIdCrypt): Application|Response|ResponseFactory
    {
        return $this->simService->editSim($simAutoIdCrypt);
    }

    public function update(SimUpdateRequest $request, $simAutoIdCrypt): Application|Response|ResponseFactory
    {
        return $this->simService->updateSim($request, $simAutoIdCrypt);
    }


    public function activeSimPagination(Request $request): ResponseFactory|Application|Response
    {
        return $this->simService->paginationSim($request);
    }

  /*  public function edit($employeeAutoIdCrypt): Application|Response|ResponseFactory
    {
        return $this->employeeService->editEmployee($employeeAutoIdCrypt);
    }

    public function update(EmployeeUpdateRequest $request, $employeeAutoIdCrypt): Application|Response|ResponseFactory
    {
        return $this->employeeService->updateEmployee($request, $employeeAutoIdCrypt);
    }


    public function delete(EmployeeDeleteRequest $request, $employeeAutoIdCrypt): FoundationApplication|Response|ResponseFactory
    {
        return $this->employeeService->deleteEmployee($request, $employeeAutoIdCrypt);
    }

    public function deActiveEmployeePagination(Request $request): ResponseFactory|Application|Response
    {
        return $this->employeeService->paginationEmployee($request, false);
    }

    public function activate(ActivateEmployeeRequest $request): ResponseFactory|Application|Response
    {
        return $this->employeeService->activateEmployee($request);
    }

    public function dataForCreate(): FoundationApplication|Response|ResponseFactory
    {
        return $this->employeeService->dataForCreateEmployee();
    } */

}
