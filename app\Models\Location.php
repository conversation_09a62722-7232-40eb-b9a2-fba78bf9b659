<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Crypt;

class Location extends Model
{
    use HasFactory;

    protected $table = 'locations';
    protected $primaryKey = 'LOCATION_ID';
    protected $appends = ['LOCATION_ID_crypt'];
    protected $fillable = [
        'DIVISION_ID',
        'LOCATION_NAME',
        'LATITUDE',
        'LONGITUDE',
        'ACTIVE',
        'CREATE_BY',
        'CREATED_DATE',
        'UPDATED_BY',
        'UPDATETED_DATE'
        
    ];

	public function getLOCATIONIDCryptAttribute(): string
    {
        return Crypt::encryptString($this->LOCATION_ID);
    }
}
