<?php

namespace App\Models;

use App\Helpers\CommonFunction;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Roster extends Model
{
    use HasFactory;

    protected $table = 'rosters';
    protected $primaryKey = 'ROSTER_ID';

    public $timestamps = false;
	
	 protected $appends = ['name_decrypted','tripstatus','passenger_status'];

    protected $fillable = [
        'ROSTER_ID',
        'BRANCH_ID',
        'ROUTE_ID',
        'FILE_ID',
        'ESTIMATE_START_TIME',
        'ACTUAL_START_TIME',
        'ESTIMATE_END_TIME',
        'ACTUAL_END_TIME',
        'TRIP_TYPE',
        'START_LOCATION',
        'END_LOCATION',
        'VENDOR_ID',
        'CAB_ID',
        'ROSTER_ALLOT_TIME',
        'CAB_ALLOT_TIME',
        'PASSENGER_ALLOT_COUNT',
        'PASSENGER_ALLOT_IN_ROUT_COUNT',
        'PASSENGER_DEACTIVE_COUNT',
        'PASSENGER_NOSHOW_COUNT',
        'PASSENGER_CLUBING_COUNT',
        'CAB_CAPACITY_COUNT',
        'TOTAL_KM',
        'TRIP_APPROVED_KM',
        'ROSTER_STATUS',
        'ACTIVE',
        'CREATED_BY'
    ];
	
	 protected CommonFunction $commonFunction;

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
        $this->commonFunction = new CommonFunction();
    }
	public function getNameDecryptedAttribute(): ?string
    {
       // return $this->empname ? $this->commonFunction->AES_DECRYPT($this->empname, config('app.aes_encrypt_key')) : null;
	   return null;
    }

 public function getPassengerStatusAttribute(): ?string
{
       return $this->ROSTER_PASSENGER_STATUS ? $this->commonFunction->employee_status($this->ROSTER_PASSENGER_STATUS) : null;
     // return null;
   }

	public function getTripStatusAttribute(): ?string
    {
        return $this->ROSTER_STATUS ? $this->commonFunction->TripStatus($this->ROSTER_STATUS) : null;
	  // return null;
    }

}
