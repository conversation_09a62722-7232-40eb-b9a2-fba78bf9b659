<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\PostSuportUpdateRequest;
use App\Http\Requests\Admin\SupportRemarksRequest;
use App\Http\Requests\Admin\SupportApprovalRequest;
use App\Http\Requests\Admin\PostReopenSuportUpdateRequest;
use App\Services\Admin\TransportDataService;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class TransportDataController extends Controller
{
    protected TransportDataService $transportDataService;

    public function __construct(TransportDataService $transportDataService)
    {
        $this->transportDataService = $transportDataService;
    }

    public function Support(): Application|Response|ResponseFactory
    {
        return $this->transportDataService->Support();
    }

    public function PostSuportUpdate(PostSuportUpdateRequest $request): Application|Response|ResponseFactory
    {
        return $this->transportDataService->PostSuportUpdate($request);
    }

    public function PostReopenSuportUpdate(PostReopenSuportUpdateRequest $request): ResponseFactory|Application|Response
    {
        return $this->transportDataService->PostReopenSuportUpdate($request);
    }

    public function SupportRemarks(SupportRemarksRequest $request): ResponseFactory|Application|Response
    {
        return $this->transportDataService->SupportRemarks($request);
    }

    public function SupportApproval(SupportApprovalRequest $request): ResponseFactory|Application|Response
    {
        return $this->transportDataService->SupportApproval($request);
    }
}
