<?php

namespace App\Http\Controllers\SampleCode;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class SampleCodeController extends Controller
{

    public function upload(Request $request): \Illuminate\Http\JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'csv_file' => 'required|file|mimes:csv,txt',
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()], 400);
        }

        $file = $request->file('csv_file');

        if (($handle = fopen($file, "r")) !== FALSE) {
            $header = fgetcsv($handle);

            $expectedHeaders = [
                'RouteId',
                'TripType',
                'LoginDate',
                'PickupTime',
                'EmpId',
                'VendorName',
                'TariffType'
            ];


            if ($header !== $expectedHeaders) {
                fclose($handle);
                return response()->json([
                    'error' => 'Invalid CSV format. Headers do not match expected format or order.',
                    'expected' => $expectedHeaders,
                    'received' => $header
                ], 400);
            }

            $data = [];
            while (($row = fgetcsv($handle)) !== FALSE) {
                $data[] = array_combine($expectedHeaders, $row);
            }

            fclose($handle);


            return response()->json(['message' => 'CSV file processed successfully', 'data' => $data], 200);
        }

        return response()->json(['error' => 'Unable to open file'], 500);
    }
}
