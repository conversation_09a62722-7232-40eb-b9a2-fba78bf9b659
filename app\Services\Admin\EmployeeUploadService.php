<?php

namespace App\Services\Admin;

use App\Helpers\CommonFunction;
use App\Helpers\MyHelper;
use App\Models\Branch;
use App\Models\property;
use App\Models\Roster;
use App\Models\RosterPassenger;
use App\Models\email_notification;
use App\Models\Cab_allocation;
use App\Models\OtpVerify;
use App\Models\RouteEscorts;
use App\Models\Reason_Log;
use App\Models\Cab;
use App\Models\Sms;
use App\Models\Location;
use App\Models\CabAttendance;
use App\Models\Vehicle;
use App\Models\VehicleModel;
use App\Models\Driver;
use App\Models\Employee;
use App\Models\AlertSignalFailure;
use App\Models\File_upload;
use App\Models\Input_data;
use App\Models\Employee_input_data;
use App\Models\Vendor;
use App\Models\User;
use Validator;
use Redirect;

use Carbon\Carbon;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Crypt;

use Illuminate\Http\Request;

use App\Http\Controllers\ElasticController;


class EmployeeUploadService
{
    protected CommonFunction $commonFunction;
    protected ?Branch $currentBranch = null;
     

    public function __construct(CommonFunction $commonFunction)
    {
        $this->commonFunction = $commonFunction;
       
    }

    private function getPropertyValue()
    {
        $branch_id= Auth::user()->BRANCH_ID;
        //    exit;
        $property = property::where('BRANCH_ID',  $branch_id)
            ->where('ACTIVE', MyHelper::$RS_ACTIVE)
            //->where('PROPERTIE_NAME', $propertyName)
            ->get();
			
        return $property;
    }

    public function post_employee_upload($request): FoundationApplication|Response|ResponseFactory
    {   
                
        try {
            $dbname = Auth::user()->dbname;
            $branch_id = Auth::user()->BRANCH_ID;
            // getting all of the post data
            $request->file('file');
            $file = array('file' => $request->file('file'));
            //echo "test";exit;
            // setting up rules
            $rules = array('file' => 'required|max:10000',); //mimes:csv,bmp,png and for max size max:10000
            // doing the validation, passing post data, rules and the messages
            $validator = Validator::make($file, $rules);

            if ($validator->fails()) {
                // send back to the page with the input data and errors
                return response([
                    'success' => true,
                    'status' => 3,
                    'message' => "Invalid input",
                ]);
               
              //  return Redirect::to('upload')->withInput()->withErrors($validator);
            } else {
                // checking file is valid.                   
                if ($request->file('file')->isValid()) {

                    // $destinationPath = 'public/uploads'; // upload path

                    $extension = $request->file('file')->getClientOriginalExtension(); // getting image extension                       
                    $filename = $request->file('file')->getClientOriginalName();
                    $size = $request->file('file')->getSize();

                    // Input::file('file')->move($destinationPath, $filename); // uploading file to given path 

                   
                    
                    if ($filename == MyHelper::$EMP_UPLOAD_NAME) {
                       
                        $user_id = Auth::user()->id;   //get session user name
                        $file_upload = new File_upload;
                        //$file_upload=$file_upload1->setConnection("$dbname");
                        $file_upload->FILE_NAME = $filename;
                        $file_upload->FILE_SIZE = $size;
                        $file_upload->FILE_TYPE = $extension;
                        $file_upload->CREATED_BY = $user_id;
                        $file_upload->UPDATED_BY = $user_id;
                        $file_upload->BRANCH_ID = $branch_id;
                        $file_upload->STATUS = 0;
                        $file_upload->save();
                        $FILE_ID = $file_upload->FILE_ID;
                        $customerArr = $this->importempCsv($request->file('file')->getRealPath(), $FILE_ID);
                       // $customerArr = $this->importempCsv(Input::file('file')->getRealPath(), $FILE_ID);
                        if ($customerArr) {
                            return $customerArr;
                          //  return $this->emp_locationupdate();
                        } else {
                            $this->employee_input_datas();
                            return Redirect::to('upload');
                        }
                    }
                    else
                    {
                        return response([
                           'success' => false,
                           'status' => 3,
                           'message' => "Invalid file name",
                        ]);
                    }
                }
            }
           
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'No data',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function importempCsv($file, $FILE_ID) {
        return $this->csvToempArray($FILE_ID, $file);
    }

    function csvToempArray($FILE_ID, $filename = '', $delimiter = ',') {

        try {
            $dbname = Auth::user()->dbname;
            if (!file_exists($filename) || !is_readable($filename))
                return false;

            $header = null;
            $j = 0;
            $isCorrectLocation = false;
            $input_Arr = array();
            $location_list_Arr = array();
            $employees_list_Arr = array();
            $is_input_data_Arr = array();

            $branch_id = Auth::user()->BRANCH_ID; //get session branch id 

            $propertie = property::on("$dbname")->where([['BRANCH_ID', '=', $branch_id], ['ACTIVE', '=', 1]])->get();
            $PR_SMSTAG = MyHelper::$PR_SMSTAG;
            for ($ii = 0; $ii < count($propertie); $ii++) {

                $PROPERTIE_NAME = $propertie[$ii]->PROPERTIE_NAME;
                $PROPERTIE_VALUE = $propertie[$ii]->PROPERTIE_VALUE;

                switch ($PROPERTIE_NAME) {
                    case $PR_SMSTAG:
                        $SMSTAG = $PROPERTIE_VALUE;
                        break;
                    case 'EMP REQUEST LOGIN':
                        $roster_request_value = $PROPERTIE_VALUE;
                        break;
                    case 'LOGIN_OTP_EMAIL':
                        $LOGIN_OTP_EMAIL = $PROPERTIE_VALUE;
                        break;
                    case 'EMPLOYEE_REGISTRATION_SMS':
                        $EMPLOYEE_REGISTRATION_SMS = $PROPERTIE_VALUE;
                        break;
                    default:
                        break;
                }
            }


            $location_rs_list = Location::on("$dbname")->where([['ACTIVE', '=', 1],])->get();
            foreach ($location_rs_list as $location_list) {
                $location_list_Arr[] = $location_list->LOCATION_NAME;
            }

            $employees_rs_list = Employee::on("$dbname")->where([['ACTIVE', '=', 1], ['BRANCH_ID', '=', $branch_id],])->get();
            foreach ($employees_rs_list as $employees) {
                $employees_list_Arr[] = $employees->EMPLOYEES_ID;
            }

            $employee_input_datas_rs_list = Employee_input_data::on("$dbname")->where([['BRANCH_ID', '=', $branch_id],])
                    ->get();
            foreach ($employee_input_datas_rs_list as $input_datas) {
                $is_input_data_Arr[] = $input_datas->EMPLOYEE_ID . '|' . $input_datas->EMPLOYEE_NAME . '|' . $input_datas->EMPLOYEE_MOBILE;
            }

            if (($handle = fopen($filename, 'r')) !== false) {
                while (($row = fgetcsv($handle, 1000, $delimiter)) !== false) {
                    if (!$header) {
                        $header = $row;
                        $err_msg = '';
                        for ($i = 0; $i < count($row); $i++) {
                            if ($i == 0) {
                                if ($row[$i] != 'EmpId') {
                                    $err_msg = "Don't Change or Replace the " . $row[$i] . ", It Should be in Cell No " . $i . "th Position";
                                   
                                }
                            } else if ($i == 1) {
                                if ($row[$i] != 'EmpName') {
                                    $err_msg = "Don't Change or Replace the " . $row[$i] . ", It Should be in Cell No " . $i . "th Position";
                                   
                                }
                            } else if ($i == 2) {
                                if ($row[$i] != 'Gender') {
                                    $err_msg = "Don't Change or Replace the " . $row[$i] . ", It Should be in Cell No " . $i . "th Position";
                                    
                                }
                            } else if ($i == 3) {
                                if ($row[$i] != 'EmpMobile') {
                                    $err_msg = "Don't Change or Replace the " . $row[$i] . ", It Should be in Cell No " . $i . "th Position";
                                   
                                }
                            } else if ($i == 4) {
                                if ($row[$i] != 'Location') {
                                    $err_msg = "Don't Change or Replace the " . $row[$i] . ", It Should be in Cell No " . $i . "th Position";
                                    
                                }
                            } else if ($i == 5) {
                                if ($row[$i] != 'ProjectName') {
                                    $err_msg = "Don't Change or Replace the " . $row[$i] . ", It Should be in Cell No " . $i . "th Position";
                                   
                                }
                            } else if ($i == 6) {
                                if ($row[$i] != 'Address') {
                                    $err_msg = "Don't Change or Replace the " . $row[$i] . ", It Should be in Cell No " . $i . "th Position";
                                   
                                }
                            } else if ($i == 7) {
                                if ($row[$i] != 'Latitude') {
                                    $err_msg = "Don't Change or Replace the " . $row[$i] . ", It Should be in Cell No " . $i . "th Position";
                                   
                                }
                            } else if ($i == 8) {
                                if ($row[$i] != 'Longitude') {
                                    $err_msg = "Don't Change or Replace the " . $row[$i] . ", It Should be in Cell No " . $i . "th Position";
                                    
                                }
                            } else if ($i == 9) {
                                if ($row[$i] != 'Email') {
                                    $err_msg = "Don't Change or Replace the " . $row[$i] . ", It Should be in Cell No " . $i . "th Position";
                                   
                                }
                            } else if ($i == 10) {
                                if ($row[$i] != 'Emergency Contant') {
                                    $err_msg = "Don't Change or Replace the " . $row[$i] . ", It Should be in Cell No " . $i . "th Position";
                                   
                                }
                            } else if ($i == 11) {
                                if ($row[$i] != 'Rfid Card') {
                                    $err_msg = "Don't Change or Replace the " . $row[$i] . ", It Should be in Cell No " . $i . "th Position";
                                 
                                }
                            }
                            if ($err_msg != '') {
                                return response([
                                    'success' => false,
                                    'status' => 3,
                                    'message' => $err_msg,
                                ]);
                               
                            }
                        }
                    } else {
                        $rfid_card = '';
                        for ($i = 0; $i < count($row); $i++) {
                            if ($i == 0) {
                                $emp_id = stripslashes(trim($row[$i]));
                            } else if ($i == 1) {
                                $emp_name = stripslashes(trim($row[$i]));
                            } else if ($i == 2) {
                                $gender = stripslashes(trim($row[$i]));
                            } else if ($i == 3) {
                                $emp_mobile = stripslashes(trim($row[$i]));
                            } else if ($i == 4) {
                                $location = strtoupper(stripslashes(trim($row[$i])));
                            } else if ($i == 5) {
                                $project_name = stripslashes(trim($row[$i]));
                            } else if ($i == 6) {
                                $address = stripslashes(trim($row[$i]));
                            } else if ($i == 7) {
                                $latitude = stripslashes(trim($row[$i]));
                            } else if ($i == 8) {
                                $longitude = stripslashes(trim($row[$i]));
                            } else if ($i == 9) {
                                $email = stripslashes(trim($row[$i]));
                            } else if ($i == 10) {
                                $emergency_contant = stripslashes(trim($row[$i]));
                            } else if ($i == 11) {
                                $rfid_card = stripslashes(trim($row[$i]));
                            }
                        }


                        $is_already_route = $emp_id . '|' . $emp_name . '|' . $emp_mobile;

                        $date = Carbon::now();
                        $current_datetime = $date->format("Y-m-d H:i:s");
                        $user_id = Auth::user()->id;   //get session user name
                        $branch_id = Auth::user()->BRANCH_ID; //get session branch id 

                        /*
                         * below inarray check uploaded file location valid or invalid
                         */
                        if(in_array($location, $location_list_Arr, true)) {
                            $status = 1;
                        } else {
                            $status = 0;
                            $isCorrectLocation = true;
                        }

                        /*
                         * below inarray check uploaded file employees valid or invalid
                         */

                        if (in_array($emp_id, $employees_list_Arr, true)) {
                            $err_msg = '(' . $emp_id . '-' . $emp_name . ')' . ' Already Uploaded employee user , please upload New employee user ';
                            return response([
                                'success' => false,
                                'status' => 3,
                                'message' => $err_msg,
                            ]);
                        }
                        if (in_array($is_already_route, $is_input_data_Arr, true)) {
                            $err_msg = "Already Uploaded Data " . $emp_id . '/' . $emp_name . '/' . $emp_mobile;
                            return response([
                                'success' => false,
                                'status' => 3,
                                'message' => $err_msg,
                            ]);

                        } else {
                            $input_Arr[$j] = array("FILE_ID" => $FILE_ID,
                                "BRANCH_ID" => $branch_id,
                                "LOCATION" => $location,
                                'EMPLOYEE_ID' => $emp_id,
                                "EMPLOYEE_NAME" => $emp_name,
                                "GENDER" => $gender,
                                "EMPLOYEE_MOBILE" => $emp_mobile,
                                "PROJECT_NAME" => $project_name,
                                "ADDRESS" => $address,
                                "LATITUDE" => $latitude,
                                "LONGITUDE" => $longitude,
                                "MAIL_ID" => $email,
                                "EMERGENCY_CONTACT_NO" => $emergency_contant,
                                "RFID_CARD" => $rfid_card,
                                "STATUS" => $status,
                                "CREATED_BY" => $user_id,
                                "created_at" => $current_datetime);
                            if ($EMPLOYEE_REGISTRATION_SMS == 1) {
                                $message = 'Hi ' . $emp_name . '. Please Download Zingo Mobile App "Zingo Corp" for better office commute. Andorid Link: http://bit.ly/2rmm933, iOS link: https://apple.co/2sck4a9';
                                $insert_sms = array("BRANCH_ID" => $branch_id, "ORIGINATOR" => $SMSTAG, "RECIPIENT" => $emp_mobile, "MESSAGE" => $message,
                                    "SENT_DATE" => '1900-01-01 00:00:00', "REF_NO" => '--', "CREATED_BY" => $user_id, "CREATED_DATE" => date('Y-m-d H:i:s'), "CATEGORY" => 'EMP REG');
                                //sms::insert($insert_sms);
                            }
                        }
                        $j++;
                    }
                }
                fclose($handle);
            }
            $isinserted = Employee_input_data::on("$dbname")->insert($input_Arr);
            if ($isinserted) {
                $this->employee_input_datas();
                return response([
                    'success' => true,
                    'status' => 3,
                    'message' => 'success '. $j. ' Rows Uploaded successfully',
                ]);
                //return 'success '. $j . ' Rows Uploaded successfully';
                /* if ($isCorrectLocation) {
                    echo "sdfsf";
                  //  return $isCorrectLocation;
                    exit;
                } else {
                   
                } */
            }
        } catch (Exception $exc) {
            echo $exc->getTraceAsString();
        }
    }

    public function employee_input_datas() {

        ini_set('max_execution_time', 0);
        $dbname = Auth::user()->dbname;
        //uploadrunemp
        $BRANCH_ID = Auth::user()->BRANCH_ID;
        $user_id = Auth::user()->id;

        $property_roster_request = DB::table("properties")->select("PROPERTIE_NAME", "PROPERTIE_VALUE")->where("BRANCH_ID", "=", $BRANCH_ID)->where("PROPERTIE_NAME", "=", "EMP REQUEST LOGIN")->get();
        $roster_request_value = $property_roster_request[0]->PROPERTIE_VALUE;

        $branch_lat = DB::connection("$dbname")->table("branch")->select("BRANCH_ID", "LAT", "LONG", "DIVISION_ID")->where("BRANCH_ID", "=", $BRANCH_ID)->get();
        $cmp_lat = $branch_lat[0]->LAT;
        $cmp_long = $branch_lat[0]->LONG;
        $division_id = $branch_lat[0]->DIVISION_ID;
        $RS_ACTIVE = MyHelper::$RS_ACTIVE;
        //$obj = new CommonController();
        $sql = "SELECT BRANCH_ID,LOCATION,EMPLOYEE_ID,EMPLOYEE_NAME,GENDER,EMPLOYEE_MOBILE,PROJECT_NAME,ADDRESS,
			LATITUDE,LONGITUDE,MAIL_ID,EMERGENCY_CONTACT_NO,STATUS,INPUT_ID,RFID_CARD FROM `employee_input_datas` where STATUS='" . $RS_ACTIVE . "' and BRANCH_ID = '" . $BRANCH_ID . "' ";
        $datas = DB::connection("$dbname")->select($sql);
        $location_select = "select LOCATION_ID,LOCATION_NAME,LATITUDE,LONGITUDE from locations where ACTIVE=1 and DIVISION_ID='" . $division_id . "'";
        $location_id_res = DB::connection("$dbname")->select($location_select);
        $checklocarray = array();

        foreach ($location_id_res as $val) {
            $checklocarray[$val->LOCATION_ID] = $val->LOCATION_NAME;
        }
        // echo '<pre>';
        // print_r($checklocarray);exit;

        if (count($datas) > 0) {
            $arr = array();
            $arr2 = array();
            $user_arr = array();
            $input_id_list = array();
            $i = 0;
            $location_id = 0;
            $distance = 0;
            foreach ($datas as $val) {
                $EMPLOYEE_NAME = trim($val->EMPLOYEE_NAME);
                $GENDER = trim($val->GENDER);
                $EMPLOYEE_ID = trim($val->EMPLOYEE_ID);
                $LOCATION = trim($val->LOCATION);
                $BRANCH_ID = $val->BRANCH_ID;
                $EMPLOYEE_MOBILE = trim($val->EMPLOYEE_MOBILE);
                $PROJECT_NAME = trim($val->PROJECT_NAME);
                $ADDRESS = trim($val->ADDRESS);
                $EMP_LATITUDE = trim($val->LATITUDE);
                $EMP_LONGITUDE = trim($val->LONGITUDE);
                $MAIL_ID = trim($val->MAIL_ID);
                $EMERGENCY_CONTACT_NO = trim($val->EMERGENCY_CONTACT_NO);
                $STATUS = $val->STATUS;
                $input_id = $val->INPUT_ID;
                $RFID_CARD = $val->RFID_CARD;

                $location_id = array_search($LOCATION, $checklocarray);
                // $LOCATION_NAME = $location_id_res[0]->LOCATION_NAME;
                // $LOC_LATITUDE = $location_id_res[0]->LATITUDE;
                // $LOC_LONGITUDE = $location_id_res[0]->LONGITUDE;


                /* approve Distance */
                // $waypoints = $LOC_LATITUDE . ',' . $LOC_LONGITUDE;
                // $waypoints_origin = $cmp_lat . ',' . $cmp_long;
                // $waypoints_destination = $cmp_lat . ',' . $cmp_long;
                // $approve_dis = $obj->Getkm($waypoints_origin, $waypoints_destination, $waypoints);
                // $approve_split = explode('-', $approve_dis);
                // $approve_distance = round($approve_split[0] / 1000);
                /* approve Distance End */

                /* employee Distance */
                /*  $dis_origin = $EMP_LATITUDE . ',' . $EMP_LONGITUDE;
                  $dis_destination = $cmp_lat . ',' . $cmp_long;
                  $emp_distance = $obj->Getkm_two_point($dis_origin, $dis_destination);
                  $distance_split = explode("-", $emp_distance);
                  if($distance_split[0]!=0)
                  {
                  $distance = $distance_split[0] / 1000;
                  }
                  else
                  {
                  $distance=0;
                  } */
                /* Employee Distance End */
                $emp_name = $this->commonFunction->AES_ENCRYPT($EMPLOYEE_NAME, env('AES_ENCRYPT_KEY'));
                $mobile_no = $this->commonFunction->AES_ENCRYPT($EMPLOYEE_MOBILE, env('AES_ENCRYPT_KEY'));
                $email_id = $this->commonFunction->AES_ENCRYPT($MAIL_ID, env('AES_ENCRYPT_KEY'));
                $password = $this->commonFunction->AES_ENCRYPT($EMPLOYEE_MOBILE, env('AES_ENCRYPT_KEY'));

                /*  $arr[] = "('" . $EMPLOYEE_ID . "','" . $BRANCH_ID . "','" . $emp_name . "','" . $password . "','" . $mobile_no . "','" . $GENDER . "','" . $PROJECT_NAME . "', '" . $location_id . "','" . $ADDRESS . "','" . $RS_ACTIVE . "','" . $EMP_LATITUDE . "','" . $EMP_LONGITUDE . "', '" . $distance . "', '" . $email_id . "','" . $user_id . "', '" . date("Y-m-d H:i:s") . "','" . date("Y-m-d H:i:s") . "','Emp')"; */
                $arr[] = array("EMPLOYEES_ID" => $EMPLOYEE_ID,
                    "BRANCH_ID" => $BRANCH_ID,
                    "NAME" => $emp_name,
                    'PASSWORD' => $password,
                    "MOBILE" => $mobile_no,
                    "GENDER" => $GENDER,
                    "PROJECT_NAME" => $PROJECT_NAME,
                    "LOCATION_ID" => $location_id,
                    "ADDRESS" => $ADDRESS,
                    "ACTIVE" => 1,
                    "LATITUDE" => $EMP_LATITUDE,
                    "LONGITUDE" => $EMP_LONGITUDE,
                    "DISTANCE" => $distance,
                    "EMAIL" => $email_id,
                    "RFID_CARD" => $RFID_CARD,
                    "CREATED_BY" => $user_id,
                    "CREATED_DATE" => date("Y-m-d H:i:s"),
                    "updated_at" => date("Y-m-d H:i:s"),
                    "CATEGORY" => 'Emp');
                if ($roster_request_value == 'Y') {
                    $user_arr[] = array("BRANCH_ID" => $BRANCH_ID, "name" => $EMPLOYEE_MOBILE, "user_type" => "EMPLOYEE", "vendor_id" => '0', "active_status" => 1, "CREATED_BY" => $user_id, "created_at" => date("Y-m-d H:i:s"), "updated_at" => date("Y-m-d H:i:s"), "login_category" => "ZINGO");
                }
                // $exist_location = DB::table("approve_distances")->select("BRANCH_ID", "LOCATION_ID")->where("BRANCH_ID", "=", $BRANCH_ID)->where("LOCATION_ID", "=", $location_id)->get();
                // $cnt = count($exist_location);
                // $approve_insert = '';
                // if ($cnt == 0) {
                // $arr2[] = "('" . $BRANCH_ID . "','" . $location_id . "','" . $approve_distance . "','" . $user_id . "','" . date("Y-m-d H:i:s") . "')";
                // }
                $input_id_list[$i] = $input_id;
                $i++;
            }
            /*  echo "<pre>";
              print_r($arr);exit; */
            $res = Employee::on("$dbname")->insert($arr);
            $user_res = User::insert($user_arr);

            // if(count($arr2) > 0 && ($res == true)){
            // $QUERY2 = "insert into approve_distances(`BRANCH_ID`,`LOCATION_ID`,`APPROVED_DISTANCE`,`CREATE_BY`,`CREATED_DATE`) values " . implode(',', $arr2) . "";
            // $result = DB::insert($QUERY2);
            // }           
            if ($res == true) {
                foreach ($input_id_list as $input_id) {
                    Employee_input_data::on("$dbname")->where('INPUT_ID', '=', $input_id)
                            ->update(array('STATUS' => 2));
                }
            }
        }
    }
    
    public function dataForCreate(): FoundationApplication|Response|ResponseFactory
    {

        try {
            $user = Auth::user();
            $RS_ACTIVE = MyHelper::$RS_ACTIVE;
            $branchId = $user->BRANCH_ID;
            $vendor_id = $user->vendor_id;
            $curdate = date("Y-m-d");
			
            $reason=DB::table("reason_master")->select('REASON','REASON_ID')
			 	->where([["active","=",$RS_ACTIVE],["CATEGORY","=",'WebOverSpeed'],["BRANCH_ID","=",$branchId]])->get();

                
            $panic_reason=DB::table("reason_master")->select('REASON','REASON_ID')
			 	->where([["active","=",$RS_ACTIVE],["CATEGORY","=",'WebPanicEmp'],["BRANCH_ID","=",$branchId]])->get();

                
                return response([
                'success' => true,
                'status' => 3,
                'reason' => $reason,
                'panic_reason' => $panic_reason
                
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Data For Create Roster Upload Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ],500);
        }
    }
    
    
}
