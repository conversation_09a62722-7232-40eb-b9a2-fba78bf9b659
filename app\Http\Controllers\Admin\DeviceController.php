<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\DeviceRequest;
use App\Http\Requests\Admin\DeviceDeleteRequest;
use App\Http\Requests\Admin\DeviceUpdateRequest;
use App\Services\Admin\DeviceService;


use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class DeviceController extends Controller
{
    protected DeviceService $deviceService;

    public function __construct(DeviceService $deviceService)
    {
        $this->deviceService = $deviceService;
    }

    public function index(): ResponseFactory|Application|Response
    {
        return $this->deviceService->indexDevice();
    }

   public function store(DeviceRequest $request): ResponseFactory|Application|Response
    {
        return $this->deviceService->storeDevice($request);
    }
    public function delete(DeviceDeleteRequest $request, $deviceAutoIdCrypt): FoundationApplication|Response|ResponseFactory
    {
        return $this->deviceService->deleteDevice($request, $deviceAutoIdCrypt);
    }
	
	 public function edit($deviceAutoIdCrypt): Application|Response|ResponseFactory
    {
        return $this->deviceService->editDevice($deviceAutoIdCrypt);
    }

    public function update(DeviceUpdateRequest $request, $deviceAutoIdCrypt): Application|Response|ResponseFactory
    {
        return $this->deviceService->updateDevice($request, $deviceAutoIdCrypt);
    }


    public function activeDevicePagination(Request $request): ResponseFactory|Application|Response
    {
        return $this->deviceService->paginationDevice($request);
    }

}
