<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Crypt;

class Branch extends Model
{
    use HasFactory;

    protected $table = 'branch';
    protected $primaryKey = 'BRANCH_ID';

    protected $fillable = [
        'ORG_ID',
        'COMPANY_ID',
        'DIVISION_ID',
        'BRANCH_NAME',
        'LOCATION',
        'LAT',
        'LONG',
        'ACTIVE',
        'OPERATION_TYPE',
        'CREATED_BY',
        'UPDATED_BY',
        'REMARKS',
    ];


    protected $appends = ['id_crypt'];

    public function createdBy()
    {
        return $this->hasOne(User::class,'id','CREATED_BY');
    }
    public function updatedBy()
    {
        return $this->hasOne(User::class,'id','UPDATED_BY');
    }

    public function getIdCryptAttribute($value)
    {
        return Crypt::encryptString($this-><PERSON><PERSON><PERSON>_ID);
    }
}
