<?php

namespace App\Http\Controllers\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Http\Requests\SuperAdmin\OrganizationDeleteRequest;
use App\Http\Requests\SuperAdmin\OrganizationRequest;
use App\Http\Requests\SuperAdmin\BranchWiseVendorRequest;
use App\Http\Requests\SuperAdmin\PostRegisterRequest;
use App\Http\Requests\SuperAdmin\UpdateRegisterRequest;
use App\Http\Requests\SuperAdmin\AddPropertyRequest;
use App\Services\SuperAdmin\OrganizationService;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class OrganizationController extends Controller
{
    protected OrganizationService $organizationService;

    public function __construct(OrganizationService $organizationService)
    {
        $this->organizationService = $organizationService;
    }

    public function index(): FoundationApplication|Response|ResponseFactory
    {
        return $this->organizationService->indexOrganization();
    }

    public function store(OrganizationRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->organizationService->storeOrganization($request);
    }

    public function pagination(Request $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->organizationService->paginationOrganization($request);
    }

    public function edit($organizationIdCrypt): FoundationApplication|Response|ResponseFactory
    {
        return $this->organizationService->editOrganization($organizationIdCrypt);
    }

    public function update(OrganizationRequest $request, $organizationIdCrypt): FoundationApplication|Response|ResponseFactory
    {
        return $this->organizationService->updateOrganization($request, $organizationIdCrypt);
    }

    public function delete(OrganizationDeleteRequest $request, $organizationIdCrypt): FoundationApplication|Response|ResponseFactory
    {
        return $this->organizationService->deleteOrganization($request, $organizationIdCrypt);
    }

    public function dataForCreate(): FoundationApplication|Response|ResponseFactory
    {
        return $this->organizationService->dataForCreateOrganization();
    }

    public function Register(): FoundationApplication|Response|ResponseFactory
    {
        return $this->organizationService->Register();
    }

    public function GetRegister(): FoundationApplication|Response|ResponseFactory
    {
        return $this->organizationService->GetRegister();
    }

    public function BranchWiseVendor(BranchWiseVendorRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->organizationService->BranchWiseVendor($request);
    }

    public function PostRegister(PostRegisterRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->organizationService->PostRegister($request);
    }

    public function UpdateRegister(UpdateRegisterRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->organizationService->UpdateRegister($request);
    }

    public function GetProperty(): FoundationApplication|Response|ResponseFactory
    {
        return $this->organizationService->GetProperty();
    }

    public function AddProperty(AddPropertyRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->organizationService->AddProperty($request);
    }
}
