<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\EscortRequest;
use App\Http\Requests\Admin\EscortDeleteRequest;
use App\Http\Requests\Admin\EscortUpdateRequest;
use App\Services\Admin\EscortService;


use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class EscortController extends Controller
{
    protected EscortService $escortService;

    public function __construct(EscortService $escortService)
    {
        $this->escortService = $escortService;
    }

    public function index(): ResponseFactory|Application|Response
    {
        
        return $this->escortService->indexEscort();
    }

   public function store(EscortRequest $request): ResponseFactory|Application|Response
    {
        return $this->escortService->storeEscort($request);
    }
    public function delete(EscortDeleteRequest $request, $escortAutoIdCrypt): FoundationApplication|Response|ResponseFactory
    {
        return $this->escortService->deleteEscort($request, $escortAutoIdCrypt);
    }
	
	 public function edit($escortAutoIdCrypt): Application|Response|ResponseFactory
    {
        return $this->escortService->editescort($escortAutoIdCrypt);
    }

    public function update(EscortUpdateRequest $request, $escortAutoIdCrypt): Application|Response|ResponseFactory
    {
        return $this->escortService->updateEscort($request, $escortAutoIdCrypt);
    }


    public function activeEscortPagination(Request $request): ResponseFactory|Application|Response
    {
        return $this->escortService->paginationEscort($request);
    }

}
