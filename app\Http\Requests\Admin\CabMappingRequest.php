<?php
namespace App\Http\Requests\Admin;

use App\Helpers\CommonFunction;
use Closure;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;

class CabMappingRequest extends FormRequest
{
    protected CommonFunction $commonFunction;

    public function __construct(CommonFunction $commonFunction)
    {
        $this->commonFunction = $commonFunction;
    }

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        $branchId = $this->BRANCH_ID;
     
        return [
            'VEHICLE_ID' => ['required'],
            'DRIVER_ID' => ['required'],
            'DEVICE_ID' => 'required',
            'SIM_ID' => 'required',
            'VENDOR_ID' => ['required'],
            'TARIFF_TYPE' => 'required',
            'BRANCH_ID' => 'required',
            'ATTACHMENT_DATE' => 'required',
            'CAB_STATUS' => 'required',
            // 'DRIVER_MOBILE' => [
            //     'required',
            //     'numeric',
            //      Rule::unique('drivers', 'DRIVER_MOBILE')
            // ],
            
        ];
    }
   

    public function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json([
            'success' => false,
            'validation_error' => true,
            'message' => 'Validation errors',
            'data' => $validator->errors()
        ]));
    }

    private function checkDeactivateCab($branchId): Closure
    {
        return function (string $attribute, mixed $value, Closure $fail) use ($branchId) {
            $deactivatedCab = DB::table("cab")
                ->where("CAB_ID", $value)
                ->where("ACTIVE", 2)
                ->exists();

            if ($deactivatedCab) {
                $fail("Cab already exists and is deactivated.");
            }
        };
    }

   
}
