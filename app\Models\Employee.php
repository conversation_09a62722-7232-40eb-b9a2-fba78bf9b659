<?php

namespace App\Models;

use App\Helpers\CommonFunction;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Crypt;

class Employee extends Model
{
    use HasFactory;

    protected $table = 'employees';
    protected $primaryKey = 'id';
    public $timestamps = false;

    protected $appends = ['name_decrypted', 'mobile_decrypted', 'email_decrypted', 'id_crypt'];

    protected $fillable = [
        'EMPLOYEES_ID',
        'BRANCH_ID',
        'SHUTTLE_COMPANY_ID',
        'BLOCK_NAME',
        'NAME',
        'LAST_NAME',
        'password',
        'MOBILE',
        'EMERGENCY_CONTACT_NO',
        'ADDRESS_TYPE',
        'GENDER',
        'PROJECT_NAME',
        'DEPARTMENT',
        'LOCATION_ID',
        'ADDRESS',
        'ACTIVE',
        'LATITUDE',
        'LONGITUDE',
        'DISTANCE',
        'EMAIL',
        'EMAIL_ALERT',
        'PAN_NO',
        'AADHAR_NO',
        'REASON',
        'CARE_TAKER',
        'ID_PROOF',
        'EMPLOYEE_PHOTO',
        'RFID_CARD',
        'CATEGORY',
        'SESSION_ID',
        'PUBLIC_KEY',
        'PRIVATE_KEY',
        'MOBILE_GCM',
        'DEVICE_INFO',
        'DEVICE_ID',
        'APP_VERSION',
        'MOBILE_CATEGORY',
        'COMPANY_CATEGORY',
        'PLACE_PICKER_STATUS',
        'HOME_EDIT_STATUS',
        'RATING',
        'RATE_STATUS',
        'RATE_DATE',
        'CREATED_BY',
        'CREATED_DATE',
        'UPDATED_BY',
        'updated_at',
        'remember_token',
        'SEND_MAIL_STATUS',
        'ARRIVED_NOTIFICATION_TIME',
        'SMS_ALERT',
        'LOGINDATETIME',
        'CORPORATE_BOOKING_STATUS',
        'LOGIN_TIME',
        'LOGOUT_TIME',
        'DEPARTMENT_ID',
        'TEAM_ID',
        'ROUTE_ID',
        'STOPS_ID',
        'RADIUS',
        'special_employee',
        'user_id'
    ];

    protected CommonFunction $commonFunction;

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
        $this->commonFunction = new CommonFunction();
    }

    public function primaryLocationName()
    {
        return $this->hasOne(Location::class,'LOCATION_ID','LOCATION_ID');
    }

    public function employeeAddress()
    {
        return $this->hasOne(EmployeeAddress::class,'EMP_AUTO_ID','id');
    }

    public function getNameDecryptedAttribute(): ?string
    {
        return $this->NAME ? $this->commonFunction->AES_DECRYPT($this->NAME, config('app.aes_encrypt_key')) : null;
    }

    public function getMobileDecryptedAttribute(): ?string
    {
        return $this->MOBILE ? $this->commonFunction->AES_DECRYPT($this->MOBILE, config('app.aes_encrypt_key')) : null;
    }

    public function getEmailDecryptedAttribute(): ?string
    {
        return $this->EMAIL ? $this->commonFunction->AES_DECRYPT($this->EMAIL, config('app.aes_encrypt_key')) : null;
    }

    public function getIdCryptAttribute(): ?string
    {
        return $this->id ? Crypt::encryptString($this->id) : null;
    }
}
