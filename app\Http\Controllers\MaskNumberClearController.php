<?php
	
	namespace App\Http\Controllers;
	
	use DB;
	use App\Models\propertie;
	use App\Models\Roster;
	use App\Models\Roster_passanger;
	use App\Models\sms;
	
	class MaskNumberClearController extends Controller { 
		
		public function Clear_MaskNumber($roster_passenger_id,$branch_id,$roster_id,$r_or_p) {
			//echo 'Clear Mask Number';
			try
			{
				
				$AES_KEY = env("AES_ENCRYPT_KEY");
				$roster_id_val=explode(',',$roster_id);
				//and R.MASK_ASSIGN_STATUS='1' and RP.PASSENGER_MASK_NUMBER!='--' and RP.ACTIVE in(1,3)"
				$cond=$r_or_p=="Passenger"?"RP.ROSTER_PASSENGER_ID=".$roster_passenger_id."":"R.ROSTER_ID=".$roster_id." ";
				
				 $passeng_det="select RP.ROSTER_PASSENGER_ID,RP.ROSTER_PASSENGER_STATUS,RP.PASSENGER_MASK_NUMBER,<PERSON><PERSON>`NAME` as EMPLOYEE_NAME,E.<PERSON>ER,E.M<PERSON>,E.MOBILE_CATEGORY from roster_passengers RP
				inner join employees E ON E.EMPLOYEES_ID=RP.EMPLOYEE_ID and E.BRANCH_ID='".$branch_id."' 
				inner join rosters R on R.ROSTER_ID=RP.ROSTER_ID
				where $cond and E.ACTIVE=1  ";				
				
				$passeng_det_array = DB::select($passeng_det);
				$cust_mob_arr=array();
				if(count($passeng_det_array)>0)
				{
					//if($r_or_p=='Passenger')
					//{
						for($kk=0;$kk<count($passeng_det_array);$kk++)
						{
							$cus_mob =$this->AES_DECRYPT($passeng_det_array[$kk]->MOBILE, $AES_KEY);
							//$cus_mob =7305275023;
							array_push($cust_mob_arr,$cus_mob);
							
						}
					/*}
					else if($r_or_p=='Roster')
					{
						for($jj=0;$jj<count($passeng_det_array);$jj++)
						{
							$cus_mob =$passeng_det_array[$jj]->DRIVER_MOBILE;
							array_push($cust_mob_arr,$cus_mob);
							
						}
					}*/
					$post_arr=array("bookingid"=>$roster_id_val[0],"phonenumber"=>$cust_mob_arr,"api_key"=>"7e4d7bf081f8286447ac383e25588c7f");
					$post_data=json_encode(array($post_arr));
					//echo $post_data;
					if(is_array($post_arr) && count($post_arr) > 0)
					{
						//$ch = curl_init('http://115.252.65.22:7000/aster-dialer-api/callmasking/createbooking.php');
						//$ch = curl_init('http://115.252.65.22:7000/aster-dialer-api/callmasking/closebooking.php');
						$ch=curl_init("http://128.199.179.78/aster-dialer-ntl/services/closebooking.php");
						curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
						curl_setopt($ch, CURLINFO_HEADER_OUT, true);
						curl_setopt($ch, CURLOPT_POST, true);
						curl_setopt($ch, CURLOPT_POSTFIELDS, $post_data);
						
						// Set HTTP Header for POST request 
						curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));
						
						// Submit the POST request
						$result = curl_exec($ch); 
						
						// Close cURL session handle
						curl_close($ch);
						 // echo "<pre>";
						 // print_r($result);
						$decode_result=JSON_DECODE($result);
						
						/* $update_sts="update MASK_TRANSACTION set MASK_CLEAR_RESPONSE='".$result."', UPDATED_AT='".date("Y-m-d H:i:s")."' where ROSTER_PASSENGER_ID ='".$roster_passenger_id."'  ";
						
						DB::update($update_sts); */
						
						if($r_or_p!='Roster')
						{
						
						$update_free="UPDATE MASK_TRANSACTION MT
						INNER JOIN MASK_DID_MASTER MD ON MD.DID_NUMBER=MT.MASK_DID_NUMBER
						SET 
						MT.AVAILABLITY_STATUS=1,MD.AVAILABLITY_STATUS=1,
						MT.MASK_CLEAR_RESPONSE='".$result."', MT.UPDATED_AT='".date("Y-m-d H:i:s")."',MD.UPDATED_AT='".date("Y-m-d H:i:s")."' WHERE MT.ROSTER_PASSENGER_ID='".$roster_passenger_id."'";
						}
						else
						{
							$update_free="UPDATE MASK_TRANSACTION MT
							INNER JOIN MASK_DID_MASTER MD ON MD.DID_NUMBER=MT.MASK_DID_NUMBER
							SET 
							MT.AVAILABLITY_STATUS=1,MD.AVAILABLITY_STATUS=1,
							MT.MASK_CLEAR_RESPONSE='".$result."', MT.UPDATED_AT='".date("Y-m-d H:i:s")."',MD.UPDATED_AT='".date("Y-m-d H:i:s")."' WHERE MT.ROSTER_ID='".$roster_id."'";
						}
						DB::update($update_free); 
						
						
					}
				}
				else
				{
					//echo "EMPTY ARRAY";
				}
			}
			catch (Exception $exc) {
				echo $exc->getTraceAsString();
			}
			
			
			
		} 
		public function mask_clear_all()
		{
			$date=date("Y-m-d");
			$from_date = date('Y-m-d', strtotime('-3 days', strtotime($date))); 
			//$to_date = date('Y-m-d', strtotime('-1 days', strtotime($date)));
			$to_date = date('Y-m-d', strtotime('0 days', strtotime($date))); 
			//and MASK_CLEAR_RESPONSE is null
			//echo  $sql="select * from MASK_TRANSACTION where date(CREATE_DATE) between '".$from_date."' and '".$to_date."'  ";
			/*echo  $sql="select * from MASK_TRANSACTION where date(CREATE_DATE) ='2022-04-28'  ";
			 exit; */
			$sql="select * from MASK_TRANSACTION where ROSTER_ID=997843 ";
			//MASKING_ID =165 
			$result=DB::select($sql);
			if(count($result)>0)
			{
				
			foreach($result as $val)
			{
				$MASKING_ID=$val->MASKING_ID;
				$roster_id=$val->ROSTER_ID;
				$ROSTER_PASSENGER_ID=$val->ROSTER_PASSENGER_ID;
				$branch_id=$val->BRANCH_ID;
				//$branch_id=18;
				//$branch_id=32;
				$type="Passenger";
				$json_response=$this->Clear_MaskNumber($ROSTER_PASSENGER_ID,$branch_id,$roster_id,'Passenger');
				
				/* $update_sts="update MASK_TRANSACTION set MASK_CLEAR_RESPONSE='".json_encode($json_response)."' where MASKING_ID ='".$MASKING_ID."' ";
                DB::update($update_sts); */
				
			}
			}
			else
			{
				echo "No Records";
				exit;
			}
		}
		public function mask_clear_1hr_once()
		{
			$current_date_time=date("Y-m-d H:i:s");
			//$sql="SELECT MT.*,if(TIMEDIFF(MT.END_TIME,'".$current_date_time."')>0,1,0)as diff 
			//from MASK_TRANSACTION MT 
			//where date(MT.CREATE_DATE) ='".date("Y-m-d")."' and MT.AVAILABLITY_STATUS=2  having diff=0 ORDER by MT.START_TIME asc";

                $sql="SELECT MT.*   
			from MASK_TRANSACTION MT 
			where MT.END_TIME < '".$current_date_time."'  and MT.AVAILABLITY_STATUS=2  ORDER by MT.START_TIME asc";

// 			$sql="SELECT MT.MASKING_ID,MT.ROSTER_PASSENGER_ID,MT.BRANCH_ID,MT.ROSTER_ID,md.DID_NUMBER
// 			from MASK_DID_MASTER md
// inner join MASK_TRANSACTION MT on md.DID_NUMBER=MT.MASK_DID_NUMBER
// 			where md.AVAILABLITY_STATUS=2 and MT.AVAILABLITY_STATUS=2 and date(md.UPDATED_AT)=date(MT.CREATE_DATE) ORDER by MT.START_TIME asc";
			$result=DB::select($sql);
			if(count($result)>0)
			{
				foreach($result as $val)
				{
					$MASKING_ID=$val->MASKING_ID;
					$roster_id=$val->ROSTER_ID;
					$ROSTER_PASSENGER_ID=$val->ROSTER_PASSENGER_ID;
					$branch_id=$val->BRANCH_ID;
					//$branch_id=18;
					//$branch_id=32;
					$type="Passenger";
					$json_response=$this->Clear_MaskNumber($ROSTER_PASSENGER_ID,$branch_id,$roster_id,'Passenger');
					//$update_sts="update MASK_TRANSACTION set MASK_CLEAR_RESPONSE='".$json_response."' where MASKING_ID ='".$MASKING_ID."' ";
					//DB::update($update_sts);
					
				}
			}
			
			
		}
		
		public function AES_ENCRYPT($value, $secret) {
			
			
			return rtrim(
			base64_encode(
			mcrypt_encrypt(
			MCRYPT_RIJNDAEL_256, $secret, $value, MCRYPT_MODE_ECB, mcrypt_create_iv(
			mcrypt_get_iv_size(
			MCRYPT_RIJNDAEL_256, MCRYPT_MODE_ECB
			), MCRYPT_RAND)
			)
			), "\0"
			);
		}
		
		public function AES_DECRYPT($value, $secret) {
			return rtrim(
			mcrypt_decrypt(
			MCRYPT_RIJNDAEL_256, $secret, base64_decode($value), MCRYPT_MODE_ECB, mcrypt_create_iv(
			mcrypt_get_iv_size(
			MCRYPT_RIJNDAEL_256, MCRYPT_MODE_ECB
			), MCRYPT_RAND
			)
			), "\0"
			);
		}
		
	}
	
?>	