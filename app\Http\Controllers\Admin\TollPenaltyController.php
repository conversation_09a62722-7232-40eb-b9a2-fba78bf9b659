<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;

use App\Services\Admin\TollPenaltyService;
use App\Http\Requests\Admin\TollPenaltyRequest;
use App\Http\Requests\Admin\TollRequest;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class TollPenaltyController extends Controller
{
    protected TollPenaltyService $tollPenaltyService;

    public function __construct(TollPenaltyService $tollPenaltyService)
    {
        $this->tollPenaltyService = $tollPenaltyService;
    }


    public function fetch_Toll_Penalty_Details(TollPenaltyRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->tollPenaltyService->fetch_Toll_Penalty_Details($request);
    }

     public function dataForCreatePenalties(): FoundationApplication|Response|ResponseFactory
    {
        return $this->tollPenaltyService->dataForCreatePenalties();
    }
    
   public function store(TollRequest $request): ResponseFactory|Application|Response
    {
        return $this->tollPenaltyService->storeTollCharges($request);
    }


    
}
