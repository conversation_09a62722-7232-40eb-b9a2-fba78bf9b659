<?php

//namespace App\Http\Requests\Admin;
namespace App\Http\Requests\Admin;

use App\Helpers\CommonFunction;
use Closure;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;

class ReclubedNewRosterRequest extends FormRequest
{
    protected CommonFunction $commonFunction;

    public function __construct(CommonFunction $commonFunction)
    {
        
    }

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        
        return [
            'selected_old_roster_id' => ['required','numeric'],
            'selected_new_roster_id' => ['required','numeric'],
            'selected_passenger_id' => ['required','numeric'],
            'old_roster_cab_id' => ['numeric','nullable'],           
            ];
    }

    public function messages(): array
    {
        return [
            'selected_old_roster_id.required' => 'selected_old_roster_id field is required',           
            'selected_new_roster_id.required' => 'selected_new_roster_id field is required',           
            'selected_passenger_id.required' => 'selected_passenger_id field is required',           
            'old_roster_cab_id.required' => 'old_roster_cab_id field is required',    
                  
        ];
    }
   

    public function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json([
            'success' => false,
            'validation_error' => true,
            'message' => 'Validation errors',
            'data' => $validator->errors()
        ]));
    }
   
}
