APP_ENV=local
#APP_DEBUG=true
APP_DEBUG=true
APP_KEY=base64:9WluMZa9RgbVumnSG5NAV2MGRhHn/BwL3lrsOvl7Jbc=
APP_URL=http://localhost

DB_CONNECTION=mysql
DB_HOST=mats-ntl-prime.c4ruwpn24miq.ap-southeast-1.rds.amazonaws.com
DB_PORT=6005
DB_DATABASE=topsalara
DB_USERNAME=etioswan
DB_PASSWORD=$occer6a!!on$ky

DB_CONNECTION1=mysql2
DB_HOST1=************
DB_PORT1=7001
DB_DATABASE1=phonedata
DB_USERNAME1=dbuser
DB_PASSWORD1=dbpassword

DB_CONNECTION2=mysql3
DB_HOST2=mats-ntl-prime.c4ruwpn24miq.ap-southeast-1.rds.amazonaws.com
DB_PORT2=6005
DB_DATABASE2=topsa_test
DB_USERNAME2=etioswan
DB_PASSWORD2=$occer6a!!on$ky

DB_CONNECTION3=mysql4
DB_HOST3=mats-ntl-prime.c4ruwpn24miq.ap-southeast-1.rds.amazonaws.com
DB_PORT3=6005
DB_DATABASE3=topsalara
DB_USERNAME3=etioswan
DB_PASSWORD3=$occer6a!!on$ky

DB_CONNECTION4=mysql6
DB_HOST4=mats-ntl-prime.c4ruwpn24miq.ap-southeast-1.rds.amazonaws.com
DB_PORT4=6005
DB_DATABASE4=RRDZINGO
DB_USERNAME4=etioswan
DB_PASSWORD4=$occer6a!!on$ky

CACHE_DRIVER=file
SESSION_DRIVER=file
QUEUE_DRIVER=sync

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_DRIVER=smtp
#MAIL_HOST=mailtrap.io
MAIL_HOST=smtp.gmail.com
#MAIL_PORT=2525
MAIL_PORT=587
MAIL_USERNAME=
MAIL_PASSWORD=
MAIL_ENCRYPTION=tls

ES_HOST = 'http://localhost:9200'
#ES_HOST = 'http://************:9200'
#ES_INDEX=topsa_track
ES_INDEX=gtslogs
ES_TYPE_GPS_LOGGING=driver_gps_logging
ES_TYPE_GPS_ALERT=driver_gps_alert
ES_TYPE_SPEED_ALERT=driver_speed_alert
ES_TYPE_DEVIATION_ALERT=route_deviation_alert
ES_TYPE_ACTION_LOGS=action_logs
ES_TYPE_EXCEPTION_LOGS=exception_logs
ES_TYPE_CAB_LIVE=cab_live_status
ES_TYPE_ROUTE_PATH=shuttle_route_path
ES_TYPE_GOOGLE_ADDR=google_address
ES_TYPE_DISTANCE_ALERT=distance_alert
ES_TYPE_WEB_LOG=web_action_logs


ES_FIELD_LOGIN_STATUS=LOGIN_STATUS
ES_FIELD_GPS_STATUS=GPS_STATUS
ES_FIELD_BATTERY_STATUS=BATTERY_STATUS
ES_FIELD_ROUTE_STATUS=ROUTE_STATUS
ES_FIELD_LOGIN_STATUS=LOGIN_STATUS
ES_FIELD_ACTIVE_STATUS=ACTIVE_STATUS
ES_FIELD_GPS_DATE=GPS_DATE
ES_FIELD_CAB_ID=CAB_ID
ES_FIELD_ROUTE_ID=ROUTE_ID
ES_FIELD_ADDRESS=ADDRESS
ES_FIELD_POSITION=POSITION


ES_SIZE_SEARCH=10
ES_SIZE_REPORT=30
ES_SIZE_MAX=10000
ES_SIZE_ONE=1
ES_SIZE_ZERO=0
ES_SIZE_SINGLE=1
ES_SIZE_THIRTY=30
ES_DEFAULT_KM ='5km'
ES_SCROLL_TIME=30s

ES_STATUS_ALL=0
ES_CAB_STATUS=1
ES_STATUS_LOGOUT=Logout
ES_STATUS_LOGIN='Login'
ES_STATUS_OVER_SPEED='Over Speeds'
ES_STATUS_IDLE=2
ES_STATUS_GPS_OFF='GPS Off'
ES_STATUS_GPS_NOTFIX='GPS Not Fix'
ES_STATUS_GPS_FIX='GPS Fix'
ES_STATUS_BATTERY_LOW=41
ES_STATUS_VACANT=Vacant
ES_STATUS_ONTRIP='On Duty'
ES_STATUS_ONEHOUR_BELOW='GPS Not Fix 5min to 1hour'
ES_STATUS_SIXHOURS_BELOW='GPS Not Fix 1hour to 6hour'
ES_STATUS_SIXHOURS_ABOVE='GPS Not Fix 6hour above'
ES_CAB_FILTER='filter'

ES_DEFAULT_DATE='1900-01-01 00:00:00'
ES_TIME_ZONE='Asia/Kolkata'
ES_DEFAULT_DATETIMEF='Y-m-d H:i:s'
ES_DEFAULT_DATEF='Y-m-d'
ES_DEFAULT_TIMEF='H:i:s'
ES_DEFAULT_ZERO=0
ES_DEFAULT=--
ES_GPS_OFF=0
ES_GPS_NOTFIX=2
ES_GPS_FIX=3
ES_BATTERY=20
ES_VACANT=0
ES_ONTRIP=1
ES_BOARDED=2
ES_LOGOUT=0
ES_CAB_DEACTIVE=0
ES_LOGIN=1
ES_NOT_SENT=0
ES_SENT=1

ES_IDLE_TIME_5MIN_MINUS='-5 minute'
ES_IDLE_TIME_15MIN_MINUS='-15 minute'
ES_IDLE_TIME_1HOUR_MINUS='-1 hour'
ES_IDLE_TIME_6HOUR_MINUS='-6 hour'
ES_IDLE_TIME_3HOUR_MINUS='-3 hour'

#User Type 
UT_OPTION = ADMIN
UT_VENDOR = VENDOR
EC_ESCORT = ESCORT
BC_BILLABLE = 'change'
UT_VENDORID = 0

#Escort Status
ESCORT_NEW = 1
ESCORT_ALLOT = 2
ESCORT_VALID = 3
ESCORT_SETOTP = 4
ESCORT_INTER = 5
ESCORT_REMOVE = 6
ESCORT_YES = 'YES'
ESCORT_NO = 'NO'

#SMS CONTENT
SMS_PICKUP = 'Pickup'
SMS_DROP = 'Drop'

#Tracking Dashboard
TD_NORMAL = 1

#Roster Active Status
RS_ACTIVE = 1
RS_INACTIVE = 2
VERIFIED_STATUS = 0
VERIFIEDSTATUS = 1
RS_SHUTTLE_TYPE=S
RS_MGENDER = M
RS_FGENDER = F
CAPACITY4 = 4
CAPACITY9 = 9
PROP_AUTOPICK = Y
PROP_YES = Y

# Total Roster Status 
RS_NEWROSTER = 1,3

#Total Cab Allot Status
RS_TOTALALLOT = 9,11,13,15

#Total Cab Accepted Status
RS_TOTALACCEPT = 25,27,29,31

#Total Cab Rejected Status
RS_TOTALREJECT = 41,43,45,47

#Total Cab Executed Status
RS_TOTALEXECUTE = 89,91,93,95

#Total Can Breakdown Status
RS_TOTALBREAKDOWN = 217,219,221,223

#Total Cab TripClose Status
RS_TOTALTRIPCLOSE = 345,347,349,351

#Total Cab Delay Routes Status
RS_TOTALDELAYROUTES = 857,859,861,863

#Total Cab Manual TripClose Status
RS_TOTALMANUALTRIPCLOSE = 1024,1113,1115,1117,1119,1033,1035,1037,1039,1049,1051,1053,1055

#Total Cab TripSheetAccepted Status
RS_TOTALTRIPSHEETACCEPT = 2397,2399,2909,2911,3165,3167,3081,3083,3085,3087,3097,3101,3103

#Total TripSheetRejected Status
RS_TOTALTRIPSHEETREJECT = 4445,4447,4957,4959,5213,5215,5129,5131,5133,5135,5145,5149
#Total TripSheet Cancel
#RS_TOTALTRIPSHEET_CANCEL=16473,16475,16477,16479,16393,16395,16397,16399

RS_TOTALTRIPSHEET_CANCEL=17497,17499,17501,17503,17417,17419,17421,17423

#Total AutoCancel
RS_TOTAL_AUTOCANCEL = 8193,8205,8283,8221,8281,8201,8225,8285,8195
#AutoCancel
RS_AUTOCANCEL = 8192

#Roster Creation
RS_ROSTER = 1

#Vendorchange
RS_VENDORCHANGE = 2

#Billable 
RS_BILLABLE = 4

#Cab Alloted
RS_CABALLOT = 8

#Accepted
RS_ACCEPT = 16

#Rejected
RS_REJECT = 32

#Executed
RS_EXECUTE = 64

#Breakdown
RS_BREAKDOWN = 128

#TripClose
RS_TRIPCLOSE = 256

#Delay Routes
RS_DELAYROUTES = 512

#Manual TripClose
RS_MANUALTRIPCLOSE = 1024

#TripSheetAccepted
RS_TRIPSHEETACCEPTED = 2048

#TripSheetRejected
RS_TRIPSHEETREJECTED = 4096

#TripSheetRejected
RS_AUTOCANCEL = 8192

#Trip Cancel
RS_TRIPCANCEL=16384

#Roster Passenger Status
#Roster Passenger Created
RPS_CREATE = 1

#Roster Passenger Clubbing  
RPS_CLUBBING = 2

#Roster Passenger Arrived
RPS_ARRIVAL = 4

#Roster Passenger Cab Delay
RPS_CABDELAY = 8

#Roster Passenger NoShow
RPS_NOSHOW = 16

#Roster Passenger SystemOTP
RPS_SYSTEMOTP = 32

#Roster Passenger Employee Delay
RPS_EMPLOYEEDELAY = 64

#Roster Passenger ManualOTP
RPS_MANUALOTP = 128

#Roster Passenger Safe Drop
RP_safe_drop = 256
#Total Roster Passenger Created
RPS_TTLCREATE = 1,3

#Total Roster Passenger Arrived
RPS_TTLARRIVAL = 5,7

#Total Roster Passenger Arrived
RPS_TTLCABDELAY = 13,15

#Total Roster Passenger NoShow
RPS_TTLNOSHOW = 16,17,19,21,23,29,31,85

#Total Roster Passenger SystemOTP
RPS_TTLSYSTEMOTP = 37,39

RPS_TTLCABSYSTEMOTP = 45,47

#Total Roster Passenger Employee Delay with SYSEMOTP
RPS_TTLEMPLOYEEDELAY_SYSTEMOTP = 101,103

#Total Roster Passenger with out SystemOTP Employee Delay
RPS_TTLEMPLOYEEDELAY = 69,71

#Total Roster Passenger Employee Delay
RPS_TTLCABEMPLOYEEDELAY = 109,111

#Total Roster Passenger ManualOTP
RPS_TTLMANUALOTP = 133,135,141,143,229,231,237,239,197,199
RPS_SAFE_DROP_OTP = 293,295,357,359,389,391,453,455

#Total Cab Attandance
CAS_ATTANDANCE = 1,2

#Google API with Lat Long Details
API_DISTANCEMATRIX = https://maps.googleapis.com/maps/api/distancematrix/json?
API_DIRECTIONS = https://maps.googleapis.com/maps/api/directions/json?
#API_CLIENTID = AIzaSyDzV4NRR1D1SgtArsEsTGVRsKrwaazSSps
API_CLIENTID = gme-newtravellinesindia

#API_KEY = AIzaSyDzV4NRR1D1SgtArsEsTGVRsKrwaazSSps
#API_KEY = AIzaSyAS2M_iJaj5xQz8xz3mtJAsNyuCNCsdhp4
#API_KEY = AIzaSyDx_mlLKjowvCx4OvJXn2WEQR_jCFCRFU8
#API_KEY = AIzaSyDNyekx486ndlYebuDeFaiWcUOxLHE12EA
#API_KEY = AIzaSyA9Rx0xIW-XcMBYepuDoNlIkAWi8SCDmeM
#API_KEY = AIzaSyACKus8X1z0xJ0k8hDZxgpOPeZwebpUEGY
API_KEY = AIzaSyAzzKMQ0y1hSrMGJDkStw4QwgXlvoY0otA     #Sharveshpg
#API_KEY = AIzaSyDlmCghQf2Pm_6rWUWJpVbNm4wn_gmOdOo
#TRIPCLOSE_API_KEY = AIzaSyApCpDPwdRObR90D4faKPh2zCOlKMkCYpU # Arun sir login (Not working)
#TRIPCLOSE_API_KEY = AIzaSyDs04gONLucsr_VoxZPDb7Q8vzfWLprLc8 # Arun sir login
#TRIPCLOSE_API_KEY = AIzaSyAlIQtyLLiJLDBHdAwd6qva4NGW5CzpXTY   #Sharveshpg

API_KEY_NEW = AIzaSyBMHOgf2A1skrppq64oVA1j-xhESPNrsqs  #NEW API KEY

TRIPCLOSE_API_KEY = AIzaSyA726lGlhUc0QdQMrQb8mDloSuPolUNE2Y

#SELVAM UPDATE START 

#Idle List
IDLE_FIVE_MINUTE = 00:05:00
IDLE_ONE_HOUR = 01:00:00
IDLE_SIX_HOUR = 06:00:00

#Vehicle Tracking Image Path
VEHICLE_TRACKING_IMAGE=https://topsa.in/TMS/assets/map-icon

#Vehicle Tracking Status
VT_LOGIN_STATUS=1
VT_LOGOUT_STATUS=0
VT_GPS_OFF=0
VT_GPS_NOT_FIX=2
VT_GPS_FIX=3
VT_BATTERY_LOW=20
VT_ONDUTY_STATUS=1
VT_VACANT_STATUS=0
VT_CASE_GPS_OFF=GPSOFF
VT_CASE_GPSNOT_FIX=GPSNOTFIX
VT_CASE_GPS_FIX=GPSFIX
VT_CASE_LOGIN=LOGIN
VT_CASE_LOGOUT=LOGOUT
VT_CASE_VACANT=VACANT
VT_CASE_ONDUTY=ONDUTY
VT_CASE_Idle5min_to_1h='Idle 5min to 1h'
VT_CASE_Idle_1h_to_6h='Idle 1h to 6h'
VT_CASE_Idle_6h_above='Idle 6h above'
VT_CASE_ALLCAB_ALL_STATUS='ALLCAB-ALLSTATUS'
VT_CASE_SINGLECAB_ALL_STATUS='SINGLE-CAB-ALLSTATUS'

#Google Map marker

#GOOGLE_MAP_LINK= https://maps.googleapis.com/maps/api/js?key=AIzaSyA9Rx0xIW-XcMBYepuDoNlIkAWi8SCDmeM
#GOOGLE_MAP_LINK= https://maps.googleapis.com/maps/api/js?key=AIzaSyDNyekx486ndlYebuDeFaiWcUOxLHE12EA
GOOGLE_MAP_LINK= https://maps.googleapis.com/maps/api/js?key=AIzaSyAzzKMQ0y1hSrMGJDkStw4QwgXlvoY0otA

#Roster Display Status
RS_Created_sts='Trip created'
RS_Alloted_sts='Trip Alloted'
RS_ACCEPTed_sts='Trip Accepted'
RS_Rejected_sts='Trip Rejected'
RS_Execute_sts='Trip Executed'
RS_Noresponse_sts='Trip NoResponse'
RS_Breakdown_sts='Trip Breakdown'
RS_Closed_sts='System Closed'
RS_Manual_Closed_sts='Trip Manual closed'
RS_Tripsheet_Accept_sts='Tripsheet Accepted'
RS_Tripsheet_Reject_sts='Tripsheet Rejected'
RS_Closed_Delay_sts='Cab Delay and Trip Closed'
RS_Tripsheet_cancelled_sts='Tripsheet Cancelled'
RS_Autocancelled_sts='Auto Cancelled'

#Roster Passenger Display Status
RP_Boarded_sts='Employee Boarded'
RP_Not_Boarded_sts='Employee not Boarded'
RP_Not_Enabled='Not Enabled'
RP_Created_sts='Created'
RP_Club_sts='Clubbed'
RP_Arrival_sts='Arrived'
RP_cab_delay_sts='Cab Delay'
RP_Noshow_sts='Noshow'
RP_system_otp_sts='System OTP'
RP_Manual_otp_sts='Manual OTP'
RP_employee_delay_sts='Employee Delay'
RP_cabemployee_delay_sts ='Cab and Employee Delay'
RP_manualclosed = 'Manual Closed'
RP_employee_delay_system_otp='Employee Delay and System OTP'
RP_employee_safe_drop='Employee Safe Drop'
RP_cab_delay_system_otp_sts='Cab Delay and System OTP'

AES_ENCRYPT_KEY=flowercat_123456
SUCCESS=Success
ERROR=Error
FAIL=Failure
EXIST=Exist
SAME=Same
#SELVAM END

#Master forms Start
MOBILENO_MIN_LENGTH=10
MOBILENO_MAX_LENGTH=10
LOCATION_MIN_LENGTH=3
LOCATION_MAX_LENGTH=100
ORG_NAME_MIN_LENGTH=3
ORG_NAME_MAX_LENGTH=60
REMARKS_MIN_LENGTH=10
REMARKS_MAX_LENGTH=50

#Places API
GOOGLE_PLACES_API=https://maps.googleapis.com/maps/api/js?key=AIzaSyAzzKMQ0y1hSrMGJDkStw4QwgXlvoY0otA&v=3.exp&sensor=false&libraries=places

#End

# Manual OTP Notication Message
PICKUP_HEADING='Cab Arrived'
DROP_HEADING='Boarded'
OTP_PICKUP_HEADING='OTP'
OTP_DROP_HEADING='OTP'
NOTIFICATION_CAT=3
NOSHOW_MSG='Noshow has been updated from admin'
NOSHOW_HEADING='Noshow'
#SELVAM END

#Reports start
RP_ZEROTIME = '00:00:00'
RP_ALLREPORT = 'ALL'
RP_REPLACE = ', Chennai, Tamil Nadu, India'
RP_CONST = 1
RP_ZERO = 0
RP_PICKROUTE = 'P'
RP_DROPROUTE = 'D'
RP_TTLTYPE = 'P,D'
RP_PICKUP = 'IN'
RP_DROP = 'OUT'
RP_OVERALL = 'overall'
RP_EMPLOYEEREPORT = 'employeereport'
RP_VEHICLEREPORT = 'vehiclereport'
RP_EMPNOSHOW = 'empnoshow'
RP_ONTIME = 'ontime'
RP_MISREPORT = 'misreport'
RP_CANCEL = 'cancel'
RP_ACCEPTREJECT = 'acceptreject'
RP_OTPREPORT = 'otpreport'
RP_PENALTY = 'penalty'
RP_TOLL = 'toll'
RP_FEEDBACK = 'feedback'
RP_VEHICLE = 'vehicle'
RP_BREAKDOWN = 'breakdown'
RP_PANIC = 'panic'
RP_OVERSPEED = 'overspeed'
RP_OVERTIME = 'overtime'
RP_VENDORCHANGE = 'vendorchange'
RP_WRONGLOCATION = 'wronglocation'
RP_SMS = 'smsreport'
RP_WEEKROSTER = 'weekroster'
RP_RFIDSWIPE = 'rfidswipe'


RP_NOSHOW = 'No Show'
RP_CABDELAY = 'Cab Delay'
RP_EMPLOYEEDELAY = 'Employee Delay'
RP_CABEMPLOYEEDELAY = 'Cab and Employee Delay'
RPS_ONTIME = 'On Time'

RP_ACTIVE = 1
RP_ACTIVESTS = 'ACTIVE'
RP_INACTIVESTS = 'IN-ACTIVE'

RP_ALLREPORT = 'ALL'
RP_EMPLOYEEPANIC = 'EMPLOYEE'
RP_DRIVERPANIC = 'DRIVER'

RPSYSOTP = 'S'
RPMANUALOTP = 'M'
RP_SYSOTP = 'SYSTEM OTP'
RP_MANUALOTP = 'MANUAL OTP'
RP_SWINGSTART = '06:00:00'
RP_SWINGEND = '19:00:00'

#Reports End

#Reason Master
RM_TARIFFTYPE = 'TariffType'

#XLS File Upload
FILE_UPLOAD_DAYS_LIMIT = 3
FILE_UPLOAD_NAME = 'fms_pickup_rost.csv'
EMP_UPLOAD_NAME = 'employee_master.csv'
EMP_COMP_UPLOAD_NAME = 'company_employee_master.csv'
AUTO_ROUTE_UPLOAD_NAME = 'auto_route_master.csv'
DEACTIVE_EMP_UPLOAD_NAME = 'deactive_employee.csv'

#Breakdown Dropdown
BD_DRIVER = 'Driver Issue'
BD_VEHICLE = 'Vehicle Issue'
BD_BREAKDOWN = 'Break Down'
BD_ACCIDENT = 'Accident' 
BD_INCIDENT = 'Incident'

#Escort option
ES_WEBESCORT = 'WebEscort'
ES_ROMOVEINTER = '5,6'

#Tracking Dashboard page
TD_TEXTAREA = 255
TD_ALLNORMAL = 'Normal'
TD_NOTALLOTED = 'Not Alloted'
TD_ALLOTED = 'Alloted'
TD_TRIPACCEPTED = 'Trip Accepted'
TD_TRIPREJECTED = 'Trip Rejected'
TD_TRIPEXECUTED = 'Trip Executed'
TD_NORESPONSE = 'No Response'
TD_TRIPNOTEXECUTED = 'Trip Not Executed'
TD_BREAKDOWN = 'BreakDown'
TD_WAITINGATPICKUPPOINT = 'Waiting At PickUp Point'
TD_SLEEPALERT = 'Sleep Alert'
TD_SAFEDROP = 'Safe Drop'
TD_PANIC = 'Panic'
TD_OVERSPEED = 'Over Speed'
TD_ESCORT = 'Escort'
TD_TRIPCLOSE = 'Trip Close'
TD_OVERTIMEALERT = 'Over Time Alert'
TD_ALERTSIGNALFAILURE = 'Alert Signal Failure'
TD_DEVIATIONALERT = 'Deviation Alert'
TD_NOCABCOND = 0
TD_INCABCOND = 1
TD_EMPCLUBMSG = 'Successfully done the operation.'
TD_SEARCHALL='search all'
TD_NOSHOW='No Show'

PR_AUTOPICKUPTIME = 'AUTO PICKUPTIME'
PR_ESTIMATESUBTIME = 'ESTIMATE SUBTIME'
PR_ESTIMATEADDTIME = 'ESTIMATE ADDTIME'
PR_ESTIMATECURTIME = 'ESTIMATE CURTIME'
PR_HELPLINENO = 'HELPLINE NO'
PR_SMSTAG = 'SMS TAG'
PR_LEVEL2 = 'ESTIMATE INTERVAL LEVEL2'
PR_ESTIMATECABALLOT = 'ESTIMATE CABALLOT'
PR_LEVEL1 = 'ESTIMATE INTERVAL LEVEL1'
PR_TRIPNOTEXECUTED = 'TRIP NOT EXECUTED'
PR_INTIMEBUFFER = 'INTIME BUFFER'
PR_OUTTIMEBUFFER = 'OUTTIME BUFFER'
PR_PANICEMPESCALATE = 'PANIC EMP ESCALATE NO'
PR_PANICEMPESCALATEEMAIL = 'PANIC EMP ESCALATE EMAIL'
PR_PANICDRIVERESCALATE = 'PANIC DRIVER ESCALATE NO'
PR_PANICDRIVERESCALATEEMAIL = 'PANIC DRIVER ESCALATE EMAIL'


TD_NOTNOW = 'notnow'
TD_CHANGE = 'change'
TD_SAME = 'same'

#Overall Dashboard

OD_DELAYBUFF = '00:15:00'
PR_MASKCHECK = 'CALL MASKING OPTION'
SHOW_ROUTES_BUFFER_TIME=18000
#SHOW_ROUTES_BUFFER_TIME=10800

TD_PANICEMP = 'PANICEMP'
TD_PANICDRIVER = 'PANICDRIVER'
PR_ESCORT_ENABLE='ESCORT ENABLE'
PR_ESCORT_START_TIME='ESCORT START TIME'
PR_ESCORT_END_TIME='ESCORT END TIME'
RP_PERFORMANCE = 'performance'

TD_ESTIMATE_ENDTIME = 'ESTIMATE_END_TIME'
TD_ESTIMATE_STARTTIME = 'ESTIMATE_START_TIME'
TD_ROSTERPASSENGERID = 'ROSTER_PASSENGER_ID'
TD_ACTIVE = 'ACTIVE'
TD_ROSTERPASSENGERSTATUS = 'ROSTER_PASSENGER_STATUS'
TD_ROSTERID = 'ROSTER_ID'
TD_REASONID = 'REASON_ID'
TD_CREATEBY = 'CREATE_BY'
TD_CREATEDDATE = 'CREATED_DATE'
TD_EMPLOYEEID = 'EMPLOYEE_ID'
TD_ESCORTID = 'ESCORT_ID'
TD_STATUS = 'STATUS'
TD_BRANCHID = 'BRANCH_ID'
TD_CREATEDBY = 'CREATED_BY'
TD_createdat = 'created_at'
TD_ACTUALENDTIME = 'ACTUAL_END_TIME'
TD_ACTUALSTARTTIME = 'ACTUAL_START_TIME'
TD_ORIGINATOR = 'ORIGINATOR'
TD_RECIPIENT = 'RECIPIENT'
TD_MESSAGE = 'MESSAGE'
TD_SENTDATE = 'SENT_DATE'
TD_REFNO = 'REF_NO'
TD_VENDORID = 'VENDOR_ID'
TD_USERID = 'USER_ID'
TD_TRACKINGDASHBOARD = 'TRACKING DASHBOARD'
TD_ACTION = 'ACTION'
TD_PROCESSDATE = 'PROCESS_DATE'
TD_CATEGORY = 'CATEGORY'
TD_UPDATEDBY = 'UPDATED_BY'
TD_ROUTEORDER = 'ROUTE_ORDER'
TD_ROSTERSTATUS = 'ROSTER_STATUS'
TD_REMARKS = 'REMARKS'
TD_ROUTEID = 'ROUTE_ID'
TD_TRIPAPPROVEDKM = 'TRIP_APPROVED_KM'
TD_ROSTERALLOTTIME = 'ROSTER_ALLOT_TIME'
TD_CABCAPACITYCOUNT = 'CAB_CAPACITY_COUNT'
TD_ALLOTINROUTCOUNT = 'PASSENGER_ALLOT_IN_ROUT_COUNT'
TD_PASSENGERALLOTCOUNT = 'PASSENGER_ALLOT_COUNT'
TD_TRIPTYPE = 'TRIP_TYPE'
TD_CABALLOTTIME = 'CAB_ALLOT_TIME'
TD_FILEID = 'FILE_ID'
TD_PASSENGERCLUBINGCOUNT = 'PASSENGER_CLUBING_COUNT'
TD_CABID = 'CAB_ID'
TD_TOTALKM = 'TOTAL_KM'
TD_STARTLOCATION = 'START_LOCATION'
TD_ENDLOCATION = 'END_LOCATION'
TD_ACCEPTANCEREJECTSTATE = 'ACCEPTANCE_REJECT_STATE'
TD_ACCEPTANCEREJECTDATE = 'ACCEPTANCE_REJECT_DATE_TIME'
TD_REJECTREASONID = 'REJECT_REASON_ID'
TD_ROUTEESCORTID = 'ROUTE_ESCORT_ID'
TD_SMS_TAG = 'SMS TAG'
TD_VENDORNAME = 'VENDORNAME'
TD_VEHICLEREGNO = 'VEHICLE_REG_NO'
TD_MODEL = 'MODEL'
TD_REASON = 'REASON'
TD_DRIVERMOBILE = 'DRIVER_MOBILE'
TD_EMPCNT = 'EMPCNT'
TD_EMPNAME = 'EMPNAME'
TD_DRIVERSNAME = 'DRIVERS_NAME'
TD_CABNO = 'CABNO'
TD_BRANCHNAME = 'BRANCH_NAME'
TD_GENDER = 'GENDER'
TD_LOCATION_NAME = 'LOCATION_NAME'
TD_DRIVERARRIVALTIME = 'DRIVER_ARRIVAL_TIME'
TD_ADDRESS = 'ADDRESS'
TD_ACTIONREMARK = 'ACTION_REMARK'
TD_CAB_NO = 'CAB_NO'
TD_NOGPSFIX = 'No gps fixed'
TD_BLINKBUTTON = 'blinkbutton'
TD_DISTINCTREMARK = 'distinct_remark'
TD_VALUE = 'value'
TD_SELECTED = 'selected'

RP_CHECKLOGIN = 'login.loginView'
RP_MOBILE = 'MOBILE'

CS_FROMDATE = 'fromdate'
CS_TODATE = 'todate'

CC_ORIGINS = 'origins='
CC_ORIGIN = 'origin='
CC_DESTINATIONS = '&destinations='
CC_DESTINATION = '&destination='
CC_WAYPOINTS = '&waypoints='
CC_FALSESENSOR = '&sensor=false'
CC_DRIVINGMODE = '&mode=driving&alternatives=true'
CC_ELEMENTS = 'elements'
CC_DURATION = 'duration'
CC_DISTANCE = 'distance'
CC_ROUTES = 'routes'
RP_EMPADHOC='Adhoc'
RRD_SHED_DEFAULT_ENABLE='Y'
RRD_SHED_DEFAULT_KM='5'


PR_WEEKROSTERENABLE = 'WEEK_ROSTER_ENABLE_DATE'
PR_WEEKROSTERENABLEOPTION = 'WEEK_ROSTER_ENABLE_OPTION'
ES_CAB_FILTER='filter'
FANATICS_PICKUP_CHANGE_TIME=8
FANATICS_DROP_CHANGE_TIME=1

RRD_ACCESS_KEY='RRD.Zingo'

//RRD_CLIENT_SECRET='Zn&Zq8@Vm95ac2z$U&#)9'
RRD_CLIENT_SECRET='GZxc89$@!je74w&(5DHm$&'

//RRD_CLIENT_CODE='CL06002'
RRD_CLIENT_CODE='CL01005'

//RRD_TOKEN_GENERATION_URL='https://uatmyc-ext-api.rrd.com/EmployeeAPIService/Authentication/GetAuthenticationToken'
RRD_TOKEN_GENERATION_URL='https://myc-ext-api.rrd.com/EmployeeAPIService/Authentication/GetAuthenticationToken'

//RRD_MIS_URL='https://uatmyc-ext-api.rrd.com/EmployeeAPIService/ZingoCabEmployee/AddCabMISDetails'
RRD_MIS_URL='https://myc-ext-api.rrd.com/EmployeeAPIService/ZingoCabEmployee/AddCabMISDetails'

//RRD_OVERALL_URL='https://uatmyc-ext-api.rrd.com/EmployeeAPIService/ZingoCabEmployee/AddCabRosterDetails'
RRD_OVERALL_URL='https://myc-ext-api.rrd.com/EmployeeAPIService/ZingoCabEmployee/AddCabRosterDetails'