<?php

namespace App\Services\Elastic;
use App\Facades\Elasticsearch;
use Illuminate\Pagination\LengthAwarePaginator;



use Elasticsearch\ClientBuilder;

class ElasticSearchCount {

    protected $client;

    public function __construct() {
//        $this->client = ClientBuilder::create()->build();
        $ES_HOST = env('ES_HOST');        
        $this->client = ClientBuilder::create()->setHosts(['hosts' => $ES_HOST])->build();
    }

    public function getCount($query) {
        $items = $this->client->search($query);
        $count = $items['aggregations'];
        return $count;
    }

    public function setSpeedUpdate($query) {
        $items = $this->client->update($query);
        return $items;
    }

    //***********Common Group count for speed and deviation*********
    public function getCountGroup($query) {
        $items = $this->client->search($query);
        //print_r($items);
        $speedCountResponse = array();
        $result = array();
        
        for ($i = 0; $i < count($items['aggregations']['top-uids']['buckets']); $i++) {
            $speedCountResponse = array("SpeedCount" => $items['aggregations']['top-uids']['buckets'][$i]['doc_count']);
            $speedResponse = $items['aggregations']['top-uids']['buckets'][$i]['top_uids_hits']['hits']['hits'][0]['_source'];
            $result[$i] = array_merge($speedCountResponse, $speedResponse);
        }
        return $result;
    }
    
    public function getEmpDistanceGroup($query) {
        $items = $this->client->search($query); 

       // $result = array();        
        for ($i = 0; $i < count($items['aggregations']['top-uids']['buckets']); $i++) {            
            $result[]= $items['aggregations']['top-uids']['buckets'][$i]['top_uids_hits']['hits']['hits'][0]['_source'];
        }
        return $result;
    }

    public function getData($query) {
        $items = $this->client->search($query);		
        if (0 < count($items['hits']['hits'])) {
            for ($i = 0; $i < count($items['hits']['hits']); $i++) {
                $Response[] = $items['hits']['hits'][$i]['_source'];
            }
        } else {
            $Response = 'No Record';
        }
        return $Response;
    }
	
	 public function getDataPagination(array $query, $page, $perPage,$orderBy,$order)
    {
        try {
            $items = $this->client->count($this->countQuery($query));
            $total = $items['count'];


            $items = $this->client->search($query);


            $Response = [];
            if (!empty($items['hits']['hits'])) {
                foreach ($items['hits']['hits'] as $hit) {
                    $Response[] = $hit['_source'];
                }
            }

            $paginated = new LengthAwarePaginator(
                $Response,
                $total,
                $perPage,
                $page,
                ['path' => request()->url(), 'query' => request()->query()]
            );

          return $paginated;

        } catch (\Throwable $e) {
            \Log::error('Elasticsearch Pagination Error', ['message' => $e->getMessage()]);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Elasticsearch pagination failed',
                'validation_controller' => true,
                'error' => $e->getMessage()
            ], 500);
        }
    }
	
	 public function countQuery(array $searchQuery)
    {

        if (isset($searchQuery['size'])) {
            unset($searchQuery['size']);
        }
        if (isset($searchQuery['from'])) {
            unset($searchQuery['from']);
        }

        if (isset($searchQuery['body']['sort'])) {
            unset($searchQuery['body']['sort']);
        }

        return $searchQuery;
    }

    public function getCabIdleData($query) {
        $items = $this->client->search($query);
        if (0 < count($items['hits']['hits'])) {
            for ($i = 0; $i < count($items['hits']['hits']); $i++) {
                $id=array();
                $Response=array();
                $id[] = $items['hits']['hits'][$i]['_id'];
                $Response[] = $items['hits']['hits'][$i]['_source'];
                $result[$i] = array_merge($id, $Response);
            }
        } else {
            $result = 'No Record';
        }
        return $result;
    }

    //***********Cab notlive Update*********
   public function cabNotLiveUpdate(array $idArr) {              
        $elasticAcc = new \App\Services\Elastic\ElasticAccess;       
        for ($i = 0; $i < count($idArr); $i++) {             
            $id = $idArr[$i];            
            $params['body'][] = $elasticAcc->updateIndex(env('ES_INDEX'), env('ES_TYPE_CAB_LIVE'), $id);
            $params['body'][] = $elasticAcc->cabLiveUpdateRecord(env('ES_SENT'));
        }
        $updateResponse = $this->client->bulk($params);   
        print_r($updateResponse);      
    }

    //***********Common Update for speed and deviation*********
    public function updateGroup($type, $query, $reason_id, $remark) {
        $elasticAcc = new \App\Services\Elastic\ElasticAccess;
        $items = $this->client->search($query);
        if (0 < count($items['hits']['hits'])) {
            for ($i = 0; $i < count($items['hits']['hits']); $i++) {
                $params['body'][] = $elasticAcc->updateIndex(env('ES_INDEX'), $type, $items['hits']['hits'][$i]['_id']);
                $params['body'][] = $elasticAcc->updateRecord($reason_id, $remark);
            }
            $updateResponse = $this->client->bulk($params);
            $rcnt = count($updateResponse['items']);
        } else {
            $updateResponse[] = 'No Record';
        }
        return $updateResponse;
    }

    //***********Gps Location Insert*********
    public function gpsLocationInsert(array $locationArr) {
        $elasticAcc = new \App\Elastic\ElasticAccess;
        foreach ($locationArr as $arr) {
            $gpsaddress = $arr['LOCATION'];
            $latitude = $arr['LATITUDE'];
            $longitude = $arr['LONGITUDE'];
            $params['body'][] = $elasticAcc->eindex(env('ES_INDEX'), env('ES_TYPE_GOOGLE_ADDR'));
            $params['body'][] = $elasticAcc->gpsLocation($latitude, $longitude, $gpsaddress);
        }
        $response = $this->client->bulk($params);
        $rcnt = count($response['items']);
    }

    //***********Shuttle Path Insert*********
    public function pathInsert(array $pathArr) {
        $elasticAcc = new \App\Elastic\ElasticAccess;
        foreach ($pathArr as $arr) {
            $route_id = $arr['Route_id'];
            $branch_id = $arr['Branch_id'];
            $position = $arr['Position'];
            $arrivel_time = $arr['Duration'];
            $route_date = $arr['Route_Date'];
            $params['body'][] = $elasticAcc->eindex(env('ES_INDEX'), env('ES_TYPE_ROUTE_PATH'));
            $params['body'][] = $elasticAcc->routePath($route_id, $branch_id, $position, $arrivel_time,$route_date);
        }
        $response = $this->client->bulk($params);
        $rcnt = count($response['items']);
        if ($rcnt == 0) {
            return $element_arr = array("Path" => "False");
        } else {
            return $element_arr = array("Path" => "True");
        }
    }
    
    
    //**********Route Path Data****************
    public function getPathData($query) {
        $items = $this->client->search($query);
        if (0 < count($items['hits']['hits'])) {
            for ($i = 0; $i < count($items['hits']['hits']); $i++) {
                $id[] = $items['hits']['hits'][$i]['_id'];
                $Response[] = $items['hits']['hits'][$i]['_source'];
                $result[$i] = array_merge($id, $Response);
            }
        } else {
            $result = 'No Record';
        }
        return $result;
    }
    
    
    //**********Driver Alert Message Insert****************
    public function driverMsgInsert($roster_id,$branch_id, $vendor_id,$cab_id, $action,$msg,$status,$speed_interval,
            $request_time,$acknowledge_time) {
        $elasticAcc = new \App\Elastic\ElasticAccess;       
            $params['body'][] = $elasticAcc->eindex(env('ES_INDEX'), env('ES_TYPE_DRIVER_MSG'));
            $params['body'][] = $elasticAcc->driverMsg($roster_id,$branch_id, $vendor_id,$cab_id, $action,$msg,$status,$speed_interval,
            $request_time,$acknowledge_time);       
        $response = $this->client->bulk($params);
        $rcnt = count($response['items']);
        if ($rcnt == 0) {
            return $element_arr = array("Status" => "False");
        } else {
            return $element_arr = array("Status" => "True");
        }
    }

    
    //**********Web Log Insert****************
    public function webLogInsert($log_array) {

        $elasticAcc = new \App\Elastic\ElasticAccess;       
            $params['body'][] = $elasticAcc->eindex(env('ES_INDEX'), env('ES_TYPE_WEB_LOG'));
            $params['body'][] = $log_array;    
		
        $response = $this->client->bulk($params);
		// print_r($response);	exit;
	    $rcnt = count($response['items']);
        if ($rcnt == 0) {
            return $element_arr = array("Status" => "False");
        } else {
            return $element_arr = array("Status" => "True");
        }
    }
    
    //********Get distance base on From-To GPS time******* 
    public function getDistance($query) {
        $items = $this->client->search($query);
        $distance = $items['aggregations']['return']['value'];
        return $distance;
    }
	
	
	 //********Get max speed base on From-To GPS time******* 
    public function getMaxSpeed($query) {
        $items = $this->client->search($query);
		$speed = $items['aggregations']['max_speed']['value'];
        return $speed;
    }
	
	
	//**********Get ID Cab Live Status Data****************
    public function getCabLiveStatusId($query) {
        try {
            $items = $this->client->search($query);
			
            if (0 < count($items['hits']['hits'])) {               
                    $result= $items['hits']['hits'][0]['_id']; 
            } else {
                $result = '0';
            }
            return $result;
        } catch (\Exception $e) {
            // Everything else
        }
    }
	
	
	//***********Cab Login Status Update*********
    public function cabLoginStatusUpdate($id) {
        $elasticAcc = new \App\Elastic\ElasticAccess;        
            $params['body'][] = $elasticAcc->updateIndex(env('ES_INDEX'), env('ES_TYPE_CAB_LIVE'),$id);
            $params['body'][] = $elasticAcc->cabUpdateLoginStatus(env('ES_LOGOUT'));
        try {
            $updateResponse = $this->client->bulk($params);
			
            $rcnt = count($updateResponse['items']);
            return $rcnt;
        } catch (\Exception $e) {
            // Everything else
        }
        // print_r($updateResponse);      
    }
	 public function cabMappingDeactiveStatus($id) {
        $elasticAcc = new \App\Elastic\ElasticAccess;        
            $params['body'][] = $elasticAcc->updateIndex(env('ES_INDEX'), env('ES_TYPE_CAB_LIVE'),$id);
            $params['body'][] = $elasticAcc->cabMappingDeactiveStatus(env('ES_CAB_DEACTIVE'));
        try {
            $updateResponse = $this->client->bulk($params);
			
            $rcnt = count($updateResponse['items']);
            return $rcnt;
        } catch (\Exception $e) {
           
        }
            
    }
	public function close_onduty($id) {
        $elasticAcc = new \App\Elastic\ElasticAccess;        
            $params['body'][] = $elasticAcc->updateIndex(env('ES_INDEX'), env('ES_TYPE_CAB_LIVE'),$id);
            $params['body'][] = $elasticAcc->close_onduty(env('ES_CAB_DEACTIVE'));
        try {
            $updateResponse = $this->client->bulk($params);
			
            $rcnt = count($updateResponse['items']);
            return $rcnt;
        } catch (\Exception $e) {
           
        }
            
    }

}
