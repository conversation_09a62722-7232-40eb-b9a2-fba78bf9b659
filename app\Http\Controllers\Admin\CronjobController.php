<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Services\Admin\CronjobService;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class CronjobController extends Controller
{
    protected CronjobService $cronjobservice;

    public function __construct(CronjobService $cronjobservice)
    {
        $this->cronjobservice = $cronjobservice;
    }

    public function mask_channel(): FoundationApplication|Response|ResponseFactory
    {
        return $this->cronjobservice->mask_channel();
    }

    public function update_secondary_address(): FoundationApplication|Response|ResponseFactory
    {
        return $this->cronjobservice->update_secondary_address();
    }

    public function masknumber_new(): FoundationApplication|Response|ResponseFactory
    {
        return $this->cronjobservice->masknumber();
    }

    public function update_tollpayment(): FoundationApplication|Response|ResponseFactory
    {
        return $this->cronjobservice->update_tollpayment();
    }

    public function AddCabRosterDetails():FoundationApplication|Response|ResponseFactory
    {
        return $this->cronjobservice->AddCabRosterDetails();
    }

    public function AddCabMISDetails():FoundationApplication|Response|ResponseFactory
    {
        return $this->cronjobservice->AddCabMISDetails();
    }
}
