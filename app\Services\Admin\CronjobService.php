<?php
namespace App\Services\Admin;

use App\Helpers\CommonFunction;
use App\Helpers\MyHelper;
use App\Models\AccessTokens;
use App\Models\Branch;
use App\Models\property;
use App\Models\ReportAPIFiles;
use App\Models\Sms;
use Carbon\Carbon;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\ElasticController;

class CronjobService
{
    protected CommonFunction $commonFunction;
    protected ?Branch $currentBranch = null;
    protected $TRIPCLOSE;
    protected $TTLTYPE;
    protected $ALLREPORT;
    protected $MANUALTRIPCLOSE;

    public function __construct(CommonFunction $commonFunction)
    {
        $this->commonFunction = $commonFunction;
        $this->TTLTYPE = env('RP_TTLTYPE');
        $this->ALLREPORT = env('RP_ALLREPORT');
        $this->TRIPCLOSE = env('RS_TRIPCLOSE');
        $this->MANUALTRIPCLOSE = env('RS_MANUALTRIPCLOSE');
    }

    public function mask_channel(): FoundationApplication|Response|ResponseFactory
    {
        try{  
            $ch = curl_init('http://**************/aster-dialer-ntl/services/live_calls.php');
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            $result = curl_exec($ch);
            
            curl_close($ch);						
            $decode_result=JSON_DECODE($result,true);					       
            $status=$decode_result['status'];
            
            DB::insert("insert into CHANNEL_RESPONSE (CHANNEL_RESPONSE,CREATED_AT,UPDATED_AT) values('".str_replace("'", "\'", $result)."','".date("Y-m-d H:i:s")."','".date("Y-m-d H:i:s")."')");
            
            if($status=='200')
            {
                $calls_count=$decode_result['data']['calls'];
                DB::update("update MASK_CHANNEL set CHANNEL_COUNT='".$calls_count."',UPDATED_AT='".date("Y-m-d H:i:s")."' where CHANNEL_ID=1 ");
            }  
            
            return response([
                'success' => true,
                'status' => 1,               
		        'message' => 'Mask Channel Successfully',
            ], 200);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Mask Channel Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ],500);
        }
    }

    public function update_secondary_address() : FoundationApplication|Response|ResponseFactory
    {
        try {
            $date=date("Y-m-d");
				
            $res=DB::select("SELECT E.*,ED.SEC_ADDR_DURATION,ED.ADDRESS_ID,ED.EMP_AUTO_ID from employees E 
                inner join employee_address ED on ED.EMP_AUTO_ID=E.id
                where E.BRANCH_ID=32 and E.ACTIVE=1 and E.ADDRESS_TYPE='S' and SEC_ADDR_DURATION <='".$date."' and ED.ACTIVE=1 order by E.id ASC ");
                
            if(count($res)>0)
            {
                foreach($res as $key=>$val)
                {
                    $id=$val->id;
                    echo "test ".$ADDRESS_ID=$val->ADDRESS_ID;
                    DB::update("update employees set ADDRESS_TYPE='P',updated_at='".date("Y-m-d H:i:s")."' where id='".$id."'");
                    DB::update("update employee_address set ACTIVE='2',UPDATED_AT='".date("Y-m-d H:i:s")."',UPDATED_REMARKS='CRON UPDATED' where ADDRESS_ID='".$ADDRESS_ID."'");
                }
            }

            return response([
                'success' => true,
                'status' => 1,               
		        'message' => 'Update Secondary Address Successfully',
            ], 200);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Update Secondary Address Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ],500);
        }
    }

    public function masknumber() {
        try {
            ini_set("max_execution_time",0);
            
            $RS_TOTALACCEPT = env('RS_TOTALACCEPT');
            $AES_KEY = env("AES_ENCRYPT_KEY");
            $RS_ACTIVE = env('RS_ACTIVE');
            
            $mask_branch_datas = property::where([
                                ['PROPERTIE_NAME', '=', 'CALL MASKING OPTION'],
                                ['PROPERTIE_VALUE', '=', 'Y'],
                                //['BRANCH_ID', '=', '18'],
                                //['BRANCH_ID', '=', '32'],
                                ['ACTIVE', '=', '1'],
                                ])->get();
            $gender_type='';
            $passanger_sms_arr =array();
            
            for ($i = 0; $i < count($mask_branch_datas); $i++) {
                
                $branch_code = $mask_branch_datas[$i]['BRANCH_ID'];
                $SMS_TAG = '';
                $HELP_LINE = '';
                $SMS_datas = property::where([
                                ['BRANCH_ID', '=', $branch_code],
                                ['ACTIVE', '=', '1'],
                                ])->get();
                for($j = 0; $j < count($SMS_datas); $j++) {
                    $property_name = $SMS_datas[$j]['PROPERTIE_NAME'];
                    $property_value = $SMS_datas[$j]['PROPERTIE_VALUE'];
                    
                    switch ($property_name) {
                        
                        case "SMS TAG":
                        $SMS_TAG = $property_value;
                        break;
                        case "HELPLINE NO":
                        $HELP_LINE = $property_value;
                        break;
                        case "CALL MASKING OPTION":
                        $masking_option = $property_value;
                        break;
                        case "MASKING GENDER TYPE":
                        $gender_type = $property_value;
                        break;
                        case "MASKING TRIPTYPE":
                        $masking_trip_type = $property_value;
                        break;
                        case 'SMS_LAST_TAG':
                        $SMS_LAST_TAG = $property_value;
                        break;
                        default:
                        break;
                        
                    } 
                }

                if($gender_type=="B"){
                    $gender_cond=''; //M & F
                } elseif($gender_type=="M" || $gender_type=="F") {
                    $gender_cond="and E.GENDER in('".$gender_type."')"; // M or F
                }
                $cn = 0;
                
                $masking_triptype="";
                $drvMobile='';
                $SENDSMSTIME=date("Y-m-d H:i:s");
                                
                if($masking_option=='Y')
                {
                    $curdatetime = date('Y-m-d H:i:s');
                    $predatetime = date('Y-m-d H:i:s',strtotime('-4 hours',strtotime($curdatetime)));
                    $nextdatetime = date('Y-m-d H:i:s',strtotime('+10 hours',strtotime($curdatetime)));
                    
                    $masking_triptype=$masking_trip_type=='B'?' ':'AND  R.TRIP_TYPE IN ("'.$masking_trip_type.'")';
                    $roster_ids = "SELECT R.TRIP_TYPE,D.DRIVERS_ID,D.DRIVER_MOBILE,B.BRANCH_NAME,R.ESTIMATE_END_TIME,
                    if(R.TRIP_TYPE = 'P',R.ESTIMATE_END_TIME,R.ESTIMATE_START_TIME) as LOGIN_DATE_TIME,
                    if(R.TRIP_TYPE = 'P',TIME(R.ESTIMATE_END_TIME),TIME(R.ESTIMATE_START_TIME)) as LOGIN_TIME,
                    if(R.TRIP_TYPE = 'P',R.ESTIMATE_END_TIME,R.ESTIMATE_START_TIME) as END_TIME,R.TRIP_TYPE,R.ROSTER_STATUS,
                    R.MASK_ASSIGN_TIME,R.CAB_ALLOT_TIME,R.ROSTER_ID,R.CAB_ID,R.ACTUAL_START_TIME FROM rosters R
                    INNER JOIN branch B ON B.BRANCH_ID = R.BRANCH_ID 
                    INNER JOIN cab C ON C.CAB_ID = R.CAB_ID 
                    INNER JOIN drivers D ON  D.DRIVERS_ID = C.DRIVER_ID 
                    inner join roster_passengers RP on RP.ROSTER_ID=R.ROSTER_ID				
                    WHERE R.BRANCH_ID='$branch_code' AND R.ACTIVE = '1' AND R.CAB_ID IS NOT NULL 
                    AND if(R.TRIP_TYPE='P',R.ESTIMATE_END_TIME >='".$predatetime."' && R.ESTIMATE_END_TIME <='".$nextdatetime."',R.ESTIMATE_START_TIME >='".$predatetime."' && R.ESTIMATE_START_TIME <='".$nextdatetime."' )	".$masking_triptype." 
                    and R.ROSTER_STATUS not in(8193,8205,8283,8221,8281,8201,8225,8285,8195,17497,17499,17501,17503,17417,17419,17421,17423,1024,1113,1115,1117,1119,1033,1035,1037,1039,1049,1051,1053,857,859,861,863,345,347,349,351)  and R.MASK_ASSIGN_STATUS=0 and R.ACTUAL_START_TIME is not null
                    
                    group by R.ROSTER_ID  ORDER BY R.ROSTER_ID";	
                    
                    
                    $roster_id_array = DB::select($roster_ids);                                              
                    $post_arr=array();
                    $passanger_sms_arr =array();
                    
                        $cnt=count($roster_id_array);
                        $rp_id_arr=array();
                                            
                    for ($k = 0; $k < $cnt; $k++){
                        $driverid = $roster_id_array[$k]->DRIVERS_ID;
                        $drvMobile = $roster_id_array[$k]->DRIVER_MOBILE;
                        $endTime = $roster_id_array[$k]->END_TIME;
                        $cabid = $roster_id_array[$k]->CAB_ID;
                        $intime = $roster_id_array[$k]->LOGIN_TIME;
                        $roster_id = $roster_id_array[$k]->ROSTER_ID;
                        $trip_type = $roster_id_array[$k]->TRIP_TYPE;						
                        $login_date_time = $roster_id_array[$k]->LOGIN_DATE_TIME;
                        $estimate_end_time = $roster_id_array[$k]->ESTIMATE_END_TIME;
                        $ACTUAL_START_TIME = $roster_id_array[$k]->ACTUAL_START_TIME;
                        if($trip_type=='D'){
                            $drop_buffer = date('Y-m-d H:i:s',strtotime('+45 minutes',strtotime($login_date_time)));
                        } else {
                            $drop_buffer = date('Y-m-d H:i:s',strtotime('+30 minutes',strtotime($login_date_time)));
                        }
                        
                        if(strtotime($curdatetime) <  strtotime($login_date_time)){
                            $starttime=$ACTUAL_START_TIME;
                        } else {
                            $starttime=date('Y-m-d H:i:s',strtotime('+10 min',strtotime($curdatetime)));
                        }
                        
                        $passeng_det="select RP.ROSTER_PASSENGER_ID,RP.ROSTER_PASSENGER_STATUS,RP.PASSENGER_MASK_NUMBER,E.`NAME` as EMPLOYEE_NAME,E.GENDER,E.MOBILE as EMPLOYEES_MOBILE,E.MOBILE_CATEGORY,E.MOBILE_GCM as MOBILE_GCM,E.EMPLOYEES_ID from roster_passengers RP
                            inner join employees E ON E.EMPLOYEES_ID=RP.EMPLOYEE_ID and E.BRANCH_ID='".$branch_code."'						
                            where RP.ROSTER_ID='".$roster_id."' and RP.ROSTER_PASSENGER_STATUS not in(16,17,19,21,23,29,31,85,37,39,45,47,101,103,69,71,109,111,133,135,141,143,229,231,237,239,197,199) and E.BRANCH_ID='$branch_code' and RP.ACTIVE=1 ".$gender_cond." order by RP.ROSTER_PASSENGER_ID";						
                        $passeng_det_array = DB::select($passeng_det);
                        $cust_mob_arr=array();
                                                
                        if(count($passeng_det_array)>0){
                            for($kk=0;$kk<count($passeng_det_array);$kk++){
                                $rp_id=$passeng_det_array[$kk]->ROSTER_PASSENGER_ID;
                                $cus_mob =$this->AES_DECRYPT($passeng_det_array[$kk]->EMPLOYEES_MOBILE, $AES_KEY);
                                array_push($cust_mob_arr,$cus_mob);
                                array_push($rp_id_arr,$rp_id);
                                
                                $res=$this->Clear_MaskNumber($rp_id,$branch_code,$roster_id,'Passenger');
                            }
                        
                            $post_arr[]=array("bookingid"=>$roster_id,"driverid"=>$cabid,"drivernumber"=>$drvMobile,"customernumbers"=>$cust_mob_arr,"starttime"=>$starttime,"endtime"=>$drop_buffer,"bookingtype"=>$trip_type,"branchcode"=>$branch_code,"companycode"=>$branch_code,"api_key"=>"7e4d7bf081f8286447ac383e25588c7f");
                        }
                    }
                                        
                    $post_data=json_encode($post_arr);
                                        
                    if(is_array($post_arr) && count($post_arr) > 0)
                    {
                        $ch = curl_init('http://**************/aster-dialer-ntl/services/createbooking.php');
                        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                        curl_setopt($ch, CURLINFO_HEADER_OUT, true);
                        curl_setopt($ch, CURLOPT_POST, true);
                        curl_setopt($ch, CURLOPT_POSTFIELDS, $post_data);
                        
                        // Set HTTP Header for POST request 
                        curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));
                        
                        // Submit the POST request
                        $result = curl_exec($ch); 
                        print_r($result);
                        // Close cURL session handle
                        curl_close($ch);						
                        $decode_result=JSON_DECODE($result);					       
                        /*echo "result count ".  */
                        $ins="insert into MASKING_RESPONSE (MASKING_REQUEST,MASKING_RESPONSE,CREATED_DATE_TIME,BRANCH_ID) values('".str_replace("'", "\'",$post_data)."','".str_replace("'", "\'", $result)."','".date("Y-m-d H:i:s")."','".$branch_code."')";
                        DB::insert($ins);
                        echo "res".$decode_result_cnt=count($decode_result);
                        
                        if($decode_result_cnt > 0)
                        {
                            $data_status=$decode_result->status;
                            $result_status_resp=$decode_result->Response;
                            
                            if($result_status_resp=='200' && $data_status =='SUCCESS')
                            { 
                                $customer_det=count($decode_result->values);
                                                                    $customer_det_json=$decode_result->values;
                                
                                print_r($customer_det_json);
                                $roster_arr=array();
                                for($c=0;$c<$customer_det;$c++)
                                {
                                                                        
                                                                        
                                    echo "did".$did=$customer_det_json[$c]->did_no;
                                    echo "<br>";
                                    echo "booking id==".$bookingid=$customer_det_json[$c]->booking_id; 

                                        if(strlen($customer_det_json[$c]->did_no) > 10){
                                            $mask_no="0".substr($customer_det_json[$c]->did_no,2);
                                        }else{
                                            $mask_no="0".$customer_det_json[$c]->did_no;
                                        } 
                                    
                                    array_push($roster_arr,$bookingid);
                                    
                                    $update1="update roster_passengers set PASSENGER_MASK_NUMBER='".$mask_no."' where ROSTER_ID='$bookingid' and  ROSTER_PASSENGER_ID='".$rp_id_arr[$c]."' ";
                                    $update=DB::update($update1);
                                }									
                                /* sms */
                                $roster_arr_str= implode(",", $roster_arr);
                                $update_roster="update rosters set MASK_ASSIGN_STATUS=1 where ROSTER_ID in($roster_arr_str) ";
                                DB::update($update_roster);
                                //exit;
                                $sms_det="SELECT R.TRIP_TYPE,V.VEHICLE_REG_NO,C.CAB_ID,R.ESTIMATE_END_TIME,
                                if(R.TRIP_TYPE = 'P',R.ESTIMATE_END_TIME,DATE_ADD(R.ESTIMATE_START_TIME,INTERVAL 30 MINUTE)) as LOGIN_DATE_TIME,
                                if(R.TRIP_TYPE = 'P',TIME(R.ESTIMATE_END_TIME),TIME(R.ESTIMATE_START_TIME)) as LOGIN_TIME,
                                if(R.TRIP_TYPE = 'P',R.ESTIMATE_END_TIME,R.ESTIMATE_START_TIME) as END_TIME,R.TRIP_TYPE,R.ROSTER_STATUS,
                                R.MASK_ASSIGN_TIME,R.CAB_ALLOT_TIME,R.ROSTER_ID,RP.PASSENGER_MASK_NUMBER,R.DRIVER_MASK_NUMBER,E.`NAME` as EMPLOYEE_NAME,R.ACTUAL_START_TIME,
                                if(R.TRIP_TYPE='P',RP.ESTIMATE_START_TIME,R.ESTIMATE_START_TIME) as pickup_time,E.MOBILE as EMPLOYEES_MOBILE,O.OTP,D.DRIVER_MOBILE,D.DRIVERS_NAME,L.LOCATION_NAME,VM.MODEL,RP.ROSTER_PASSENGER_ID,R.ROUTE_ID 
                                FROM rosters R
                                inner join roster_passengers RP ON RP.ROSTER_ID=R.ROSTER_ID 
                                inner join employees E ON E.EMPLOYEES_ID=RP.EMPLOYEE_ID and E.BRANCH_ID='".$branch_code."' 
                                inner join otp_verification O ON O.ROSTER_PASSENGER_ID=RP.ROSTER_PASSENGER_ID and O.VERIFIED_STATUS=0 
                                INNER JOIN cab C ON C.CAB_ID = R.CAB_ID 
                                INNER JOIN vehicles V ON V.VEHICLE_ID = C.VEHICLE_ID 
                                INNER JOIN drivers D ON  D.DRIVERS_ID = C.DRIVER_ID 
                                                                    INNER JOIN locations L ON L.LOCATION_ID=RP.LOCATION_ID 
                                                                    INNER JOIN vehicle_models as VM ON VM.VEHICLE_MODEL_ID=V.VEHICLE_MODEL_ID 
                                WHERE R.BRANCH_ID='".$branch_code."' AND RP.ACTIVE = '1' and R.ROSTER_ID in($roster_arr_str)";
                                
                                $sms_result=DB::select($sms_det);
                                $passanger_sms_arr =array();

                                for($ss=0;$ss<count($sms_result);$ss++)
                                {
                                    $emp_mob = $this->AES_DECRYPT($sms_result[$ss]->EMPLOYEES_MOBILE, $AES_KEY);
                                    $emp_name = $this->AES_DECRYPT($sms_result[$ss]->EMPLOYEE_NAME, $AES_KEY);
                                    $empName=substr(str_replace(' ', '', $emp_name), 0, 5);

                                    $TRIP_TYPE = $sms_result[$ss]->TRIP_TYPE;
                                    $cabno = $sms_result[$ss]->VEHICLE_REG_NO;
                                    $pickupTime = $sms_result[$ss]->pickup_time;
                                    $otp = $sms_result[$ss]->OTP;
                                    $drvDID = $sms_result[$ss]->DRIVER_MASK_NUMBER;
                                    $passenger_mask_number = $sms_result[$ss]->PASSENGER_MASK_NUMBER;
                                    $DRIVER_MOBILE = $sms_result[$ss]->DRIVER_MOBILE;
                                    $LOCATION_NAME=$sms_result[$ss]->LOCATION_NAME;
                                    $DRIVERS_NAME=$sms_result[$ss]->DRIVERS_NAME;
                                    $MODEL=$sms_result[$ss]->MODEL;
                                    $ROSTER_ID=$sms_result[$ss]->ROSTER_ID;
                                    $ACTUAL_START_TIME=$sms_result[$ss]->ACTUAL_START_TIME;
                                    $LOGIN_DATE_TIME=$sms_result[$ss]->LOGIN_DATE_TIME;
                                    $ROSTER_PASSENGER_ID=$sms_result[$ss]->ROSTER_PASSENGER_ID;
                                    $ROUTE_ID=$sms_result[$ss]->ROUTE_ID;
                                    if($passenger_mask_number!='--') {
                                        $insert_mask="insert into MASK_TRANSACTION (ROSTER_ID,MASK_DID_NUMBER,START_TIME,END_TIME,STATUS,CREATE_DATE,ROSTER_PASSENGER_ID,BRANCH_ID,AVAILABLITY_STATUS) values('".$ROSTER_ID."','".$passenger_mask_number."','".$ACTUAL_START_TIME."','".$LOGIN_DATE_TIME."',1,'".date("Y-m-d H:i:s")."','".$ROSTER_PASSENGER_ID."','".$branch_code."','2')";
                                        DB::insert($insert_mask);
                                        
                                        $update_master="update MASK_DID_MASTER set AVAILABLITY_STATUS='2',UPDATED_AT='".date("Y-m-d H:i:s")."' where DID_NUMBER='".$passenger_mask_number."' and STATUS=1";
                                        DB::update($update_master);
                                    }
                                    
                                    if($trip_type=="P" ){
                                        if($passenger_mask_number=='--')
                                        {
                                            if($branch_code==32){
                                                $message="Hi $empName, your estimated pickup time on ".date('d/m/Y',strtotime($pickupTime))." is ".date('H:i',strtotime($pickupTime)).". Cab $cabno-$passenger_mask_number, OTP - $otp. Helpline - $HELP_LINE.$SMS_LAST_TAG";
                                            } else {
                                                $message="Hi ,Your estimated pickup is ".date('d/m/Y',strtotime($pickupTime))." ".date('H:i',strtotime($pickupTime)).".Cab detail $cabno-$passenger_mask_number, OTP - $otp. Helpline - $HELP_LINE.$SMS_LAST_TAG";
                                            }
                                        }
                                        else
                                        {
                                            if($branch_code==32){
                                                $message="Hi $empName, your estimated pickup time on ".date('d/m/Y',strtotime($pickupTime))." is ".date('H:i',strtotime($pickupTime)).". Cab $cabno-$DRIVER_MOBILE, OTP - $otp. Helpline - $HELP_LINE.$SMS_LAST_TAG";
                                            } else {
                                                $message="Hi ,Your estimated pickup is ".date('d/m/Y',strtotime($pickupTime))." ".date('H:i',strtotime($pickupTime)).".Cab detail $cabno-$DRIVER_MOBILE, OTP - $otp. Helpline - $HELP_LINE.$SMS_LAST_TAG";
                                            }	
                                        }
                                    } elseif($trip_type=='D') {
                                        if($passenger_mask_number=='--'){
                                            $message="Hi $empName, your drop to $LOCATION_NAME is scheduled for ".date('H:i',strtotime($pickupTime))." on ".date('d/m/Y',strtotime($pickupTime))." Cab $cabno-$DRIVER_MOBILE and your Route no. is $ROUTE_ID. OTP - $otp. Reporting time ".date('H:i',strtotime($pickupTime)).". Helpline - $HELP_LINE.$SMS_LAST_TAG";
                                        }
                                        else{
                                            $message="Hi $empName, your drop to $LOCATION_NAME is scheduled for ".date('H:i',strtotime($pickupTime))." on ".date('d/m/Y',strtotime($pickupTime))." Cab $cabno-$passenger_mask_number and your Route no. is $ROUTE_ID. OTP - $otp. Reporting time ".date('H:i',strtotime($pickupTime)).". Helpline - $HELP_LINE.$SMS_LAST_TAG";
                                        }
                                    }
                                    
                                    $passanger_sms_arr[] = array("BRANCH_ID" => $branch_code,
                                    "ORIGINATOR" => $SMS_TAG,
                                    "RECIPIENT" => $emp_mob,
                                    "MESSAGE" => $message,
                                    "STATUS" => 'U',
                                    "SENT_DATE" => '1900-01-01 00:00:00',
                                    "REF_NO" => '--',
                                    "CREATED_BY" => '0',                         
                                    "CATEGORY" => 'MASKING',                         
                                    "CREATED_DATE" => $SENDSMSTIME);
                                }
                                
                                Sms::insert($passanger_sms_arr);
                                $passanger_sms_arr =array();
                            } else {
                                echo "Booking Failed!";
                            }
                        }
                    }
                    else
                    {
                        echo "Empty Array";
                    }
                    
                } 
            }
        }  catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Mask Number Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ],500);
        }
    }

    public function AES_DECRYPT($value, $secret) {
        return rtrim(
        mcrypt_decrypt(
        MCRYPT_RIJNDAEL_256, $secret, base64_decode($value), MCRYPT_MODE_ECB, mcrypt_create_iv(
        mcrypt_get_iv_size(
        MCRYPT_RIJNDAEL_256, MCRYPT_MODE_ECB
        ), MCRYPT_RAND
        )
        ), "\0"
        );
    }

    public function Clear_MaskNumber($roster_passenger_id,$branch_id,$roster_id,$r_or_p) {
        try {
            $AES_KEY = env("AES_ENCRYPT_KEY");
            $roster_id_val=explode(',',$roster_id);
            //and R.MASK_ASSIGN_STATUS='1' and RP.PASSENGER_MASK_NUMBER!='--' and RP.ACTIVE in(1,3)"
            $cond=$r_or_p=="Passenger"?"RP.ROSTER_PASSENGER_ID=".$roster_passenger_id."":"R.ROSTER_ID=".$roster_id." ";
            
                $passeng_det="select RP.ROSTER_PASSENGER_ID,RP.ROSTER_PASSENGER_STATUS,RP.PASSENGER_MASK_NUMBER,E.`NAME` as EMPLOYEE_NAME,E.GENDER,E.MOBILE,E.MOBILE_CATEGORY from roster_passengers RP
                    inner join employees E ON E.EMPLOYEES_ID=RP.EMPLOYEE_ID and E.BRANCH_ID='".$branch_id."' 
                    inner join rosters R on R.ROSTER_ID=RP.ROSTER_ID
                    where $cond and E.ACTIVE=1  ";				
            
            $passeng_det_array = DB::select($passeng_det);
            $cust_mob_arr=array();
            if(count($passeng_det_array)>0){
                for($kk=0;$kk<count($passeng_det_array);$kk++){
                    $cus_mob =$this->AES_DECRYPT($passeng_det_array[$kk]->MOBILE, $AES_KEY);
                    array_push($cust_mob_arr,$cus_mob);
                }

                $post_arr=array("bookingid"=>$roster_id_val[0],"phonenumber"=>$cust_mob_arr,"api_key"=>"7e4d7bf081f8286447ac383e25588c7f");
                $post_data=json_encode(array($post_arr));
                
                if(is_array($post_arr) && count($post_arr) > 0)
                {
                    $ch=curl_init("http://**************/aster-dialer-ntl/services/closebooking.php");
                    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                    curl_setopt($ch, CURLINFO_HEADER_OUT, true);
                    curl_setopt($ch, CURLOPT_POST, true);
                    curl_setopt($ch, CURLOPT_POSTFIELDS, $post_data);
                    curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));
                    $result = curl_exec($ch); 
                    curl_close($ch);

                    $decode_result = JSON_DECODE($result);
                    
                    if($r_or_p!='Roster'){
                        $update_free="UPDATE MASK_TRANSACTION MT
                        INNER JOIN MASK_DID_MASTER MD ON MD.DID_NUMBER=MT.MASK_DID_NUMBER
                        SET 
                        MT.AVAILABLITY_STATUS=1,MD.AVAILABLITY_STATUS=1,
                        MT.MASK_CLEAR_RESPONSE='".$result."', MT.UPDATED_AT='".date("Y-m-d H:i:s")."',MD.UPDATED_AT='".date("Y-m-d H:i:s")."' WHERE MT.ROSTER_PASSENGER_ID='".$roster_passenger_id."'";
                    } else {
                        $update_free="UPDATE MASK_TRANSACTION MT
                        INNER JOIN MASK_DID_MASTER MD ON MD.DID_NUMBER=MT.MASK_DID_NUMBER
                        SET 
                        MT.AVAILABLITY_STATUS=1,MD.AVAILABLITY_STATUS=1,
                        MT.MASK_CLEAR_RESPONSE='".$result."', MT.UPDATED_AT='".date("Y-m-d H:i:s")."',MD.UPDATED_AT='".date("Y-m-d H:i:s")."' WHERE MT.ROSTER_ID='".$roster_id."'";
                    }
                    DB::update($update_free);  
                }
            } else {
                //echo "EMPTY ARRAY";
            }
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Clear Mask Number Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ],500);
        }
    }

    public function update_tollpayment(){
        try {
            $elastic_controler = new \App\Http\Controllers\ElasticController;

            $RS_TOTALTRIPCLOSE      	= env('RS_TOTALTRIPCLOSE');
            $RS_TOTALMANUALTRIPCLOSE    = env('RS_TOTALMANUALTRIPCLOSE');
            $RS_TOTALTRIPSHEETACCEPT    = env('RS_TOTALTRIPSHEETACCEPT');
            $RS_TOTALDELAYROUTES      	= env('RS_TOTALDELAYROUTES');

            $sql = "SELECT  R.ROSTER_ID,R.CAB_ID,R.ESTIMATE_START_TIME, R.BRANCH_ID , R.TRIP_TYPE,DRB.TRIP_STATUS ,  MIN(RP.DRIVER_ARRIVAL_TIME) as first_pickup_time, max(RP.ACTUAL_END_TIME) AS last_drop_time  ,R.ESTIMATE_END_TIME,ADDTIME(R.ACTUAL_START_TIME,'03:00:00')as actual_time,R.ACTUAL_START_TIME,R.ACTUAL_END_TIME, vm.MODEL,date(R.ACTUAL_START_TIME) as 'trip_date'  
                    from rosters R 
                    left join cab c on R.CAB_ID = c.CAB_ID 
                    left join vehicles v on v.VEHICLE_ID = c.VEHICLE_ID 
                    left join vehicle_models vm on v.VEHICLE_MODEL_ID = vm.VEHICLE_MODEL_ID
                    left join roster_passengers RP ON RP.ROSTER_ID = R.ROSTER_ID AND   IF(R.TRIP_TYPE='P',RP.DRIVER_ARRIVAL_TIME is not NULL,RP.ACTUAL_END_TIME  is not NULL)
                    left join driver_billing_summary DRB ON DRB.ROSTER_ID = R.ROSTER_ID  
                    -- where R.ACTIVE = 1 AND  R.ROSTER_ID in('2068115')  and R.BRANCH_ID = 63 
                    where R.ACTIVE = 1 AND (R.tollpayment_checked != 'checked' or R.tollpayment_checked is null) and R.BRANCH_ID IN ('63','62')
                    and R.ROSTER_STATUS in ($RS_TOTALTRIPCLOSE,$RS_TOTALMANUALTRIPCLOSE,$RS_TOTALTRIPSHEETACCEPT,$RS_TOTALDELAYROUTES)
                    and R.ESTIMATE_START_TIME > '2024-10-25 00:01:00' and R.ACTUAL_START_TIME is not null AND R.ACTUAL_START_TIME !='' AND R.ROUTE_ID NOT LIKE 'LD%' GROUP BY R.ROSTER_ID LIMIT 2";

            $result = DB::select($sql);

            if (count($result) > 0) {
                foreach ($result as $val) {
                    $cab_id = $val->CAB_ID;
                    $route_no = $val->ROSTER_ID;
                    $BRANCH_ID = $val->BRANCH_ID;
                    $TRIP_TYPE = $val->TRIP_TYPE;
                    $TRIP_MODE = $val->TRIP_STATUS;

                    $fromdatetime= $val->ACTUAL_START_TIME;

                    if($val->ACTUAL_END_TIME==''){
                        $todatetime= $val->actual_time ;
                    } else {
                        $todatetime=$val->ACTUAL_END_TIME;
                    }
                        
                    $matchedDetails = [];

                    if ($val->MODEL != '' && $val->MODEL != null) {
                        $latitude = '';
                        $longitude = '';
                        
                        $toll_master_sql = "SELECT * from toll_master where VEHICLE_TYPE = '$val->MODEL' and sourounging_latlang is not null;";
                        $toll_master_result = DB::select($toll_master_sql);

                        if (count($toll_master_result) > 0) {
                            foreach ($toll_master_result as $toll_master) {
                                $sourounging_latlang = $toll_master->sourounging_latlang;
                                $matched_count = 0;
                                $matched_count_1 = 0;
                                if ($sourounging_latlang != '' && $sourounging_latlang != null) {
                                    $sourounging_latlangArr = explode('|', $sourounging_latlang);
                                    if (count($sourounging_latlangArr) > 0) {
                                        foreach ($sourounging_latlangArr as $latlang) {
                                            $latlangArr = explode(',', $latlang);
                                            $lat = isset($latlangArr[0]) ? $latlangArr[0] : '';
                                            $lang = isset($latlangArr[1]) ? $latlangArr[1] : '';
                                            if ($lat != '' && $lat != null && $lang != '' && $lang != null) {

                                                $oneway_pickup_end_time = $val->first_pickup_time;
                                                $oneway_drop_end_time =$val->last_drop_time;

                                                if ($oneway_pickup_end_time != '' && $TRIP_TYPE == 'P') {
                                                    $matchedData = $elastic_controler->tollCrossedPatSearch($BRANCH_ID, $cab_id, $route_no, $fromdatetime, $oneway_pickup_end_time, $lat, $lang);

                                                    if ($matchedData != 'No Record' && $matchedData != '') {
                                                        $matchedDetails[] = $matchedData;
                                                        $matched_count++;
                                                    }

                                                    $matchedData1 = $elastic_controler->tollCrossedPatSearch($BRANCH_ID, $cab_id, $route_no, $oneway_pickup_end_time, $todatetime, $lat, $lang);

                                                    if ($matchedData1 != 'No Record' && $matchedData1 != '') {
                                                        $matchedDetails[] = $matchedData1;
                                                        $matched_count_1++;
                                                    }
                                                } else if ($oneway_drop_end_time != '' && $TRIP_TYPE == 'D') {

                                                    $matchedData = $elastic_controler->tollCrossedPatSearch($BRANCH_ID, $cab_id, $route_no, $fromdatetime, $oneway_drop_end_time, $lat, $lang);

                                                    if ($matchedData != 'No Record' && $matchedData != '') {
                                                        $matchedDetails[] = $matchedData;
                                                        $matched_count++;
                                                    }
                                                } else {
                                                    $matchedData = $elastic_controler->tollCrossedPatSearch($BRANCH_ID, $cab_id, $route_no, $fromdatetime,  $todatetime, $lat, $lang);

                                                    if ($matchedData != 'No Record' && $matchedData != '') {
                                                        $matchedDetails[] = $matchedData;
                                                        $matched_count++;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }

                                if ($matched_count > 3 || $matched_count_1 > 3) {
                                    $TOLL_ID = $toll_master->TOLL_ID;
                                    $TOLL_CHARGE = $toll_master->ONE_WAY;
                                    $LOCATION = $toll_master->LOCATION;
                                    $PAYMENT_DATE = $val->trip_date;
                                    $curDateTime = date('Y-m-d H:i:s');

                                    if ($matched_count > 3 && $matched_count_1 > 3) {
                                        $TOLL_CHARGE = $toll_master->ONE_TWO;
                                    }

                                                        if ($matched_count > 3 && $matched_count_1 == 0 && $TRIP_MODE =='N' && $TRIP_TYPE == 'D') {
                                        $TOLL_CHARGE = $toll_master->ONE_TWO;
                                    }

                                    $check_ticket = DB::select("select TOLL_ID from toll_payment where ROSTER_ID='" . $route_no . "' and TOLL_ID='" . $TOLL_ID . "' and PAYMENT_DATE='" . $PAYMENT_DATE . "'");
                
                                    if (count($check_ticket) == 0) {
                                        $insertSql = "INSERT INTO toll_payment(ROSTER_ID,TOLL_ID,TICKET_NO,CAB_ID,TOLL_CHARGE,PAYMENT_DATE,CREATED_DATE) VALUES('$route_no','$TOLL_ID','$LOCATION','$cab_id','$TOLL_CHARGE','$PAYMENT_DATE','$curDateTime')";
                                        $toll_payment_result = DB::insert($insertSql);
                                    }
                                }
                            }
                        }
                    }

                    $update_status_sql = "UPDATE rosters set tollpayment_checked = 'checked' where ROSTER_ID = '$route_no'";
                    $update_status_result = DB::update($update_status_sql);
                }
            }
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Update Toll Payment Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ],500);
        }
    }

    public function AddCabRosterDetails(){
        try {
            $BRANCH_ID = 32;            
            $currentDate = Carbon::now();
            $yesterday = $currentDate->subDay();
            $yesterday = $yesterday->format('Y-m-d');
            // $yesterday = '2025-03-01';
            $from_date = $yesterday;
            $to_date = $yesterday;
           
            $results = array();
            $tripemp_status = '';
            $tripcmt_status = '--';
            $RP_EMPLOYEEDELAY = env('RP_EMPLOYEEDELAY');
            $RP_manualclosed = env('RP_manualclosed');
            $RP_SYSOTP = env('RP_SYSOTP');
            $RP_ZEROTIME = env('RP_ZEROTIME');
            $AES_KEY = env("AES_ENCRYPT_KEY");
            $normal_array_values = [];
            $resultData = [];

            $accessToken = $this->RRDGetAccessToken();

            if($accessToken != '' && $accessToken != null){
                $result = $this->OverallReport($from_date, $to_date, 'ALL');
                $json_arr = json_encode($result);
                $normal_array = json_decode($json_arr, true);

                for ($i = 0; $i < count($normal_array); $i++) {
                    if($i == 0){
                        $normal_array_values[] = array_keys($normal_array[0]);
                    }
                    $DELAYALLOT_TIME = $normal_array[$i]['DELAYALLOT_TIME'];
                    $CABDELAY = $normal_array[$i]['CABDELAY'];
                    $EMPDELAY = $normal_array[$i]['EMPDELAY'];
                    $MANUALOTP_STATUS = $normal_array[$i]['MANUALOTPSTATUS'];
                    $NOSHOWSTATUS = $normal_array[$i]['NOSHOWSTATUS'];
                    $CABSTATUS = $normal_array[$i]['CABSTATUS'];
                    $EMPSTATUS = $normal_array[$i]['EMPSTATUS'];
                    $ROSTER_PASSENGER_STATUS = $normal_array[$i]['ROSTER_PASSENGER_STATUS'];
                    $ROSTER_STATUS = $normal_array[$i]['ROSTER_STATUS'];
                    $CAB_ID = $normal_array[$i]['CAB_ID'];
                    $DELAYROUTES = $normal_array[$i]['DELAYROUTES'];
                    if ($BRANCH_ID == '32' || $BRANCH_ID == '53' || $BRANCH_ID == '54') {
                        $GENDER = $normal_array[$i]['GENDER'] == 'M' ? 'Masculine' : 'Feminine';
                    } else {
                        $GENDER = $normal_array[$i]['GENDER'] == 'M' ? 'Male' : 'Female';
                    }
                    $TripStatus = $this->TripStatus($ROSTER_STATUS);
                    if ($BRANCH_ID != '46') {
                        $tripemp_status = $this->Employee_status($ROSTER_PASSENGER_STATUS);
    
    
                        if ($tripemp_status == 'Employee Delay & System OTP') {
                            $tripemp_status = $RP_EMPLOYEEDELAY;
                        }
    
                        if ($tripemp_status == 'Manual OTP') {
                            $tripemp_status = $RP_manualclosed;
                        }
                        if ($tripemp_status == 'System OTP' || $tripemp_status == 'Manual OTP') {
                            $tripemp_status = $RP_SYSOTP;
    
                        }
    
                    } else {
                        $tripemp_status = $this->Employee_status($ROSTER_PASSENGER_STATUS);
    
                        if ($tripemp_status == 'Cab Delay') {
                            $tripemp_status = env('RP_Arrival_sts');
                            $tripcmt_status = 'Cab Delay';
                        }
                        if ($tripemp_status == 'Employee Delay') {
                            $tripemp_status = 'Boarded';
                            $tripcmt_status = 'Employee Delay';
                        }
                        if ($tripemp_status == 'Cab Delay and System OTP') {
                            $tripemp_status = 'System OTP';
                            $tripcmt_status = 'Cab Delay';
                        }
                        if ($tripemp_status == 'Cab and Employee Delay') {
                            $tripemp_status = 'System OTP';
                            $tripcmt_status = 'Cab and Employee Delay';
                        }
                        if ($tripemp_status == 'Employee Delay & System OTP') {
                            $tripemp_status = 'System OTP';
                            $tripcmt_status = 'Employee Delay';
                        }
                        if ($tripemp_status == 'Employee Delay and System OTP') {
                            $tripemp_status = 'System OTP';
                            $tripcmt_status = 'Employee Delay';
                        }
                        if ($tripemp_status == 'Employee Safe Drop') {
                            $tripemp_status = 'System OTP';
                            $tripcmt_status = 'Employee Safe Drop';
                        }
                        if (strpos($TripStatus, 'Trip Closed') !== false) {
                            $TripStatus = $DELAYROUTES;
                        }else{
                            $TripStatus = $TripStatus;
                        }
                    }
    
                    if ($DELAYALLOT_TIME > $RP_ZEROTIME) {
                        $delaytimediff = $DELAYALLOT_TIME;
                    } else {
                        $delaytimediff = 0;
                    }
    
                    if ($CABDELAY > $RP_ZEROTIME) {
                        $cabdelaydiff = $CABDELAY;
                    } else {
                        $cabdelaydiff = 0;
                    }
    
                    if ($EMPDELAY > $RP_ZEROTIME) {
                        $empdelaydiff = $EMPDELAY;
                    } else {
                        $empdelaydiff = 0;
                    }
    
                    $empname = $normal_array[$i]['EMPLOYEE_NAME'];
                    if ($empname != '') {
                        $EMP_NAME = $this->commonFunction->AES_DECRYPT($empname, $AES_KEY);
                    } else {
                        $EMP_NAME = '--';
                    }
                    if ($BRANCH_ID != '46') {
                        $emp_arr_val = array("CAB_DELAY" => $cabdelaydiff, "EMP_DELAY" => $empdelaydiff, "DELAYALLOTTIME" => $delaytimediff, "EMPLOYEE_NAME" => $EMP_NAME, "ALLSTATUS" => $tripemp_status, "DELAYROUTES" => $TripStatus, "GENDER" => $GENDER);
                    } else {
                        $emp_arr_val = array("CAB_DELAY" => $cabdelaydiff, "EMP_DELAY" => $empdelaydiff, "DELAYALLOTTIME" => $delaytimediff, "EMPLOYEE_NAME" => $EMP_NAME, "ALLSTATUS" => $tripemp_status, "COMMENTS" => $tripcmt_status, "DELAYROUTES" => $TripStatus);
                    }
                    $results[$i] = array_replace($normal_array[$i], $emp_arr_val);
                    $normal_array_values[] = array_values($results[$i]);
                }
                
                if(count($results) > 0){
                    $output = [];
    
                    foreach($results as $res)
                    {
                        if(!isset($output[$res['ROSTER_ID']]))
                        {
                            $output[$res['ROSTER_ID']]['cabRosterDetails'] = [
                                "travelType"=> $res['TRIP_TYPE'],
                                "cabNo"=> $res['VEHICLE_REG_NO'],
                                "cabModel"=> $res['MODEL'],
                                "capacity"=> (int)$res['CAPACITY'],
                                "loginLogoutDate"=> $res['LOGIN_DATE'],
                                "loginLogoutTime"=> $res['LOGIN_TIME'],
                                "vendorName"=> $res['VENDOR_NAME'],
                                "routeId"=> (int)$res['ROUTE_ID'],
                            ];
                        }
    
                        $output[$res['ROSTER_ID']]['employeeDetails'][] = [
                            
                            "associateName" => $res['EMPLOYEE_NAME'],
                            "associateId" => $res['EMPLOYEE_ID'],
                            "gender" => $res['GENDER'],
                            "location" => $res['LOCATION_NAME'],
                            "schedulePickupDropTime" => $res['ESTIMATE_START_TIME']. "",//
                            "cabArrivedTime" => $res['DRIVER_ARRIVAL_TIME']. "", //
                            "cabDelayTime" => $res['CAB_DELAY']."",
                            "associatePickupDropTime" => $res['ACTUAL_START_TIME']. "",
                            "associateDelayTime" => $res['EMP_DELAY']."",
                            "reasonRemark" => $res['REASON'],
                            "cabStatus" => $res['DELAYROUTES'],
                            "tripCloseTime" => $res['ACTUAL_END_TIME']. "",
                            "employeeStatus" => $res['ALLSTATUS'],
                            "scheduleCabAllotTime" => $res['SCHEDULE_CABALLOT']."",
                            "actualCabAllotTime" => $res['CAB_ALLOT_TIME']."",
                            "delayAllotTime" => $res['DELAYALLOTTIME']."",
                            "action" => 'insert',
                        ];
    
    
                    }
                    $resultData['employeeRosterDetails'] = array_values($output);
                    $filename = 'Cab-'. $from_date. '.csv';
                    $filePath = 'RRD/Cab/'. $filename;
    
                    $RRD_OVERALL_URL = env('RRD_OVERALL_URL');
                    $bearerStr = 'Authorization: Bearer '. $accessToken;
                    $header = [
                        'Content-Type: application/json',
                        $bearerStr
                    ];    
                    list($curlResponse,$statusCode)  = $this->sendCurlRequest($RRD_OVERALL_URL,'POST',json_encode($resultData),$header);
                    $this->storeDataAsCsv($filePath,$normal_array_values,'Cab',$filename,$curlResponse,$statusCode);
                    echo $curlResponse;
                }
            }else{
                echo "Empty Access Token";
            }
            return response([
                'success' => true,
                'status' => 1,               
		        'message' => 'Add Cab Roster Details Successfully',
            ], 200);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Add Cab Roster Details Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ],500);
        }
    }

    public function RRDGetAccessToken()
    {
        try {
            $accessToken = $this->getAccessToken();
            if ($accessToken != '' && $accessToken != null) {
                return $accessToken;
            }
            $RRD_ACCESS_KEY = env('RRD_ACCESS_KEY');
            $RRD_CLIENT_SECRET = env('RRD_CLIENT_SECRET');
            $RRD_CLIENT_CODE = env('RRD_CLIENT_CODE');
            $RRD_TOKEN_GENERATION_URL = env('RRD_TOKEN_GENERATION_URL');
            $inputs = [
                'accessKey' => $RRD_ACCESS_KEY,
                'clientSecret' => $RRD_CLIENT_SECRET,
                'clientCode' => $RRD_CLIENT_CODE
            ];
                        
            $inputJson = json_encode($inputs);
            $headerFields = ['Content-Type: application/json']; 
            list($response,$statusCode) = $this->sendCurlRequest($RRD_TOKEN_GENERATION_URL, 'POST', $inputJson,$headerFields);

            if ($response != '' && $response != null) {
                $accessToken = $this->storeAccessToken($response);              
            }

            return $accessToken;
        } catch (\Exception $e) {
            echo $e->getMessage();
        }
    }

    public function TripStatus($sts)
    {
        $Trip_created = env('RS_NEWROSTER');
        $Trip_allot = env('RS_TOTALALLOT');
        $Trip_accepted = env('RS_TOTALACCEPT');
        $Trip_rejectd = env('RS_TOTALREJECT');
        $Trip_executed = env('RS_TOTALEXECUTE');
        $Trip_breakdown = env('RS_TOTALBREAKDOWN');
        $Trip_close = env('RS_TOTALTRIPCLOSE');
        $Trip_manual_close = env('RS_TOTALMANUALTRIPCLOSE');
        $Tripsheet_accepted = env('RS_TOTALTRIPSHEETACCEPT');
        $Tripsheet_rejected = env('RS_TOTALTRIPSHEETREJECT');
        $RS_TOTALDELAYROUTES = env('RS_TOTALDELAYROUTES');
        $RS_TOTALTRIPSHEET_CANCEL = env('RS_TOTALTRIPSHEET_CANCEL');
        $RS_TOTAL_AUTOCANCEL = env('RS_TOTAL_AUTOCANCEL');
        $trip_status = "";
        switch ($sts) {
            case preg_match('/' . $sts . '/', $Trip_created) ? true : false:
                $trip_status = env('RS_Created_sts');
                break;

            case preg_match('/' . $sts . '/', $Trip_allot) ? true : false:
                $trip_status = env('RS_Alloted_sts');
                break;

            case preg_match('/' . $sts . '/', $Trip_accepted) ? true : false:
                $trip_status = env('RS_ACCEPTed_sts');
                break;

            case preg_match('/' . $sts . '/', $Trip_rejectd) ? true : false:
                $trip_status = env('RS_Rejected_sts');
                break;

            case preg_match('/' . $sts . '/', $Trip_executed) ? true : false:
                $trip_status = env('RS_Execute_sts');
                break;

            case preg_match('/' . $sts . '/', $Trip_breakdown) ? true : false:
                $trip_status = env('RS_Breakdown_sts');
                break;

            case preg_match('/' . $sts . '/', $Trip_close) ? true : false:
                $trip_status = env('RS_Closed_sts');
                break;
            case preg_match('/' . $sts . '/', $RS_TOTALDELAYROUTES) ? true : false:
                $trip_status = env('RS_Closed_Delay_sts');
                break;
            case preg_match('/' . $sts . '/', $Trip_manual_close) ? true : false:
                $trip_status = env('RS_Manual_Closed_sts');
                /* if($branch_id==48)
                            {
                                $trip_status='TripClosed';
                            } */
                break;

            case preg_match('/' . $sts . '/', $Tripsheet_accepted) ? true : false:
                $trip_status = env('RS_Tripsheet_Accept_sts');
                break;

            case preg_match('/' . $sts . '/', $Tripsheet_rejected) ? true : false:
                $trip_status = env('RS_Tripsheet_Reject_sts');
                break;

            case preg_match('/' . $sts . '/', $RS_TOTALTRIPSHEET_CANCEL) ? true : false:
                $trip_status = env('RS_Tripsheet_cancelled_sts');
                break;

            case preg_match('/' . $sts . '/', $RS_TOTAL_AUTOCANCEL) ? true : false:
                $trip_status = env('RS_Autocancelled_sts');
                break;
            default:
                $trip_status = '--';
                break;
        }
        return $trip_status;
    }

    public function Employee_status($sts)
    {
        $RP_safe_drop = env('RP_safe_drop');
        $RP_create = explode(',', env('RPS_CREATE'));
        $RP_club = explode(',', env('RPS_CLUBBING'));
        $RP_arrival = explode(',', env('RPS_TTLARRIVAL'));
        $RPS_TTLCABSYSTEMOTP = explode(',', env('RPS_TTLCABSYSTEMOTP'));
        $RP_cab_delay = explode(',', env('RPS_TTLCABDELAY'));
        $RP_noshow = explode(',', env('RPS_TTLNOSHOW'));
        $RP_system_otp = explode(',', env('RPS_TTLSYSTEMOTP'));
        $RP_employee_delay = explode(',', env('RPS_TTLEMPLOYEEDELAY'));

        $RP_cabemployee_delay = explode(',', env('RPS_TTLCABEMPLOYEEDELAY'));
        $RP_manual_otp = explode(',', env('RPS_TTLMANUALOTP'));
        $RPS_TTLEMPLOYEEDELAY_SYSTEMOTP = explode(',', env('RPS_TTLEMPLOYEEDELAY_SYSTEMOTP'));
        if (in_array($sts, $RP_create) == true) {
            $stsus = env('RP_Created_sts');
        } else if (in_array($sts, $RP_club) == true) {
            $stsus = env('RP_Club_sts');
        } else if (in_array($sts, $RP_arrival) == true) {
            $stsus = env('RP_Arrival_sts');
        } else if (in_array($sts, $RPS_TTLCABSYSTEMOTP) == true) {
            $stsus = env('RP_cab_delay_system_otp_sts');
        } else if (in_array($sts, $RP_cab_delay) == true) {
            $stsus = env('RP_cab_delay_sts');
        } else if (in_array($sts, $RP_noshow) == true) {
            $stsus = env('RP_Noshow_sts');
        } else if (in_array($sts, $RP_system_otp) == true) {
            $stsus = env('RP_system_otp_sts');
        } else if (in_array($sts, $RP_employee_delay) == true) {
            $stsus = env('RP_employee_delay_sts');
        } else if (in_array($sts, $RP_cabemployee_delay) == true) {
            $stsus = env('RP_cabemployee_delay_sts');
        } else if (in_array($sts, $RP_manual_otp) == true) {
            $stsus = env('RP_Manual_otp_sts');
        } else if (in_array($sts, $RPS_TTLEMPLOYEEDELAY_SYSTEMOTP) == true) {
            $stsus = env('RP_employee_delay_system_otp');
        } else if ($sts >= $RP_safe_drop) {
            $stsus = env('RP_employee_safe_drop');
        } else {
            $stsus = env('RP_Not_Enabled');
        }
        return $stsus;
    }

    public function sendCurlRequest($url, $method, $postFields,$headerFields)
    {
        try {
            $curl = curl_init();
            curl_setopt_array(
                $curl,
                array(
                    CURLOPT_URL => $url,
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_ENCODING => '',
                    CURLOPT_MAXREDIRS => 10,
                    CURLOPT_TIMEOUT => 0,
                    CURLOPT_FOLLOWLOCATION => true,
                    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                    CURLOPT_CUSTOMREQUEST => $method,
                    CURLOPT_POSTFIELDS => $postFields,
                    CURLOPT_HTTPHEADER => $headerFields,
                )
            );

            $response = curl_exec($curl);
            $statusCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
            curl_close($curl);
            return [$response,$statusCode];
        }catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Send CURL Request Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ],500);
        }
    }

    public function OverallReport($from_date,$to_date,$trip_type) {
        $RPS_CABDELAY = env('RPS_CABDELAY');
        $RPS_NOSHOW = env('RPS_NOSHOW');
        $RPS_MANUALOTP = env('RPS_MANUALOTP');
        $RPTTLTYPE = explode(',', $this->TTLTYPE);
        $RPS_EMPLOYEEDELAY = env('RPS_EMPLOYEEDELAY');
         $RS_DELAYROUTES = env('RS_DELAYROUTES');  
        $dbname = 'mysql4';
		$to_date=$from_date;
        $vendor = "";
        if($trip_type == $this->ALLREPORT)
        {
            $triptype = "AND R.TRIP_TYPE IN ('$RPTTLTYPE[0]','$RPTTLTYPE[1]')";
        }
        else
        {
            $triptype = "AND R.TRIP_TYPE ='$trip_type'";
        }

        $comments = '';
		 $query = "SELECT R.ROSTER_ID,R.ROUTE_ID,R.TRIP_TYPE,V.VEHICLE_REG_NO,VM.MODEL,VM.CAPACITY,
        VE.`NAME` as VENDOR_NAME,E.`NAME` as EMPLOYEE_NAME,RP.EMPLOYEE_ID,E.GENDER as GENDER,L.LOCATION_NAME,
        if(R.TRIP_TYPE = 'P',DATE(R.ESTIMATE_END_TIME),DATE(R.ESTIMATE_START_TIME)) as LOGIN_DATE,
        if(R.TRIP_TYPE = 'P',TIME(R.ESTIMATE_END_TIME),TIME(R.ESTIMATE_START_TIME)) as LOGIN_TIME,
        if(R.TRIP_TYPE = 'P',R.ESTIMATE_START_TIME,DATE_SUB(R.ESTIMATE_START_TIME, INTERVAL 30 MINUTE)) as SCHEDULE_CABALLOT,
        R.CAB_ALLOT_TIME, '0' AS DELAYALLOTTIME,
        RP.ESTIMATE_START_TIME,
        if(R.TRIP_TYPE = 'P',RP.DRIVER_ARRIVAL_TIME,R.ACTUAL_START_TIME) as DRIVER_ARRIVAL_TIME,'0' AS CAB_DELAY,
        RP.ACTUAL_START_TIME,'0' AS EMP_DELAY,R.ACTUAL_END_TIME,if(RP.ACTIVE=3,RM.REASON,'--')as REASON $comments ,if(R.ROSTER_STATUS & $RS_DELAYROUTES,'Delay','Ontime') AS DELAYROUTES,'--' AS ALLSTATUS,
        if(R.TRIP_TYPE = 'P',TIMEDIFF(RP.DRIVER_ARRIVAL_TIME,RP.ESTIMATE_START_TIME),TIMEDIFF(R.ACTUAL_START_TIME,RP.ESTIMATE_START_TIME)) AS CABDELAY,
        if(R.TRIP_TYPE = 'P',TIMEDIFF(RP.ACTUAL_START_TIME,if(RP.ESTIMATE_START_TIME > RP.DRIVER_ARRIVAL_TIME,RP.ESTIMATE_START_TIME,RP.DRIVER_ARRIVAL_TIME)),TIMEDIFF(RP.ACTUAL_START_TIME,if(RP.ESTIMATE_START_TIME > R.ACTUAL_START_TIME,RP.ESTIMATE_START_TIME,R.ACTUAL_START_TIME))) AS EMPDELAY,
        if(RP.ROSTER_PASSENGER_STATUS & $RPS_CABDELAY,1,0) as CABSTATUS,if(RP.ROSTER_PASSENGER_STATUS & $RPS_NOSHOW,1,0) as NOSHOWSTATUS,
        if(RP.ROSTER_PASSENGER_STATUS & $RPS_EMPLOYEEDELAY,1,0) as EMPSTATUS,if(RP.ROSTER_PASSENGER_STATUS & $RPS_MANUALOTP,1,0) as MANUALOTPSTATUS,
        if(R.TRIP_TYPE = 'P',TIMEDIFF(R.CAB_ALLOT_TIME,R.ESTIMATE_START_TIME),TIMEDIFF(R.CAB_ALLOT_TIME,DATE_SUB(R.ESTIMATE_START_TIME, INTERVAL 30 MINUTE))) AS DELAYALLOT_TIME,RP.ROSTER_PASSENGER_STATUS,R.ROSTER_STATUS,R.CAB_ID
        FROM rosters R
        INNER JOIN roster_passengers as RP
        on RP.ROSTER_ID=R.ROSTER_ID and RP.ACTIVE in(1,3)       
        INNER JOIN branch B ON B.BRANCH_ID = R.BRANCH_ID
        INNER JOIN vendors VE ON VE.VENDOR_ID = R.VENDOR_ID
        LEFT JOIN cab C ON C.CAB_ID = R.CAB_ID
        LEFT JOIN vehicles V ON V.VEHICLE_ID = C.VEHICLE_ID
        LEFT JOIN vehicle_models VM ON VM.VEHICLE_MODEL_ID = V.VEHICLE_MODEL_ID
        LEFT JOIN employees E ON E.EMPLOYEES_ID = RP.EMPLOYEE_ID AND E.BRANCH_ID = R.BRANCH_ID
        LEFT JOIN locations L ON L.LOCATION_ID = RP.LOCATION_ID
		left join reason_log RL on RL.ROSTER_PASSENGER_ID=RP.ROSTER_PASSENGER_ID
		LEFT join reason_master RM on RM.REASON_ID=RL.REASON_ID
		
        WHERE R.BRANCH_ID = 32 AND R.ACTIVE = 1 $vendor  AND RP.ACTIVE in(1,3)
        AND (DATE(RP.ESTIMATE_START_TIME) BETWEEN '$from_date'
        AND '$to_date' OR DATE(RP.ESTIMATE_END_TIME) BETWEEN '$from_date'
        AND '$to_date') $triptype  ORDER BY RP.ROSTER_PASSENGER_ID DESC";
        return DB::connection("$dbname")->select($query);
    }

    public function storeDataAsCsv($filePath,$data,$report_type,$filename,$apiResponse,$statusCode)
    {
        // Convert data to CSV format
        $csvContent = implode("\n", array_map(function ($row) {
            return implode(',', $row);
        }, $data));
        
        // Specify the storage disk and file path
        $disk = 'local'; // You can use other disks like 'public', 's3', etc.
        // $filePath = 'path/to/your/file.csv';
        
        // Store the CSV content in the specified file
        Storage::disk($disk)->put($filePath, $csvContent);
        $recordCount = count($data) - 1;
        $this->storeFileUploadDetails($report_type,$filePath,$recordCount,'Topsa Cron',$filename,$apiResponse,$statusCode);
        return true;
    }

    public function getAccessToken()
    {
        $sql = "SELECT accessToken from access_tokens where active = 1 and expiry > now()";
        $result = DB::select($sql);

        if (count($result) > 0) {
            return $result[0]->accessToken;
        }

        return '';
    }

    public function storeAccessToken($responseJson)
    {

        try {

            if ($responseJson != '' && $responseJson != null) {
                $responseArr = json_decode($responseJson, true);
                if (count($responseArr) > 0) {
                    $currentDateTime = Carbon::now();
                    $accessToken = $responseArr['accessToken'];
                    $tokenType = $responseArr['tokenType'];
                    $expiresIn = $responseArr['expiresIn'];
                    $refreshToken = $responseArr['refreshToken'];
                    $issued = $this->convertUTCtoIST($responseArr['issued']);
                    $expiry = $this->convertUTCtoIST($responseArr['expiry']);
                    $error = $responseArr['error'];
                    $errorDescription = $responseArr['errorDescription'];

                    $inputData = [
                        'accessToken' => $accessToken,
                        'tokenType' => $tokenType,
                        'expiresIn' => $expiresIn,
                        'refreshToken' => $refreshToken,
                        'issued' => $issued,
                        'expiry' => $expiry,
                        'error' => $error,
                        'errorDescription' => $errorDescription,
                        'api_response' => $responseJson,
                        'created_at' => $currentDateTime,
                        'created_by' => 'Topsa Cron'
                    ];
                    $result = AccessTokens::insert($inputData);

                    return $accessToken;
                }

            }
        } catch (\Exception $e) {
            echo $e->getMessage();
        }

    }

    public function storeFileUploadDetails($report_type,$filePath,$data_count,$created_by,$filename,$apiResponse,$statusCode)
    {
        $inputData = [
            "report_type" => $report_type,
            "file_name" => $filename,
            "branch_id" => 32,
            "data_count" => $data_count,
            "created_by" => $created_by,
            "api_response" => $apiResponse,
            "api_response_code" => $statusCode
        ];
        $result = ReportAPIFiles::insert($inputData);
        return $result;
    }

    public function convertUTCtoIST($utcDateTime)
    {
        try {
            if ($utcDateTime == '' || $utcDateTime == null) {
                return '';
            }
            $istDateTime = Carbon::parse($utcDateTime, 'UTC')->setTimezone('Asia/Kolkata');
            return $istDateTime;
        } catch (\Exception $e) {
            echo $e->getMessage();
        }
    }

    public function AddCabMISDetails()
    {
        try {
            $currentDate = Carbon::now();
            $yesterday = $currentDate->subDay();
            $yesterday = $yesterday->format('Y-m-d');
        
            $from_date = $yesterday;
            $to_date = $yesterday;
            
            $normal_array_values = [];
            $resultData = [];
            $accessToken = $this->RRDGetAccessToken();
            if($accessToken != '' && $accessToken != null)
            {
                $datarrd = $this->RRDGetAllDetails2($from_date, $to_date, 0, 0, 0);
                $empty_data =[]; // $this->empty_km($from_date, $to_date, 0, 0);
                $misdata = array_merge($datarrd, $empty_data);
                if (count($misdata) > 0) {
                    $count = 0;
                    foreach ($misdata as $rrddata) {
                        if (is_object($rrddata)) {
                            $rrddata = (array) $rrddata;
                        }
                        if($count == 0){
                            $normal_array_values[0] = array_keys($rrddata);
                        }
                        $output[$rrddata['ROSTERID']]['cabMISDetails']['date'] = $rrddata['LOGINDATE'];
                        $output[$rrddata['ROSTERID']]['cabMISDetails']['cabNo'] = $rrddata['CABID'];
                        $output[$rrddata['ROSTERID']]['cabMISDetails']['cabType'] = $rrddata['MODEL'];
                        $output[$rrddata['ROSTERID']]['cabMISDetails']['pickupDrop'] = $rrddata['TRIP_TYPE'];
                        $output[$rrddata['ROSTERID']]['cabMISDetails']['noOfStaffsTravelled'] = isset($output[$rrddata['ROSTERID']]['cabMISDetails']['noOfStaffsTravelled']) ? $output[$rrddata['ROSTERID']]['cabMISDetails']['noOfStaffsTravelled'] + 1 : 1;
        
                        if ($rrddata['ROSTERID'] == 'Empty Trip') {
                            $price = $this->packageprice($rrddata['VENDOR_ID'], $rrddata['VEHICLE_MODEL_ID']);
                            $trip_amount = $rrddata['EMPTY_KM'] * $price;
                            $tax_with_amount = round($trip_amount * 1.05, 2);
        
                            $rrddata['RATEPERKM'] = $price;
                            $rrddata['AMOUNT'] = $trip_amount;
                            $rrddata['AMOUNTWITHTAX'] = $tax_with_amount;
                        }
        
                        $output[$rrddata['ROSTERID']]['misEmployeeDetails'][] = [
                            "rosterId" => (int)$rrddata['ROSTERID'],
                            "employeeId" => $rrddata['EMPLOYEE_ID'],
                            "empName" => $rrddata['EMPNAME'],
                            "teamName" => $rrddata['PROJECT_NAME'],
                            "divisionName" =>'RRD POLYGON',
                            "gender" => $rrddata['GENDER'],
                            "pickupDrop" => $rrddata['TRIP_TYPE'],
                            "routeOrder" => $rrddata['ROUTE_ORDER'],
                            "area" => $rrddata['LOCATION_NAME'],
                            "startingTime" => $rrddata['START_TIME'],
                            "closingTime" => $rrddata['END_TIME'],
                            "dropPickupTime" => $rrddata['LOGIN_TIME'],
                            "tripStatus" => $rrddata['TRIP_STATUS'],
                            "closeStatus" => $rrddata['CLOSE_STS'],
                            "deviceKm" => $rrddata['DEVICE_KM']."",
                            "shedKm" => $rrddata['SHED_KM']. "",
                            "travelledKms" => $rrddata['DEVICE_KM']. "",
                            "googleKm" => $rrddata['GOOGLE_KM']. "",
                            "googleShedKm" => $rrddata['GOOGLE_SHED_KM']. "",
                            "dividedKm" => $rrddata['DIVIDEDKM']. "",
                            "approvedKm" => $rrddata['APPROVE_KM']. "",
                            "ratePerKm" => $rrddata['RATEPERKM'],
                            "amount" => $rrddata['AMOUNT']."",
                            "amountWithTax" => $rrddata['AMOUNTWITHTAX']."",
                            "comments" => $rrddata['REMARKS'],
                            "billableStatus" => $rrddata['BILLABLE_STS'],
                            "escortDuty" => $rrddata['ESCORT'],
                            "vendorName" => $rrddata['VENDORNAME'],
                            "assetMovement" => $rrddata['ASSET_MOVEMENT'],
                            "facility" => "--",
                            "tollAmount" => $rrddata['TOLL_CHARGE'],
                            "comment" => 'km',
                        ];
                        $count++;
                        $normal_array_values[] = array_values($rrddata);
                    }
        
                    $resultData['employeeMISDetails'] = array_values($output);
                    $filename = 'Mis-'. $from_date. '.csv';
                    $filePath = 'RRD/Mis/' . $filename;
                   
                    $RRD_MIS_URL = env('RRD_MIS_URL');
                    $bearerStr = 'Authorization: Bearer '. $accessToken;
                    $header = [
                        'Content-Type: application/json',
                        $bearerStr
                    ];
                    list($curlResponse,$statusCode)  = $this->sendCurlRequest($RRD_MIS_URL,'POST',json_encode($resultData),$header);
                    $this->storeDataAsCsv($filePath,$normal_array_values,'Mis',$filename,$curlResponse,$statusCode);
                    echo $curlResponse;
                }else{
                    echo 'No data available for '. $from_date;
                }
                return true;

            }else{
                echo 'Access Token is empty';
            }
    
            return true;
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Mask Channel Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ],500);
        }
    }

    public function RRDGetAllDetails2($fromdate, $todate, $trip_type, $vehiclevise, $employeevise)
    {
        try {

            $RP_ZERO = env('RP_ZERO');
            $ENCRYPT_KEY = env('AES_ENCRYPT_KEY');
            
            $resultss = array();
            $result = array();
            $results = array();
            $speedarray = array();
            $END_TIME = '';
            
            $misdata = $this->RRDGetMisData2($fromdate, $todate, $trip_type, $vehiclevise, $employeevise);
            $counts = count($misdata);
            $json_arr = json_encode($misdata);
            $normal_array = json_decode($json_arr, true);
            $old_roster_id = '';
            $billable_sts = '';
            $approve_km = 0;
            $route_count1 = 0;
            
            for ($i = 0; $i < count($normal_array); $i++) {
                $SCHEDULE_COUNT = 0;
                $VEHICLE_MODEL_ID = '';
                $VENDOR_ID = '';
                $LOGIN_DATE = $normal_array[$i]['LOGIN_DATE'];
                $ROSTER_ID = $normal_array[$i]['ROSTER_ID'];
                $VEHICLE_REG_NO = $normal_array[$i]['VEHICLE_REG_NO'];
                $CAB_ID = $normal_array[$i]['CAB_ID'];
                $VENDOR_ID = $normal_array[$i]['VENDOR_ID'];
                $VEHICLE_MODEL_ID = $normal_array[$i]['VEHICLE_MODEL_ID'];
                $ACTUAL_START_TIME = str_replace(" ", "T", $normal_array[$i]['ACTUAL_START_TIME']);
                $ACTUAL_END_TIME = str_replace(" ", "T", $normal_array[$i]['ACTUAL_END_TIME']);
                $MODEL = $normal_array[$i]['MODEL'];
                $EMPLOYEE_ID = $normal_array[$i]['EMPLOYEE_ID'];
                $EMPNAME = $this->commonFunction->AES_DECRYPT($normal_array[$i]['EMPNAME'], $ENCRYPT_KEY);
                $PROJECT_NAME = $normal_array[$i]['PROJECT_NAME'];
                $GENDER = $normal_array[$i]['GENDER'];
                $TRIP_TYPE = $normal_array[$i]['TRIP_TYPE'];
                $LOCATION_NAME = $normal_array[$i]['LOCATION_NAME'];
                $START_TIME = $normal_array[$i]['START_TIME'];
                $END_TIME = $normal_array[$i]['END_TIME'];
                $LOGIN_TIME = $normal_array[$i]['LOGIN_TIME'];
                $TOTAL_KM = $normal_array[$i]['TOTAL_KM'];
                $ROSTER_PASSENGER_STATUS = $normal_array[$i]['ROSTER_PASSENGER_STATUS'];
                $ROUTE_ORDER = $normal_array[$i]['ROUTE_ORDER'];
                $ASSET_MOVEMENT = $normal_array[$i]['ASSET_MOVEMENT'];

                $ROSTER_STATUS = $normal_array[$i]['ROSTER_STATUS'];

                $close_sts = $this->TripStatus($ROSTER_STATUS);

                $REMARKS = $normal_array[$i]['REMARKS'];
                $VENDORNAME = $normal_array[$i]['VENDOR_NAME'];
                $route_count_old = $normal_array[$i]['route_count'];
                $DEVICE_KM = $normal_array[$i]['ROSTER_TOTAL_KM'];
                $GOOGLE_KM = $normal_array[$i]['GOOGLE_KM'];
                $GOOGLE_SHED_KM = $normal_array[$i]['GOOGLE_SHED_KM'];
                $CAPACITY = $normal_array[$i]['CAPACITY'];
                $TOLL_CHARGE = $normal_array[$i]['TOLL_CHARGE'];

                //echo $ROSTER_ID.'<br>' ;

                $RPS_TTLNOSHOW = explode(",", env('RPS_TTLNOSHOW'));
                if ($TRIP_TYPE == 'P' && (in_array($ROSTER_PASSENGER_STATUS, $RPS_TTLNOSHOW) == true)) {
                    $REMARKS = 'Noshow Pickup';
                }
                if ($old_roster_id != $ROSTER_ID) {
                    $route_count1 = $this->passenger_cnt($ROSTER_ID, $TRIP_TYPE);
                    $route_count = $route_count1;
                } else {
                    $route_count = $route_count1;
                }

                if ($normal_array[$i]['TRIP_STATUS'] == 'FT' || $normal_array[$i]['TRIP_STATUS'] == 'LT') {
                    if ($VENDOR_ID == 39 || $VENDOR_ID == 129) {
                        $SHED_KM = '15';
                    } else {

                        $SHED_KM = '5';
                    }
                } else {
                    $SHED_KM = '0';
                }
                $DIVIDEDKM = round(($GOOGLE_KM + $SHED_KM) / ($route_count), 6);
                if ($route_count == 1 && $TRIP_TYPE == 'D') {
                    if ($VENDOR_ID == 40 || $VENDOR_ID == 83 || $VENDOR_ID == 124) {
                        $trip_amount = round(($DIVIDEDKM * $normal_array[$i]['EXTRA_KMS_CHARGE']), 2);
                        $billable_sts = 'Non Billable';
                        $PACKAGE_PRICE = $normal_array[$i]['EXTRA_KMS_CHARGE'];
                    } elseif ($VENDOR_ID == 82 || $VENDOR_ID == 39 || $VENDOR_ID == 81 || $VENDOR_ID == 102 || $VENDOR_ID == 119 || $VENDOR_ID == 134) {
                        $trip_amount = round(($DIVIDEDKM * $normal_array[$i]['EXTRA_KMS_CHARGE']), 2);
                        $billable_sts = 'Non Billable';
                        $PACKAGE_PRICE = $normal_array[$i]['EXTRA_KMS_CHARGE'];
                    }

                } else {
                    $PACKAGE_PRICE = $this->packageprice($VENDOR_ID, $VEHICLE_MODEL_ID);
                    $trip_amount = round(($DIVIDEDKM * $PACKAGE_PRICE), 2);
                    $billable_sts = '';


                }

                $tax_with_amount = round($trip_amount * 1.05, 2);
                if ($old_roster_id != $ROSTER_ID) {

                    $SCHEDULE_COUNT = $normal_array[$i]['SCHEDULE_COUNT'];
                    $ESCORTROUTE = $normal_array[$i]['ESCORTROUTE'];
                    $DEVICE_KM = $normal_array[$i]['ROSTER_TOTAL_KM'];
                    $GOOGLE_KM = $normal_array[$i]['GOOGLE_KM'];
                    $DEVIATION_KM = $normal_array[$i]['DEVIATION_KM'];
                    $TRIP_STATUS = $normal_array[$i]['TRIP_STATUS'];
                    $GOOGLE_SHED_KM = $normal_array[$i]['GOOGLE_SHED_KM'];
                    $EMPTY_KM = $normal_array[$i]['EMPTY_KM'];
                    $approve_km = $GOOGLE_KM + $SHED_KM;
                } else {
                    $SCHEDULE_COUNT = 0;
                    $route_count = 0;
                    $ESCORTROUTE = '';
                    $START_TIME = '';
                    $END_TIME = '';
                    $LOGIN_TIME = '';
                    $TOTAL_KM = 0;
                    $DEVICE_KM = 0;
                    $GOOGLE_KM = 0;
                    $DEVIATION_KM = 0;
                    $SHED_KM = 0;
                    $TRIP_STATUS = '--';
                    $GOOGLE_SHED_KM = '0';
                    $EMPTY_KM = '';
                    $approve_km = '';
                    $TOLL_CHARGE = '';
                }

                $results[] = array(
                    "LOGINDATE" => $LOGIN_DATE,
                    "ROSTERID" => $ROSTER_ID,
                    "CABID" => $VEHICLE_REG_NO,
                    "MODEL" => $MODEL,
                    "VENDORNAME" => $VENDORNAME,
                    "EMPNAME" => $EMPNAME,
                    "EMPLOYEE_ID" => $EMPLOYEE_ID,
                    "GENDER" => $GENDER,
                    "PROJECT_NAME" => $PROJECT_NAME,
                    "TRIP_TYPE" => $TRIP_TYPE,
                    "SCHEDULECNT" => $route_count,
                    "LOCATION_NAME" => $LOCATION_NAME,
                    "LOGIN_TIME" => $LOGIN_TIME,
                    "START_TIME" => $START_TIME,
                    "END_TIME" => $END_TIME,
                    "TOTAL_KM" => $TOTAL_KM,
                    "DIVIDEDKM" => $DIVIDEDKM,
                    "APPROVE_KM" => $approve_km,
                    "RATEPERKM" => $PACKAGE_PRICE,
                    "AMOUNT" => $trip_amount,
                    "AMOUNTWITHTAX" => $tax_with_amount,
                    "ESCORT" => $ESCORTROUTE,
                    "REMARKS" => $REMARKS,
                    "SHED_KM" => $SHED_KM,
                    "TRIP_STATUS" => $TRIP_STATUS,
                    "GOOGLE_KM" => $GOOGLE_KM,
                    "CLOSE_STS" => $close_sts,
                    "DEVICE_KM" => $DEVICE_KM,
                    "GOOGLE_SHED_KM" => $GOOGLE_SHED_KM,
                    "CAB_ID" => $CAB_ID,
                    "VENDOR_ID" => $VENDOR_ID,
                    "ACTUAL_START_TIME" => $ACTUAL_START_TIME,
                    "ACTUAL_END_TIME" => $ACTUAL_END_TIME,
                    "BILLABLE_STS" => $billable_sts,
                    "EMPTY_KM" => $EMPTY_KM,
                    "ROUTE_ORDER" => $ROUTE_ORDER,
                    "ASSET_MOVEMENT" => $ASSET_MOVEMENT,
                    "TOLL_CHARGE" => $TOLL_CHARGE
                );
                $old_roster_id = $ROSTER_ID;

            }

            return $results;
        } catch (\Exception $e) {
            echo $e->getMessage();
        }
    }

    public function packageprice($vendor_id,$model_id)
	{
		 $sql="select PACKAGE_PRICE from tariff where VENDOR_ID='".$vendor_id."' and ACTIVE=1 and VEHICLE_MODEL_ID='".$model_id."' ";
		$result=DB::select($sql);
		if(count($result)>0)
		{
			$package_price= $result[0]->PACKAGE_PRICE;
		}
		else
		{
			echo "RosterM id: ".$model_id;
			exit;
		}
		if($package_price==0 || $package_price=='')
			{
				$errorlogpath = pathinfo(ini_get('error_log'));
				//$errorlogfile = "D:/xampp/htdocs/TMS_BACKUP/storage/logs/" . date('Y-m-d') . "-upload.log";
				$errorlogfile = "/var/www/html/TMS/storage/logs/APII_LOGS.log";
				if (file_exists($errorlogfile)) {
					ini_set('error_log', $errorlogfile);
				} else {
					$errfh = fopen($errorlogfile, "a+");
					if (is_resource($errfh)) {
						ini_set('error_log', $errorlogfile);
						fclose($errfh);
					}
				}
				error_log("~~~~~~~  URL    ~~~~~~  " , 0); 
				echo "Roster id MM: ".$model_id;
				exit;
			}  
		return $package_price;
	}

    public function RRDGetMisData2($from_date, $to_date, $trip_type, $vehiclevise, $employeevise)
    {
        $dbname = 'mysql4';
        $vendor = "";
        if ($vehiclevise == 0) {
            $cabid = "";
        } else {
            $cabid = "AND R.CAB_ID='$vehiclevise'";
        }

        if ($employeevise == 0) {
            $empid = "";
        } else {
            $empid = "AND EMPLOYEE_ID ='$employeevise'";
        }
        $RPS_TTLNOSHOW = env('RPS_TTLNOSHOW');
        $RS_TOTALTRIPSHEET_CANCEL = env('RS_TOTALTRIPSHEET_CANCEL');
        
        $query = "SELECT if(R.TRIP_TYPE = 'P',DATE(R.ESTIMATE_END_TIME),DATE(R.ESTIMATE_START_TIME)) as LOGIN_DATE, if(R.TRIP_TYPE = 'P',TIME(R.ESTIMATE_END_TIME),TIME(R.ESTIMATE_START_TIME)) as LOGIN_TIME, R.ROSTER_ID,R.TRIP_TYPE,C.CAB_ID,T.PACKAGE_PRICE,V.VEHICLE_REG_NO,VM.MODEL,VM.CAPACITY,VM.VEHICLE_MODEL_ID,VN.`NAME` as VENDOR_NAME,VN.VENDOR_ID,T.PACKAGE_PRICE * R.TOTAL_KM as TOTAL_AMOUNT,T.EXTRA_KMS_CHARGE,L.LOCATION_NAME,ES.ESCORT_ID,ES.ESCORT_NAME,if(RE.ROSTER_ID IS NULL,'NO','YES') as ESCORTROUTE,
		TIME(R.ACTUAL_START_TIME) AS START_TIME,E.PROJECT_NAME,E.DISTANCE,E.GENDER,E.NAME AS EMPNAME,(R.PASSENGER_ALLOT_COUNT+R.PASSENGER_CLUBING_COUNT) as route_count,R.PASSENGER_CLUBING_COUNT,R.PASSENGER_ALLOT_IN_ROUT_COUNT as SCHEDULE_COUNT,R.PASSENGER_ALLOT_COUNT,RP.EMPLOYEE_ID,TIME(R.ACTUAL_END_TIME) AS END_TIME,RP.ROSTER_PASSENGER_STATUS,RP.REMARKS,DS.DEVICE_KM,(R.TOTAL_KM+if(DS.SHED_KM is null || DS.SHED_KM=0 ,0,DS.SHED_KM)) as TOTAL_KM,DS.GOOGLE_KM,DS.GOOGLE_SHED_KM,DS.DEVIATION_KM,DS.SHED_KM,DS.TRIP_STATUS,R.ROSTER_STATUS,R.TOTAL_KM as ROSTER_TOTAL_KM,R.ACTUAL_START_TIME,R.ACTUAL_END_TIME,EC.EMPTY_KM,RP.ROUTE_ORDER,RP.ASSET_MOVEMENT,TP.TOLL_CHARGE  from rosters R
		inner join roster_passengers RP on RP.ROSTER_ID=R.ROSTER_ID and RP.ACTIVE=1
		inner join cab C on C.CAB_ID=R.CAB_ID
		inner join vehicles V on V.VEHICLE_ID=C.VEHICLE_ID
		inner join vehicle_models VM on VM.VEHICLE_MODEL_ID=V.VEHICLE_MODEL_ID
		inner join vendors VN on VN.VENDOR_ID=C.VENDOR_ID
		LEFT JOIN employees E ON E.EMPLOYEES_ID = RP.EMPLOYEE_ID AND E.BRANCH_ID = R.BRANCH_ID
		LEFT JOIN locations L ON L.LOCATION_ID = RP.LOCATION_ID 
		LEFT JOIN route_escorts RE ON RE.ROSTER_ID = R.ROSTER_ID AND RE.BRANCH_ID = R.BRANCH_ID AND RE.STATUS NOT IN (5,6)
		LEFT JOIN escorts ES ON ES.ESCORT_ID = RE.ESCORT_ID
		left join driver_billing_summary DS on DS.ROSTER_ID=R.ROSTER_ID and DS.ACTIVE=1
		left join tariff T on T.TARIFF_ID=DS.TARIFF_ID
		left join empty_cab_km EC on EC.CAB_ID=R.CAB_ID and EC.APPROVE_STATUS=2 and date(IN_OUT_TIME) between '$from_date' AND '$to_date'
		LEFT JOIN toll_payment TP on TP.ROSTER_ID=R.ROSTER_ID

		where R.BRANCH_ID=32 $vendor $cabid 
		
		and if(R.TRIP_TYPE='P',date(R.ESTIMATE_END_TIME),date(R.ESTIMATE_START_TIME)) BETWEEN '$from_date' AND '$to_date'
		and R.ACTIVE=1  and (R.ROSTER_STATUS  & $this->TRIPCLOSE or R.ROSTER_STATUS & $this->MANUALTRIPCLOSE)
		
		and R.ROSTER_STATUS not in($RS_TOTALTRIPSHEET_CANCEL) and 
		if(R.TRIP_TYPE='P' and RP.ROSTER_PASSENGER_STATUS &16 
		and RP.START_LAT!='',RP.ROSTER_PASSENGER_STATUS in(16,17,19,21,23,29,31,85),RP.ROSTER_PASSENGER_STATUS not in(16,17,19,21,23,29,31,85)) group by RP.ROSTER_PASSENGER_ID order by R.ROSTER_ID ASC,RP.ROUTE_ORDER DESC";

        return DB::connection("$dbname")->select($query);
    }

    public function passenger_cnt($roster_id,$TRIP_TYPE)
	{
		$sql="select count(RP.ROSTER_ID) as pas_cnt from roster_passengers RP
		inner join rosters R on R.ROSTER_ID=RP.ROSTER_ID 
		where
		RP.ACTIVE=1 and RP.ROSTER_ID='".$roster_id."' and 
		if(R.TRIP_TYPE='P' and RP.ROSTER_PASSENGER_STATUS &16 
		and RP.START_LAT!='',RP.ROSTER_PASSENGER_STATUS in(16,17,19,21,23,29,31,85),RP.ROSTER_PASSENGER_STATUS not in(16,17,19,21,23,29,31,85))";
		
		$result=DB::select($sql);
		return $result[0]->pas_cnt;
	}
}