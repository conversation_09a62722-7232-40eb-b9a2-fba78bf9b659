<?php

//namespace App\Http\Requests\Admin;
namespace App\Http\Requests\Admin;

use App\Helpers\CommonFunction;
use Closure;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;

class EscortResetRequest extends FormRequest
{
    protected CommonFunction $commonFunction;

    public function __construct(CommonFunction $commonFunction)
    {
        
    }

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        
        return [
            'route_escort_id' => ['required','numeric'],
            'selected_roster_id' => ['required','numeric'],
            ];
    }

    public function messages(): array
    {
        return [
            'selected_roster_id.required' => 'selected_roster_id field is required',       
            'route_escort_id.required' => 'route_escort_id field is required',       
                  
        ];
    }
   

    public function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json([
            'success' => false,
            'validation_error' => true,
            'message' => 'Validation errors',
            'data' => $validator->errors()
        ]));
    }
   
}
