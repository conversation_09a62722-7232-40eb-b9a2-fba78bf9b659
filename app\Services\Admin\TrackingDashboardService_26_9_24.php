<?php

namespace App\Services\Admin;

use App\Helpers\CommonFunction;
use App\Helpers\MyHelper;
use App\Models\Branch;
use App\Models\property;
use App\Models\Roster;
use App\Models\RosterPassenger;
use App\Models\Cab;
use Carbon\Carbon;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Crypt;

class TrackingDashboardService
{
    protected CommonFunction $commonFunction;
    protected ?Branch $currentBranch = null;

    public function __construct(CommonFunction $commonFunction)
    {
        $this->commonFunction = $commonFunction;
    }

    private function getPropertyValue()
    {
        $branch_id= Auth::user()->BRANCH_ID;
        //    exit;
        $property = Property::where('BRANCH_ID',  $branch_id)
            ->where('ACTIVE', MyHelper::$RS_ACTIVE)
            //->where('PROPERTIE_NAME', $propertyName)
            ->get();
			
        return $property;
    }

    public function tracking_dashboard($request): FoundationApplication|Response|ResponseFactory
    {

        $authUser = Auth::user();
        $tracking_type = $request->tracking_type;
        $date = Carbon::now();
        $curdatetime = $date->format("Y-m-d H:i:s");
        try {
            // echo "try";
            $PR_ESTIMATESUBTIME = MyHelper::$PR_ESTIMATESUBTIME;
            $PR_ESTIMATEADDTIME = MyHelper::$PR_ESTIMATEADDTIME;
            $PR_ESTIMATECURTIME = MyHelper::$PR_ESTIMATECURTIME;
            $PR_LEVEL2 = MyHelper::$PR_LEVEL2;
            $PR_ESTIMATECABALLOT = MyHelper::$PR_ESTIMATECABALLOT;
            $PR_LEVEL1 = MyHelper::$PR_LEVEL1;
            $PR_TRIPNOTEXECUTED = MyHelper::$PR_TRIPNOTEXECUTED;
            $PR_EMPLOYEE_BUFFER = MyHelper::$PR_EMPLOYEE_BUFFER;
			$user_type=$authUser->user_type;

            $property_result = $this->getPropertyValue();
            // print_r($property_result);
            // exit;
            for ($i = 0; $i < count($property_result); $i++) {
                $PROPERTIE_NAME = $property_result[$i]->PROPERTIE_NAME;
                $PROPERTIE_VALUE = $property_result[$i]->PROPERTIE_VALUE;
                switch ($PROPERTIE_NAME) {
                    case $PR_ESTIMATESUBTIME:
                        $ESTIMATE_SUBTIME = $PROPERTIE_VALUE;
                        break;
                    case $PR_ESTIMATEADDTIME:
                        $ESTIMATE_ADDTIME = $PROPERTIE_VALUE;
                        break;
                    case $PR_ESTIMATECURTIME:
                        $ESTIMATE_CURTIME = $PROPERTIE_VALUE;
                        break;
                    case $PR_LEVEL2:
                        $ESTIMATE_INTERVAL60 = $PROPERTIE_VALUE;
                        break;
                    case $PR_ESTIMATECABALLOT:
                        $ESTIMATE_CABALLOT = $PROPERTIE_VALUE;
                        break;
                    case $PR_LEVEL1:
                        $ESTIMATE_INTERVAL30 = $PROPERTIE_VALUE;
                        break;
                    case $PR_TRIPNOTEXECUTED:
                        $TRIP_NOT_EXECUTE = $PROPERTIE_VALUE;
                        break;
                     case $PR_EMPLOYEE_BUFFER:
                         $wapptime = $PROPERTIE_VALUE;
                        break;
                    default:
                        break;
                }
            }
			 if ($user_type == MyHelper::$ADMIN) {
				$vendorid = "";
				} else {
				$vendorid = "AND R.VENDOR_ID='$vendor_id'";
				}
            $track_data = Roster::query()
					->select(
                    'rosters.ROSTER_ID',
                    'rosters.BRANCH_ID',
                    'rosters.ROUTE_ID',
                    'rosters.TRIP_TYPE',
                    DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN rosters.ESTIMATE_END_TIME ELSE rosters.ESTIMATE_START_TIME END AS TRIPINTIME'),
                    DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN DATE(rosters.ESTIMATE_END_TIME) ELSE DATE(rosters.ESTIMATE_START_TIME) END AS TRIPDATE'),
                    DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN TIME(rosters.ESTIMATE_END_TIME) ELSE TIME(rosters.ESTIMATE_START_TIME) END AS TRIPTIME'),
                    'VE.NAME AS VENDORNAME',
                    'rosters.START_LOCATION',
                    'rosters.END_LOCATION',
                    'rosters.CAB_ID',
                    'rosters.VENDOR_ID',
                    'rosters.PASSENGER_ALLOT_COUNT',
                    'rosters.PASSENGER_CLUBING_COUNT',
                    'rosters.CAB_CAPACITY_COUNT',
                    'rosters.TRIP_APPROVED_KM',
                    'RD.TOLL_ROUTE_STATUS','rosters.ROSTER_STATUS',
                    DB::raw('IF(RE.STATUS IS NULL, 0, 1) AS ESCORTSTATUS'),
                    DB::raw('IF(RE.ESCORT_ID IS NULL, 0, 1) AS ESCORTALLOTSTATUS'),
                    'RE.ROUTE_ESCORT_ID',
                    DB::raw("IF(rosters.TRIP_TYPE = 'P', '$ESTIMATE_INTERVAL30', 'Interval 180 minute') AS EXECUTETIME"),
                    DB::raw('GROUP_CONCAT(RP.EMPLOYEE_ID) as empids0')
                )
                ->where('rosters.ACTIVE', MyHelper::$RS_ACTIVE)
                ->where('rosters.BRANCH_ID', $branch_id)
				 
				->whereIn('rosters.ROSTER_STATUSS',[$RS_NEWROSTER])
							
                ->whereIn('rosters.TRIP_TYPE', ['P', 'D'])
                ->leftjoin('roster_passengers as RP', 'RP.ROSTER_ID', '=', 'rosters.ROSTER_ID')
                ->leftJoin('vendors as VE', 'VE.VENDOR_ID', '=', 'rosters.VENDOR_ID')
                ->leftJoin('route_distance as RD', 'RD.ROSTER_ID', '=', 'rosters.ROSTER_ID')
                ->leftJoin('route_escorts as RE', 'RE.ROSTER_ID', '=', 'rosters.ROSTER_ID')
                ->groupBy('rosters.ROSTER_ID')
                ->havingRaw("TRIPINTIME BETWEEN SUBTIME('2019-07-01 01:30:00',$ESTIMATE_SUBTIME) AND ADDTIME('2019-07-30 01:30:00',$ESTIMATE_ADDTIME) AND '$curdatetime'>=SUBTIME(TRIPINTIME,$ESTIMATE_CURTIME)")
                ->orderBy("TRIPTIME")
                ->get();
            return response([
                'success' => true,
                'status' => 3,
                'employees' => $track_data,
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'No data',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function pagination_tracking_dashboard($request, bool $active = true): FoundationApplication|Response|ResponseFactory
    {
        try {


            $authUser = Auth::user();
			$branchid=$authUser->BRANCH_ID;
            $tracking_type = $request->tracking_type;
            $date = Carbon::now();
            $curdatetime = $date->format("Y-m-d H:i:s");
			$curdate= date('Y-m-d');


            $PR_ESTIMATESUBTIME = MyHelper::$PR_ESTIMATESUBTIME;
            $PR_ESTIMATEADDTIME = MyHelper::$PR_ESTIMATEADDTIME;
            $PR_ESTIMATECURTIME = MyHelper::$PR_ESTIMATECURTIME;
            $PR_LEVEL2 = MyHelper::$PR_LEVEL2;
            $PR_ESTIMATECABALLOT = MyHelper::$PR_ESTIMATECABALLOT;
            $PR_LEVEL1 = MyHelper::$PR_LEVEL1;
            $PR_TRIPNOTEXECUTED = MyHelper::$PR_TRIPNOTEXECUTED;
            $PR_EMPLOYEE_BUFFER = MyHelper::$PR_EMPLOYEE_BUFFER;
			
			$RS_ROSTER=MyHelper::$RS_ROSTER;
			
			$ESCORT_INTER=MyHelper::$ESCORT_INTER;
			$ESCORT_REMOVE=MyHelper::$ESCORT_REMOVE;
			
			$TD_ALLNORMAL=MyHelper::$TD_ALLNORMAL;
			$TD_NOTALLOTED=MyHelper::$TD_NOTALLOTED;
			$TD_ALLOTED=MyHelper::$TD_ALLOTED;
			$TD_TRIPACCEPTED=MyHelper::$TD_TRIPACCEPTED;
			$TD_TRIPREJECTED=MyHelper::$TD_TRIPREJECTED;
			$TD_TRIPEXECUTED=MyHelper::$TD_TRIPEXECUTED;
			$TD_NORESPONSE=MyHelper::$TD_NORESPONSE;
			$TD_TRIPNOTEXECUTED=MyHelper::$TD_TRIPNOTEXECUTED;
			$TD_BREAKDOWN=MyHelper::$TD_BREAKDOWN;
			$TD_WAITINGATPICKUPPOINT=MyHelper::$TD_WAITINGATPICKUPPOINT;
			$TD_SAFEDROP=MyHelper::$TD_SAFEDROP;
			
			
			$RS_NEWROSTER=MyHelper::$RS_NEWROSTER;
			$RS_TOTALALLOT=MyHelper::$RS_TOTALALLOT;
			$RS_TOTALACCEPT=MyHelper::$RS_TOTALACCEPT;
			$RS_TOTALREJECT=MyHelper::$RS_TOTALREJECT;
			$RS_TOTALEXECUTE=MyHelper::$RS_TOTALEXECUTE;
			$RS_TOTALBREAKDOWN=MyHelper::$RS_TOTALBREAKDOWN;
			
			$RPS_TTLARRIVAL=MyHelper::$RPS_TTLARRIVAL;
			$RPS_TTLCABDELAY=MyHelper::$RPS_TTLCABDELAY;
			
			
			

            $property_result = $this->getPropertyValue();
			$user_type=$authUser->user_type;
			$vendor_id= Auth::user()->vendor_id;
            // print_r($property_result);
            // exit;
            for ($i = 0; $i < count($property_result); $i++) {
                $PROPERTIE_NAME = $property_result[$i]->PROPERTIE_NAME;
                $PROPERTIE_VALUE = $property_result[$i]->PROPERTIE_VALUE;
                switch ($PROPERTIE_NAME) {
                    case $PR_ESTIMATESUBTIME:
                        $ESTIMATE_SUBTIME = $PROPERTIE_VALUE;
                        break;
                    case $PR_ESTIMATEADDTIME:
                        $ESTIMATE_ADDTIME = $PROPERTIE_VALUE;
                        break;
                    case $PR_ESTIMATECURTIME:
                        $ESTIMATE_CURTIME = $PROPERTIE_VALUE;
                        break;
                    case $PR_LEVEL2:
                        $ESTIMATE_INTERVAL60 = $PROPERTIE_VALUE;
                        break;
                    case $PR_ESTIMATECABALLOT:
                        $ESTIMATE_CABALLOT = $PROPERTIE_VALUE;
                        break;
                    case $PR_LEVEL1:
                        $ESTIMATE_INTERVAL30 = $PROPERTIE_VALUE;
                        break;
                    case $PR_TRIPNOTEXECUTED:
                        $TRIP_NOT_EXECUTE = $PROPERTIE_VALUE;
                        break;
                     case $PR_EMPLOYEE_BUFFER:
                         $wapptime = $PROPERTIE_VALUE;
						
                        break;
                    default:
                        break;
                }
            }

            $perPage = $request->input('per_page', MyHelper::$PAGINATION_PER_PAGE);
            $branch_id = Auth::user()->BRANCH_ID;
            $activeStatus = $active ? MyHelper::$RS_ACTIVE : MyHelper::$RS_INACTIVE;
			
			if($user_type == MyHelper::$ADMIN)
			{
				$vendorid = "";
			} else {
				//$vendorid = "AND R.VENDOR_ID='$vendor_id'";
				$vendorid = $vendor_id;
			}
			
//$curdatetime='2019-07-01 01:30:00';
//$curdate='2019-07-01';
       /*  $select_filed = "R.ROSTER_ID,R.BRANCH_ID,R.ROUTE_ID,R.TRIP_TYPE,
        IF(R.TRIP_TYPE = 'P',R.ESTIMATE_END_TIME,R.ESTIMATE_START_TIME) as TRIPINTIME,
        IF(R.TRIP_TYPE = 'P',DATE(R.ESTIMATE_END_TIME),DATE(R.ESTIMATE_START_TIME)) as TRIPDATE,
        IF(R.TRIP_TYPE = 'P',TIME(R.ESTIMATE_END_TIME),TIME(R.ESTIMATE_START_TIME)) as TRIPTIMES,
        VE.`NAME` AS VENDORNAME,R.START_LOCATION,R.END_LOCATION,R.CAB_ID,
        R.VENDOR_ID,R.PASSENGER_ALLOT_COUNT,R.PASSENGER_CLUBING_COUNT,R.CAB_CAPACITY_COUNT,R.TRIP_APPROVED_KM,RD.TOLL_ROUTE_STATUS,
		IF(RE.`STATUS` IS NULL,0,1) AS ESCORTSTATUS,IF(RE.ESCORT_ID IS NULL,0,1) AS ESCORTALLOTSTATUS,RE.ROUTE_ESCORT_ID,
        if(R.TRIP_TYPE = 'P','$ESTIMATE_INTERVAL30','Interval 180 minute') AS EXECUTETIME";

        $cabselect = "V.VEHICLE_REG_NO,VM.MODEL,RM.REASON,D.DRIVER_MOBILE";
        $vendor_join = "INNER JOIN vendors VE ON VE.VENDOR_ID=R.VENDOR_ID
		LEFT JOIN route_distance RD ON RD.ROSTER_ID = R.ROSTER_ID
      
	  LEFT JOIN route_escorts RE ON RE.ROSTER_ID = R.ROSTER_ID AND (DATE(R.ESTIMATE_END_TIME) = '$curdate' or date(R.ESTIMATE_START_TIME)='$curdate')
        AND RE.`STATUS` NOT IN ($ESCORT_INTER,$ESCORT_REMOVE)";
		

        $cabdetail_join = "INNER JOIN cab C ON C.CAB_ID=R.CAB_ID
        INNER JOIN vehicles V ON V.VEHICLE_ID=C.VEHICLE_ID
        INNER JOIN vehicle_models VM ON VM.VEHICLE_MODEL_ID=V.VEHICLE_MODEL_ID
        INNER JOIN drivers D ON D.DRIVERS_ID=C.DRIVER_ID
        INNER JOIN reason_master RM ON RM.REASON_ID=C.TARIFF_TYPE";
		$passenger_join="inner join roster_passengers RP on RP.ROSTER_ID=R.ROSTER_ID and R.ACTIVE=1";

        $whercond = "R.BRANCH_ID='$branchid' $vendorid and R.ACTIVE=".MyHelper::$RS_ACTIVE ." and R.TRIP_TYPE IN ('P','D')";
        $betweentime = "T.TRIPINTIME BETWEEN SUBTIME('$curdatetime',$ESTIMATE_SUBTIME) AND ADDTIME('$curdatetime',$ESTIMATE_ADDTIME)";
        $timeinterval = "AND (('$curdatetime'-$ESTIMATE_INTERVAL60)< T.TRIPINTIME)";
        
 */
			
			 switch ($tracking_type) 
			 {
				case $TD_ALLNORMAL:
					$data = Roster::query()
					->select(
                    'rosters.ROSTER_ID',
                    'rosters.BRANCH_ID',
                    'rosters.ROUTE_ID',
                    'rosters.TRIP_TYPE',
                    DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN rosters.ESTIMATE_END_TIME ELSE rosters.ESTIMATE_START_TIME END AS TRIPINTIME'),
                    DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN DATE(rosters.ESTIMATE_END_TIME) ELSE DATE(rosters.ESTIMATE_START_TIME) END AS TRIPDATE'),
                    DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN TIME(rosters.ESTIMATE_END_TIME) ELSE TIME(rosters.ESTIMATE_START_TIME) END AS TRIPTIME'),
                    'VE.NAME AS VENDORNAME',
                    'rosters.START_LOCATION',
                    'rosters.END_LOCATION',
                    'rosters.CAB_ID',
                    'rosters.VENDOR_ID',
                    'rosters.PASSENGER_ALLOT_COUNT',
                    'rosters.PASSENGER_CLUBING_COUNT',
                    'rosters.CAB_CAPACITY_COUNT',
                    'rosters.TRIP_APPROVED_KM',
                    'RD.TOLL_ROUTE_STATUS',
                    DB::raw('IF(RE.STATUS IS NULL, 0, 1) AS ESCORTSTATUS'),
                    DB::raw('IF(RE.ESCORT_ID IS NULL, 0, 1) AS ESCORTALLOTSTATUS'),
                    'RE.ROUTE_ESCORT_ID',
                    DB::raw("IF(rosters.TRIP_TYPE = 'P', '$ESTIMATE_INTERVAL30', 'Interval 180 minute') AS EXECUTETIME"),
                    DB::raw('GROUP_CONCAT(RP.EMPLOYEE_ID) as empids0')
                )
                ->where('rosters.ACTIVE', MyHelper::$RS_ACTIVE)
                ->where('rosters.ROSTER_STATUS', MyHelper::$RS_ROSTER)
				->where('rosters.BRANCH_ID', $branch_id)
				->when($vendorid, function ($query, $vendorid) {
						return $query->where('rosters.VENDOR_ID', $vendorid);
					})
                ->whereIn('rosters.TRIP_TYPE', ['P', 'D'])
                ->leftjoin('roster_passengers as RP', 'RP.ROSTER_ID', '=', 'rosters.ROSTER_ID')
                ->leftJoin('vendors as VE', 'VE.VENDOR_ID', '=', 'rosters.VENDOR_ID')
                ->leftJoin('route_distance as RD', 'RD.ROSTER_ID', '=', 'rosters.ROSTER_ID')
                ->leftJoin('route_escorts as RE', 'RE.ROSTER_ID', '=', 'rosters.ROSTER_ID')
                ->groupBy('rosters.ROSTER_ID')
                ->havingRaw("TRIPINTIME BETWEEN SUBTIME('$curdatetime',$ESTIMATE_SUBTIME) AND ADDTIME('$curdatetime',$ESTIMATE_ADDTIME) and  '$curdatetime' < SUBTIME(TRIPINTIME,$ESTIMATE_CURTIME)  "); 
				
				break;
				
				case $TD_NOTALLOTED:
					$status= explode(",",$RS_NEWROSTER); 
					
					$data = Roster::query()
					->select(
                    'rosters.ROSTER_ID',
                    'rosters.BRANCH_ID',
                    'rosters.ROUTE_ID',
                    'rosters.TRIP_TYPE',
                    DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN rosters.ESTIMATE_END_TIME ELSE rosters.ESTIMATE_START_TIME END AS TRIPINTIME'),
                    DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN DATE(rosters.ESTIMATE_END_TIME) ELSE DATE(rosters.ESTIMATE_START_TIME) END AS TRIPDATE'),
                    DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN TIME(rosters.ESTIMATE_END_TIME) ELSE TIME(rosters.ESTIMATE_START_TIME) END AS TRIPTIME'),
                    'VE.NAME AS VENDORNAME',
                    'rosters.START_LOCATION',
                    'rosters.END_LOCATION',
                    'rosters.CAB_ID',
                    'rosters.VENDOR_ID',
                    'rosters.PASSENGER_ALLOT_COUNT',
                    'rosters.PASSENGER_CLUBING_COUNT',
                    'rosters.CAB_CAPACITY_COUNT',
                    'rosters.TRIP_APPROVED_KM',
                    'RD.TOLL_ROUTE_STATUS','rosters.ROSTER_STATUS',
                    DB::raw('IF(RE.STATUS IS NULL, 0, 1) AS ESCORTSTATUS'),
                    DB::raw('IF(RE.ESCORT_ID IS NULL, 0, 1) AS ESCORTALLOTSTATUS'),
                    'RE.ROUTE_ESCORT_ID',
                    DB::raw("IF(rosters.TRIP_TYPE = 'P', '$ESTIMATE_INTERVAL30', 'Interval 180 minute') AS EXECUTETIME"),
                    DB::raw('GROUP_CONCAT(RP.EMPLOYEE_ID) as empids0')
                )
                ->where('rosters.ACTIVE', MyHelper::$RS_ACTIVE)
                ->where('rosters.BRANCH_ID', $branch_id)
				->when($vendorid, function ($query, $vendorid) {
						return $query->where('rosters.VENDOR_ID', $vendorid);
					})
				 
				->whereIn('rosters.ROSTER_STATUS',$status)
							
                ->whereIn('rosters.TRIP_TYPE', ['P', 'D'])
                ->leftjoin('roster_passengers as RP', 'RP.ROSTER_ID', '=', 'rosters.ROSTER_ID')
                ->leftJoin('vendors as VE', 'VE.VENDOR_ID', '=', 'rosters.VENDOR_ID')
                ->leftJoin('route_distance as RD', 'RD.ROSTER_ID', '=', 'rosters.ROSTER_ID')
                ->leftJoin('route_escorts as RE', 'RE.ROSTER_ID', '=', 'rosters.ROSTER_ID')
                ->groupBy('rosters.ROSTER_ID')
                ->havingRaw("TRIPINTIME BETWEEN SUBTIME('$curdatetime',$ESTIMATE_SUBTIME) AND ADDTIME('$curdatetime',$ESTIMATE_ADDTIME) AND '$curdatetime'>=SUBTIME(TRIPINTIME,$ESTIMATE_CURTIME)");
				
				break;
				
				//Trip Alloted
				case $TD_ALLOTED:   
				
				$status= explode(",",$RS_TOTALALLOT); 
				
				$data= DB::table('rosters')
				->leftJoin('cab as C', 'C.CAB_ID', '=', 'rosters.CAB_ID')
				->leftJoin('vehicles as VH', 'VH.VEHICLE_ID', '=', 'C.VEHICLE_ID')
				->leftJoin('vehicle_models as VM', 'VM.VEHICLE_MODEL_ID', '=', 'VH.VEHICLE_MODEL_ID')
				->leftJoin('drivers as D', 'D.DRIVERS_ID', '=', 'C.DRIVER_ID')
				->leftJoin('reason_master as RM', 'RM.REASON_ID', '=', 'C.TARIFF_TYPE')
				->leftJoin('roster_passengers as RP', 'RP.ROSTER_ID', '=', 'rosters.ROSTER_ID')
				->leftJoin('vendors as VE', 'VE.VENDOR_ID', '=', 'rosters.VENDOR_ID')
				->leftJoin('route_distance as RD', 'RD.ROSTER_ID', '=', 'rosters.ROSTER_ID')
				->leftJoin('route_escorts as RE', function ($join) use ($curdate,$ESCORT_INTER,$ESCORT_REMOVE) {
				$join->on('RE.ROSTER_ID', '=', 'rosters.ROSTER_ID')
             ->where(function ($query) use ($curdate) {
                 $query->whereDate('rosters.ESTIMATE_END_TIME', '=', $curdate)
                       ->orWhereDate('rosters.ESTIMATE_START_TIME', '=', $curdate);
             })
             ->whereNotIn('RE.STATUS',[$ESCORT_INTER,$ESCORT_REMOVE]);
           
    })
    ->select([
        'rosters.ROSTER_ID', 
        'rosters.BRANCH_ID', 
        'rosters.ROUTE_ID', 
        'rosters.TRIP_TYPE',
        DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN rosters.ESTIMATE_END_TIME ELSE rosters.ESTIMATE_START_TIME END AS TRIPINTIME'),
        DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN DATE(rosters.ESTIMATE_END_TIME) ELSE DATE(rosters.ESTIMATE_START_TIME) END AS TRIPDATE'),
        DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN TIME(rosters.ESTIMATE_END_TIME) ELSE TIME(rosters.ESTIMATE_START_TIME) END AS TRIPTIME'),
        'VE.NAME as VENDORNAME',
        'rosters.START_LOCATION', 
        'rosters.END_LOCATION', 
        'rosters.CAB_ID', 
        'rosters.VENDOR_ID', 
        'rosters.PASSENGER_ALLOT_COUNT',
        'rosters.PASSENGER_CLUBING_COUNT',
        'rosters.CAB_CAPACITY_COUNT',
        'rosters.CAB_ALLOT_TIME',
        'rosters.TRIP_APPROVED_KM',
        'VH.VEHICLE_REG_NO', 
        'VM.MODEL', 
        'RM.REASON', 
        'D.DRIVER_MOBILE', 
        'RD.TOLL_ROUTE_STATUS',
        'rosters.ROSTER_STATUS',
        DB::raw('IF(RE.STATUS IS NULL, 0, 1) AS ESCORTSTATUS'),
        DB::raw('IF(RE.ESCORT_ID IS NULL, 0, 1) AS ESCORTALLOTSTATUS'),
        'RE.ROUTE_ESCORT_ID',
        DB::raw('IF(rosters.TRIP_TYPE = "P", "Interval 30 minute", "Interval 180 minute") AS EXECUTETIME'),
        DB::raw('GROUP_CONCAT(RP.EMPLOYEE_ID) as empids0')
    ])
    ->where('rosters.ACTIVE', 1)
    ->where('rosters.BRANCH_ID', $branch_id)
    ->whereIn('rosters.ROSTER_STATUS', $status)
	->when($vendorid, function ($query, $vendorid) {
						return $query->where('rosters.VENDOR_ID', $vendorid);
					})
    ->whereIn('rosters.TRIP_TYPE', ['P', 'D'])
    ->groupBy('rosters.ROSTER_ID')
                ->havingRaw("TIMEDIFF('$curdatetime',`rosters`.`CAB_ALLOT_TIME`) < '$ESTIMATE_CABALLOT' and TRIPINTIME BETWEEN SUBTIME('$curdatetime',$ESTIMATE_SUBTIME) AND ADDTIME('$curdatetime',$ESTIMATE_ADDTIME) AND (('$curdatetime'-$ESTIMATE_INTERVAL60)< TRIPINTIME)");
				
				break;
				// Trip Accepted
				case $TD_TRIPACCEPTED:
				
				$status= explode(",",$RS_TOTALACCEPT); 
				
					$data = Roster::query()
					->select(
                    'rosters.ROSTER_ID',
                    'rosters.BRANCH_ID',
                    'rosters.ROUTE_ID',
                    'rosters.TRIP_TYPE',
                    DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN rosters.ESTIMATE_END_TIME ELSE rosters.ESTIMATE_START_TIME END AS TRIPINTIME'),
                    DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN DATE(rosters.ESTIMATE_END_TIME) ELSE DATE(rosters.ESTIMATE_START_TIME) END AS TRIPDATE'),
                    DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN TIME(rosters.ESTIMATE_END_TIME) ELSE TIME(rosters.ESTIMATE_START_TIME) END AS TRIPTIME'),
                    'VE.NAME AS VENDORNAME',
                    'rosters.START_LOCATION',
                    'rosters.END_LOCATION',
                    'rosters.CAB_ID',
                    'rosters.VENDOR_ID',
                    'rosters.PASSENGER_ALLOT_COUNT',
                    'rosters.PASSENGER_CLUBING_COUNT',
                    'rosters.CAB_CAPACITY_COUNT','rosters.CAB_ALLOT_TIME',
                    'rosters.TRIP_APPROVED_KM','VH.VEHICLE_REG_NO','VM.MODEL','RM.REASON','D.DRIVER_MOBILE',
                    'RD.TOLL_ROUTE_STATUS','rosters.ROSTER_STATUS',
                    DB::raw('IF(RE.STATUS IS NULL, 0, 1) AS ESCORTSTATUS'),
                    DB::raw('IF(RE.ESCORT_ID IS NULL, 0, 1) AS ESCORTALLOTSTATUS'),
                    'RE.ROUTE_ESCORT_ID',
                    DB::raw("IF(rosters.TRIP_TYPE = 'P', '$ESTIMATE_INTERVAL30', 'Interval 180 minute') AS EXECUTETIME"),
                    DB::raw('GROUP_CONCAT(RP.EMPLOYEE_ID) as empids0')
                )
                ->where('rosters.ACTIVE', MyHelper::$RS_ACTIVE)
                ->where('rosters.BRANCH_ID', $branch_id)
				->when($vendorid, function ($query, $vendorid) {
						return $query->where('rosters.VENDOR_ID', $vendorid);
					})
				 
				->whereIn('rosters.ROSTER_STATUS',$status)
							
                ->whereIn('rosters.TRIP_TYPE', ['P', 'D'])
				->leftjoin('cab as C','C.CAB_ID',"=","rosters.CAB_ID")
				->leftjoin('vehicles as VH','VH.VEHICLE_ID',"=","C.VEHICLE_ID")
				->leftjoin('vehicle_models as VM','VM.VEHICLE_MODEL_ID',"=","VH.VEHICLE_MODEL_ID")
				->leftjoin('drivers as D','D.DRIVERS_ID',"=","C.DRIVER_ID")
				->leftjoin('reason_master as RM','RM.REASON_ID',"=","C.TARIFF_TYPE")
                ->leftjoin('roster_passengers as RP', 'RP.ROSTER_ID', '=', 'rosters.ROSTER_ID')
                ->leftJoin('vendors as VE', 'VE.VENDOR_ID', '=', 'rosters.VENDOR_ID')
                ->leftJoin('route_distance as RD', 'RD.ROSTER_ID', '=', 'rosters.ROSTER_ID')
                //->leftJoin('route_escorts as RE', 'RE.ROSTER_ID', '=', 'rosters.ROSTER_ID',)
				/*  ->leftjoin('route_escorts as RE', function($join) use ($curdate, $ESCORT_INTER, $ESCORT_REMOVE) {
					$join->on('RE.ROSTER_ID', '=', 'rosters.ROSTER_ID')
						 ->whereDate('rosters.ESTIMATE_END_TIME', $curdate)
						 ->orWhereDate('rosters.ESTIMATE_START_TIME', $curdate)
						 ->whereNotIn('RE.STATUS', [$ESCORT_INTER, $ESCORT_REMOVE]); 
						})
				 */
				 ->leftJoin('route_escorts as RE', function ($join) use ($curdate,$ESCORT_INTER, $ESCORT_REMOVE) {
				$join->on('RE.ROSTER_ID', '=', 'rosters.ROSTER_ID')
				->where(function ($query) use ($curdate) {
                 $query->whereDate('rosters.ESTIMATE_END_TIME', '=', $curdate)
                       ->orWhereDate('rosters.ESTIMATE_START_TIME', '=', $curdate);
             })
             ->whereNotIn('RE.STATUS', [$ESCORT_INTER,$ESCORT_REMOVE]);
    })
				
                ->groupBy('rosters.ROSTER_ID')
                ->havingRaw("TRIPINTIME BETWEEN SUBTIME('$curdatetime',$ESTIMATE_SUBTIME) AND ADDTIME('$curdatetime',$ESTIMATE_ADDTIME) AND (('$curdatetime'-$ESTIMATE_INTERVAL60)< TRIPINTIME)");
				
				break;
				// Trip Rejected
				case $TD_TRIPREJECTED:
				
				$status= explode(",",$RS_TOTALREJECT); 
				
					$data = Roster::query()
					->select(
                    'rosters.ROSTER_ID',
                    'rosters.BRANCH_ID',
                    'rosters.ROUTE_ID',
                    'rosters.TRIP_TYPE',
                    DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN rosters.ESTIMATE_END_TIME ELSE rosters.ESTIMATE_START_TIME END AS TRIPINTIME'),
                    DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN DATE(rosters.ESTIMATE_END_TIME) ELSE DATE(rosters.ESTIMATE_START_TIME) END AS TRIPDATE'),
                    DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN TIME(rosters.ESTIMATE_END_TIME) ELSE TIME(rosters.ESTIMATE_START_TIME) END AS TRIPTIME'),
                    'VE.NAME AS VENDORNAME',
                    'rosters.START_LOCATION',
                    'rosters.END_LOCATION',
                    'rosters.CAB_ID',
                    'rosters.VENDOR_ID',
                    'rosters.PASSENGER_ALLOT_COUNT',
                    'rosters.PASSENGER_CLUBING_COUNT',
                    'rosters.CAB_CAPACITY_COUNT','rosters.CAB_ALLOT_TIME',
                    'rosters.TRIP_APPROVED_KM','VH.VEHICLE_REG_NO','VM.MODEL','RM.REASON','D.DRIVER_MOBILE',
                    'RD.TOLL_ROUTE_STATUS','rosters.ROSTER_STATUS',
                    DB::raw('IF(RE.STATUS IS NULL, 0, 1) AS ESCORTSTATUS'),
                    DB::raw('IF(RE.ESCORT_ID IS NULL, 0, 1) AS ESCORTALLOTSTATUS'),
                    'RE.ROUTE_ESCORT_ID',
                    DB::raw("IF(rosters.TRIP_TYPE = 'P', '$ESTIMATE_INTERVAL30', 'Interval 180 minute') AS EXECUTETIME"),
                    DB::raw('GROUP_CONCAT(RP.EMPLOYEE_ID) as empids0')
                )
                ->where('rosters.ACTIVE', MyHelper::$RS_ACTIVE)
                ->where('rosters.BRANCH_ID', $branch_id)
				->when($vendorid, function ($query, $vendorid) {
						return $query->where('rosters.VENDOR_ID', $vendorid);
					})
				 
				->whereIn('rosters.ROSTER_STATUS',$status)
							
                ->whereIn('rosters.TRIP_TYPE', ['P', 'D'])
				->leftjoin('cab as C','C.CAB_ID',"=","rosters.CAB_ID")
				->leftjoin('vehicles as VH','VH.VEHICLE_ID',"=","C.VEHICLE_ID")
				->leftjoin('vehicle_models as VM','VM.VEHICLE_MODEL_ID',"=","VH.VEHICLE_MODEL_ID")
				->leftjoin('drivers as D','D.DRIVERS_ID',"=","C.DRIVER_ID")
				->leftjoin('reason_master as RM','RM.REASON_ID',"=","C.TARIFF_TYPE")
                ->leftjoin('roster_passengers as RP', 'RP.ROSTER_ID', '=', 'rosters.ROSTER_ID')
                ->leftJoin('vendors as VE', 'VE.VENDOR_ID', '=', 'rosters.VENDOR_ID')
                ->leftJoin('route_distance as RD', 'RD.ROSTER_ID', '=', 'rosters.ROSTER_ID')
                ->leftJoin('route_escorts as RE', function ($join) use ($curdate,$ESCORT_INTER, $ESCORT_REMOVE) 
				{
					$join->on('RE.ROSTER_ID', '=', 'rosters.ROSTER_ID')
					->where(function ($query) use ($curdate) {
					 $query->whereDate('rosters.ESTIMATE_END_TIME', '=', $curdate)
						   ->orWhereDate('rosters.ESTIMATE_START_TIME', '=', $curdate);
					})
					->whereNotIn('RE.STATUS', [$ESCORT_INTER, $ESCORT_REMOVE]);
				})
				
                ->groupBy('rosters.ROSTER_ID')
                ->havingRaw("TIMEDIFF('$curdatetime',`rosters`.`CAB_ALLOT_TIME`) < '$ESTIMATE_CABALLOT' and TRIPINTIME BETWEEN SUBTIME('$curdatetime',$ESTIMATE_SUBTIME) AND ADDTIME('$curdatetime',$ESTIMATE_ADDTIME) AND (('$curdatetime'-$ESTIMATE_INTERVAL60)< TRIPINTIME)");
				
				break;
				
				//Trip Executed
				
				case $TD_TRIPEXECUTED: 
				
				$status= explode(",",$RS_TOTALEXECUTE); 
				
					$data = Roster::query()
					->select(
                    'rosters.ROSTER_ID',
                    'rosters.BRANCH_ID',
                    'rosters.ROUTE_ID',
                    'rosters.TRIP_TYPE',
                    DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN rosters.ESTIMATE_END_TIME ELSE rosters.ESTIMATE_START_TIME END AS TRIPINTIME'),
                    DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN DATE(rosters.ESTIMATE_END_TIME) ELSE DATE(rosters.ESTIMATE_START_TIME) END AS TRIPDATE'),
                    DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN TIME(rosters.ESTIMATE_END_TIME) ELSE TIME(rosters.ESTIMATE_START_TIME) END AS TRIPTIME'),
                    'VE.NAME AS VENDORNAME',
                    'rosters.START_LOCATION',
                    'rosters.END_LOCATION',
                    'rosters.CAB_ID',
                    'rosters.VENDOR_ID',
                    'rosters.PASSENGER_ALLOT_COUNT',
                    'rosters.PASSENGER_CLUBING_COUNT',
                    'rosters.CAB_CAPACITY_COUNT','rosters.CAB_ALLOT_TIME',
                    'rosters.TRIP_APPROVED_KM','VH.VEHICLE_REG_NO','VM.MODEL','RM.REASON','D.DRIVER_MOBILE',
                    'RD.TOLL_ROUTE_STATUS','rosters.ROSTER_STATUS',
                    DB::raw('IF(RE.STATUS IS NULL, 0, 1) AS ESCORTSTATUS'),
                    DB::raw('IF(RE.ESCORT_ID IS NULL, 0, 1) AS ESCORTALLOTSTATUS'),
                    'RE.ROUTE_ESCORT_ID',
                    //DB::raw("IF(rosters.TRIP_TYPE = 'P', '$ESTIMATE_INTERVAL30', 'Interval 180 minute') AS EXECUTETIME"),
					DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN "Interval 30 minute" ELSE "Interval 180 minute" END AS EXECUTETIME'),
                    DB::raw('GROUP_CONCAT(RP.EMPLOYEE_ID) as empids0')
                )
                ->where('rosters.ACTIVE', MyHelper::$RS_ACTIVE)
                ->where('rosters.BRANCH_ID', $branch_id)
				->when($vendorid, function ($query, $vendorid) {
						return $query->where('rosters.VENDOR_ID', $vendorid);
					})
				 
				->whereIn('rosters.ROSTER_STATUS',$status)
							
                ->whereIn('rosters.TRIP_TYPE', ['P', 'D'])
				->leftjoin('cab as C','C.CAB_ID',"=","rosters.CAB_ID")
				->leftjoin('vehicles as VH','VH.VEHICLE_ID',"=","C.VEHICLE_ID")
				->leftjoin('vehicle_models as VM','VM.VEHICLE_MODEL_ID',"=","VH.VEHICLE_MODEL_ID")
				->leftjoin('drivers as D','D.DRIVERS_ID',"=","C.DRIVER_ID")
				->leftjoin('reason_master as RM','RM.REASON_ID',"=","C.TARIFF_TYPE")
                ->leftjoin('roster_passengers as RP', 'RP.ROSTER_ID', '=', 'rosters.ROSTER_ID')
                ->leftJoin('vendors as VE', 'VE.VENDOR_ID', '=', 'rosters.VENDOR_ID')
                ->leftJoin('route_distance as RD', 'RD.ROSTER_ID', '=', 'rosters.ROSTER_ID')
                ->leftJoin('route_escorts as RE', function ($join) use ($curdate,$ESCORT_INTER, $ESCORT_REMOVE) 
				{
					$join->on('RE.ROSTER_ID', '=', 'rosters.ROSTER_ID')
					->where(function ($query) use ($curdate) {
					 $query->whereDate('rosters.ESTIMATE_END_TIME', '=', $curdate)
						   ->orWhereDate('rosters.ESTIMATE_START_TIME', '=', $curdate);
					})
					->whereNotIn('RE.STATUS', [$ESCORT_INTER, $ESCORT_REMOVE]);
				})
				
                ->groupBy('rosters.ROSTER_ID')
               // ->havingRaw("TRIPINTIME BETWEEN SUBTIME('$curdatetime',$ESTIMATE_SUBTIME) AND ADDTIME('$curdatetime',$ESTIMATE_ADDTIME) AND (('$curdatetime'-$ESTIMATE_INTERVAL60)< TRIPINTIME) and  (('$curdatetime'- IF(TRIP_TYPE = 'P', '$ESTIMATE_INTERVAL30', 'Interval 180 minute') < TRIPINTIME)");
                ->havingRaw("TRIPINTIME BETWEEN SUBTIME('$curdatetime',$ESTIMATE_SUBTIME) AND ADDTIME('$curdatetime',$ESTIMATE_ADDTIME) AND (('$curdatetime'-$ESTIMATE_INTERVAL60)< TRIPINTIME) ");
				//and  (DATE_SUB('$curdatetime',INTERVAL IF(TRIP_TYPE = 'P',  '$ESTIMATE_INTERVAL30', 180) MINUTE) < TRIPINTIME)
				break;
				
				case $TD_NORESPONSE:   
				
				$status= explode(",",$RS_TOTALALLOT); 
				
				$data= Roster::query()
				->leftJoin('cab as C', 'C.CAB_ID', '=', 'rosters.CAB_ID')
				->leftJoin('vehicles as VH', 'VH.VEHICLE_ID', '=', 'C.VEHICLE_ID')
				->leftJoin('vehicle_models as VM', 'VM.VEHICLE_MODEL_ID', '=', 'VH.VEHICLE_MODEL_ID')
				->leftJoin('drivers as D', 'D.DRIVERS_ID', '=', 'C.DRIVER_ID')
				->leftJoin('reason_master as RM', 'RM.REASON_ID', '=', 'C.TARIFF_TYPE')
				->leftJoin('roster_passengers as RP', 'RP.ROSTER_ID', '=', 'rosters.ROSTER_ID')
				
				//->leftJoin('roster_passengers AS RP', 'RP.ROSTER_ID', '=', 'rosters.ROSTER_ID')
				->leftJoin('employees AS E', function ($join) use ($branch_id) {
				 $join->on('E.EMPLOYEES_ID', '=', 'RP.EMPLOYEE_ID')
				->where('E.BRANCH_ID',$branch_id);
					})
				
				->leftJoin('vendors as VE', 'VE.VENDOR_ID', '=', 'rosters.VENDOR_ID')
				->leftJoin('route_distance as RD', 'RD.ROSTER_ID', '=', 'rosters.ROSTER_ID')
				->leftJoin('route_escorts as RE', function ($join) use ($curdate,$ESCORT_INTER,$ESCORT_REMOVE) {
				$join->on('RE.ROSTER_ID', '=', 'rosters.ROSTER_ID')
             ->where(function ($query) use ($curdate) {
                 $query->whereDate('rosters.ESTIMATE_END_TIME', '=', $curdate)
                       ->orWhereDate('rosters.ESTIMATE_START_TIME', '=', $curdate);
             })
             ->whereNotIn('RE.STATUS',[$ESCORT_INTER,$ESCORT_REMOVE]);
           
    })
    ->select([
        'rosters.ROSTER_ID', 
        'rosters.BRANCH_ID', 
        'rosters.ROUTE_ID', 
        'rosters.TRIP_TYPE',
        DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN rosters.ESTIMATE_END_TIME ELSE rosters.ESTIMATE_START_TIME END AS TRIPINTIME'),
        DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN DATE(rosters.ESTIMATE_END_TIME) ELSE DATE(rosters.ESTIMATE_START_TIME) END AS TRIPDATE'),
        DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN TIME(rosters.ESTIMATE_END_TIME) ELSE TIME(rosters.ESTIMATE_START_TIME) END AS TRIPTIME'),
        'VE.NAME as VENDORNAME',
        'rosters.START_LOCATION', 
        'rosters.END_LOCATION', 
        'rosters.CAB_ID', 
        'rosters.VENDOR_ID', 
        'rosters.PASSENGER_ALLOT_COUNT',
        'rosters.PASSENGER_CLUBING_COUNT',
        'rosters.CAB_CAPACITY_COUNT',
        'rosters.CAB_ALLOT_TIME',
        'rosters.TRIP_APPROVED_KM',
        'VH.VEHICLE_REG_NO', 
        'VM.MODEL', 
        'RM.REASON', 
        'D.DRIVER_MOBILE', 
        'RD.TOLL_ROUTE_STATUS',
        'rosters.ROSTER_STATUS','E.NAME as empname','E.GENDER',
        DB::raw('IF(RE.STATUS IS NULL, 0, 1) AS ESCORTSTATUS'),
        DB::raw('IF(RE.ESCORT_ID IS NULL, 0, 1) AS ESCORTALLOTSTATUS'),
        'RE.ROUTE_ESCORT_ID',
        DB::raw('IF(rosters.TRIP_TYPE = "P", "Interval 30 minute", "Interval 180 minute") AS EXECUTETIME'),
        DB::raw('GROUP_CONCAT(RP.EMPLOYEE_ID) as empids0')
    ])
    ->where('rosters.ACTIVE', 1)
    ->where('rosters.BRANCH_ID', $branch_id)
    ->whereIn('rosters.ROSTER_STATUS', $status)
	->when($vendorid, function ($query, $vendorid) {
						return $query->where('rosters.VENDOR_ID', $vendorid);
					})
    ->whereIn('rosters.TRIP_TYPE', ['P', 'D'])
    ->groupBy('rosters.ROSTER_ID')
                ->havingRaw("TIMEDIFF('$curdatetime',`rosters`.`CAB_ALLOT_TIME`) > '$ESTIMATE_CABALLOT' and TRIPINTIME BETWEEN SUBTIME('$curdatetime',$ESTIMATE_SUBTIME) AND ADDTIME('$curdatetime',$ESTIMATE_ADDTIME) AND (('$curdatetime'-$ESTIMATE_INTERVAL60)< TRIPINTIME)");
				
				break;
				
				//Trip Not Executed
				case $TD_TRIPNOTEXECUTED:
				
				$status= explode(",",$RS_TOTALACCEPT); 
				
					$data = Roster::query()
					->select(
                    'rosters.ROSTER_ID',
                    'rosters.BRANCH_ID',
                    'rosters.ROUTE_ID',
                    'rosters.TRIP_TYPE','rosters.ESTIMATE_START_TIME',
                    DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN rosters.ESTIMATE_END_TIME ELSE rosters.ESTIMATE_START_TIME END AS TRIPINTIME'),
                    DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN DATE(rosters.ESTIMATE_END_TIME) ELSE DATE(rosters.ESTIMATE_START_TIME) END AS TRIPDATE'),
                    DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN TIME(rosters.ESTIMATE_END_TIME) ELSE TIME(rosters.ESTIMATE_START_TIME) END AS TRIPTIME'),
                    'VE.NAME AS VENDORNAME',
                    'rosters.START_LOCATION',
                    'rosters.END_LOCATION',
                    'rosters.CAB_ID',
                    'rosters.VENDOR_ID',
                    'rosters.PASSENGER_ALLOT_COUNT',
                    'rosters.PASSENGER_CLUBING_COUNT',
                    'rosters.CAB_CAPACITY_COUNT','rosters.CAB_ALLOT_TIME',
                    'rosters.TRIP_APPROVED_KM','VH.VEHICLE_REG_NO','VM.MODEL','RM.REASON','D.DRIVER_MOBILE',
                    'RD.TOLL_ROUTE_STATUS','rosters.ROSTER_STATUS',
                    DB::raw('IF(RE.STATUS IS NULL, 0, 1) AS ESCORTSTATUS'),
                    DB::raw('IF(RE.ESCORT_ID IS NULL, 0, 1) AS ESCORTALLOTSTATUS'),
                    'RE.ROUTE_ESCORT_ID',
                    DB::raw("IF(rosters.TRIP_TYPE = 'P', '$ESTIMATE_INTERVAL30', 'Interval 180 minute') AS EXECUTETIME"),
                    DB::raw('GROUP_CONCAT(RP.EMPLOYEE_ID) as empids0')
                )
                ->where('rosters.ACTIVE', MyHelper::$RS_ACTIVE)
                ->where('rosters.BRANCH_ID', $branch_id)
				->when($vendorid, function ($query, $vendorid) {
						return $query->where('rosters.VENDOR_ID', $vendorid);
					})
				 
				->whereIn('rosters.ROSTER_STATUS',$status)
							
                ->whereIn('rosters.TRIP_TYPE', ['P', 'D'])
				->leftjoin('cab as C','C.CAB_ID',"=","rosters.CAB_ID")
				->leftjoin('vehicles as VH','VH.VEHICLE_ID',"=","C.VEHICLE_ID")
				->leftjoin('vehicle_models as VM','VM.VEHICLE_MODEL_ID',"=","VH.VEHICLE_MODEL_ID")
				->leftjoin('drivers as D','D.DRIVERS_ID',"=","C.DRIVER_ID")
				->leftjoin('reason_master as RM','RM.REASON_ID',"=","C.TARIFF_TYPE")
                ->leftjoin('roster_passengers as RP', 'RP.ROSTER_ID', '=', 'rosters.ROSTER_ID')
                ->leftJoin('vendors as VE', 'VE.VENDOR_ID', '=', 'rosters.VENDOR_ID')
                ->leftJoin('route_distance as RD', 'RD.ROSTER_ID', '=', 'rosters.ROSTER_ID')
                //->leftJoin('route_escorts as RE', 'RE.ROSTER_ID', '=', 'rosters.ROSTER_ID',)
				/*  ->leftjoin('route_escorts as RE', function($join) use ($curdate, $ESCORT_INTER, $ESCORT_REMOVE) {
					$join->on('RE.ROSTER_ID', '=', 'rosters.ROSTER_ID')
						 ->whereDate('rosters.ESTIMATE_END_TIME', $curdate)
						 ->orWhereDate('rosters.ESTIMATE_START_TIME', $curdate)
						 ->whereNotIn('RE.STATUS', [$ESCORT_INTER, $ESCORT_REMOVE]); 
						})
				 */
				 ->leftJoin('route_escorts as RE', function ($join) use ($curdate,$ESCORT_INTER, $ESCORT_REMOVE) {
				$join->on('RE.ROSTER_ID', '=', 'rosters.ROSTER_ID')
				->where(function ($query) use ($curdate) {
                 $query->whereDate('rosters.ESTIMATE_END_TIME', '=', $curdate)
                       ->orWhereDate('rosters.ESTIMATE_START_TIME', '=', $curdate);
             })
             ->whereNotIn('RE.STATUS', [$ESCORT_INTER,$ESCORT_REMOVE]);
    })
				
                ->groupBy('rosters.ROSTER_ID')
                ->havingRaw("TIMEDIFF('$curdatetime',ESTIMATE_START_TIME) < '$TRIP_NOT_EXECUTE' and TRIPINTIME BETWEEN SUBTIME('$curdatetime',$ESTIMATE_SUBTIME) AND ADDTIME('$curdatetime',$ESTIMATE_ADDTIME) AND (('$curdatetime'-$ESTIMATE_INTERVAL60)< TRIPINTIME)");
				
				break;
				
				// Break Down
			case $TD_BREAKDOWN:
				
					$status= explode(",",$RS_TOTALBREAKDOWN); 
					$data = Roster::query()
							->select(
								'rosters.ROSTER_ID',
								'rosters.BRANCH_ID',
								'rosters.ROUTE_ID',
								'rosters.TRIP_TYPE',
								'rosters.ESTIMATE_START_TIME',
								DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN rosters.ESTIMATE_END_TIME ELSE rosters.ESTIMATE_START_TIME END AS TRIPINTIME'),
								DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN DATE(rosters.ESTIMATE_END_TIME) ELSE DATE(rosters.ESTIMATE_START_TIME) END AS TRIPDATE'),
								DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN TIME(rosters.ESTIMATE_END_TIME) ELSE TIME(rosters.ESTIMATE_START_TIME) END AS TRIPTIME'),
								'VE.NAME AS VENDORNAME',
								'rosters.START_LOCATION',
								'rosters.END_LOCATION',
								'rosters.CAB_ID',
								'rosters.VENDOR_ID',
								'rosters.PASSENGER_ALLOT_IN_ROUT_COUNT',
								'rosters.TRIP_APPROVED_KM',
								'rosters.UPDATED_BY',
								'rosters.updated_at',
								'VH.VEHICLE_REG_NO',
								'D.DRIVERS_NAME',
								'D.DRIVER_MOBILE',
								'CA.ACTION',
								'RM.REASON',
								'U.name AS updateby'
							)
							->join('cab AS C', 'C.CAB_ID', '=', 'rosters.CAB_ID')
							->join('vendors AS VE', 'VE.VENDOR_ID', '=', 'rosters.VENDOR_ID')
							->join('vehicles AS VH', 'VH.VEHICLE_ID', '=', 'C.VEHICLE_ID')
							->join('vehicle_models AS VM', 'VM.VEHICLE_MODEL_ID', '=', 'VH.VEHICLE_MODEL_ID')
							->join('drivers AS D', 'D.DRIVERS_ID', '=', 'C.DRIVER_ID')
							
							
							 ->Join('cab_allocation AS CA', function ($join) use ($status) {
								 $join->on('CA.ROSTER_ID', '=', 'rosters.ROSTER_ID')
								->whereIn('CA.ACCEPTANCE_REJECT_STATE', $status);
				
								})
							
							->leftJoin('reason_master AS RM', 'RM.REASON_ID', '=', 'CA.REJECT_REASON_ID')
							->leftJoin('users AS U', 'U.id', '=', 'rosters.UPDATED_BY')
							->where('rosters.ACTIVE', MyHelper::$RS_INACTIVE)
							->where('rosters.BRANCH_ID', $branch_id)
							->whereIn('rosters.ROSTER_STATUS', $status)
							
							->whereDate('rosters.ESTIMATE_END_TIME', $curdate)
							//->where('CA.ACCEPTANCE_REJECT_STATE', $status)
							->when($vendorid, function ($query) use ($vendorid) {
								return $query->where('rosters.VENDOR_ID', $vendorid);
							})
							->groupBy('rosters.ROSTER_ID');   


				
				break;
				
				//Waitting At Pickup Point  Waiting At PickUp Point
					case $TD_WAITINGATPICKUPPOINT:
				
					$rps_status= explode(",",$RPS_TTLARRIVAL);
					$rps_delay_status= explode(",",$RPS_TTLCABDELAY);
					$status=array_merge($rps_status,$rps_delay_status);
					$data = Roster::query()
							->select(
								'rosters.ROSTER_ID',
								'rosters.BRANCH_ID',
								'rosters.TRIP_TYPE','rosters.ROUTE_ID',
								'RP.EMPLOYEE_ID','RP.DRIVER_ARRIVAL_TIME','RP.ESTIMATE_END_TIME','E.NAME as empname','E.GENDER',
								DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN rosters.ESTIMATE_END_TIME ELSE rosters.ESTIMATE_START_TIME END AS TRIPINTIME'),
								DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN DATE(rosters.ESTIMATE_END_TIME) ELSE DATE(rosters.ESTIMATE_START_TIME) END AS TRIPDATE'),
								DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN TIME(rosters.ESTIMATE_END_TIME) ELSE TIME(rosters.ESTIMATE_START_TIME) END AS TRIPTIME'),
								'VE.NAME AS VENDORNAME',
								'rosters.START_LOCATION',
								'rosters.END_LOCATION',
								'rosters.CAB_ID',
								'rosters.VENDOR_ID',
								'rosters.PASSENGER_ALLOT_IN_ROUT_COUNT',
								'rosters.TRIP_APPROVED_KM',
								'rosters.UPDATED_BY',
								'rosters.updated_at',
								'VH.VEHICLE_REG_NO',
								'D.DRIVERS_NAME',
								'D.DRIVER_MOBILE','B.BRANCH_NAME','VM.MODEL','L.LOCATION_NAME'
							)
							->join('cab AS C', 'C.CAB_ID', '=', 'rosters.CAB_ID')
							->join('vendors AS VE', 'VE.VENDOR_ID', '=', 'rosters.VENDOR_ID')
							->join('vehicles AS VH', 'VH.VEHICLE_ID', '=', 'C.VEHICLE_ID')
							->join('vehicle_models AS VM', 'VM.VEHICLE_MODEL_ID', '=', 'VH.VEHICLE_MODEL_ID')
							->join('drivers AS D', 'D.DRIVERS_ID', '=', 'C.DRIVER_ID')
							->Join('branch AS B', 'B.BRANCH_ID', '=', 'rosters.BRANCH_ID')
							->Join('reason_master AS RM', 'RM.REASON_ID', '=', 'C.TARIFF_TYPE')
							
							->Join('roster_passengers AS RP', 'RP.ROSTER_ID', '=', 'rosters.ROSTER_ID')
							->Join('locations AS L', 'L.LOCATION_ID', '=', 'RP.LOCATION_ID')
							
							
							->Join('employees AS E', function ($join) use ($branch_id) {
								 $join->on('E.EMPLOYEES_ID', '=', 'RP.EMPLOYEE_ID')
								->where('E.BRANCH_ID',$branch_id);
				
								})
							
							->where('rosters.TRIP_TYPE', 'P')
							
							->where('rosters.BRANCH_ID', $branch_id)
							
							->whereIn('RP.ROSTER_PASSENGER_STATUS', $status)
							
							->when($vendorid, function ($query) use ($vendorid) {
								return $query->where('rosters.VENDOR_ID', $vendorid);
							})
							//->groupBy('rosters.ROSTER_ID')
							->havingRaw("TIMEDIFF('$curdatetime',RP.DRIVER_ARRIVAL_TIME) > '$wapptime' AND RP.ESTIMATE_END_TIME BETWEEN SUBTIME('$curdatetime',10000) AND ADDTIME('$curdatetime',480000) AND '$curdatetime'>= SUBTIME(RP.ESTIMATE_END_TIME,040000)");

							
				break;
				
			//Safe Drop
					case $TD_SAFEDROP:
					
					$curdatetime='2024-09-20 16:29:32';
				
					$data = Roster::query()
							->select(
								'rosters.ROSTER_ID',
								'rosters.BRANCH_ID',
								'rosters.ROUTE_ID',
								'rosters.TRIP_TYPE','RP.ROSTER_PASSENGER_ID','RP.ROUTE_ORDER','E.ADDRESS','RP.ACTUAL_END_TIME',
								'RP.EMPLOYEE_ID','RP.DRIVER_ARRIVAL_TIME','RP.ESTIMATE_END_TIME','E.NAME as empname','E.GENDER',
							DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN rosters.ESTIMATE_END_TIME ELSE rosters.ESTIMATE_START_TIME END AS TRIPINTIME'),
							DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN DATE(rosters.ESTIMATE_END_TIME) ELSE DATE(rosters.ESTIMATE_START_TIME) END AS TRIPDATE'),
							DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN TIME(rosters.ESTIMATE_END_TIME) ELSE TIME(rosters.ESTIMATE_START_TIME) END AS TRIPTIME'),
								DB::raw('CASE WHEN RP.ROSTER_PASSENGER_STATUS & 256 THEN 1 ELSE 0 END AS SAFEDROP'),
								'rosters.ESTIMATE_START_TIME','RP.ROSTER_PASSENGER_STATUS',
								'VE.NAME AS VENDORNAME',
								'rosters.START_LOCATION',
								'rosters.END_LOCATION',
								'rosters.CAB_ID',
								'rosters.VENDOR_ID',
								'rosters.PASSENGER_ALLOT_IN_ROUT_COUNT',
								'rosters.TRIP_APPROVED_KM',
								'rosters.UPDATED_BY',
								'rosters.updated_at',
								'VH.VEHICLE_REG_NO',
								'D.DRIVERS_NAME',
								'D.DRIVER_MOBILE','B.BRANCH_NAME',DB::raw('if(E.GENDER = "F",1,0) AS FEMALE')
							)
							->join('cab AS C', 'C.CAB_ID', '=', 'rosters.CAB_ID')
							->join('vendors AS VE', 'VE.VENDOR_ID', '=', 'rosters.VENDOR_ID')
							->join('vehicles AS VH', 'VH.VEHICLE_ID', '=', 'C.VEHICLE_ID')
							->join('vehicle_models AS VM', 'VM.VEHICLE_MODEL_ID', '=', 'VH.VEHICLE_MODEL_ID')
							->join('drivers AS D', 'D.DRIVERS_ID', '=', 'C.DRIVER_ID')
							->Join('branch AS B', 'B.BRANCH_ID', '=', 'rosters.BRANCH_ID')
							->Join('reason_master AS RM', 'RM.BRANCH_ID', '=', DB::raw($branch_id))
							
							//->Join('reason_log AS RL', 'RL.ROSTER_PASSENGER_ID', '=', 'RP.ROSTER_PASSENGER_ID')
							->Join('roster_passengers AS RP', 'RP.ROSTER_ID', '=', 'rosters.ROSTER_ID')
							->leftJoin('reason_log AS RL', function ($join) {
								 $join->on('RL.ROSTER_PASSENGER_ID', '=', 'RP.ROSTER_PASSENGER_ID')
								->where('RL.REASON_ID','RM.REASON_ID');
                            })
							
							->Join('locations AS L', 'L.LOCATION_ID', '=', 'RP.LOCATION_ID')
							->Join('employees AS E', function ($join) use ($branch_id) {
								$join->on('E.EMPLOYEES_ID', '=', 'RP.EMPLOYEE_ID')
								->where('E.BRANCH_ID',$branch_id);
								})
                                ->where("RM.REASON", "=", 'Safe Drop' )
                                ->where("RM.BRANCH_ID", "=", $branch_id)
								
							->havingRaw("rosters.BRANCH_ID = $branch_id AND DATE(rosters.ESTIMATE_START_TIME) = '$curdate' AND rosters.ESTIMATE_START_TIME BETWEEN SUBTIME('$curdatetime',10000) AND ADDTIME('$curdatetime',480000) AND '$curdatetime'>=SUBTIME(rosters.ESTIMATE_START_TIME,040000) AND rosters.TRIP_TYPE = 'D' AND (RP.ROSTER_PASSENGER_STATUS & 32 OR RP.ROSTER_PASSENGER_STATUS & 128) ");  
							
				break;
				
				
			 }

            $filterModel = $request->input('filterModel');
            if ($filterModel) {
                foreach ($filterModel as $field => $filter) {
                    if (isset($filter['filter']) && $filter['filter'] !== '') {
                        $value = $filter['filter'];
                        $type = $filter['type'];

                        switch ($field) {
                            case 'EMPLOYEES_ID':
                                $data->where('E.EMPLOYEES_ID', 'like', "%{$value}%");
                                break;
                            case 'ROSTER_ID':
                                $data->where('rosters.ROSTER_ID', 'like', "%{$value}%");
                                break;
                            case 'ROUTE_ID':
                                $data->where('rosters.ROUTE_ID', 'like', "%{$value}%");
                                break;
                            case 'VENDORNAME':
                                $data->where('VE.NAME', 'like', "%{$value}%");
                                break;
                            case 'PASSENGER_ALLOT_COUNT':
                                $data->where('rosters.PASSENGER_ALLOT_COUNT', 'like', "%{$value}%");
                                break;
                            case 'START_LOCATION':
                                $data->where('rosters.START_LOCATION', 'like', "%{$value}%");
                                break;
                            case 'END_LOCATION':
                                $data->where('rosters.END_LOCATION', 'like', "%{$value}%");
                                break;
                            case 'VEHICLE_REG_NO':
                                $data->where('VH.VEHICLE_REG_NO', 'like', "%{$value}%");
                                break;
                            case 'MODEL':
                                $data->where('VM.MODEL', 'like', "%{$value}%");
                                break;
                            case 'TRIPTIME':
                                $data->where('rosters.ESTIMATE_END_TIME', 'like', "%{$value}%")
                                ->orwhere('rosters.ESTIMATE_START_TIME', 'like', "%{$value}%");
                                break;
                            case 'TRIP_APPROVED_KM':
                                $data->where('rosters.TRIP_APPROVED_KM', 'like', "%{$value}%");
                                break;
                            case 'DRIVERS_NAME':
                                $data->where('D.DRIVERS_NAME', 'like', "%{$value}%");
                                break;
                            case 'DRIVER_MOBILE':
                                $data->where('D.DRIVER_MOBILE', 'like', "%{$value}%");
                                break;
                            case 'TARIFF_TYPE':
                                $data->where('RM.REASON', 'like', "%{$value}%");
                                break;
                            case 'BRANCH_NAME':
                                $data->where('B.BRANCH_NAME', 'like', "%{$value}%");
                                break;
                            case 'LOCATION_NAME':
                                $data->where('L.LOCATION_NAME', 'like', "%{$value}%");
                                break;

                        }
                    }
                }
            }


            if ($request->has('orderBy') && !empty($request->input('orderBy'))) {
                $orderBy = $request->input('orderBy');
                $order = $request->input('order', 'asc');
                $data->orderBy($orderBy, $order);
            } else {
				
                $data->orderBy("TRIPTIME");
            }


            if ($perPage === MyHelper::$PAGINATION_PER_PAGE_ALL) {
                $paginatedData = $data->paginate($data->count());
            } else {
                $perPage = is_numeric($perPage) ? (int)$perPage : MyHelper::$PAGINATION_PER_PAGE;
				
                $paginatedData = $data->paginate($perPage);
            }
			/* $paginatedData = $paginatedData->map(function ($item) {
				$item->decrypted_name = Crypt::decrypt($item->empname);
				return $item;
			});
 */
            return response([
                'success' => true,
                'status' => 3,
                "normal_dashboard_data" => $paginatedData,
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'Data Pagination Unsuccessful first' : 'Data Pagination asasda Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }
	
	public function dashboard_count(): FoundationApplication|Response|ResponseFactory
    {

        $authUser = Auth::user();
        $branch_id = $authUser->BRANCH_ID;
		$RPTTLTYPE = explode(',',MyHelper::$RP_TTLTYPE);
		$RP_PICKROUTE = MyHelper::$RP_PICKROUTE;
		$RS_NEWROSTER=MyHelper::$RS_NEWROSTER;
		$RS_TOTALALLOT=MyHelper::$RS_TOTALALLOT;
		$RS_TOTALACCEPT=MyHelper::$RS_TOTALACCEPT;
		$RS_TOTALREJECT=MyHelper::$RS_TOTALREJECT;
		$RS_TOTALEXECUTE=MyHelper::$RS_TOTALEXECUTE;
		$RS_TOTALBREAKDOWN=MyHelper::$RS_TOTALBREAKDOWN;
		$RPS_TTLARRIVAL=MyHelper::$RPS_TTLARRIVAL;
		$RPS_TTLCABDELAY=MyHelper::$RPS_TTLCABDELAY;
		$RS_TOTALTRIPCLOSE=MyHelper::$RS_TOTALTRIPCLOSE;
		$RS_TOTALMANUALTRIPCLOSE=MyHelper::$RS_TOTALMANUALTRIPCLOSE;
		$RS_TOTALTRIPSHEETACCEPT=MyHelper::$RS_TOTALTRIPSHEETACCEPT;
		$RS_TOTALDELAYROUTES=MyHelper::$RS_TOTALDELAYROUTES;
		
			
		
        $date = Carbon::now();
        $currentDateTime = $date->format("Y-m-d H:i:s");
		$normal_cnt = 0;
        $notalloted_cnt = 0;
        $alloted_cnt = 0;
        $tripaccept_cnt = 0;
        $triprejected_cnt = 0;
        $tripnotexecute_cnt = 0;
        $tripexecuted_cnt = 0;
        $noresponse_cnt = 0;
        $tripclose_cnt = 0;
		$all_cnt = 0;
		
        try {
            
            $PR_ESTIMATESUBTIME = MyHelper::$PR_ESTIMATESUBTIME;
            $PR_ESTIMATEADDTIME = MyHelper::$PR_ESTIMATEADDTIME;
            $PR_ESTIMATECURTIME = MyHelper::$PR_ESTIMATECURTIME;
            $PR_LEVEL2 = MyHelper::$PR_LEVEL2;
            $PR_ESTIMATECABALLOT = MyHelper::$PR_ESTIMATECABALLOT;
            $PR_LEVEL1 = MyHelper::$PR_LEVEL1;
            $PR_TRIPNOTEXECUTED = MyHelper::$PR_TRIPNOTEXECUTED;
            $PR_EMPLOYEE_BUFFER = MyHelper::$PR_EMPLOYEE_BUFFER;
			$user_type=$authUser->user_type;

            $property_result = $this->getPropertyValue();
           
            for ($i = 0; $i < count($property_result); $i++) {
                $PROPERTIE_NAME = $property_result[$i]->PROPERTIE_NAME;
                $PROPERTIE_VALUE = $property_result[$i]->PROPERTIE_VALUE;
                switch ($PROPERTIE_NAME) {
                    case $PR_ESTIMATESUBTIME:
                        $ESTIMATE_SUBTIME = $PROPERTIE_VALUE;
                        break;
                    case $PR_ESTIMATEADDTIME:
                        $ESTIMATE_ADDTIME = $PROPERTIE_VALUE;
                        break;
                    case $PR_ESTIMATECURTIME:
                        $ESTIMATE_CURTIME = $PROPERTIE_VALUE;
                        break;
                    case $PR_LEVEL2:
                        $ESTIMATE_INTERVAL60 = $PROPERTIE_VALUE;
                        break;
                    case $PR_ESTIMATECABALLOT:
                        $ESTIMATE_CABALLOT = $PROPERTIE_VALUE;
                        break;
                    case $PR_LEVEL1:
                        $ESTIMATE_INTERVAL30 = $PROPERTIE_VALUE;
                        break;
                    case $PR_TRIPNOTEXECUTED:
                        $TRIP_NOT_EXECUTE = $PROPERTIE_VALUE;
                        break;
                     case $PR_EMPLOYEE_BUFFER:
                         $wapptime = $PROPERTIE_VALUE;
                        break;
                    default:
                        break;
                }
            }
			if($user_type == MyHelper::$ADMIN) 
			{
				$vendorid = "";
				} else {
				$vendorid = $vendorid;
				}
        for($x=0;$x<count($RPTTLTYPE);$x++)
        {
            if($RPTTLTYPE[$x] == $RP_PICKROUTE)
            {
                 $TRIPTIME = 'ESTIMATE_END_TIME';
                $TREXECUTE = "DATE_SUB('$currentDateTime',$ESTIMATE_INTERVAL30)"; 
				
				
            }
            else
            {
                $TRIPTIME = 'ESTIMATE_START_TIME';
                $TREXECUTE = "SUBTIME('$currentDateTime',30000)";
            }

$results = DB::table('rosters')
    ->select(
        DB::raw("SUM(IF(ROSTER_STATUS IN (1, 3) AND ESTIMATE_END_TIME BETWEEN SUBTIME('$currentDateTime', $ESTIMATE_SUBTIME) AND ADDTIME('$currentDateTime', $ESTIMATE_ADDTIME) AND PASSENGER_ALLOT_COUNT > 0 AND '$currentDateTime' < SUBTIME($TRIPTIME, $ESTIMATE_CURTIME), 1, 0)) AS normal_cnt"),
        
        DB::raw("SUM(IF(ROSTER_STATUS IN ($RS_NEWROSTER) AND $TRIPTIME BETWEEN SUBTIME('$currentDateTime', $ESTIMATE_SUBTIME) AND ADDTIME('$currentDateTime', $ESTIMATE_ADDTIME) AND PASSENGER_ALLOT_COUNT > 0 AND '$currentDateTime' >= SUBTIME($TRIPTIME, $ESTIMATE_CURTIME), 1, 0)) AS notalloted_cnt"),
        
        DB::raw("SUM(IF(ROSTER_STATUS IN ($RS_TOTALALLOT) AND $TRIPTIME BETWEEN SUBTIME('$currentDateTime', $ESTIMATE_SUBTIME) AND ADDTIME('$currentDateTime', $ESTIMATE_ADDTIME) AND ('$currentDateTime' - $ESTIMATE_INTERVAL60) < $TRIPTIME AND TIMEDIFF('$currentDateTime',CAB_ALLOT_TIME) < '$ESTIMATE_CABALLOT', 1, 0)) AS alloted_cnt"),
        
        DB::raw("SUM(IF(ROSTER_STATUS IN ($RS_TOTALACCEPT) AND $TRIPTIME BETWEEN SUBTIME('$currentDateTime', $ESTIMATE_SUBTIME) AND ADDTIME('$currentDateTime', $ESTIMATE_ADDTIME) AND ('$currentDateTime' - $ESTIMATE_INTERVAL60) < $TRIPTIME, 1, 0)) AS tripaccept_cnt"),
        
        DB::raw("SUM(IF(ROSTER_STATUS IN ($RS_TOTALREJECT) AND $TRIPTIME BETWEEN SUBTIME('$currentDateTime', $ESTIMATE_SUBTIME) AND ADDTIME('$currentDateTime', $ESTIMATE_ADDTIME) AND ('$currentDateTime' - $ESTIMATE_INTERVAL60) < $TRIPTIME, 1, 0)) AS triprejected_cnt"),
        
        DB::raw("SUM(IF(ROSTER_STATUS IN ($RS_TOTALACCEPT) AND $TRIPTIME BETWEEN SUBTIME('$currentDateTime', $ESTIMATE_SUBTIME) AND ADDTIME('$currentDateTime', $ESTIMATE_ADDTIME) AND TIMEDIFF('$currentDateTime', ESTIMATE_START_TIME) < '$TRIP_NOT_EXECUTE', 1, 0)) AS tripnotexecute_cnt"),
        
        DB::raw("SUM(IF(ROSTER_STATUS IN ($RS_TOTALEXECUTE) AND $TRIPTIME BETWEEN SUBTIME('$currentDateTime', $ESTIMATE_SUBTIME) AND ADDTIME('$currentDateTime',$ESTIMATE_ADDTIME) AND ($TREXECUTE < $TRIPTIME), 1, 0)) AS tripexecuted_cnt"),
        
        DB::raw("SUM(IF(ROSTER_STATUS IN ($RS_TOTALALLOT) AND $TRIPTIME BETWEEN SUBTIME('$currentDateTime', $ESTIMATE_SUBTIME) AND ADDTIME('$currentDateTime', $ESTIMATE_ADDTIME) AND ('$currentDateTime' - $ESTIMATE_INTERVAL60) < $TRIPTIME AND TIMEDIFF('$currentDateTime', CAB_ALLOT_TIME) > '$ESTIMATE_CABALLOT', 1, 0)) AS noresponse_cnt"),
        
        DB::raw("SUM(IF(ROSTER_STATUS IN ($RS_TOTALTRIPCLOSE, $RS_TOTALMANUALTRIPCLOSE, $RS_TOTALTRIPSHEETACCEPT, $RS_TOTALDELAYROUTES) AND             $TRIPTIME BETWEEN SUBTIME('$currentDateTime', $ESTIMATE_SUBTIME) AND ADDTIME('$currentDateTime', $ESTIMATE_ADDTIME) AND ('$currentDateTime' < $TRIPTIME), 1, 0)) AS tripclose_cnt")
    )
    ->where('BRANCH_ID', $branch_id)
    ->where('ACTIVE', '1')
    ->when($vendorid, function ($query) use ($vendorid) {
        return $query->where('rosters.VENDOR_ID', $vendorid);
    })
    ->whereBetween($TRIPTIME, [
        DB::raw("SUBTIME('$currentDateTime', $ESTIMATE_SUBTIME)"),
        DB::raw("ADDTIME('$currentDateTime', $ESTIMATE_ADDTIME)")
    ])
    ->where('TRIP_TYPE', $RPTTLTYPE[$x])
    ->get();	
			$normal_cnt = $normal_cnt + $results[0]->normal_cnt;
            $notalloted_cnt = $notalloted_cnt + $results[0]->notalloted_cnt;
            $alloted_cnt = $alloted_cnt + $results[0]->alloted_cnt;
            $tripaccept_cnt = $tripaccept_cnt + $results[0]->tripaccept_cnt;
            $triprejected_cnt = $triprejected_cnt + $results[0]->triprejected_cnt;
            $tripnotexecute_cnt = $tripnotexecute_cnt + $results[0]->tripnotexecute_cnt;
            $tripexecuted_cnt = $tripexecuted_cnt + $results[0]->tripexecuted_cnt;
            $noresponse_cnt = $noresponse_cnt + $results[0]->noresponse_cnt;
            $tripclose_cnt = $tripclose_cnt + $results[0]->tripclose_cnt;
            
        }
		$breakdown_count=0;$waiting_cnt=0;
		$breakdown_count=$this->breakdown_count();
		
		$wapp_count=$this->wapp_count($wapptime);
		

  $returnarray = array("normal_cnt"=>$normal_cnt,"notalloted_cnt"=>$notalloted_cnt,"alloted_cnt"=>$alloted_cnt,"tripaccept_cnt"=>$tripaccept_cnt,
        "triprejected_cnt"=>$triprejected_cnt,"tripnotexecute_cnt"=>$tripnotexecute_cnt,"tripexecuted_cnt"=>$tripexecuted_cnt,
        "noresponse_cnt"=>$noresponse_cnt,"tripclose_cnt"=>$tripclose_cnt,"all_cnt"=>$all_cnt,"breakdown_count"=>$breakdown_count,"wapp_count"=>$wapp_count);
		//wapp_count() ,breakdown_count,//overtime_count //ttlasf_count //
                
            return response([
                'success' => true,
                'status' => 3,
                'dashboard_count' => $returnarray,
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'No data',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }
	
	public function breakdown_count():int
    {
		try
		{
		$currtDate = date("Y-m-d");
		$vendor = Auth::user()->user_type;
        $BRANCH_ID = Auth::user()->BRANCH_ID;
        $vendor_id = Auth::user()->vendor_id;
        $dbname = Auth::user()->dbname;
        $vendorid = "";
        $RS_INACTIVE = MyHelper::$RS_INACTIVE;
        $RS_TOTALBREAKDOWN = MyHelper::$RS_TOTALBREAKDOWN;
		

        if ($vendor == MyHelper::$ADMIN) {
            $vendorid = "";
        } else {
           // $vendorid = "AND R.VENDOR_ID='$vendor_id'";
		   $vendorid=$vendor_id;
        }
       
	   $breakdown_cnt=0;
	  $status= explode(',', $RS_TOTALBREAKDOWN);
       
		$result=DB::table('rosters as R')
		->select(
					DB::raw('COUNT(R.ROUTE_ID) as readcnt'),
					DB::raw('SUM(CASE WHEN CA.ACTION IS NULL THEN 1 ELSE 0 END) AS unreadcnt')
				)
			 ->Join('cab_allocation AS CA', function ($join) use ($status) {
								 $join->on('CA.ROSTER_ID', '=', 'R.ROSTER_ID')
								 ->on('CA.CAB_ID', '=', 'R.CAB_ID')
								->whereIn('CA.ACCEPTANCE_REJECT_STATE',$status);
				
								}) 
			->whereIn('R.ROSTER_STATUS', explode(',', $RS_TOTALBREAKDOWN))
			->where('R.BRANCH_ID', '=', $BRANCH_ID)
			->whereDate('R.ESTIMATE_END_TIME', '=', $currtDate)
			->where('R.ACTIVE', '=', $RS_INACTIVE)
			->when($vendorid, function ($query) use ($vendorid) {
					return $query->where('R.VENDOR_ID', $vendorid);
				})
			->get();
		
		$retcnt = count($result);
		
        if ($retcnt > 0) {
            $breakdown_cnt = $result[0]->readcnt;
            $unreadbreak = $result[0]->unreadcnt;
        }
		
		return $breakdown_cnt;
	}catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'No data',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
	}
	
public function wapp_count($wapptime):string
    {
		try
		{
		$date = Carbon::now();
		$curdatetime = $date->format("Y-m-d H:i:s");
		$currtDate = date('Y-m-d');
		$vendor = Auth::user()->user_type;
        $BRANCH_ID = Auth::user()->BRANCH_ID;
        $vendor_id = Auth::user()->vendor_id;
        $dbname = Auth::user()->dbname;
        $vendorid = "";
        $RS_ACTIVE = MyHelper::$RS_ACTIVE;
        $RPS_TTLARRIVAL = MyHelper::$RPS_TTLARRIVAL;
        $RPS_TTLCABDELAY = MyHelper::$RPS_TTLCABDELAY;
        $RS_ACTIVE = MyHelper::$RS_ACTIVE;
		$rps_status= explode(",",$RPS_TTLARRIVAL);
		$rps_delay_status= explode(",",$RPS_TTLCABDELAY);
		$status=array_merge($rps_status,$rps_delay_status);
		
		

        if ($vendor == MyHelper::$ADMIN) {
            $vendorid = "";
        } else {
            $vendorid = "AND R.VENDOR_ID='$vendor_id'";
		   //$vendorid=$vendor_id;
        }
       /*
		$result=DB::table('roster_passengersS as RP')
		->select(
					DB::raw('COUNT(RP.EMPLOYEE_ID) as ttlwapp'),'RP.DRIVER_ARRIVAL_TIME','RP.ESTIMATE_END_TIME','R.CAB_ID','R.TRIP_TYPE'
				)
			 ->join("rosters as R","R.ROSTER_ID","=","RP.ROSTER_ID")
			 
			->whereIn('RP.ROSTER_PASSENGER_STATUS', $status)
			->where('R.BRANCH_ID', '=', $BRANCH_ID)
			
			->where('R.ACTIVE', '=', $RS_ACTIVE)
			->when($vendorid, function ($query) use ($vendorid) {
					return $query->where('R.VENDOR_ID', $vendorid);
				})
				
			 	->whereBetween('RS.ESTIMATE_END_TIME', [
						DB::raw("SUBTIME('$currtDatetime', 10000)"),
						DB::raw("ADDTIME('$currtDatetime', 480000)")
					]) 
				
				->havingRaw("TIMEDIFF('$currtDatetime',RS.DRIVER_ARRIVAL_TIME)> '$wapptime' and R.CAB_ID!='' AND R.TRIP_TYPE='P'  AND '$currtDatetime'>=SUBTIME(RS.ESTIMATE_END_TIME,040000)")
				->get();*/
				 $wapp_data = "SELECT COUNT(RS.EMPLOYEE_ID) as ttlwapp
					FROM roster_passengers RS
					INNER JOIN rosters R ON R.ROSTER_ID = RS.ROSTER_ID and R.ACTIVE=$RS_ACTIVE
					WHERE TIMEDIFF('$curdatetime',RS.DRIVER_ARRIVAL_TIME)> '$wapptime' AND 
					RS.ROSTER_PASSENGER_STATUS IN ($RPS_TTLARRIVAL,$RPS_TTLCABDELAY) and R.CAB_ID!=''
					and R.BRANCH_ID='$BRANCH_ID' $vendorid AND R.TRIP_TYPE='P' AND RS.ESTIMATE_END_TIME BETWEEN 
					SUBTIME('$curdatetime',10000) AND ADDTIME('$curdatetime',480000) AND '$curdatetime'>=SUBTIME(RS.ESTIMATE_END_TIME,040000) ";

					$retqry = DB::select($wapp_data);
			
		$retcnt = count($retqry);
		
        if ($retcnt > 0) {
            $waiting_cnt = $retqry[0]->ttlwapp;
        }
		
		return $waiting_cnt;
	}catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'No data',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
	}

    public function roster_details($request): FoundationApplication|Response|ResponseFactory
    {
        $roster_id = $request->roster_id;
        $date = Carbon::now();
        $curdatetime = $date->format("Y-m-d H:i:s");
        try 
		{
			$RS_ACTIVE = MyHelper::$RS_ACTIVE;
			$branchid = Auth::user()->BRANCH_ID;
			$trip_type = DB::table("rosters as R")->select("R.TRIP_TYPE")
						->where("R.ROSTER_ID", "=", $roster_id)->get();
		   // $order = $trip_type[0]->TRIP_TYPE == 'P' ? 'DESC' : 'ASC';

			$order = 'ASC'; 
			if(isset($trip_type[0]) && $trip_type[0]->TRIP_TYPE == 'P'){
				$order = 'DESC';
			}  

            $RS_ACTIVE = MyHelper::$RS_ACTIVE; // Assuming this is defined somewhere

            $data = RosterPassenger::query()
                ->select(
                    'E.NAME as empname',
                    'E.ADDRESS',
                    'B.BRANCH_NAME',
                    'E.DISTANCE',
                    'E.GENDER',
                    'R.ROUTE_ID',
                    'R.ROSTER_STATUS',
                    'R.TRIP_TYPE',
                    DB::raw("IF(R.TRIP_TYPE = 'P', R.ESTIMATE_END_TIME, R.ESTIMATE_START_TIME) AS ESTIMATE_END_TIME"),
                    DB::raw("IF(R.TRIP_TYPE = 'P', 'roster_passengers.ACTUAL_START_TIME', 'roster_passengers.ACTUAL_END_TIME') AS ACTUAL_START_TIME"),
                    DB::raw("IF(R.TRIP_TYPE = 'P', 'roster_passengers.DRIVER_ARRIVAL_TIME', 'roster_passengers.ACTUAL_START_TIME') AS DRIVER_ARRIVAL_TIME"),
                    'roster_passengers.ESTIMATE_START_TIME',
                    'L.LOCATION_NAME',
                    'roster_passengers.EMPLOYEE_ID',
                    'roster_passengers.ROSTER_PASSENGER_STATUS',
                    'roster_passengers.ROSTER_PASSENGER_ID',
                    'roster_passengers.ROUTE_ORDER'
                )
                ->join('rosters AS R', 'R.ROSTER_ID', '=', 'roster_passengers.ROSTER_ID')
                ->join('employees AS E', function ($join) use ($branchid) {
                    $join->on('E.EMPLOYEES_ID', '=', 'roster_passengers.EMPLOYEE_ID')
                         ->where('E.BRANCH_ID', '=', $branchid);
                })
                ->join('locations AS L', 'L.LOCATION_ID', '=', 'roster_passengers.LOCATION_ID')
                ->join('branch AS B', 'B.BRANCH_ID', '=', DB::raw("'$branchid'"))
                ->where('R.BRANCH_ID', '=', $branchid)
                ->where('roster_passengers.ROSTER_ID', '=', $roster_id)
                ->where('roster_passengers.ACTIVE', '=', $RS_ACTIVE);
                //->orderBy('RP.ROUTE_ORDER', $order)
                //->get();

                $filterModel = $request->input('filterModel');
                if ($filterModel) {
                    foreach ($filterModel as $field => $filter) {
                        if (isset($filter['filter']) && $filter['filter'] !== '') {
                            $value = $filter['filter'];
                            $type = $filter['type'];
    
                            switch ($field) {
                                case 'EMPLOYEES_ID':
                                    $data->where('E.EMPLOYEES_ID', 'like', "%{$value}%");
                                    break;
                                case 'ROSTER_ID':
                                    $data->where('rosters.ROSTER_ID', 'like', "%{$value}%");
                                    break;
                                case 'ROUTE_ID':
                                    $data->where('rosters.ROUTE_ID', 'like', "%{$value}%");
                                    break;
                                 case 'START_LOCATION':
                                    $data->where('rosters.START_LOCATION', 'like', "%{$value}%");
                                    break;
                                case 'END_LOCATION':
                                    $data->where('rosters.END_LOCATION', 'like', "%{$value}%");
                                    break;
                                case 'VEHICLE_REG_NO':
                                    $data->where('VH.VEHICLE_REG_NO', 'like', "%{$value}%");
                                    break;
                                case 'MODEL':
                                    $data->where('VM.MODEL', 'like', "%{$value}%");
                                    break;
                                case 'TRIPTIME':
                                    $data->where('rosters.ESTIMATE_END_TIME', 'like', "%{$value}%")
                                    ->orwhere('rosters.ESTIMATE_START_TIME', 'like', "%{$value}%");
                                    break;
                                case 'TRIP_APPROVED_KM':
                                    $data->where('rosters.TRIP_APPROVED_KM', 'like', "%{$value}%");
                                    break;
                                case 'DRIVERS_NAME':
                                    $data->where('D.DRIVERS_NAME', 'like', "%{$value}%");
                                    break;
                                case 'DRIVER_MOBILE':
                                    $data->where('D.DRIVER_MOBILE', 'like', "%{$value}%");
                                    break;
                                case 'LOCATION_NAME':
                                    $data->where('L.LOCATION_NAME', 'like', "%{$value}%");
                                    break;
    
                            }
                        }
                    }
                }
    
    
                if ($request->has('orderBy') && !empty($request->input('orderBy'))) {
                    $orderBy = $request->input('orderBy');
                    $order = $request->input('order', 'asc');
                    $data->orderBy($orderBy, $order);
                } else {
                    
                    $data->orderBy("roster_passengers.ROUTE_ORDER", $order);
                }
                $perPage = $request->input('per_page', MyHelper::$PAGINATION_PER_PAGE);
    
                if ($perPage === MyHelper::$PAGINATION_PER_PAGE_ALL) {
                    $paginatedData = $data->paginate($data->count());
                } else {
                    $perPage = is_numeric($perPage) ? (int)$perPage : MyHelper::$PAGINATION_PER_PAGE;
                    
                    $paginatedData = $data->paginate($perPage);
                }
               
                return response([
                    'success' => true,
                    'status' => 3,
                    'roster_details' =>$paginatedData,
                ]);
			
		}
		catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'No data',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }
	
    public function get_reason_details($request): FoundationApplication|Response|ResponseFactory
    {
        
        try 
		{
            $branchid = Auth::user()->BRANCH_ID;
            $categoryName = $request->categoryName;
           $reason= $this->commonFunction->getReasonList($categoryName,$branchid);

                return response([
                    'success' => true,
                    'status' => 3,
                    'reason' => $reason,
                ]);
			
		}
		catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'No data',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }
	
    public function roster_vendor_change($request): FoundationApplication|Response|ResponseFactory
    {
        
        try 
		{
            print_r($request->all());
            exit;
            DB::beginTransaction();
            $date = Carbon::now();
            $branchid = Auth::user()->BRANCH_ID;
            $vendor_id = $request->vendor_id;
            $roster_id = $request->roster_id;
           
            $roster = Roster::where('ACTIVE', MyHelper::$RS_ACTIVE)
               // ->where('BRANCH_ID', $authUser->BRANCH_ID)
                ->findOrFail($roster_id);

            $roster->update([
                'REMARKS' => $request->input('remark'),
                'VENDOR_ID' => $vendor_id,
                'UPDATED_BY' => Auth::user()->id,
                'UPDATED_AT' => $date->format("Y-m-d H:i:s"),
            ]);

            DB::commit();

                return response([
                    'success' => true,
                    'status' => 3,
                    'Vendor' =>'Vendor Update Successfully',
                ]);
			
		}
		catch (\Throwable|\Exception $e) {
            DB::rollBack();
            $this->commonFunction->logException($e);
           
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'No data',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }
	
	
	


}
