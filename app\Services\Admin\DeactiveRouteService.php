<?php

namespace App\Services\Admin;

use App\Helpers\CommonFunction;
use App\Helpers\MyHelper;
use Carbon\Carbon;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use App\Models\property;
use App\Models\Branch;
use App\Models\Roster;
use App\Models\RosterPassenger;
use App\Models\Cab_allocation;

class DeactiveRouteService
{
    protected CommonFunction $commonFunction;
    protected ?Branch $currentBranch = null;

    public function __construct(CommonFunction $commonFunction)
    {
        $this->commonFunction = $commonFunction;
    }


    public function fetch_Deactive_Route_Details($request, bool $active = true): FoundationApplication|Response|ResponseFactory
    {
        try {
            $perPage = $request->input('per_page', MyHelper::$PAGINATION_PER_PAGE);
            $branch_id = Auth::user()->BRANCH_ID;
            $user_type = Auth::user()->user_type;
            $vendor_id = Auth::user()->vendor_id;
            $name = Auth::user()->name;

           
            if ($name != 'SURESHPOLY') {
                $cond = "rosters.CAB_ID IS NULL";
            } else {
                $cond = "";
            }



            $from_date = Carbon::parse($request->from_date)->startOfDay()->toDateTimeString();
            $to_date = Carbon::parse($request->to_date)->endOfDay()->toDateTimeString();

            $RS_ACTIVE = MyHelper::$RS_ACTIVE;

            $data = Roster::query()
                ->select(
                    'rosters.ROSTER_ID',
                    'rosters.BRANCH_ID',
                    'rosters.ROUTE_ID',
                    'rosters.START_LOCATION',
                    'rosters.END_LOCATION',
                    'rosters.ESTIMATE_START_TIME', 
                    'rosters.ESTIMATE_END_TIME',
                    'rosters.TRIP_TYPE',
                    DB::raw("if(rosters.TRIP_TYPE='P',rosters.ESTIMATE_END_TIME,rosters.ESTIMATE_START_TIME) as LOGIN_TIME"),
                    'rosters.CAB_ID',
                    'V.VEHICLE_REG_NO',
                    'rosters.PASSENGER_ALLOT_COUNT',
                    'VE.NAME as vendor_name',

                )

                ->join('roster_passengers as RP', function ($join) {
                    $join->on('RP.ROSTER_ID', '=', 'rosters.ROSTER_ID');
                })

                ->leftJoin('cab as  C', 'C.CAB_ID', '=', 'rosters.CAB_ID')
                ->leftJoin('vehicles as V', 'V.VEHICLE_ID', '=', 'C.VEHICLE_ID')
                ->leftJoin('vendors as  VE', 'VE.VENDOR_ID', '=', 'rosters.VENDOR_ID')

                ->where('rosters.ACTIVE', $RS_ACTIVE)
                ->where('rosters.BRANCH_ID', $branch_id)
                ->whereNotIn('rosters.ROSTER_STATUS', ['8193', '8205', '8283', '8221', '8281', '8201', '8225', '8285', '8195'])

                ->when($cond, function ($query, $cond) {
                    return $query->havingRaw($cond);
                })
                ->havingRaw("(rosters.ESTIMATE_END_TIME BETWEEN  '$from_date' AND '$to_date' OR  rosters.ESTIMATE_START_TIME BETWEEN  '$from_date' AND '$to_date')")
                ->groupBy('rosters.ROSTER_ID');

            $filterModel = $request->input('filterModel');
            if ($filterModel) {
                foreach ($filterModel as $field => $filter) {
                    if (isset($filter['filter']) && $filter['filter'] !== '') {
                        $value = $filter['filter'];
                        $type = $filter['type'];

                        switch ($field) {
                            case 'VEHICLE_REG_NO':
                                $data->where('V.VEHICLE_REG_NO', 'like', "%{$value}%");
                                break;
                        }
                    }
                }
            }


            if ($request->has('orderBy') && !empty($request->input('orderBy'))) {
                $orderBy = $request->input('orderBy');
                $order = $request->input('order', 'DESC');
                $data->orderBy($orderBy, $order);
            } else {
                $data->orderBy('rosters.ESTIMATE_START_TIME', 'DESC');
            }


            if ($perPage === MyHelper::$PAGINATION_PER_PAGE_ALL) {
                $paginateddata = $data->paginate($data->count());
            } else {
                $perPage = is_numeric($perPage) ? (int)$perPage : MyHelper::$PAGINATION_PER_PAGE;
                $paginateddata = $data->paginate($perPage);
            }

            return response([
                'success' => true,
                'status' => 3,
                'deactive_route_list' => $paginateddata,
            ]);
        } catch (\Throwable | \Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'Deactive Route Pagination Unsuccessful' : 'Deactive Route Pagination Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ], 500);
        }
    }





   public function deactivate_Routes($request): FoundationApplication|Response|ResponseFactory
    {
        $date = Carbon::now();
        $authUser = Auth::user();
        $userid = Auth::user()->id;
        try {
            DB::beginTransaction();

            $deactivated_count = 0;
            $failedIds = [];

            foreach ($request->selected_roster_ids as $roster_id) {
                try {


                   
                    $arr = array("ACTIVE" => 6, "UPDATED_BY" => $userid, "updated_at" => $date->format("Y-m-d H:i:s"));
                    Roster::where("ACTIVE", "=", MyHelper::$RS_ACTIVE)
                        ->where("BRANCH_ID","=", $authUser->BRANCH_ID)
                        ->where("ROSTER_ID","=", $roster_id)
                        ->update($arr);



                    $arr1 = array("ACTIVE" => 2, "UPDATED_BY" => $userid, "updated_at" => $date->format("Y-m-d H:i:s"));
                    RosterPassenger::where("ACTIVE", "=", MyHelper::$RS_ACTIVE)
                        ->where("ROSTER_ID","=", $roster_id)
                        ->update($arr1);


                    $arr2 = array("ACCEPTANCE_REJECT_STATE" => 8193, "UPDATED_BY" => $userid, "updated_at" => $date->format("Y-m-d H:i:s"));
                    Cab_allocation::where("ROSTER_ID","=", $roster_id)
                        ->update($arr2);



                    $deactivated_count++;

                } catch (\Exception $e) {
                    Log::error("Failed to deactivate route: " . $e->getMessage());
                    $failedIds[] = $roster_id;
                }
            }

            DB::commit();

            $message = $deactivated_count > 0
                ? "$deactivated_count routes(s) deactivated successfully."
                : "No routes were deactivated.";

            if (!empty($failedIds)) {
                $message .= " Failed to deactivate " . count($failedIds) . " routes(s).";
            }

            return response([
                'success' => true,
                'message' => $message,
                'status' => 3,
                'deactivated_count' => $deactivated_count,
                'failed_ids' => $failedIds,
            ]);
        } catch (\Throwable | \Exception $e) {
            DB::rollBack();
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'validation_controller' => true,
                'message' => 'Route deactivation process failed',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}
