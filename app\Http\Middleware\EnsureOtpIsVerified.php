<?php

namespace App\Http\Middleware;

use App\Models\OtpVerify;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class EnsureOtpIsVerified
{
    /**
     * Handle an incoming request.
     *
     * @param \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response) $next
     */
    public function handle(Request $request, Closure $next)
    {
        $user = Auth::guard('api')->user();

        $tokenId = $user->token()->id;

        $latestOtp = OtpVerify::where('MOBILE_NO', $user->name)
            ->where('token_id', $tokenId)
            ->where('VERIFIED_STATUS', '1')
            ->first();


        if (!$latestOtp) {
            return response([
                'success' => false,
                'message' => 'OTP not verified for this session',
            ], 403);
        }

        return $next($request);
    }
}
