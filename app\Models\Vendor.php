<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Crypt;

class Vendor extends Model
{
    use HasFactory;

    protected $table = 'vendors';
    protected $primaryKey = 'VENDOR_ID';


    protected $fillable = [        
        'BRANCH_ID',
        'NAME',
        'MO<PERSON><PERSON>',
        'EMAILID',
        'ADDRESS',
        'PANCARD',
        'BANK_AC_NO',
        'BANK_BRANCH',
        'IFSC_CODE',
        'ACTIVE',
        'REMARKS',
        'CREATED_BY',
        'UPDATED_BY',
        'UPDATED_At'        
    ];


    protected $appends = ['id_crypt'];

    public function createdBy()
    {
        return $this->hasOne(User::class,'id','CREATED_BY');
    }

    public function updatedBy()
    {
        return $this->hasOne(User::class,'id','UPDATED_BY');
    }

    public function getIdCryptAttribute($value)
    {
        return Crypt::encryptString($this->VENDOR_ID);
    }
}
