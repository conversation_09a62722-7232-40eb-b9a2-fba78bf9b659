<?php

//namespace App\Http\Requests\Admin;
namespace App\Http\Requests\Admin;

use App\Helpers\CommonFunction;
use Closure;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;

class VehicleTravelPathRequest extends FormRequest
{
    protected CommonFunction $commonFunction;

    public function __construct(CommonFunction $commonFunction)
    {
        
    }

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {   
        return [
            'selected_date' => ['required','date'],
            'from_time' => ['required','string'],
            'to_time' => ['required','string'],
            'selected_vehicle' => ['required','string'],
            'selected_vendor_id' => ['required','numeric'],
          
            ];
    }

    public function messages(): array
    {
        return [
            'selected_date.required' => 'selected_date field is required',   
            'from_time.required' => 'from_time field is required',
            'to_time.required' => 'to_time field is required',
            'selected_vehicle.required' => 'selected_vehicle field is required',
            'selected_vendor_id.required' => 'selected_vendor_id field is required'
                  
        ];
    }
   

    public function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json([
            'success' => false,
            'validation_error' => true,
            'message' => 'Validation errors',
            'data' => $validator->errors()
        ]));
    }
   
}
