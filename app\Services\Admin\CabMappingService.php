<?php

namespace App\Services\Admin;

use App\Helpers\CommonFunction;
use App\Helpers\MyHelper;
use Carbon\Carbon;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use App\Models\Driver;
use App\Models\Cab;
use App\Models\Device;
use App\Models\Sim;

class CabMappingService
{
    protected CommonFunction $commonFunction;
    protected ?Branch $currentBranch = null;

    public function __construct(CommonFunction $commonFunction)
    {
        $this->commonFunction = $commonFunction;
    }
	
	 public function indexCab(): FoundationApplication|Response|ResponseFactory
    {
        $authUser = Auth::user();

        try 
		{
            $cab_data = cab::select('cab.CAB_ID',
                'cab.DRIVER_ID','cab.VENDOR_ID','cab.VEHICLE_ID','cab.SIM_ID','cab.CAB_STATUS',
                'cab.TARIFF_TYPE','cab.ATTACHMENT_DATE',
                'DR.DRIVERS_NAME',
                'DR.DRIVERS_ADRESS',
                'DR.DRIVERS_ADDR_LAT',
                'DR.DRIVERS_ADDR_LONG',
                'DR.DRIVER_LOC_LAT',
                'DR.DRIVER_LOC_LONG',
                'DR.DRIVER_MOBILE',
                'DR.DRIVER_LICENSE',
                'DR.LICENCE_EXPIRY',
                'DR.BADGE_EXPIRY',
                'DR.LOCATION_NAME',
                'DR.SHIFT_IN_TIME',
                'DR.SHIFT_OUT_TIME',
                'rm.REASON','rm.REASON_ID'
            )
              
			    ->join("drivers as DR", "DR.DRIVERS_ID", "=", 'cab.DRIVER_ID')
                ->Join("sim as S", "S.SIM_ID", "=", "cab.SIM_ID")
                ->Join("vendors as V", "V.VENDOR_ID", "=", "cab.VENDOR_ID")
                ->Join("reason_master as rm", "rm.REASON_ID", "=","cab.TARIFF_TYPE")
                ->where('cab.ACTIVE', MyHelper::$RS_ACTIVE)
               ->where("cab.BRANCH_ID",DB::raw( $authUser->BRANCH_ID))
             //  ->where(DB::raw('rm.CATEGORY'), '=', 'TariffType')

                ->get();

            return response([
                'success' => true,
                'status' => 3,
                'cab' => $cab_data,
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Cab Index Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ],500);
        }
    }

	

    public function storeCab($request): FoundationApplication|Response|ResponseFactory
    {

        try {
			 $date = Carbon::now();

            DB::beginTransaction();
            $auth_user = Auth::user();

            $cabData = $this->prepareCabData($request, $auth_user, MyHelper::$RS_ACTIVE, $auth_user->id);
            $cabResult = Cab::create($cabData);
			
			$simNew = Sim::findOrFail($request->SIM_ID);
            $simNew->update([
                'SIM_MAP_STATUS' => MyHelper::$STATUS_MAPPED,
                'UPDATED_BY' => $auth_user->id
            ]);
			
			$deviceNew = Device::findOrFail($request->DEVICE_ID);
            $deviceNew->update([
                'COMPLIANT_STATUS' => MyHelper::$STATUS_MAPPED,
                'UPDATED_BY' => $auth_user->id,"updated_at" => $date->format("Y-m-d H:i:s")
            ]);
			
			
            return response([
                'success' => true,
                'status' => 3,
                'message' => 'Cab Created Successfully',

            ]);
			
			
			

        } catch (\Throwable|\Exception $e) 
		{
            DB::rollBack();
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'validation_controller' => true,
                'status' => 4,
                'message' => 'Cab Created UnSuccessfully',
                'errorM' => $e->getMessage(),
            ],500);
        } finally {
            DB::commit();
        }


    }
	
    /**
     * @throws ConnectionException
     */
    private function prepareCabData($request, $auth_user, $active, $userId): array
    {
        $date = Carbon::now();

        return [
		
			'VEHICLE_ID' => $request->VEHICLE_ID,
            'DRIVER_ID' => $request->DRIVER_ID,
            'DEVICE_ID' => $request->DEVICE_ID,
            'SIM_ID' => $request->SIM_ID,
            'VENDOR_ID' => $request->VENDOR_ID,
            'TARIFF_TYPE' => $request->TARIFF_TYPE,
            'BRANCH_ID' => $auth_user->BRANCH_ID,
            'ATTACHMENT_DATE' => $request->ATTACHMENT_DATE,
            'CAB_STATUS' => $request->CAB_STATUS,
            "ACTIVE" => $active,
			"CREATED_BY" => $auth_user->id,
            "created_at" => $date->format("Y-m-d H:i:s"),
		
                     
        ];
    }

    public function deleteCab($request, $cabAutoIdCrypt): FoundationApplication|Response|ResponseFactory
    {
        $authUser = Auth::user();
		$date = Carbon::now();
       
        try {
            DB::beginTransaction();

            $cabId = Crypt::decryptString($cabAutoIdCrypt);

            $cab = Cab::where('ACTIVE', MyHelper::$RS_ACTIVE)
               // ->where('BRANCH_ID', $authUser->BRANCH_ID)
                ->findOrFail($cabId);

            $cab->update([
                'REMARKS' => $request->input('remark'),
                'ACTIVE' => MyHelper::$RS_INACTIVE,
                'UPDATED_BY' => Auth::user()->id,
            ]);

			$cab = Cab::findOrFail($cabId);

            $release_SimId = $cab->SIM_ID;
            $release_DeviceId = $cab->DEVICE_ID;
			
			$simNew = Sim::findOrFail($release_SimId);
            $simNew->update([
                'SIM_MAP_STATUS' => MyHelper::$STATUS_UNMAPPED,
                'UPDATED_BY' => $authUser->id
            ]);
			
			$deviceNew = Device::findOrFail($release_DeviceId);
            $deviceNew->update([
                'COMPLIANT_STATUS' => MyHelper::$STATUS_UNMAPPED,
                'UPDATED_BY' => $authUser->id,"updated_at" => $date->format("Y-m-d H:i:s")
            ]);
			
			
            DB::commit();

            return response([
                'success' => true,
                'status' => 3,
                'message' => 'Cab Deleted Successfully',
            ]);
        } catch (\Throwable|\Exception $e) {
            DB::rollBack();
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Cab Delete Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ],500);
        }
    }
    
    
    public function simchange($request, $cabAutoIdCrypt): FoundationApplication|Response|ResponseFactory
    {
        try {
            DB::beginTransaction();

            $authUser = Auth::user();
            $cabAutoId = Crypt::decryptString($cabAutoIdCrypt);
            $cab = Cab::findOrFail($cabAutoId);

            $oldSimId = $cab->SIM_ID;
            $newSimId = $request->SIM_ID;


            $cab->update(['SIM_ID' => $newSimId]);


            $simOld = Sim::findOrFail($oldSimId);
            $simOld->update([
                'SIM_MAP_STATUS' => MyHelper::$STATUS_UNMAPPED,
                'UPDATED_BY' => $authUser->id
            ]);


            $simNew = Sim::findOrFail($newSimId);
            $simNew->update([
                'SIM_MAP_STATUS' => MyHelper::$STATUS_MAPPED,
                'UPDATED_BY' => $authUser->id
            ]);

            DB::commit();

            return response([
                'success' => true,
                'status' => 3,
                'message' => 'Cab Sim Updated Successfully',
            ]);

        } catch (\Throwable $e) {
            DB::rollBack();
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'message' => 'Cab Sim Update Unsuccessful',
                'status' => 4,
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }
	public function devicechange($request, $cabAutoIdCrypt): FoundationApplication|Response|ResponseFactory
    {
        try {
			$date = Carbon::now();
            DB::beginTransaction();

            $authUser = Auth::user();
            $cabAutoId = Crypt::decryptString($cabAutoIdCrypt);
            $cab = Cab::findOrFail($cabAutoId);

            $oldDeviceId = $cab->DEVICE_ID;
            $newDeviceId = $request->DEVICE_ID;


            $cab->update(['DEVICE_ID' => $newDeviceId,"updated_at" => $date->format("Y-m-d H:i:s")]);


            $deviceOld = Device::findOrFail($oldDeviceId);
            $deviceOld->update([
                'COMPLIANT_STATUS' => MyHelper::$STATUS_UNMAPPED,
                'UPDATED_BY' => $authUser->id,"updated_at" => $date->format("Y-m-d H:i:s")
            ]);


            $deviceNew = Device::findOrFail($newDeviceId);
            $deviceNew->update([
                'COMPLIANT_STATUS' => MyHelper::$STATUS_MAPPED,
                'UPDATED_BY' => $authUser->id,"updated_at" => $date->format("Y-m-d H:i:s")
            ]);

            DB::commit();

            return response([
                'success' => true,
                'status' => 3,
                'message' => 'Cab Device Updated Successfully',
            ]);

        } catch (\Throwable $e) {
            DB::rollBack();
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'message' => 'Cab Device Update Unsuccessful',
                'status' => 4,
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }





    public function editcab($cabAutoIdCrypt): FoundationApplication|Response|ResponseFactory
    {
        $authUser = Auth::user();

        try {
            
            $cabAutoId = Crypt::decryptString($cabAutoIdCrypt);
           
            $cab = Cab::where('cab.ACTIVE', MyHelper::$RS_ACTIVE)
                 //->where('drivers.ORG_ID', "=","O.ORGANIZATIONID")
				 ->where("cab.CAB_ID", $cabAutoId)
			   
                 ->select('cab.CAB_ID',
                 'cab.DRIVER_ID','cab.VENDOR_ID','cab.VEHICLE_ID','cab.SIM_ID','cab.CAB_STATUS',
                 'cab.TARIFF_TYPE','cab.ATTACHMENT_DATE',
                 'DR.DRIVERS_NAME',
                 'DR.DRIVERS_ADRESS',
                 'DR.DRIVERS_ADDR_LAT',
                 'DR.DRIVERS_ADDR_LONG',
                 'DR.DRIVER_LOC_LAT',
                 'DR.DRIVER_LOC_LONG',
                 'DR.DRIVER_MOBILE',
                 'DR.DRIVER_LICENSE',
                 'DR.LICENCE_EXPIRY',
                 'DR.BADGE_EXPIRY',
                 'DR.LOCATION_NAME',
                 'DR.SHIFT_IN_TIME',
                 'DR.SHIFT_OUT_TIME',
                 'rm.REASON','rm.REASON_ID'
             )
               
                 ->join("drivers as DR", "DR.DRIVERS_ID", "=", 'cab.DRIVER_ID')
                 ->Join("sim as S", "S.SIM_ID", "=", "cab.SIM_ID")
                 ->Join("vendors as V", "V.VENDOR_ID", "=", "cab.VENDOR_ID")
                 ->Join("reason_master as rm", "rm.REASON_ID", "=", "cab.TARIFF_TYPE")
                 
                ->where("cab.BRANCH_ID",DB::raw( $authUser->BRANCH_ID))
                ->firstOrFail();

            return response([
                'success' => true,
                'status' => 3,
                'driver' => $cab,
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Cab Edit Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ],500);
        }
    }

    public function updateCab($request, $cabAutoIdCrypt): FoundationApplication|Response|ResponseFactory
    {
        $authUser = Auth::user();
        try {
            DB::beginTransaction();
            $cabAutoId = Crypt::decryptString($cabAutoIdCrypt);
            $cab = Cab::findOrFail($cabAutoId);
            $auth_user = Auth::user();

            $cabData = $this->prepareCabDataForUpdate($request, $auth_user, $cab);
            $cab->update($cabData);

            DB::commit();


            return response([
                'success' => true,
                'status' => 3,
                'message' => 'Cab Updated Successfully',
            ]);

        } catch (\Throwable|\Exception $e) {
            DB::rollBack();
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'message' => 'Cab Update Unsuccessful',
                'status' => 4,
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ],500);
        }
    }

    /**
     * @throws ConnectionException
     */
    private function prepareDriverDataForUpdate($request, $auth_user, $driver): array
    {
        $date = Carbon::now();

        return [
            'DRIVERS_NAME' => $request->DRIVERS_NAME,
            'DRIVER_MOBILE' => $request->DRIVER_MOBILE,
            'DRIVERS_ADRESS' => $request->DRIVERS_ADRESS,
            'DRIVERS_ADDR_LAT' => $request->DRIVERS_ADDR_LAT,
            'DRIVERS_ADDR_LONG' => $request->DRIVERS_ADDR_LONG,
            'DRIVER_LICENSE' => $request->DRIVER_LICENSE,
            'LICENCE_EXPIRY' => $request->LICENCE_EXPIRY,
            'BADGE_EXPIRY' => $request->BADGE_EXPIRY,
            'MEDICAL_STATUS' => $request->MEDICAL_STATUS,
            'LOCATION_NAME' => $request->LOCATION_NAME,
            'SHIFT_IN_TIME' => $request->SHIFT_IN_TIME,
            'SHIFT_OUT_TIME' => $request->SHIFT_OUT_TIME,
            "UPDATED_BY" => $auth_user->id,
            "updated_at" => $date->format("Y-m-d H:i:s"),
        ];
    }

    


    public function dataForCreateCab(): FoundationApplication|Response|ResponseFactory
    {

        try {
            $user = Auth::user();
            $rsActive = MyHelper::$RS_ACTIVE;
            $branchId = $user->BRANCH_ID;

            $branch = DB::table('branch')
                ->select('BRANCH_ID', 'DIVISION_ID','ORG_ID','BRANCH_NAME')
                ->where('ACTIVE', $rsActive)
                ->where('BRANCH_ID', $branchId)
                ->get();
                
				
			$vendor = DB::table('vendors')
                ->select('VENDOR_ID', 'NAME as vendor_name')
                ->where('ACTIVE', $rsActive)
				->where('BRANCH_ID', $branchId)
                ->get();
               // print_r($vendor);exit;
        		
			$vehicles = DB::table('vehicles')
                ->select('VEHICLE_ID', 'VEHICLE_REG_NO')
                ->where('ACTIVE', $rsActive)
                ->where('ORG_ID', $branch[0]->ORG_ID)
                ->get();

           $drivers = DB::table('drivers')
                ->select('DRIVERS_ID', 'DRIVERS_NAME','DRIVER_MOBILE')
                ->where('ACTIVE', $rsActive)
                ->where('ORG_ID', $branch[0]->ORG_ID)
                ->get();

            $sim = DB::table('sim')
                ->select('SIM_ID', 'SIM_MOBILE_NO','SIM_SERIAL_NO')
                ->where('ACTIVE', $rsActive)
                ->where('SIM_MAP_STATUS', $rsActive)
                ->where('ORG_ID', $branch[0]->ORG_ID)
                ->get();
				
			$device = DB::table('devices')
                ->select('DEVICE_ID', 'IMEI_NO_1','IMEI_NO_2','DEVICE_MODEL',)
                ->where('ACTIVE', $rsActive)
                ->where('COMPLIANT_STATUS', $rsActive)
                ->where('ORG_ID', $branch[0]->ORG_ID)
                ->get();
				
			 

                $tariffType = DB::table('reason_master')
                ->select('REASON_ID', 'REASON')
                ->where('ACTIVE', $rsActive)
                ->where('CATEGORY', 'TariffType')
                ->where('BRANCH_ID', $branchId)
                ->get()
                ->map(function ($item) {
                    return [
                        'tariff_id' => $item->REASON_ID,
                        'tariff_value' => $item->REASON
                    ];
                });

                $cab_status = array(
                    array(
                        'value' => 1,
                        'name' => 'Non-Inducted'
                    ),
                    array(
                        'value' => 0,
                        'name' => 'Inducted'
                    )
                );
				

                return response([
                'success' => true,
                'status' => 3,
                'tariff_type' => $tariffType,
                'device' => $device,
                'sim' => $sim,
                'drivers' => $drivers,
                'vehicles' => $vehicles,
                'vendor' => $vendor,
                'branch' =>
                    $branch
                ,
                'cab_status'=> $cab_status
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Data For Create for Cab  Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ],500);
        }
    }
	
  
  

    public function paginationCab($request, bool $active = true): FoundationApplication|Response|ResponseFactory
    {
        try {
           
            $perPage = $request->input('per_page', MyHelper::$PAGINATION_PER_PAGE);
            $branch_id = Auth::user()->BRANCH_ID;
            $activeStatus = $active ? MyHelper::$RS_ACTIVE : MyHelper::$RS_INACTIVE;
            $authUser = Auth::user();
           // echo $authUser->BRANCH_ID;exit;
            $cab = Cab::select('cab.CAB_ID',
            'cab.DRIVER_ID','cab.VENDOR_ID','cab.VEHICLE_ID','cab.SIM_ID','cab.CAB_STATUS','cab.BRANCH_ID',
            'cab.TARIFF_TYPE','cab.ATTACHMENT_DATE',
            'DR.DRIVERS_NAME',
            'DR.DRIVERS_ADRESS',
            'DR.DRIVERS_ADDR_LAT',
            'DR.DRIVERS_ADDR_LONG',
            'DR.DRIVER_LOC_LAT',
            'DR.DRIVER_LOC_LONG',
            'DR.DRIVER_MOBILE',
            'DR.DRIVER_LICENSE',
            'DR.LICENCE_EXPIRY',
            'DR.BADGE_EXPIRY',
            'DR.LOCATION_NAME',
            'DR.SHIFT_IN_TIME',
            'DR.SHIFT_OUT_TIME',
            'D.APP_VERSION',
            'users.name AS CREATED_BY',
            'cab.CREATED_DATE',
            //'rm.REASON_ID',
            'rm.REASON AS TARIFF_REASON','S.SIM_ID','S.SIM_MOBILE_NO','S.SIM_SERIAL_NO','S.SIM_PROVIDER','D.DEVICE_ID',
			'D.IMEI_NO_1','D.IMEI_NO_2','D.DEVICE_MODEL','VH.VEHICLE_REG_NO','VM.BRAND','VM.MODEL','VM.CAPACITY','V.NAME as vendor_name'
        )
          
            ->join("drivers as DR", "DR.DRIVERS_ID", "=", 'cab.DRIVER_ID')
            ->join("vehicles as VH", "VH.VEHICLE_ID", "=", 'cab.VEHICLE_ID')
            ->join("vehicle_models as VM", "VM.VEHICLE_MODEL_ID", "=", 'VH.VEHICLE_MODEL_ID')
            ->join("devices as D", "D.DEVICE_ID", "=", 'cab.DEVICE_ID')
            ->Join("sim as S", "S.SIM_ID", "=", "cab.SIM_ID")
            ->Join("vendors as V", "V.VENDOR_ID", "=", "cab.VENDOR_ID")
            ->Join("reason_master as rm", "rm.REASON_ID", "=","cab.TARIFF_TYPE")
            ->where('cab.ACTIVE', MyHelper::$RS_ACTIVE)
            ->join("users", "users.id", "=", "cab.CREATED_BY")
           ->where("cab.BRANCH_ID",DB::raw( $authUser->BRANCH_ID));
           

            $filterModel = $request->input('filterModel');
            if ($filterModel) {
                foreach ($filterModel as $field => $filter) {
                    if (isset($filter['filter']) && $filter['filter'] !== '') {
                        $value = $filter['filter'];
                        $type = $filter['type'];

                        switch ($field) {
                            case 'CAB_ID':
                                $cab->where('cab.CAB_ID', 'like', "%{$value}%");
                                break;
                            
                        }
                    }
                }
            }


            if ($request->has('orderBy') && !empty($request->input('orderBy'))) 
            {
                $orderBy = $request->input('orderBy');
                $order = $request->input('order', 'asc');
                $cab->orderBy($orderBy, $order);
            } else {
                $cab->orderBy('cab.created_at', 'desc');
            }
// echo $driver->count();exit;
            if ($perPage === MyHelper::$PAGINATION_PER_PAGE_ALL) {
                $paginatedCab = $cab->paginate($cab->count());
            } else {
                $perPage = is_numeric($perPage) ? (int)$perPage : MyHelper::$PAGINATION_PER_PAGE;
                $paginatedCab = $cab->paginate($perPage);
            }
           // print_r($paginatedDriver);exit;
            return response([
                'success' => true,
                'status' => 3,
                'drivers' => $paginatedCab,
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'Cab Pagination Unsuccessful' : 'Deactivate Cab Pagination Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ],500);
        }
    } 

}
