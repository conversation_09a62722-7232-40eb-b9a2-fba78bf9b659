<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Crypt;

class Company extends Model
{
    use HasFactory;

    protected $table = 'company';
    protected $primaryKey = 'COMPANY_ID';
    protected $appends = ['id_crypt'];

    protected $fillable = [
        'NAME',
        'LOCATION',
        'LAT',
        'LONG',
        'CREATED_BY',
        'ENTITY_PROPERTIE',
        'ACTIVE',
        'UPDATED_BY',
        'REMARKS',
    ];


    public function createdBy()
    {
        return $this->hasOne(User::class,'id','CREATED_BY');
    }
    public function updatedBy()
    {
        return $this->hasOne(User::class,'id','UPDATED_BY');
    }

    public function getIdCryptAttribute($value)
    {
        return Crypt::encryptString($this->COMPANY_ID);
    }
}
