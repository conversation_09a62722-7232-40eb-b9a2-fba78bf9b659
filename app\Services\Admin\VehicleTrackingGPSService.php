<?php

namespace App\Services\Admin;

use App\Helpers\CommonFunction;
use App\Helpers\MyHelper;
use App\Http\Controllers\ElasticController;
use App\Models\Cab_allocation;
use App\Models\Roster;
use App\Models\RosterPassenger;
use App\Models\Vendor;
use Carbon\Carbon;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use DateTime;

class VehicleTrackingGPSService
{
    protected CommonFunction $commonFunction;
    protected ?Branch $currentBranch = null;

    public function __construct(CommonFunction $commonFunction)
    {
        $this->commonFunction = $commonFunction;
    }

    public function get_roster_shifttime($request): FoundationApplication|Response|ResponseFactory
    {
        try {
            $branch_id = Auth::user()->BRANCH_ID;
            $userid = Auth::user()->id;
            $db_name = Auth::user()->dbname;
            $RS_ACTIVE = MyHelper::$RS_ACTIVE;
            $active = true;
            $authUser = Auth::user();
            $vendorId = $request->vendor_id;
            $trip_type = $request->trip_type;
            $date = carbon::now();
            // $branch_id=48;
            if ($trip_type == 'D') {
                $sql = "select R.ESTIMATE_START_TIME as estimate_time,R.BRANCH_ID from rosters as R where R.ACTIVE='$RS_ACTIVE' and R.BRANCH_ID='$branch_id' and R.TRIP_TYPE='$trip_type' and date(R.ESTIMATE_START_TIME)='" . Carbon::now()->format('Y-m-d') . "' and R.ESTIMATE_START_TIME>=SUBTIME('" . $date . "', '01:00:00') group by R.ESTIMATE_START_TIME  order by R.ESTIMATE_START_TIME";
            } else {
                $sql = "select R.ESTIMATE_END_TIME as estimate_time,R.BRANCH_ID from rosters as R where R.ACTIVE='$RS_ACTIVE' and R.ESTIMATE_END_TIME is not null and R.BRANCH_ID='$branch_id' and R.TRIP_TYPE='$trip_type' and date(R.ESTIMATE_END_TIME)='" . Carbon::now()->format('Y-m-d') . "' and R.ESTIMATE_END_TIME>=SUBTIME('" . $date . "', '01:00:00') group by R.ESTIMATE_END_TIME  order by R.ESTIMATE_END_TIME";
            }
            $roster_time = DB::connection("$db_name")->select($sql);

            return response([
                'success' => true,
                'status' => 3,
                'roster_time' => $roster_time,
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'Vehicle Tracking  status Unsuccessful' : 'Vehicle Tracking status Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ], 500);
        }
    }

    public function vehicle_status($request): FoundationApplication|Response|ResponseFactory
    {
        try {
            $RS_ACTIVE = MyHelper::$RS_ACTIVE;
            $vendorId = $request->selected_vendor_id;
            $trip_type = $request->selected_trip_type;
            $shift_time = $request->selected_shift_time;
            $branchId = Auth::user()->BRANCH_ID;
            // $branchId = 18;
            $dbname = Auth::user()->dbname;

            $trip_accept = MyHelper::$RS_TOTALACCEPT;
            $trip_execute = MyHelper::$RS_TOTALEXECUTE;
            $trip_allot = MyHelper::$RS_TOTALALLOT;
            $trip_create = MyHelper::$RS_NEWROSTER;
            $acutal_start_time = '1900-01-01 00:00:00';
            $data = array();
            $result = array();
            $res = array();

            $roster_status = "(R.ROSTER_STATUS in ($trip_accept) or R.ROSTER_STATUS in ($trip_execute) or R.ROSTER_STATUS in ($trip_allot) or R.ROSTER_STATUS in($trip_create) or R.ROSTER_STATUS>=345)";

            $date = carbon::now();

            $gps_track_control = new \App\Http\Controllers\ElasticController;

            if ($trip_type == 'P') {
                $sql2 = "select R.CAB_ID,if(R.ACTUAL_START_TIME is null,'1900-01-01 00:00:00',R.ACTUAL_START_TIME) as ACTUAL_START_TIME,if(R.ROSTER_STATUS in ($trip_accept),'accept',if(R.ROSTER_STATUS in ($trip_execute),'execute',if(R.ROSTER_STATUS in ($trip_allot),'allot',if(R.ROSTER_STATUS>=345,'close','create')))) as rostersts from rosters as R where R.ACTIVE='$RS_ACTIVE' and R.ESTIMATE_END_TIME='$shift_time' and R.BRANCH_ID='$branchId' and R.TRIP_TYPE='$trip_type' "
                    . " and $roster_status  ";
            } else {
                $sql2 = "select R.CAB_ID,if(R.ACTUAL_START_TIME is null,'1900-01-01 00:00:00',R.ACTUAL_START_TIME) as ACTUAL_START_TIME,if(R.ROSTER_STATUS in ($trip_accept),'accept',if(R.ROSTER_STATUS in ($trip_execute),'execute',if(R.ROSTER_STATUS in ($trip_allot),'allot',if(R.ROSTER_STATUS>=345,'close','create')))) as rostersts from rosters as R where R.ACTIVE='$RS_ACTIVE' and R.ESTIMATE_START_TIME='$shift_time'  and R.BRANCH_ID='$branchId' and R.TRIP_TYPE='$trip_type'  "
                    . " and $roster_status ";
            }

            $cabarr = array();
            $roster_status = array();
            // $reason=env('ES_CAB_STATUS');
            $reason = env('ES_CAB_FILTER');
            $roster_rs = DB::connection("$dbname")->select($sql2);
            foreach ($roster_rs as $val) {
                $cabid = $val->CAB_ID;
                if($val->rostersts=='accept')
                    {
                        $roster_color="#FF00CC";
                    }
                    else if($val->rostersts=='execute')
                    {
                        $roster_color="#72A65D";
                    }
                    else if($val->rostersts=='allot')
                    {
                        $roster_color="#FF00CC";
                    }
                    else if($val->rostersts=='close')
                    {
                        $roster_color="#0012FE";
                    }
                    else
                    {
                        $roster_color="#FF0000";
                    }
                if ($cabid != '')
                {   
                    array_push($cabarr, $cabid);
                    $roster_status[] = array('roster_status' => $val->rostersts, 'CAB_ID' => $cabid,"roster_color"=>$roster_color);
                }
            }
            //  echo "<pre>"; print_r($cabarr);
            if (count($cabarr) > 0) {
                $res = $gps_track_control->getCabLiveStatus($branchId, $vendorId, $reason, $cabarr);

                if ($res != 'No Record' && count($roster_status) > 0) {
                    // $result=array_merge($res, $roster_status);
                    $result = $this->merge_two_arrays($res, $roster_status);
                }
            }

            // echo "<pre>";print_r($result);
            // exit;

            $vendor_cablist = $this->getvendor_cablist($vendorId, $trip_type, $shift_time);

            $transformed_data = $this->transformGpsData($result);

            return response([
                'success' => true,
                'status' => 3,
                'gps_data' => $transformed_data,
                'vendor_data' => $vendor_cablist
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Data For Create for OnDutyvehicle Tracking GPS Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    private function transformGpsData(array $data): array
    {
        if (empty($data)) {
            return [];
        }

        return array_values($data);
    }

    public function postvehicletracking($request): FoundationApplication|Response|ResponseFactory
    {
        try {
            $RS_ACTIVE = MyHelper::$RS_ACTIVE;
            $branchId = Auth::user()->BRANCH_ID;
            $dbname = Auth::user()->dbname;
            $vendorId = $request->vendor_id;
            $cabid = $request->cab_id;
            // $reason_status = $request->reason;
            $shift_time = $request->shift_time;
            $trip_type = $request->trip_type;
            $gpsdate_time = $request->gps_time;
            $roster_id = 0;
            $enable_traffic = '';
            $date = carbon::now();
            $reason_status = MyHelper::$ES_STATUS_ONTRIP;
            $trip_accept = MyHelper::$RS_TOTALACCEPT;
            $trip_execute = MyHelper::$RS_TOTALEXECUTE;
            $trip_allot = MyHelper::$RS_TOTALALLOT;
            $otp = MyHelper::$RPS_TTLSYSTEMOTP . ',' . MyHelper::$RPS_TTLCABSYSTEMOTP . ',' . MyHelper::$RPS_TTLEMPLOYEEDELAY_SYSTEMOTP . ',' . MyHelper::$RPS_TTLEMPLOYEEDELAY . ',' . MyHelper::$RPS_TTLCABEMPLOYEEDELAY . ','
                . MyHelper::$RPS_TTLMANUALOTP;
            $noshow = MyHelper::$RPS_TTLNOSHOW;
            $roster_status = "(R.ROSTER_STATUS in ($trip_accept) or R.ROSTER_STATUS in ($trip_execute) or R.ROSTER_STATUS in ($trip_allot) or R.ROSTER_STATUS>=345)";
            $start_time = '1900-01-01 00:00:00';
            $end_time = '';
            $result = array();
            $select = '';
            $roster_sts = '';
            if ($trip_type == 'P')
            {
                $sql2 = "select if(R.ACTUAL_START_TIME is null,'1900-01-01 00:00:00',R.ACTUAL_START_TIME) as ACTUAL_START_TIME,R.ROSTER_ID,R.ACTUAL_END_TIME,if(R.ROSTER_STATUS in ($trip_accept),'accept',if(R.ROSTER_STATUS in ($trip_execute),'execute',if(R.ROSTER_STATUS in ($trip_allot),'allot',if(R.ROSTER_STATUS>=345,'close','create')))) as rostersts "
                    . "  from rosters as R where R.ACTIVE='$RS_ACTIVE' and R.ESTIMATE_END_TIME='$shift_time' and R.BRANCH_ID='$branchId' and R.TRIP_TYPE='$trip_type' and R.CAB_ID='$cabid' "
                    . " and $roster_status order by R.ESTIMATE_END_TIME desc limit 1 ";
                $select = 'START_LAT as lat,START_LONG as lng,EMPLOYEE_ID';
            }
            else 
            {
                $sql2 = "select if(R.ACTUAL_START_TIME is null,'1900-01-01 00:00:00',R.ACTUAL_START_TIME) as ACTUAL_START_TIME,R.ROSTER_ID,R.ACTUAL_END_TIME,if(R.ROSTER_STATUS in ($trip_accept),'accept',if(R.ROSTER_STATUS in ($trip_execute),'execute',if(R.ROSTER_STATUS in ($trip_allot),'allot',if(R.ROSTER_STATUS>=345,'close','create')))) as rostersts "
                    . " from rosters as R where R.ACTIVE='$RS_ACTIVE' and R.ESTIMATE_START_TIME='$shift_time'  and R.BRANCH_ID='$branchId' and R.TRIP_TYPE='$trip_type' and R.CAB_ID='$cabid' "
                    . " and $roster_status order by R.ESTIMATE_START_TIME desc limit 1";
                $select = 'END_LAT as lat,END_LONG as lng,EMPLOYEE_ID';
            }

            $execute_trip = DB::connection("$dbname")->select($sql2);
            foreach ($execute_trip as $val) {
                $acutal_start_time = $val->ACTUAL_START_TIME;
                $roster_id = $val->ROSTER_ID;
                $end_time = $val->ACTUAL_END_TIME;
                $roster_sts = $val->rostersts;
                if (strtotime($acutal_start_time) == strtotime('1900-01-01 00:00:00')) {
                    $start_time = '1900-01-01 00:00:00';
                } else if (strtotime($gpsdate_time) == strtotime('1900-01-01 00:00:00') && (strtotime($acutal_start_time) != strtotime('1900-01-01 00:00:00'))) {
                    $start_time = $acutal_start_time;
                } else {
                    $start_time = $gpsdate_time;
                }
                // $acutal_start_time=$gpsdate_time=='1900-01-01 00:00:00'?$acutal_start_time:$gpsdate_time;
            }
            $sql3 = "select GROUP_CONCAT(DISTINCT RP.EMPLOYEE_ID ORDER BY RP.EMPLOYEE_ID  SEPARATOR', ') as emp_id,$select,E.NAME,RP.LOCATION_ID,L.LATITUDE,L.LONGITUDE,if(RP.ROSTER_PASSENGER_STATUS in ($otp),'otp',if(RP.ROSTER_PASSENGER_STATUS in ($noshow),'noshow','create')) as otpsts  from roster_passengers as RP inner join employees as E on E.EMPLOYEES_ID=RP.EMPLOYEE_ID and E.BRANCH_ID='$branchId'"
                . " inner join locations as L on L.LOCATION_ID=RP.LOCATION_ID where RP.ROSTER_ID='$roster_id' and  RP.ACTIVE='$RS_ACTIVE' GROUP BY RP.LOCATION_ID,RP.ROSTER_PASSENGER_STATUS ";
            $roster_passenger = DB::connection("$dbname")->select($sql3);

            $roster_passenger=collect($roster_passenger)->map(function ($item) 
            {
                if($item->otpsts=='otp')
                {
                    $item->color="#00E64D";
                }
                elseif($item->otpsts=='noshow')
                {
                    $item->color="#FD7567";
                }
                else
                {
                    $item->color = "#6991FD";
                }
                return $item;
            });

            if ($trip_type == 'D') 
            {
                $sql = "select R.ESTIMATE_START_TIME as estimate_time,R.BRANCH_ID from rosters as R where R.ACTIVE='$RS_ACTIVE' and "
                    . "R.BRANCH_ID=$branchId  and date(R.ESTIMATE_START_TIME)='" . Carbon::now()->format('Y-m-d') . "' and R.ESTIMATE_START_TIME>=SUBTIME('" . $date . "', '01:00:00') group by R.ESTIMATE_START_TIME "
                    . ' order by R.ESTIMATE_START_TIME';  //
            } else
             {
                $sql = "select R.ESTIMATE_END_TIME as estimate_time,R.BRANCH_ID from rosters as R where R.ACTIVE='$RS_ACTIVE' and "
                    . "R.BRANCH_ID=$branchId  and date(R.ESTIMATE_END_TIME)='" . Carbon::now()->format('Y-m-d') . "' and R.ESTIMATE_END_TIME>=SUBTIME('" . $date . "', '01:00:00') group by R.ESTIMATE_END_TIME "
                    . ' order by R.ESTIMATE_END_TIME';  //
            }
            $roster_time = DB::connection("$dbname")->select($sql);
            $gps_track_control = new \App\Http\Controllers\ElasticController;

            if ($end_time == '' || $end_time == null) {
                $result = $gps_track_control->gpsCabNavigationPath($cabid, $start_time);
            } else {
                $result = $gps_track_control->gpsPathSearch(trim($cabid), $start_time, $end_time);
            }
           // if(R.ROSTER_STATUS in ($trip_accept),'accept',if(R.ROSTER_STATUS in ($trip_execute),'execute',if(R.ROSTER_STATUS in ($trip_allot),'allot',if(R.ROSTER_STATUS>=345,'close','create')))) as rostersts "

            switch($roster_sts)
            {
                case 'accept':
                    $trip_color="#FF00CC";
                    break;
                case 'allot':
                    $trip_color="#FF00CC";
                    break;
                case 'execute':
                    $trip_color="#72A65D";
                    break;
                case 'close':
                    $trip_color="#0012FE";
                    break;
                    default:
                    $trip_color="FF0000";
            }

            $roster_sts=array("roster_sts"=>$roster_sts,"trip_color"=>$trip_color);
                
            $cab_navigation = array('cab_path' => $result, 'otp_loc' => $roster_passenger, 'roster_status' => $roster_sts);
            return response([
                'success' => true,
                'status' => 3,
                'gps_data' => $cab_navigation,
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Data For Create for OnDutyvehicle Tracking GPS Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function tracking_roster_details($request): FoundationApplication|Response|ResponseFactory
    {
        try {
            $roster_id = $request->selected_roster_id;
            $roster_data = RosterPassenger::query()
                ->select(
                    'rosters.ROSTER_ID',
                    'rosters.ROUTE_ID',
                    'rosters.TRIP_TYPE',
                    'rosters.PASSENGER_ALLOT_COUNT',
                    'rosters.PASSENGER_ALLOT_IN_ROUT_COUNT',
                    'rosters.PASSENGER_NOSHOW_COUNT',
                    'rosters.PASSENGER_CLUBING_COUNT',
                    'vehicle_models.MODEL',
                    'employees.EMPLOYEES_ID',
                    'roster_passengers.LOCATION_ID',
                    'roster_passengers.ROSTER_PASSENGER_STATUS',
                    DB::raw("IF(rosters.TRIP_TYPE = 'P', rosters.ESTIMATE_END_TIME, rosters.ESTIMATE_START_TIME) AS IN_OUT"),
                    'employees.NAME',
                    'locations.LOCATION_NAME',
                    DB::raw('(rosters.PASSENGER_ALLOT_COUNT + rosters.PASSENGER_CLUBING_COUNT) as tot')
                )
                ->join('rosters', 'roster_passengers.ROSTER_ID', '=', 'rosters.ROSTER_ID')
                ->join('employees', function ($join) {
                    $join
                        ->on('employees.EMPLOYEES_ID', '=', 'roster_passengers.EMPLOYEE_ID')
                        ->on('rosters.BRANCH_ID', '=', 'employees.BRANCH_ID');
                })
                ->join('cab', 'cab.CAB_ID', '=', 'rosters.CAB_ID')
                ->join('vehicles', 'vehicles.VEHICLE_ID', '=', 'cab.VEHICLE_ID')
                ->join('vehicle_models', 'vehicle_models.VEHICLE_MODEL_ID', '=', 'vehicles.VEHICLE_MODEL_ID')
                ->join('locations', 'locations.LOCATION_ID', '=', 'roster_passengers.LOCATION_ID')
                ->where('rosters.ROSTER_ID', $roster_id)
                ->where('roster_passengers.ACTIVE', 1)
                ->groupBy('roster_passengers.EMPLOYEE_ID')
                ->get();
            return response([
                'success' => true,
                'status' => 3,
                'roster_data' => $roster_data,
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Data For Create for OnDutyvehicle Tracking GPS Roster Details Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * @throws ConnectionException
     */
    public function dataForCreate(): FoundationApplication|Response|ResponseFactory
    {
        try {
            $user = Auth::user();
            $RS_ACTIVE = MyHelper::$RS_ACTIVE;

            $branch_id = Auth::user()->BRANCH_ID;
            $vendor_id = Auth::user()->vendor_id;
            $dbname = Auth::user()->dbname;
            $date = carbon::now();

            if (Auth::user()->user_type == MyHelper::$ADMIN) {
                $vendor_list = Vendor::where('ACTIVE', '=', $RS_ACTIVE)->where('BRANCH_ID', '=', $branch_id)->get();
            } else {
                $vendor_list = Vendor::where('ACTIVE', '=', $RS_ACTIVE)->where('VENDOR_ID', '=', $vendor_id)->get();
            }

            $sql = "select R.ESTIMATE_END_TIME as estimate_time,R.BRANCH_ID from rosters as R where R.ACTIVE=$RS_ACTIVE and R.ESTIMATE_END_TIME is not null and R.BRANCH_ID='$branch_id' and R.TRIP_TYPE='P' and date(R.ESTIMATE_END_TIME)='" . Carbon::now()->format('Y-m-d') . "' and R.ESTIMATE_END_TIME>=SUBTIME('" . $date . "', '01:00:00') group by R.ESTIMATE_END_TIME order by R.ESTIMATE_END_TIME";  //
            $roster_time = DB::connection("$dbname")->select($sql);

            $category = array(
                array(
                    'value' => 'P',
                    'name' => 'pickup'
                ),
                array(
                    'value' => 'D',
                    'name' => 'Drop'
                )
            );

            $statusColors = [
                [
                    'name' => 'TRIP CLOSED',
                    'value' => '#0012FE'
                ],
                [
                    'name' => 'TRIP EXECUTED',
                    'value' => '#72A65D'
                ],
                [
                    'name' => 'NOT IN-TRIP',
                    'value' => '#FF00CC'
                ],
                [
                    'name' => 'GPS OFF/IDLE',
                    'value' => '#FF0000'
                ]
            ];

            return response([
                'success' => true,
                'status' => 3,
                'statusColors' => $statusColors,
                'vendor_list' => $vendor_list,
                'category' => $category,
                'roster_time' => $roster_time,
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Data For Create for Vehicle Tracking GPS Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function merge_two_arrays($array1, $array2)
    {
        $data = array();
        $array3 = array();
        // print_r($array1);exit;
        foreach ($array2 as $arr2) {
            $ret = $this->is_in_array($array1, 'CAB_ID', $arr2['CAB_ID']);
            if ($ret == 'yes') {
                $array3[] = array('CAB_ID' => $arr2['CAB_ID'], 'roster_status' => $arr2['roster_status'],"roster_color" => $arr2['roster_color']);
            }
        }

        $arrayAB = array_merge($array1, $array3);
        // print_r($arrayAB);
        // exit;
        foreach ($arrayAB as $value) {
            $id = $value['CAB_ID'];
            if (!isset($data[$id])) {
                $data[$id] = array();
            }
            $data[$id] = array_merge($data[$id], $value);
        }

        return $data;
    }

    public function is_in_array($array, $key, $key_value)
    {
        $within_array = 'no';
        foreach ($array as $k => $v) {
            if (is_array($v)) {
                $within_array = $this->is_in_array($v, $key, $key_value);
                if ($within_array == 'yes') {
                    break;
                }
            } else {
                if ($v == $key_value && $k == $key) {
                    $within_array = 'yes';
                    break;
                }
            }
        }

        return $within_array;
    }

    public function getvendor_cablist($vendor_id, $trip_type, $shift_time)
    {
        try {
            $RS_ACTIVE = MyHelper::$RS_ACTIVE;
            $branchId = Auth::user()->BRANCH_ID;
            $dbname = Auth::user()->dbname;
            $trip_accept = MyHelper::$RS_TOTALACCEPT;
            $trip_execute = MyHelper::$RS_TOTALEXECUTE;
            $trip_allot = MyHelper::$RS_TOTALALLOT;
            $otp = MyHelper::$RPS_TTLSYSTEMOTP . ',' . MyHelper::$RPS_TTLCABSYSTEMOTP . ',' . MyHelper::$RPS_TTLEMPLOYEEDELAY_SYSTEMOTP . ',' . MyHelper::$RPS_TTLEMPLOYEEDELAY . ',' . MyHelper::$RPS_TTLCABEMPLOYEEDELAY . ','
                . MyHelper::$RPS_TTLMANUALOTP;
            $noshow = MyHelper::$RPS_TTLNOSHOW;
            // $vendorId = $request->vendor_id;
            // $trip_type = $request->trip_type;
            $where = '';
            $date = carbon::now();
            if ($vendor_id != '0') {
                $where = " and R.VENDOR_ID='$vendor_id'";
            }
            if ($trip_type == 'D') {
                $where1 = $shift_time != '' ? " and R.ESTIMATE_START_TIME='$shift_time'" : " and R.ESTIMATE_START_TIME>=SUBTIME('" . $date . "', '01:00:00')";
                $sql = "select R.ROUTE_ID,RE.ESCORT_ID,RE.EMPLOYEE_ID ,R.CAB_ID,R.VENDOR_ID,V.`NAME`,R.ROSTER_STATUS,VH.VEHICLE_REG_NO,if(R.TRIP_TYPE='P',R.START_LOCATION,R.END_LOCATION) as loc,if(R.ROSTER_STATUS in ($trip_accept),'accept',if(R.ROSTER_STATUS in ($trip_execute),'execute',if(R.ROSTER_STATUS in ($trip_allot),'allot',if(R.ROSTER_STATUS>=345,'close','create')))) as rostersts from rosters as R inner join vendors as V on V.VENDOR_ID=R.VENDOR_ID and V.BRANCH_ID='$branchId' "
                    . 'left join cab as C on C.CAB_ID=R.CAB_ID and C.ACTIVE=1 '
                    . 'left join vehicles as VH on VH.VEHICLE_ID=C.VEHICLE_ID and VH.ACTIVE=1 '
                    . ' left JOIN route_escorts as RE on RE.ROSTER_ID=R.ROSTER_ID and RE.STATUS!=6'
                    . " where R.ACTIVE='$RS_ACTIVE' and  R.TRIP_TYPE='$trip_type' and R.BRANCH_ID=$branchId and date(R.ESTIMATE_START_TIME)='" . Carbon::now()->format('Y-m-d') . "' "
                    . "  $where1 $where order by R.VENDOR_ID";
            } else {
                $where1 = $shift_time != '' ? " and R.ESTIMATE_END_TIME='$shift_time'" : " and R.ESTIMATE_END_TIME>=SUBTIME('" . $date . "', '01:00:00')";
                $sql = "select R.ROUTE_ID,RE.ESCORT_ID,RE.EMPLOYEE_ID,R.CAB_ID,R.VENDOR_ID,V.`NAME`,R.ROSTER_STATUS,VH.VEHICLE_REG_NO,if(R.TRIP_TYPE='P',R.START_LOCATION,R.END_LOCATION) as loc,if(R.ROSTER_STATUS in ($trip_accept),'accept',if(R.ROSTER_STATUS in ($trip_execute),'execute',if(R.ROSTER_STATUS in ($trip_allot),'allot',if(R.ROSTER_STATUS>=345,'close','create')))) as rostersts  from rosters as R inner join vendors as V on V.VENDOR_ID=R.VENDOR_ID and V.BRANCH_ID='$branchId' "
                    . 'left join cab as C on C.CAB_ID=R.CAB_ID and C.ACTIVE=1 '
                    . 'left join vehicles as VH on VH.VEHICLE_ID=C.VEHICLE_ID and VH.ACTIVE=1 '
                    . 'left JOIN route_escorts as RE on RE.ROSTER_ID=R.ROSTER_ID and RE.STATUS!=6'
                    . " where R.ACTIVE='$RS_ACTIVE' and R.TRIP_TYPE='$trip_type' and R.BRANCH_ID=$branchId and date(R.ESTIMATE_END_TIME)='" . Carbon::now()->format('Y-m-d') . "'"
                    . " $where1 $where order by R.VENDOR_ID";
            }

            return $vendor_cablist = DB::connection("$dbname")->select($sql);
        } catch (Exception $ex) {
        }
    }
}
