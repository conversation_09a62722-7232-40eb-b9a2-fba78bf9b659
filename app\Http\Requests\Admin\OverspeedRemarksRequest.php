<?php

//namespace App\Http\Requests\Admin;
namespace App\Http\Requests\Admin;

use App\Helpers\CommonFunction;
use Closure;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;

class OverspeedRemarksRequest extends FormRequest
{
    protected CommonFunction $commonFunction;

    public function __construct(CommonFunction $commonFunction)
    {
        
    }

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        
        return [
            
            'cab_id' => ['required','numeric'],
            'reason_id' => ['required','numeric'],
            'remark' => ['nullable','string'],
            
        ];
    }

    public function messages(): array
    {
        return [
            'cab_id.required' => 'cab_id field is required',
            'reason_id.required' => 'reason_id field is required',
            'remark.required' => 'remark field is required',
            
        ];
    }
   

    public function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json([
            'success' => false,
            'validation_error' => true,
            'message' => 'Validation errors',
            'data' => $validator->errors()
        ]));
    }
   
}
