<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EmptyCabKm extends Model
{
    use HasFactory;

    protected $table = 'empty_cab_km';
    protected $primaryKey = 'EMPTY_ID';

    public $timestamps = false;

    protected $fillable = [

        'CAB_ID',
        'VENDOR_ID',
        'BRANCH_ID',
        'TRIP_TYPE',
        'IN_OUT_TIME',
        'EMPTY_KM',
        'APPROVE_STATUS',
        'REMARKS',
        'CREATED_BY',
        'CREATED_DATE',
        'UPDATED_BY',
        'UPDATED_DATE_TIME',
    ];
}
