<?php

namespace App\Services\Admin;

use App\Helpers\CommonFunction;
use App\Helpers\MyHelper;
use App\Models\ApproveDistance;
use App\Models\Branch;
use App\Models\Employee;
use App\Models\EmployeeAddress;
use App\Models\Location;
use App\Models\property;
use App\Models\Sms;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;

class EmployeeService
{
    protected CommonFunction $commonFunction;
    protected ?Branch $currentBranch = null;

    public function __construct(CommonFunction $commonFunction)
    {
        $this->commonFunction = $commonFunction;
    }

    public function storeEmployee($request): FoundationApplication|Response|ResponseFactory
    {

        try {

            DB::beginTransaction();
            $auth_user = Auth::user();

            $userData = $this->prepareUserData($request, $auth_user);
            $userResult = User::create($userData);

            $employeeData = $this->prepareEmployeeData($request, $auth_user, MyHelper::$RS_ACTIVE, $userResult->id);
            $employeeResult = Employee::create($employeeData);


            $this->handleAdditionalTasks($request, $auth_user, $employeeResult->id);

            return response([
                'success' => true,
                'status' => 3,
                'message' => 'Employee Created Successfully',

            ]);

        } catch (\Throwable|\Exception $e) {
            DB::rollBack();
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'validation_controller' => true,
                'status' => 4,
                'message' => 'Employee Created UnSuccessfully',
                'errorM' => $e->getMessage(),
            ], 500);
        } finally {
            DB::commit();
        }


    }

    private function prepareUserData($request, $auth_user): array
    {
        return [
            "BRANCH_ID" => $auth_user->BRANCH_ID,
            "name" => $request->MOBILE,
            "email" => "",
            "user_type" => MyHelper::$EMPLOYEE,
            "vendor_id" => '0',
            "password" => Hash::make(config('app.otp_password')),
            "active_status" => MyHelper::$RS_ACTIVE,
            "menu_id" => 1,
            "CREATED_BY" => $auth_user->id,
            "created_at" => now(),
            "updated_at" => now(),
            "login_category" => MyHelper::$LOGIN_CATEGORY_ZINGO
        ];

    }

    /**
     * @throws ConnectionException
     */
    private function prepareEmployeeData($request, $auth_user, $active, $userId): array
    {
        $date = Carbon::now();

        return [
            "EMPLOYEES_ID" => $request->EMPLOYEES_ID,
            "BRANCH_ID" => $request->BRANCH_ID,
            "NAME" => $this->commonFunction->AES_ENCRYPT(strtolower($request->NAME), config('app.aes_encrypt_key')),
            "PASSWORD" => $this->commonFunction->AES_ENCRYPT($request->PASSWORD, config('app.aes_encrypt_key')),
            "MOBILE" => $this->commonFunction->AES_ENCRYPT($request->MOBILE, config('app.aes_encrypt_key')),
            "GENDER" => $request->GENDER,
            "PROJECT_NAME" => $request->PROJECT_NAME,
            "LOCATION_ID" => $request->LOCATION,
            "ADDRESS" => $request->ADDRESS,
            "ACTIVE" => $active,
            "LATITUDE" => $request->LATITUDE,
            "LONGITUDE" => $request->LONGITUDE,
            "DISTANCE" => $this->calculateDistance($request->LATITUDE, $request->LONGITUDE, $auth_user->BRANCH_ID),
            "EMAIL" => $this->commonFunction->AES_ENCRYPT($request->EMP_EMAIL, config('app.aes_encrypt_key')),
            "CREATED_BY" => $auth_user->id,
            "CREATED_DATE" => $date->format("Y-m-d H:i:s"),
            "updated_at" => $date->format("Y-m-d H:i:s"),
            "CATEGORY" => MyHelper::$EMPLOYEE_CATEGORY,
            "LAST_NAME" => $request->LNAME,
            "ADDRESS_TYPE" => $request->addr_type,
            "RFID_CARD" => $request->rfid_no,
            "user_id" => $userId,
        ];
    }

    /**
     * @throws ConnectionException
     */
    private function handleAdditionalTasks($request, $auth_user, $employeeId): void
    {
        $this->handleSecondaryAddress($request, $auth_user, $employeeId);
        $this->handleRegistrationSMS($request, $auth_user);
        $this->handleApprovedDistance($request, $auth_user);
    }

    /**
     * @throws ConnectionException
     */
    private function handleSecondaryAddress($request, $auth_user, $employeeId): void
    {
        if ($request->SEC_ADDRESS && $request->SEC_LATITUDE && $request->SEC_LONGITUDE) {
            $secondaryAddressData = [
                "ADDRESS_TYPE" => $request->addr_type,
                "ADDRESS" => $request->SEC_ADDRESS,
                "LATITUDE" => $request->SEC_LATITUDE,
                "LONGITUDE" => $request->SEC_LONGITUDE,
                "LOCATION_ID" => $request->SEC_LOCATION,
                "EMP_AUTO_ID" => $employeeId,
                "ACTIVE" => MyHelper::$RS_ACTIVE,
                "CREATED_BY" => $auth_user->id,
                "CREATED_DATE" => date("Y-m-d H:i:s"),
                "UPDATED_AT" => date("Y-m-d H:i:s"),
                "DISTANCE" => $this->calculateDistance($request->SEC_LATITUDE, $request->SEC_LONGITUDE, $auth_user->BRANCH_ID),
                "SEC_ADDR_DURATION" => $request->num_of_day,
            ];
            EmployeeAddress::create($secondaryAddressData);
        }
    }


    private function handleRegistrationSMS($request, $auth_user): void
    {
        $employeeRegistrationSMS = $this->getPropertyValue('EMPLOYEE_REGISTRATION_SMS');

        if ($employeeRegistrationSMS == 1) {
            $message = "Hi {$request->NAME}. Please Download Zingo Mobile App \"Zingo Corp\" for better office commute. Android Link: http://bit.ly/2rmm933, iOS link: https://apple.co/2sck4a9";
            $smsData = [
                "BRANCH_ID" => $auth_user->BRANCH_ID,
                "ORIGINATOR" => $this->getPropertyValue(MyHelper::$PR_SMSTAG),
                "RECIPIENT" => $request->MOBILE,
                "MESSAGE" => $message,
                "REF_NO" => '--',
                "CREATED_BY" => $auth_user->id,
                "CREATED_DATE" => date("Y-m-d H:i:s"),
                "UPDATED_DATE" => date("Y-m-d H:i:s"),
                "CATEGORY" => MyHelper::$SMS_CATEGORY_EMP_REG
            ];

            Sms::create($smsData);
        }
    }

    /**
     * @throws ConnectionException
     */
    private function handleApprovedDistance($request, $auth_user): void
    {
        $existingLocation = DB::table("approve_distances")
            ->where("BRANCH_ID", $auth_user->BRANCH_ID)
            ->where("LOCATION_ID", $request->LOCATION)
            ->first();

        if (!$existingLocation) {
            $approveDistance = $this->calculateApprovedDistance($request->LOCATION, $auth_user->BRANCH_ID);
            ApproveDistance::create([
                "BRANCH_ID" => $auth_user->BRANCH_ID,
                "LOCATION_ID" => $request->LOCATION,
                "APPROVED_DISTANCE" => $approveDistance,
                "CREATE_BY" => $auth_user->id,
                "CREATED_DATE" => now(),
                "UPDATED_DATE" => now()
            ]);
        }
    }


    /**
     * @throws ConnectionException
     */
    private function calculateDistance($lat, $long, $branchId): float|int
    {
        $branch = $this->getBranch($branchId);
        $distance = $this->commonFunction->getKmTwoPointMasterEmp("$lat,$long", "{$branch->LAT},{$branch->LONG}");
        return explode("-", $distance)[0] / 1000;
    }


    /**
     * @throws ConnectionException
     */
    private function calculateApprovedDistance($locationId, $branchId): float
    {
        $branch = $this->getBranch($branchId);
        $location = Location::where('LOCATION_ID', $locationId)->first(['LATITUDE', 'LONGITUDE']);

        $approveDis = $this->commonFunction->getKmMasterEmp(
            "{$branch->LAT},{$branch->LONG}",
            "{$branch->LAT},{$branch->LONG}",
            "{$location->LATITUDE},{$location->LONGITUDE}"
        );
        return round(explode('-', $approveDis)[0] / 1000);
    }

    private function getBranch($branchId)
    {
        if (!$this->currentBranch || $this->currentBranch->BRANCH_ID != $branchId) {
            $this->currentBranch = Branch::where('BRANCH_ID', $branchId)->first(['BRANCH_ID', 'LAT', 'LONG']);
        }
        return $this->currentBranch;
    }

    private function getPropertyValue($propertyName)
    {
        $property = Property::where('BRANCH_ID', Auth::user()->BRANCH_ID)
            ->where('ACTIVE', MyHelper::$RS_ACTIVE)
            ->where('PROPERTIE_NAME', $propertyName)
            ->first();
        return $property ? $property->PROPERTIE_VALUE : null;
    }

    public function indexEmployee(): FoundationApplication|Response|ResponseFactory
    {
        $authUser = Auth::user();

        try {
            $employees = Employee::select('employees.id',
                'employees.EMPLOYEES_ID',
                'employees.NAME',
                'employees.EMAIL',
                'employees.PROJECT_NAME',
                'employees.GENDER',
                'employees.MOBILE',
                'branch.BRANCH_NAME',
                'users.name',
                "employees.ADDRESS as primary_addr",
                'employee_address.ADDRESS as secondary_addr'
            )
                ->where('employees.ACTIVE', MyHelper::$RS_ACTIVE)
                ->where('employees.BRANCH_ID', $authUser->BRANCH_ID)
                ->join("branch", "branch.BRANCH_ID", "=", "employees.BRANCH_ID")
                ->leftJoin("users", "users.id", "=", "employees.user_id")
                ->leftJoin("locations", "locations.LOCATION_ID", "=", "employees.LOCATION_ID")
                ->leftJoin("employee_address", "employee_address.EMP_AUTO_ID", "=", "employees.id")
                ->get();

            return response([
                'success' => true,
                'status' => 3,
                'employees' => $employees,
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Employee Index Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }


    public function paginationEmployee($request, bool $active = true): FoundationApplication|Response|ResponseFactory
    {
        try {

            $perPage = $request->input('per_page', MyHelper::$PAGINATION_PER_PAGE);
            $branch_id = Auth::user()->BRANCH_ID;
            $activeStatus = $active ? MyHelper::$RS_ACTIVE : MyHelper::$RS_INACTIVE;

            $employees = Employee::query()
                ->select(
                    'employees.id',
                    'employees.EMPLOYEES_ID',
                    'employees.NAME',
                    'employees.PROJECT_NAME',
                    'employees.GENDER',
                    'employees.MOBILE',
                    'employees.BRANCH_ID',
                    'employees.LAST_NAME as LNAME',
                    'employees.EMAIL',
                    'employees.RFID_CARD',
                    'employees.LOCATION_ID',
                    'employees.LOCATION_ID as primary_location_id',
                    "employees.ADDRESS as primary_addr",
                    'employees.LATITUDE as primary_latitude',
                    'employees.LONGITUDE as primary_longitude',
                    'employees.ADDRESS_TYPE as addr_type',


                    'branch.BRANCH_NAME',
                    'users.name',

                    'employee_address.LOCATION_ID as secondary_location_id',
                    'employee_address.ADDRESS as secondary_addr',
                    'employee_address.LATITUDE as secondary_latitude',
                    'employee_address.LONGITUDE as secondary_longitude',
                    'employee_address.SEC_ADDR_DURATION as num_of_day',

                    'primary_location.LOCATION_NAME as primary_location_name',
                    'secondary_location.LOCATION_NAME as secondary_location_name'

                )
                ->where('employees.ACTIVE', $activeStatus)
                ->where('employees.BRANCH_ID', $branch_id)
                ->join("branch", "branch.BRANCH_ID", "=", "employees.BRANCH_ID")
                ->leftJoin("users", "users.id", "=", "employees.user_id")
                ->leftJoin("locations as primary_location", "primary_location.LOCATION_ID", "=", "employees.LOCATION_ID")
                ->leftJoin("employee_address", "employee_address.EMP_AUTO_ID", "=", "employees.id")
                ->leftJoin("locations as secondary_location", "secondary_location.LOCATION_ID", "=", "employee_address.LOCATION_ID");


            $filterModel = $request->input('filterModel');
            if ($filterModel) {
                foreach ($filterModel as $field => $filter) {
                    if (isset($filter['filter']) && $filter['filter'] !== '') {
                        $value = $filter['filter'];
                        $type = $filter['type'];

                        switch ($field) {
                            case 'EMPLOYEES_ID':
                                $employees->where('employees.EMPLOYEES_ID', 'like', "%{$value}%");
                                break;
                            case 'PROJECT_NAME':
                                $employees->where('employees.PROJECT_NAME', 'like', "%{$value}%");
                                break;
                            case 'GENDER':
                                $employees->where('employees.GENDER', 'like', "%{$value}%");
                                break;
                            case 'BRANCH_NAME':
                                $employees->where('branch.BRANCH_NAME', 'like', "%{$value}%");
                                break;
                            case 'primary_addr':
                                $employees->where('employees.ADDRESS', 'like', "%{$value}%");
                                break;
                            case 'secondary_addr':
                                $employees->where('employee_address.ADDRESS', 'like', "%{$value}%");
                                break;
                            case 'name_decrypted':
                                $encryptedValue = $this->commonFunction->AES_ENCRYPT($value, config('app.aes_encrypt_key'));
                                $employees->where('employees.NAME', 'like', "%{$encryptedValue}%");
                                break;
                            case 'mobile_decrypted':
                                $encryptedValue = $this->commonFunction->AES_ENCRYPT($value, config('app.aes_encrypt_key'));
                                $employees->where('employees.MOBILE', 'like', "%{$encryptedValue}%");
                                break;
                        }
                    }
                }
            }


            if ($request->has('orderBy') && !empty($request->input('orderBy'))) {
                $orderBy = $request->input('orderBy');
                $order = $request->input('order', 'asc');
                $employees->orderBy($orderBy, $order);
            } else {
                $employees->orderBy('employees.CREATED_DATE', 'desc');
            }


            if ($perPage === MyHelper::$PAGINATION_PER_PAGE_ALL) {
                $paginatedEmployees = $employees->paginate($employees->count());
            } else {
                $perPage = is_numeric($perPage) ? (int)$perPage : MyHelper::$PAGINATION_PER_PAGE;
                $paginatedEmployees = $employees->paginate($perPage);
            }

            return response([
                'success' => true,
                'status' => 3,
                'employees' => $paginatedEmployees,
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'Employee Pagination Unsuccessful' : 'Deactivate Employee Pagination Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function editEmployee($employeeAutoIdCrypt): FoundationApplication|Response|ResponseFactory
    {
        $authUser = Auth::user();

        try {
            $employeeAutoId = Crypt::decryptString($employeeAutoIdCrypt);
            $employee = Employee::where('employees.BRANCH_ID', $authUser->BRANCH_ID)
                ->where('employees.id', $employeeAutoId)
                ->where('employees.ACTIVE', MyHelper::$RS_ACTIVE)
                ->select(
                    'employees.id',
                    'employees.EMPLOYEES_ID',
                    'employees.NAME',
                    'employees.PROJECT_NAME',
                    'employees.GENDER',
                    'employees.MOBILE',
                    'branch.BRANCH_NAME',
                    'users.name',
                    'employees.ADDRESS as primary_addr',
                    'employee_address.ADDRESS as secondary_addr'
                )
                ->join("branch", "branch.BRANCH_ID", "=", "employees.BRANCH_ID")
                ->leftJoin("users", "users.id", "=", "employees.user_id")
                ->leftJoin("locations", "locations.LOCATION_ID", "=", "employees.LOCATION_ID")
                ->leftJoin("employee_address", "employee_address.EMP_AUTO_ID", "=", "employees.id")
                ->firstOrFail();

            return response([
                'success' => true,
                'status' => 3,
                'employee' => $employee,
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Employee Edit Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function updateEmployee($request, $employeeAutoIdCrypt): FoundationApplication|Response|ResponseFactory
    {
        $authUser = Auth::user();
        try {
            DB::beginTransaction();
            $employeeAutoId = Crypt::decryptString($employeeAutoIdCrypt);
            $employee = Employee::where('BRANCH_ID', $authUser->BRANCH_ID)
                ->findOrFail($employeeAutoId);
            $user = User::where('BRANCH_ID', $authUser->BRANCH_ID)
                ->where('id', $employee->user_id)
                ->firstOrFail();

            $auth_user = Auth::user();

            $userData = $this->prepareUserDataForUpdate($request, $auth_user, $user);
            $user->update($userData);

            $employeeData = $this->prepareEmployeeDataForUpdate($request, $auth_user, $employee);
            $employee->update($employeeData);

            $this->handleAdditionalTasksForUpdate($request, $auth_user, $employee->id);

            DB::commit();


            return response([
                'success' => true,
                'status' => 3,
                'message' => 'Employee Updated Successfully',
            ]);

        } catch (\Throwable|\Exception $e) {
            DB::rollBack();
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'message' => 'Employee Update Unsuccessful',
                'status' => 4,
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    private function prepareUserDataForUpdate($request, $auth_user, $user): array
    {
        return [
            "BRANCH_ID" => $auth_user->BRANCH_ID,
            "name" => $request->MOBILE,
            "updated_at" => now(),
        ];
    }

    /**
     * @throws ConnectionException
     */
    private function prepareEmployeeDataForUpdate($request, $auth_user, $employee): array
    {
        $date = Carbon::now();

        return [
            "EMPLOYEES_ID" => $request->EMPLOYEES_ID,
            "BRANCH_ID" => $request->BRANCH_ID,
            "NAME" => $this->commonFunction->AES_ENCRYPT(strtolower($request->NAME), config('app.aes_encrypt_key')),
            "MOBILE" => $this->commonFunction->AES_ENCRYPT($request->MOBILE, config('app.aes_encrypt_key')),
            "GENDER" => $request->GENDER,
            "PROJECT_NAME" => $request->PROJECT_NAME,
            "LOCATION_ID" => $request->LOCATION,
            "ADDRESS" => $request->ADDRESS,
            "LATITUDE" => $request->LATITUDE,
            "LONGITUDE" => $request->LONGITUDE,
            "DISTANCE" => $this->calculateDistance($request->LATITUDE, $request->LONGITUDE, $auth_user->BRANCH_ID),
            "EMAIL" => $this->commonFunction->AES_ENCRYPT($request->EMP_EMAIL, config('app.aes_encrypt_key')),
            "UPDATED_BY" => $auth_user->id,
            "updated_at" => $date->format("Y-m-d H:i:s"),
            "LAST_NAME" => $request->LNAME,
            "ADDRESS_TYPE" => $request->addr_type,
            "RFID_CARD" => $request->rfid_no,
        ];
    }

    /**
     * @throws ConnectionException
     */
    private function handleAdditionalTasksForUpdate($request, $auth_user, $employeeId): void
    {
        $this->handleSecondaryAddressForUpdate($request, $auth_user, $employeeId);
        $this->handleApprovedDistanceForUpdate($request, $auth_user);
    }

    /**
     * @throws ConnectionException
     */
    private function handleSecondaryAddressForUpdate($request, $auth_user, $employeeId): void
    {

            EmployeeAddress::updateOrCreate(
                ['EMP_AUTO_ID' => $employeeId],
                [
                    "ADDRESS_TYPE" => $request->addr_type,
                    "ADDRESS" => $request->SEC_ADDRESS,
                    "LATITUDE" => $request->SEC_LATITUDE,
                    "LONGITUDE" => $request->SEC_LONGITUDE,
                    "LOCATION_ID" => $request->SEC_LOCATION,
                    "ACTIVE" => MyHelper::$RS_ACTIVE,
                    "UPDATED_BY" => $auth_user->id,
                    "UPDATED_AT" => now(),
                    "DISTANCE" => $this->calculateDistance($request->SEC_LATITUDE, $request->SEC_LONGITUDE, $auth_user->BRANCH_ID),
                    "SEC_ADDR_DURATION" => $request->num_of_day,
                ]
            );

    }

    /**
     * @throws ConnectionException
     */
    private function handleApprovedDistanceForUpdate($request, $auth_user): void
    {
        $this->handleApprovedDistance($request, $auth_user);
    }

    public function deleteEmployee($request, $employeeAutoIdCrypt): FoundationApplication|Response|ResponseFactory
    {
        $authUser = Auth::user();

        try {
            DB::beginTransaction();

            $employeeId = Crypt::decryptString($employeeAutoIdCrypt);
            $employee = Employee::where('ACTIVE', MyHelper::$RS_ACTIVE)
                ->where('BRANCH_ID', $authUser->BRANCH_ID)
                ->findOrFail($employeeId);

            $employee->update([
                'REASON' => $request->input('remark'),
                'ACTIVE' => MyHelper::$RS_INACTIVE,
                'UPDATED_BY' => Auth::user()->id,
            ]);

            User::where('id', $employee->user_id)
                ->where('BRANCH_ID', $authUser->BRANCH_ID)
                ->update([
                    'active_status' => MyHelper::$RS_INACTIVE,
                    'updated_at' => now(),
                ]);

            DB::commit();

            return response([
                'success' => true,
                'status' => 3,
                'message' => 'Employee Deleted Successfully',
            ]);
        } catch (\Throwable|\Exception $e) {
            DB::rollBack();
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Employee Delete Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }


    public function activateEmployee($request): FoundationApplication|Response|ResponseFactory
    {

        $authUser = Auth::user();

        try {
            DB::beginTransaction();

            $activatedCount = 0;
            $failedIds = [];

            foreach ($request->id_crypts as $idCrypt) {
                try {
                    $employeeId = Crypt::decryptString($idCrypt);
                    $employee = Employee::where('ACTIVE', MyHelper::$RS_INACTIVE)
                        ->where('BRANCH_ID', $authUser->BRANCH_ID)
                        ->findOrFail($employeeId);

                    $employee->update([
                        'ACTIVE' => MyHelper::$RS_ACTIVE,
                        'UPDATED_BY' => $authUser->id,
                        'updated_at' => now(),
                    ]);


                    User::where('id', $employee->user_id)
                        ->where('BRANCH_ID', $authUser->BRANCH_ID)
                        ->update([
                            'active_status' => MyHelper::$RS_ACTIVE,
                            'updated_at' => now(),
                        ]);

                    $activatedCount++;
                } catch (\Exception $e) {
                    Log::error("Failed to activate employee: " . $e->getMessage());
                    $failedIds[] = $idCrypt;
                }
            }

            DB::commit();

            $message = $activatedCount > 0
                ? "$activatedCount employee(s) activated successfully."
                : "No employees were activated.";

            if (!empty($failedIds)) {
                $message .= " Failed to activate " . count($failedIds) . " employee(s).";
            }

            return response([
                'success' => true,
                'message' => $message,
                'status' => 3,
                'activated_count' => $activatedCount,
                'failed_ids' => $failedIds,
            ]);

        } catch (\Throwable|\Exception $e) {
            DB::rollBack();
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'validation_controller' => true,
                'message' => 'Employee activation process failed',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function dataForCreateEmployee(): FoundationApplication|Response|ResponseFactory
    {

        try {
            $user = Auth::user();
            $rsActive = MyHelper::$RS_ACTIVE;
            $branchId = $user->BRANCH_ID;

            $branch = DB::table('branch')
                ->select('BRANCH_ID', 'DIVISION_ID')
                ->where('ACTIVE', $rsActive)
                ->where('BRANCH_ID', $branchId)
                ->first();

            $location = DB::table('locations')
                ->select('LOCATION_ID', 'LOCATION_NAME')
                ->where('ACTIVE', $rsActive)
                ->where('DIVISION_ID', $branch->DIVISION_ID)
                ->get();

            $branchList = DB::table('branch')
                ->select('BRANCH_ID', 'BRANCH_NAME')
                ->where('ACTIVE', $rsActive)
                ->where('BRANCH_ID', $branchId)
                ->get();


            return response([
                'success' => true,
                'status' => 3,
                'locations' => $location,
                'branch_list' => $branchList,
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Data For Create for Employee  Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function otpCreate($request): FoundationApplication|Response|ResponseFactory
    {
        $authUser = Auth::user();
        try {
            $mobile = $this->commonFunction->AES_ENCRYPT($request->mobile, config('app.aes_encrypt_key'));
            $employees = Employee::select('employees.id',
                'employees.EMPLOYEES_ID',
                'employees.NAME',
                'employees.PROJECT_NAME',
                'employees.GENDER',
                'employees.MOBILE',
                'b.BRANCH_NAME'
            //'p.PROPERTIE_VALUE'
            )
                ->where('employees.ACTIVE', MyHelper::$RS_ACTIVE)
                ->where('employees.BRANCH_ID', 18)
                ->where('employees.MOBILE', $mobile)
                ->join("branch as b", "b.BRANCH_ID", "=", "employees.BRANCH_ID")
                //->leftjoin("properties as p", "p.BRANCH_ID", "=", "employees.BRANCH_ID")
                ->get();

            return response([
                'success' => true,
                'status' => 3,
                'employees' => $employees,
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Employee Index Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ]);
        }
    }
}
