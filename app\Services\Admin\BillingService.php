<?php
namespace App\Services\Admin;

use App\Helpers\CommonFunction;
use App\Helpers\MyHelper;
use App\Models\Branch;
use Carbon\Carbon;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\ElasticController;
use App\Models\Roster;
use App\Models\TollPayment;


class BillingService
{
    protected CommonFunction $commonFunction;
    protected ?Branch $currentBranch = null;

    protected $BRANCHID;
    protected $ACTIVE;
    protected $ALLREPORT;
    protected $VENDORID;
    protected $TTLTYPE;
    protected $TRIPCLOSE;
    protected $MANUALTRIPCLOSE;
    protected $AUTOCANCEL;
    protected $INACTIVE;
    protected $SWINGSTART;
    protected $SWINGEND;

    public function __construct(CommonFunction $commonFunction)
    {
        $this->commonFunction = $commonFunction;

        $this->BRANCHID = Auth::user()->BRANCH_ID;
        $this->ACTIVE = MyHelper::$RS_ACTIVE;
        $this->ALLREPORT = MyHelper::$RP_ALLREPORT;
        $this->VENDORID = MyHelper::$UT_VENDORID;
        $this->TTLTYPE = MyHelper::$RP_TTLTYPE;
        $this->TRIPCLOSE = MyHelper::$RS_TRIPCLOSE;
        $this->MANUALTRIPCLOSE = MyHelper::$RS_MANUALTRIPCLOSE;
        $this->SWINGSTART = MyHelper::$RP_SWINGSTART;
        $this->SWINGEND = MyHelper::$RP_SWINGEND;
        $this->AUTOCANCEL = MyHelper::$RS_AUTOCANCEL;
        $this->INACTIVE = MyHelper::$RS_INACTIVE;
	    ini_set("max_execution_time","0");
    }

    public function dataForCreate(): FoundationApplication|Response|ResponseFactory
    {
        try{            
            return response([
                'success' => true,
                'status' => 3,
                'data' => [
                    'index_dropdown' => [
                        "ALL" => "ALL",
                        "Pickup" => "P", 
                        "Drop" => "D"
                    ],
                    'trip_km_approvel' => [
                        "No Billable" => "NonKm",
                        "Device KM" => "DeviceKm",
                        "Google KM" => "GoogleKm",
                        "Approved KM" => "ApprovedKm"
                    ]
                ],
                'message' => 'Data for Create Successfully',
    
            ],200);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Data For Create for MIS Report Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ],500);
        }
    }

    public function drivermisreportdataForCreate()
    {
        try {
            $allvehicle = $this->GetCabVehicle();
 
            return response([
                'success' => true,
                'status' => 1,
                'result' => [
                    'drop_down_values' => [
                        'vehicles' => $allvehicle,
                        'year' => $this->generateYears(),
                        'month' => $this->generateMonths(),
			            'export_type' => $this->getExportType(),
                        'tariff_type' => $this->getTariffType(),
                    ],
                ],                
		        'message' => 'Data for Create Driver MIS Fetch Successfully',
            ], 200);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'message' => 'Data For Create for Driver MIS Report Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /*public function ZohoMisReport($request)
    {
        try {
            $from_date = $request->from_date;
            $to_date = $request->to_date;
            $trip_type = $request->trip_type;
            $BRANCH_ID = Auth::user()->BRANCH_ID;

            $ElasticController = new ElasticController();
            $taxdetect = $this->commonFunction->GetPropertyValue('TAX');
            $result = $this->MisReport($from_date, $to_date, $trip_type);
            $json_arr = json_encode($result);
            $normal_array = json_decode($json_arr, true);
            $results = array();
            $AES_ENCRYPT_KEY = env('AES_ENCRYPT_KEY');

            for ($i = 0; $i < count($normal_array); $i++) {

                $REMAINKM = 0;
                $ROSTER_ID = $normal_array[$i]['ROSTER_ID'];
                $TRIP_TYPE = $normal_array[$i]['TRIP_TYPE'];
                $TOTAL_KM = $normal_array[$i]['TOTAL_KM'];
                
                $TRAVELEMPCNT = $normal_array[$i]['TRAVELEMPCNT'];
                $CAPACITY = $normal_array[$i]['CAPACITY'];
                $TRIP_APPROVED_KM = $normal_array[$i]['TRIP_APPROVED_KM'];
                $TARIFF = $normal_array[$i]['TARIFF'];
                $TOLLCOST = $normal_array[$i]['TOLLCOST'];
                $PENALTYCOST = $normal_array[$i]['PENALTYCOST'];
                $PACKAGE_KMS = $normal_array[$i]['PACKAGE_KMS'];
                $EXTRA_KMS_CHARGE = $normal_array[$i]['EXTRA_KMS_CHARGE'];
                $ROSTER_STATUS = $normal_array[$i]['ROSTER_STATUS'];
                $TRIP_TYPE = $normal_array[$i]['TRIP_TYPE'];
                $START_LAT = $normal_array[$i]['START_LAT'];
                $START_LONG = $normal_array[$i]['START_LONG'];
                $END_LAT = $normal_array[$i]['END_LAT'];
                $END_LONG = $normal_array[$i]['END_LONG'];
                $EMPLOYEES_ID = $normal_array[$i]['EMPLOYEES_ID'];
                $EMPLOYEES_NAME = $normal_array[$i]['EMPLOYEES_NAME'];

                $SWINGKM = 0;

                if($EMPLOYEES_NAME != ''){
                    $EMPLOYEES_NAME_ARRAY = explode(',',$EMPLOYEES_NAME);
                    $decodedArr = [];
                    foreach($EMPLOYEES_NAME_ARRAY as $EmpName){
                        $decodedArr[] = $this->commonFunction->AES_DECRYPT($EmpName, $AES_ENCRYPT_KEY);
                    }
                    $normal_array[$i]['EMPLOYEES_NAME'] = implode(',',$decodedArr);
                }
                $OPTLOCATION = '--';
                $RS_STATUS = $this->commonFunction->TripStatus($ROSTER_STATUS);

                if ($BRANCH_ID == '27' || $BRANCH_ID == '23') {
                    $TTLKM = $TRIP_APPROVED_KM + $TOTAL_KM + $SWINGKM;
                    if ($TTLKM < $PACKAGE_KMS) {
                        $TTLCOST = $TARIFF;
                    } else {
                        $REMAINKM = $TTLKM - $PACKAGE_KMS;
                        $TTLCOST = $TARIFF + ($REMAINKM * $EXTRA_KMS_CHARGE);
                    }
                } else if ($BRANCH_ID == '25') {

                    $TTLDIST = round($this->GetRosterDistance($ROSTER_ID, $TRIP_TYPE));
                    $TTLKM = (2 * $TTLDIST) + (2 * $TRAVELEMPCNT);
                    $TTLCOST = $TARIFF * $TTLKM;
                } else if ($BRANCH_ID == '48') {
                    $TTLKM = $TRIP_APPROVED_KM;
                    $TTLCOST = $TARIFF;
                } else if ($BRANCH_ID == '57') {
                    if ($TRAVELEMPCNT < $CAPACITY) {
                        if ($TRAVELEMPCNT > 0 && $TRAVELEMPCNT <= 4) {
                            $TTLCOST = 820;
                        } elseif ($TRAVELEMPCNT > 4 && $TRAVELEMPCNT < 8) {
                            $TTLCOST = 1055;
                        }
                    } else {
                        $TTLCOST = $TARIFF;
                    }
                    $TTLKM = $TRIP_APPROVED_KM + $TOTAL_KM + $SWINGKM;

                } else {
                    $TTLKM = $TRIP_APPROVED_KM + $SWINGKM;
                    $TTLCOST = $TARIFF * $TTLKM;
                }

                $FINALCOST = $TTLCOST + $TOLLCOST - $PENALTYCOST;
                if ($RS_STATUS == 'Tripsheet Cancelled') {
                    $FINALCOST = 0;
                }
                if ($FINALCOST != 0) {
                    $TtlTax = ($FINALCOST * $taxdetect) / 100;
                } else {
                    $TtlTax = 0;
                }
                if ($BRANCH_ID == '48') {
                    $emp_arr_val = array(
                        "OTPLOCATION" => $OPTLOCATION,
                        "SWINGKM" => $SWINGKM,
                        "TAX" => $TtlTax,
                        "TOTALAMT" => $FINALCOST,
                        "ALLSTATUS" => $RS_STATUS,
                        "TTLKMS" => $TTLKM
                    );
                } else {
                    $emp_arr_val = array(
                        "OTPLOCATION" => $OPTLOCATION,
                        "SWINGKM" => $SWINGKM,
                        "TOTALAMT" => $FINALCOST,
                        "ALLSTATUS" => $RS_STATUS,
                        "TTLKMS" => $TTLKM
                    );
                }

                $results[$i] = array_replace($normal_array[$i], $emp_arr_val);
            }

	    return response([
                'success' => true,
                'status' => 1,
                'result' => $results,
                'message' => 'Zoho MIS Report has been successfully Fetched.',
            ],200);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'message' => 'Real Time Tracking Data Fetch Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function MisReport($from_date,$to_date,$trip_type) {
        $vendor_id = auth::user()->vendor_id;
		$DIVISION_ID = auth::user()->DIVISION_ID;
        $dbname = Auth::user()->dbname;
        if($vendor_id == $this->VENDORID) {
            $vendor = "";
        } else {
            $vendor = "AND R.VENDOR_ID=$vendor_id";
        }

        if($trip_type == $this->ALLREPORT) {
            $RPTTLTYPE = explode(',', $this->TTLTYPE);
            $triptype = "AND R.TRIP_TYPE IN ('$RPTTLTYPE[0]','$RPTTLTYPE[1]')";
        } else {
            $triptype = "AND R.TRIP_TYPE ='$trip_type'";
        }

        $SWINGSTART_TIME = $this->commonFunction->GetPropertyValue('SWING START TIME');
        $SWINGEND_TIME = $this->commonFunction->GetPropertyValue('SWING END TIME');
		
        if($this->BRANCHID != '25'){

			if($this->BRANCHID == '48' || $this->BRANCHID == '61') {
            $kmquery = '';
            $queryttlamt = "'0' AS TAX,'0' AS TOTALAMT,";
            $querykm = ",R.TOTAL_KM,R.TRIP_APPROVED_KM,AP.APPROVED_DISTANCE,dbs.GOOGLE_KM, if(ROUND(dbs.GOOGLE_KM) >= ROUND(dbs.DEVICE_KM),'0','1') as isDeviceKm,dbs.APPROVED_KM_TYPE,dbs.TRIP_STATUS";
			
			$tariff="LEFT JOIN tariff_km_slap  T ON   T.PACKAGE_START_KM <= R.TRIP_APPROVED_KM AND T.PACKAGE_END_KM >= R.TRIP_APPROVED_KM AND T.VENDOR_ID = R.VENDOR_ID AND T.VEHICLE_MODEL_ID = V.VEHICLE_MODEL_ID AND T.BRANCH_ID = $this->BRANCHID";
			$tariff_select=" T.PACKAGE_PRICE AS TARIFF,0 as PACKAGE_KMS,0 as EXTRA_KMS_CHARGE";
			
           } else {           			   
		
					$kmquery = "R.TOTAL_KM,R.TRIP_APPROVED_KM,AP.APPROVED_DISTANCE,dbs.GOOGLE_KM, if(ROUND(dbs.GOOGLE_KM) >= ROUND(dbs.DEVICE_KM),'0','1') as isDeviceKm,dbs.APPROVED_KM_TYPE,dbs.TRIP_STATUS,";
					$querykm = '';
					$queryttlamt = "'0' AS TOTALAMT,";
					$tariff='LEFT JOIN tariff TF ON TF.TARIFF_TYPE = C.TARIFF_TYPE AND TF.BRANCH_ID = C.BRANCH_ID 
				AND TF.VENDOR_ID = C.VENDOR_ID AND TF.VEHICLE_MODEL_ID = V.VEHICLE_MODEL_ID';
                          $tariff_select=" TF.PACKAGE_PRICE AS TARIFF,TF.PACKAGE_KMS,TF.EXTRA_KMS_CHARGE";
		   }
        } else {
                $kmquery = '2 * R.TOTAL_KM,';
                $queryttlamt = '';
                $querykm = ",R.TOTAL_KM,R.TRIP_APPROVED_KM,AP.APPROVED_DISTANCE,dbs.GOOGLE_KM, if(ROUND(dbs.GOOGLE_KM) >= ROUND(dbs.DEVICE_KM),'0','1') as isDeviceKm,dbs.APPROVED_KM_TYPE,dbs.TRIP_STATUS";
			    $tariff='LEFT JOIN tariff TF ON TF.TARIFF_TYPE = C.TARIFF_TYPE AND TF.BRANCH_ID = C.BRANCH_ID AND TF.VENDOR_ID = C.VENDOR_ID AND TF.VEHICLE_MODEL_ID = V.VEHICLE_MODEL_ID';
                $tariff_select=" TF.PACKAGE_PRICE AS TARIFF,TF.PACKAGE_KMS,TF.EXTRA_KMS_CHARGE";
        }
		
		$query = "SELECT R.ROSTER_ID,R.ROUTE_ID,RS.TTLEMPCNT,RS.TRAVELEMPCNT,RS.EMPNOSHOWCNT,R.TRIP_TYPE,V.VEHICLE_REG_NO,VM.MODEL,VM.CAPACITY,
        VE.`NAME` VENDORNAME,R.START_LOCATION,R.END_LOCATION,'--' as OTPLOCATION,
        if(R.TRIP_TYPE = 'P',DATE(R.ESTIMATE_END_TIME),DATE(R.ESTIMATE_START_TIME)) as INOUT_DATE,
        if(R.TRIP_TYPE = 'P',TIME(R.ESTIMATE_END_TIME),TIME(R.ESTIMATE_START_TIME)) AS INOUT_TIME,RM.REASON,$kmquery
        '0' AS TTLKMS,if(TP.ttlcost is NULL,0,TP.ttlcost) AS TOLLCOST,if(PY.TTLAMOUNT is NULL,0,PY.TTLAMOUNT) AS PENALTYCOST,$queryttlamt
        if(RE.ROSTER_ID IS NULL,'--',CONCAT(ES.ESCORT_ID,',',ES.ESCORT_NAME)) ESCORTROUTE,'--' AS ALLSTATUS,
        $tariff_select
        ,R.ROSTER_STATUS,R.VENDOR_ID,R.CAB_ID,R.isNonBillable 
        ,RS.START_LAT,RS.START_LONG,RS.END_LAT,RS.END_LONG,RS.EMPLOYEES_ID,RS.EMPLOYEES_NAME $querykm 
        FROM rosters R
        INNER JOIN (SELECT EM.*,GROUP_CONCAT(EM.EMPLOYEE_ID SEPARATOR ',') AS EMPLOYEES_ID,GROUP_CONCAT(EM.NAME separator ',') as EMPLOYEES_NAME,COUNT(*) AS TTLEMPCNT,
        sum(if(EM.ROSTER_PASSENGER_STATUS & 16,1,0)) AS EMPNOSHOWCNT,
        SUM(if((EM.ROSTER_PASSENGER_STATUS & 32 OR EM.ROSTER_PASSENGER_STATUS & 128),1,0)) AS TRAVELEMPCNT 
        FROM (SELECT RR.ROSTER_ID,RR.EMPLOYEE_ID,RR.ROUTE_ORDER,RR.ROSTER_PASSENGER_STATUS,RR.START_LAT,RR.START_LONG,RR.END_LAT,RR.END_LONG,E.NAME        FROM rosters RU
        INNER JOIN roster_passengers RR ON RR.ROSTER_ID = RU.ROSTER_ID 
        LEFT JOIN employees E on E.EMPLOYEES_ID = RR.EMPLOYEE_ID and E.BRANCH_ID = $this->BRANCHID and E.ACTIVE = 1
        WHERE RU.BRANCH_ID=$this->BRANCHID AND RU.ACTIVE = 1 AND RR.ACTIVE = 1 AND (DATE(RR.ESTIMATE_END_TIME) BETWEEN '$from_date' AND '$to_date'
        OR DATE(RR.ESTIMATE_START_TIME) BETWEEN '$from_date' AND '$to_date') ORDER BY RR.ROUTE_ORDER DESC)
        EM GROUP BY EM.ROSTER_ID) RS ON RS.ROSTER_ID = R.ROSTER_ID
        LEFT JOIN driver_billing_summary dbs ON dbs.ROSTER_ID = R.ROSTER_ID AND dbs.ACTIVE = 1
        INNER JOIN vendors VE ON VE.VENDOR_ID = R.VENDOR_ID
        INNER JOIN cab C ON C.CAB_ID = R.CAB_ID
        INNER JOIN vehicles V ON V.VEHICLE_ID = C.VEHICLE_ID
        INNER JOIN vehicle_models VM ON VM.VEHICLE_MODEL_ID = V.VEHICLE_MODEL_ID
        INNER JOIN reason_master RM ON RM.REASON_ID = C.TARIFF_TYPE
        INNER JOIN locations L on L.LOCATION_NAME = IF(R.TRIP_TYPE = 'D', R.END_LOCATION ,R.START_LOCATION)
        INNER JOIN approve_distances AP on AP.LOCATION_ID = L.LOCATION_ID AND AP.BRANCH_ID = $this->BRANCHID  
		$tariff 
        LEFT JOIN (SELECT ROSTER_ID,SUM(TOLL_CHARGE) as ttlcost FROM toll_payment WHERE DATE(PAYMENT_DATE)
        BETWEEN '$from_date' AND '$to_date' GROUP BY ROSTER_ID) TP ON TP.ROSTER_ID = R.ROSTER_ID 
        LEFT JOIN (SELECT ROSTER_ID,SUM(AMOUNT) AS TTLAMOUNT FROM route_penalty 
        WHERE LOGIN_DATE BETWEEN '$from_date' AND '$to_date' GROUP BY ROSTER_ID) PY ON PY.ROSTER_ID = R.ROSTER_ID 
        LEFT JOIN route_escorts RE ON RE.ROSTER_ID = R.ROSTER_ID AND RE.BRANCH_ID = R.BRANCH_ID AND RE.STATUS NOT IN (5,6)
        LEFT JOIN escorts ES ON ES.ESCORT_ID = RE.ESCORT_ID
        WHERE R.BRANCH_ID=$this->BRANCHID AND R.ACTIVE = $this->ACTIVE $vendor AND (DATE(R.ESTIMATE_END_TIME) BETWEEN '$from_date' AND '$to_date' 
        OR DATE(R.ESTIMATE_START_TIME) BETWEEN '$from_date' AND '$to_date') and (R.ROSTER_STATUS &256 or R.ROSTER_STATUS &1024) $triptype ORDER BY R.ROSTER_ID DESC";

        return DB::connection("$dbname")->select($query);
    }*/
	
	public function ZohoMisReport($request,$ApprovalStatus = 0)
    {
        try {
            $from_date       = $request->from_date;
            $to_date         = $request->to_date;
            $trip_type       = $request->trip_type;
            $BRANCH_ID       = Auth::user()->BRANCH_ID;
            $AES_ENCRYPT_KEY = env('AES_ENCRYPT_KEY');
            $taxdetect       = $this->commonFunction->GetPropertyValue('TAX');

            
            $paginatedResults = $this->MisReport($from_date, $to_date, $trip_type);

            
            $paginatedResults->getCollection()->transform(function ($row) use ($AES_ENCRYPT_KEY, $BRANCH_ID, $taxdetect) {
               
                if (!empty($row->EMPLOYEES_NAME)) {
                    $empNames = explode(',', $row->EMPLOYEES_NAME);
                    $decodedArr = [];
                    foreach ($empNames as $name) {
                        $decodedArr[] = $this->commonFunction->AES_DECRYPT($name, $AES_ENCRYPT_KEY);
                    }
                    $row->EMPLOYEES_NAME = implode(',', $decodedArr);
                }

                
                $OPTLOCATION = '--';
                $row->OTPLOCATION = $OPTLOCATION;

                
                $ROSTER_STATUS     = isset($row->ROSTER_STATUS) ? $row->ROSTER_STATUS : 0;
                $RS_STATUS         = $this->commonFunction->TripStatus($ROSTER_STATUS);
                $row->ALLSTATUS    = $RS_STATUS;
                $ROSTER_ID         = isset($row->ROSTER_ID) ? $row->ROSTER_ID : 0;
                $TRIP_TYPE         = isset($row->TRIP_TYPE) ? $row->TRIP_TYPE : '';
                $TOTAL_KM          = isset($row->TOTAL_KM) ? (float)$row->TOTAL_KM : 0;
                $TRAVELEMPCNT      = isset($row->TRAVELEMPCNT) ? (int)$row->TRAVELEMPCNT : 0;
                $CAPACITY          = isset($row->CAPACITY) ? (int)$row->CAPACITY : 0;
                $TRIP_APPROVED_KM  = isset($row->TRIP_APPROVED_KM) ? (float)$row->TRIP_APPROVED_KM : 0;
                $TARIFF            = isset($row->TARIFF) ? (float)$row->TARIFF : 0;
                $TOLLCOST          = isset($row->TOLLCOST) ? (float)$row->TOLLCOST : 0;
                $PENALTYCOST       = isset($row->PENALTYCOST) ? (float)$row->PENALTYCOST : 0;
                $PACKAGE_KMS       = isset($row->PACKAGE_KMS) ? (float)$row->PACKAGE_KMS : 0;
                $EXTRA_KMS_CHARGE  = isset($row->EXTRA_KMS_CHARGE) ? (float)$row->EXTRA_KMS_CHARGE : 0;
                 
                $SWINGKM           = isset($row->SWINGKM) ? (float)$row->SWINGKM : 0;

                
                if ($BRANCH_ID == '27' || $BRANCH_ID == '23') {
                    $TTLKM = $TRIP_APPROVED_KM + $TOTAL_KM + $SWINGKM;
                    if ($TTLKM < $PACKAGE_KMS) {
                        $TTLCOST = $TARIFF;
                    } else {
                        $REMAINKM = $TTLKM - $PACKAGE_KMS;
                        $TTLCOST  = $TARIFF + ($REMAINKM * $EXTRA_KMS_CHARGE);
                    }
                } else if ($BRANCH_ID == '25') {
                    $TTLDIST = round($this->GetRosterDistance($ROSTER_ID, $TRIP_TYPE));
                    $TTLKM   = (2 * $TTLDIST) + (2 * $TRAVELEMPCNT);
                    $TTLCOST = $TARIFF * $TTLKM;
                } else if ($BRANCH_ID == '48') {
                    $TTLKM   = $TRIP_APPROVED_KM;
                    $TTLCOST = $TARIFF;
                } else if ($BRANCH_ID == '57') {
                    if ($TRAVELEMPCNT < $CAPACITY) {
                        if ($TRAVELEMPCNT > 0 && $TRAVELEMPCNT <= 4) {
                            $TTLCOST = 820;
                        } elseif ($TRAVELEMPCNT > 4 && $TRAVELEMPCNT < 8) {
                            $TTLCOST = 1055;
                        }
                    } else {
                        $TTLCOST = $TARIFF;
                    }
                    $TTLKM = $TRIP_APPROVED_KM + $TOTAL_KM + $SWINGKM;
                } else {
                    $TTLKM   = $TRIP_APPROVED_KM + $SWINGKM;
                    $TTLCOST = $TARIFF * $TTLKM;
                }

                $FINALCOST = $TTLCOST + $TOLLCOST - $PENALTYCOST;
                if ($RS_STATUS == 'Tripsheet Cancelled') {
                    $FINALCOST = 0;
                }
                if ($FINALCOST != 0) {
                    $TtlTax = ($FINALCOST * $taxdetect) / 100;
                } else {
                    $TtlTax = 0;
                }

                
                if ($BRANCH_ID == '48') {
                    $emp_arr_val = [
                        "OTPLOCATION" => $OPTLOCATION,
                        "SWINGKM"     => $SWINGKM,
                        "TAX"         => $TtlTax,
                        "TOTALAMT"    => $FINALCOST,
                        "ALLSTATUS"   => $RS_STATUS,
                        "TTLKMS"      => $TTLKM
                    ];
                } else {
                    $emp_arr_val = [
                        "OTPLOCATION" => $OPTLOCATION,
                        "SWINGKM"     => $SWINGKM,
                        "TOTALAMT"    => $FINALCOST,
                        "ALLSTATUS"   => $RS_STATUS,
                        "TTLKMS"      => $TTLKM
                    ];
                }

                
                foreach ($emp_arr_val as $key => $value) {
                    $row->{$key} = $value;
                }

                return $row;
            });
			
			 if ($ApprovalStatus == 1) {
              return $paginatedResults;
             }

            return response([
                'success' => true,
                'status'  => 1,
                'result'  => $paginatedResults,
                'message' => 'Zoho MIS Report has been successfully fetched.',
            ], 200);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success'               => false,
                'message'               => 'Zoho MIS Report Data Fetch Unsuccessful',
                'validation_controller' => true,
                'error'                 => $e->getMessage(),
            ], 500);
        }
    }
	
	public function MisReport($from_date, $to_date, $trip_type)
    {
        
        $vendor_id   = auth::user()->vendor_id;
        $DIVISION_ID = auth::user()->DIVISION_ID; // Included per original logic
        $dbname      = Auth::user()->dbname;
        $branch_id   = Auth::user()->BRANCH_ID;

        
        if ($vendor_id == $this->VENDORID) {
            $vendorCondition = "";
        } else {
            $vendorCondition = "AND R.VENDOR_ID = $vendor_id";
        }

         
        if ($trip_type == $this->ALLREPORT) {
            $triptypeCondition = "AND R.TRIP_TYPE IN ('P','D')";
        } else {
            $triptypeCondition = "AND R.TRIP_TYPE = '$trip_type'";
        }

         
        $SWINGSTART_TIME = $this->commonFunction->GetPropertyValue('SWING START TIME');
        $SWINGEND_TIME   = $this->commonFunction->GetPropertyValue('SWING END TIME');
        $taxdetect       = $this->commonFunction->GetPropertyValue('TAX');

       

        
        $subRS = DB::table(DB::raw('(
        SELECT
            RR.ROSTER_ID,
            RR.EMPLOYEE_ID,
            RR.ROUTE_ORDER,
            RR.ROSTER_PASSENGER_STATUS,
            RR.START_LAT,
            RR.START_LONG,
            RR.END_LAT,
            RR.END_LONG,
            DATE(RR.ESTIMATE_END_TIME) AS end_date,
            DATE(RR.ESTIMATE_START_TIME) AS start_date
         FROM rosters RU
         INNER JOIN roster_passengers RR ON RR.ROSTER_ID = RU.ROSTER_ID
         WHERE RU.BRANCH_ID = ' . $branch_id . '
           AND RU.ACTIVE = 1
           AND RR.ACTIVE = 1
           AND (
               DATE(RR.ESTIMATE_END_TIME) BETWEEN "' . $from_date . '" AND "' . $to_date . '"
               OR DATE(RR.ESTIMATE_START_TIME) BETWEEN "' . $from_date . '" AND "' . $to_date . '"
           )
    ) AS EM'))
            ->selectRaw('
              EM.ROSTER_ID,
              COUNT(*) AS TTLEMPCNT,
              SUM(IF(EM.ROSTER_PASSENGER_STATUS & 16, 1, 0)) AS EMPNOSHOWCNT,
              SUM(IF((EM.ROSTER_PASSENGER_STATUS & 32 OR EM.ROSTER_PASSENGER_STATUS & 128), 1, 0)) AS TRAVELEMPCNT,
              GROUP_CONCAT(EM.EMPLOYEE_ID) AS EMPLOYEES_ID,
              GROUP_CONCAT(EM.EMPLOYEE_ID) AS EMPLOYEES_NAME,
              EM.START_LAT,
              EM.START_LONG,
              EM.END_LAT,
              EM.END_LONG
         ')
            ->groupBy('EM.ROSTER_ID');

        
        $subTP = DB::table('toll_payment')
            ->selectRaw('ROSTER_ID, SUM(TOLL_CHARGE) AS ttlcost')
            ->whereBetween(DB::raw('DATE(PAYMENT_DATE)'), [$from_date, $to_date])
            ->groupBy('ROSTER_ID');

       
        $subPY = DB::table('route_penalty')
            ->selectRaw('ROSTER_ID, SUM(AMOUNT) AS TTLAMOUNT')
            ->whereBetween('LOGIN_DATE', [$from_date, $to_date])
            ->groupBy('ROSTER_ID');

        
        if ($branch_id != 25) {
            if (in_array($branch_id, [48, 61])) {
                $tariffJoin = function ($join) use ($branch_id) {
                    $join->on('T.VENDOR_ID', '=', 'R.VENDOR_ID')
                        ->on('T.VEHICLE_MODEL_ID', '=', 'V.VEHICLE_MODEL_ID')
                        ->where('T.BRANCH_ID', '=', $branch_id)
                        ->whereRaw('T.PACKAGE_START_KM <= R.TRIP_APPROVED_KM')
                        ->whereRaw('T.PACKAGE_END_KM >= R.TRIP_APPROVED_KM');
                };
                $tariffSelect = "T.PACKAGE_PRICE AS TARIFF, 0 AS PACKAGE_KMS, 0 AS EXTRA_KMS_CHARGE";
            } else {
                $tariffJoin = function ($join) {
                    $join->on('TF.TARIFF_TYPE', '=', 'C.TARIFF_TYPE')
                        ->on('TF.BRANCH_ID', '=', 'C.BRANCH_ID')
                        ->on('TF.VENDOR_ID', '=', 'C.VENDOR_ID')
                        ->on('TF.VEHICLE_MODEL_ID', '=', 'V.VEHICLE_MODEL_ID');
                };
                $tariffSelect = "TF.PACKAGE_PRICE AS TARIFF, TF.PACKAGE_KMS, TF.EXTRA_KMS_CHARGE";
            }
        } else {
            $tariffJoin = function ($join) {
                $join->on('TF.TARIFF_TYPE', '=', 'C.TARIFF_TYPE')
                    ->on('TF.BRANCH_ID', '=', 'C.BRANCH_ID')
                    ->on('TF.VENDOR_ID', '=', 'C.VENDOR_ID')
                    ->on('TF.VEHICLE_MODEL_ID', '=', 'V.VEHICLE_MODEL_ID');
            };
            $tariffSelect = "TF.PACKAGE_PRICE AS TARIFF, TF.PACKAGE_KMS, TF.EXTRA_KMS_CHARGE";
        }

        
        $query = Roster::query()
            ->from('rosters AS R')
            ->selectRaw("
              R.ROSTER_ID,
              R.ROUTE_ID,
              RS.TTLEMPCNT,
              RS.TRAVELEMPCNT,
              RS.EMPNOSHOWCNT,
              R.TRIP_TYPE,
              V.VEHICLE_REG_NO,
              VM.MODEL,
              VM.CAPACITY,
              VE.NAME AS VENDORNAME,
              R.START_LOCATION,
              R.END_LOCATION,
              '--' AS OTPLOCATION,
              IF(R.TRIP_TYPE = 'P', DATE(R.ESTIMATE_END_TIME), DATE(R.ESTIMATE_START_TIME)) AS INOUT_DATE,
              IF(R.TRIP_TYPE = 'P', TIME(R.ESTIMATE_END_TIME), TIME(R.ESTIMATE_START_TIME)) AS INOUT_TIME,
              RM.REASON,
              dbs.GOOGLE_KM,
              dbs.TRIP_STATUS,
              R.TOTAL_KM,
              R.TRIP_APPROVED_KM,
              IF(TP.ttlcost IS NULL, 0, TP.ttlcost) AS TOLLCOST,
              IF(PY.TTLAMOUNT IS NULL, 0, PY.TTLAMOUNT) AS PENALTYCOST,
              $tariffSelect,
              R.ROSTER_STATUS,
              RS.START_LAT,
              RS.START_LONG,
              RS.END_LAT,
              RS.END_LONG,
              RS.EMPLOYEES_ID,
              RS.EMPLOYEES_NAME,
			  AP.APPROVED_DISTANCE
         ")
            ->joinSub($subRS, 'RS', function ($join) {
                $join->on('RS.ROSTER_ID', '=', 'R.ROSTER_ID');
            })
            ->leftJoin('driver_billing_summary AS dbs', 'dbs.ROSTER_ID', '=', 'R.ROSTER_ID')
            ->join('vendors AS VE', 'VE.VENDOR_ID', '=', 'R.VENDOR_ID')
            ->join('cab AS C', 'C.CAB_ID', '=', 'R.CAB_ID')
            ->join('vehicles AS V', 'V.VEHICLE_ID', '=', 'C.VEHICLE_ID')
            ->join('vehicle_models AS VM', 'VM.VEHICLE_MODEL_ID', '=', 'V.VEHICLE_MODEL_ID')
            ->join('reason_master AS RM', 'RM.REASON_ID', '=', 'C.TARIFF_TYPE')
            
            ->join('locations AS L', DB::raw("L.LOCATION_NAME"), DB::raw("IF(R.TRIP_TYPE = 'D', R.END_LOCATION, R.START_LOCATION)"))
            ->join('approve_distances AS AP', function ($join) use ($branch_id) {
                $join->on('AP.LOCATION_ID', '=', 'L.LOCATION_ID')
                    ->where('AP.BRANCH_ID', '=', $branch_id);
            })
            
            ->when($branch_id != 25, function ($q) use ($tariffJoin, $branch_id) {
                if (in_array($branch_id, [48, 61])) {
                    $q->leftJoin('tariff_km_slap AS T', $tariffJoin);
                } else {
                    $q->leftJoin('tariff AS TF', $tariffJoin);
                }
            }, function ($q) use ($tariffJoin) {
                $q->leftJoin('tariff AS TF', $tariffJoin);
            })
            ->leftJoinSub($subTP, 'TP', function ($join) {
                $join->on('TP.ROSTER_ID', '=', 'R.ROSTER_ID');
            })
            ->leftJoinSub($subPY, 'PY', function ($join) {
                $join->on('PY.ROSTER_ID', '=', 'R.ROSTER_ID');
            })
            ->leftJoin('route_escorts AS RE', function ($join) {
                $join->on('RE.ROSTER_ID', '=', 'R.ROSTER_ID')
                    ->on('RE.BRANCH_ID', '=', 'R.BRANCH_ID')
                    ->whereNotIn('RE.STATUS', [5, 6]);
            })
            ->leftJoin('escorts AS ES', 'ES.ESCORT_ID', '=', 'RE.ESCORT_ID')
            ->where('R.BRANCH_ID', $branch_id)
            ->where('R.ACTIVE', 1)
            ->where(function ($q) use ($from_date, $to_date) {
                $q->whereBetween(DB::raw('DATE(R.ESTIMATE_END_TIME)'), [$from_date, $to_date])
                    ->orWhereBetween(DB::raw('DATE(R.ESTIMATE_START_TIME)'), [$from_date, $to_date]);
            })
            ->whereRaw('(R.ROSTER_STATUS & 256) OR (R.ROSTER_STATUS & 1024)')
            ->whereRaw("1=1 $triptypeCondition $vendorCondition");

        
        $filterModel = request()->input('filterModel');
        if ($filterModel) {
            foreach ($filterModel as $field => $filter) {
                if (isset($filter['filter']) && $filter['filter'] !== '') {
                    $value = $filter['filter'];
                    switch ($field) {
                        case 'ROSTER_ID':
                            $query->where('R.ROSTER_ID', 'like', "%{$value}%");
                            break;
                        case 'TRIP_TYPE':
                            $query->where('R.TRIP_TYPE', 'like', "%{$value}%");
                            break;
                        case 'VENDORNAME':
                            $query->where('VE.NAME', 'like', "%{$value}%");
                            break;
                        case 'VEHICLE_REG_NO':
                            $query->where('V.VEHICLE_REG_NO', 'like', "%{$value}%");
                            break;
                        case 'MODEL':
                            $query->where('VM.MODEL', 'like', "%{$value}%");
                            break;
                    }
                }
            }
        }

        
        if (request()->has('orderBy') && !empty(request()->input('orderBy'))) {
            $orderBy = request()->input('orderBy');
            $order = request()->input('order', 'asc');
            $query->orderBy($orderBy, $order);
        } else {
            $query->orderBy('R.ROSTER_ID', 'asc');
        }

        
        $perPage = request()->input('per_page', MyHelper::$PAGINATION_PER_PAGE);
        if ($perPage === MyHelper::$PAGINATION_PER_PAGE_ALL) {
            $paginatedResults = $query->paginate($query->count());
        } else {
            $perPage = is_numeric($perPage) ? (int)$perPage : MyHelper::$PAGINATION_PER_PAGE;
            $paginatedResults = $query->paginate($perPage);
        }

        return $paginatedResults;
    }

    public function GetRosterDistance($rosterid, $triptype)
    {
        $result = $this->GetRosterDistanceQuery($rosterid);
        $json_arr = json_encode($result);
        $results = json_decode($json_arr, true);
        $count = count($results);
        
        if ($triptype == 'D') {
            $this->aasort($results, "ROUTE_ORDER");
        }

        $ttldist = 0;
        $cnt = 1;
        $oldfrom = '';
        $oldto = '';
        $ttldist = 0;


        if ($triptype == 'P') {
            $datacnt = $count + 1;
        } else {
            $datacnt = $count;
        }
        for ($i = 0; $i < $datacnt; $i++) {
            if ($triptype == 'D') {
                if ($cnt == 1) {
                    $from = $results[$i]['SITELOC'];
                    $to = $results[$i]['LOCNAME'];
                } else {
                    $to = $results[$i]['LOCNAME'];
                }
            } else {

                if ($cnt == $datacnt) {
                    $to = $results[$i - 1]['SITELOC'];
                } else if ($cnt == 1) {
                    $from = $results[$i]['LOCNAME'];
                    $to = $results[$i]['LOCNAME'];
                } else {
                    $to = $results[$i]['LOCNAME'];
                }
            }
            $distance = $this->GetMasterDistance($from, $to);
            $ttldist += $distance;
            $from = $to;
            $cnt++;
        }
        
        return $ttldist;
    }

    function aasort(&$array, $key)
    {
        $sorter = array();
        $ret = array();
        reset($array);
        foreach ($array as $ii => $va) {
            $sorter[$ii] = $va[$key];
        }
        asort($sorter);
        foreach ($sorter as $ii => $va) {
            $ret[$ii] = $array[$ii];
        }
        $array = $ret;
    }

    public function GetRosterDistanceQuery($rosterid){
        $sql = "SELECT R.ROSTER_ID,RS.EMPLOYEE_ID,RS.LOCATION_ID,REPLACE(if(R.TRIP_TYPE = 'P',R.END_LOCATION,R.START_LOCATION),' ','') as SITELOC
         ,REPLACE(L.LOCATION_NAME,' ','') as LOCNAME,RS.ROUTE_ORDER FROM rosters R
         INNER JOIN roster_passengers RS ON RS.ROSTER_ID = R.ROSTER_ID AND RS.ACTIVE = 1
         INNER JOIN locations L ON L.DIVISION_ID = 23 AND L.LOCATION_ID = RS.LOCATION_ID  AND L.ACTIVE = 1
         WHERE R.BRANCH_ID = $this->BRANCHID AND R.ACTIVE = 1 AND R.ROSTER_ID = $rosterid ORDER BY RS.ROUTE_ORDER DESC";
         return DB::select($sql);
    }

    public function GetMasterDistance($from,$to){
		// echo '<br/><br/>';echo 
		$sql = "SELECT GOOGLE_DISTANCE FROM auto_route_distance WHERE FROM_LOCATION_ID ='$from' AND TO_LOCATION_ID = '$to'";
        $data = DB::select($sql);
        if(count($data) != 0){
            return $data[0]->GOOGLE_DISTANCE;
        }else{
            return 0;
        }
    }

    /*public function ZohoMisApprovel($request){
        try{
            $result = $this->ZohoMisReport($request);
	    
	    $datas = $result->original;

	    $final_data = array();

            foreach($datas['result'] as $data){
                if($data['TRAVELEMPCNT'] == 0){
                    $data['ROW_COLOR'] = "0404B4";
                }else{
                    $data['ROW_COLOR'] = "#21610B";
                }

		if ($data['isNonBillable'] == 0 && $data['TRAVELEMPCNT'] > 0 && $data['isDeviceKm'] == 0) {
                    $data['ACTION'] = "checkbox";
                    $data['COLOR'] = "NULL";
                } else if ($data['isNonBillable'] == 0 && $data['TRAVELEMPCNT'] == 0) {
                    $data['ACTION'] = "Pen";
                    $data['COLOR'] = "#21610b";
                } else if ($data['isNonBillable'] == 0 && $data['TRAVELEMPCNT'] > 0 && $data['isDeviceKm'] == 1) {
                    $data['ACTION'] = "Pen";
                    $data['COLOR'] = "#21610b";
                } else {
                    $data['ACTION'] = $data['APPROVED_KM_TYPE'];
                    $data['COLOR'] = "NULL";
                }               

                array_push($final_data, $data);
            }

            return response([
                'success' => true,
                'status' => 1,
                'result' => $final_data,
                'message' => 'Zoho Approvel MIS Report has been successfully Fetched.',
            ],200);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'message' => 'Zoho Approvel MIS Report Data Fetch Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }*/
	
	public function ZohoMisApprovel($request)
{
    try {
        
        $paginatedResults = $this->ZohoMisReport($request, 1);

       
        $paginatedResults->getCollection()->transform(function ($row) {
             
            if ($row->TRAVELEMPCNT == 0) {
                $row->ROW_COLOR = "0404B4";
            } else {
                $row->ROW_COLOR = "#21610B";
            }

            if ($row->isNonBillable == 0 && $row->TRAVELEMPCNT > 0 && $row->isDeviceKm == 0) {
                $row->ACTION = "checkbox";
                $row->COLOR  = "NULL";
            } else if ($row->isNonBillable == 0 && $row->TRAVELEMPCNT == 0) {
                $row->ACTION = "Pen";
                $row->COLOR  = "#21610b";
            } else if ($row->isNonBillable == 0 && $row->TRAVELEMPCNT > 0 && $row->isDeviceKm == 1) {
                $row->ACTION = "Pen";
                $row->COLOR  = "#21610b";
            } else {
                $row->ACTION = $row->APPROVED_KM_TYPE;
                $row->COLOR  = "NULL";
            }

            
            return $row;
        });

        
        return response([
            'success' => true,
            'status'  => 1,
            'result'  => $paginatedResults,
            'message' => 'Zoho Approvel MIS Report has been successfully Fetched.',
        ], 200);

    } catch (\Throwable|\Exception $e) {
        $this->commonFunction->logException($e);
        return response([
            'success'               => false,
            'message'               => 'Zoho Approvel MIS Report Data Fetch Unsuccessful',
            'validation_controller' => true,
            'error'                 => $e->getMessage(),
        ], 500);
    }
}


    public function billkm_approve_update(Request $request) {
        try {
            $USERID = Auth::user()->id;
            $branch_id = Auth::user()->BRANCH_ID;
            $roster_id = $request->roster_id;
            $billkm_type = $request->billkm_type;
            $billkm_type_rem = $request->billkm_type_rem;
            $isNonBillable = 0;

            if($billkm_type == "NonKm"){
                $isNonBillable = 2;
            }else{
                $isNonBillable = 1;
            }

            $update = "update driver_billing_summary set APPROVED_KM_TYPE='$billkm_type' , APPROVED_KM_REMARKS='$billkm_type_rem' ,UPDATED_BY='" . $USERID . "',updated_at='" . date("Y-m-d H:i:s") . "' where ROSTER_ID = '$roster_id' ";
            DB::update($update);

            $update1 = "update rosters set isNonBillable= '$isNonBillable' ,UPDATED_BY='" . $USERID . "' where ROSTER_ID = '$roster_id' and BRANCH_ID ='$branch_id' ";
            $res1 = DB::update($update1);

            return response([
                'success' => true,
                'status' => 1,
                'message' => 'Roster updated successfully',
            ],200);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'message' => 'Roster updated Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /*public function tollpayments($request) {
        try {
            $from_date = $request->trip_datepicker;
            $trip_type = $request->request_trip_type ? $request->request_trip_type : 'ALL';
            
            // Get toll payment details using the provided parameters
            $results = $this->tollPaymentDetails($from_date, $from_date, $trip_type);

            return response([
                'success' => true,
                'status' => 1,
                'result' => $results,
                'message' => 'Toll Payments has been successfully Fetched.',
            ], 200);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'message' => 'Toll Payments Data Fetch Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }*/
	
	 public function tollpayments(Request $request)
    {
        try {

            $perPage   = $request->input('per_page', MyHelper::$PAGINATION_PER_PAGE);
            $fromDate  = $request->input('trip_datepicker');
            $toDate    = $request->input('trip_datepicker');
            $tripType  = $request->input('request_trip_type', $this->ALLREPORT);
            $branchId  = Auth::user()->BRANCH_ID;
            $vendorId  = Auth::user()->vendor_id;


            $q = TollPayment::query()
                ->select([
                    'toll_payment.PAYMENT_ID',
                    'toll_payment.TOLL_CHARGE as TOLLAMOUNT',
                    'toll_payment.remarks',
                    'rosters.ROSTER_ID',
                    'rosters.ROUTE_ID',
                    'rosters.TRIP_TYPE',
                    'vendors.NAME as VENDORNAME',
                    'vehicles.VEHICLE_REG_NO',
                    'vehicle_models.MODEL',
                    'vehicle_models.CAPACITY',
                    'rosters.START_LOCATION',
                    'rosters.END_LOCATION',
                    DB::raw("IF(rosters.TRIP_TYPE = 'P', DATE(rosters.ESTIMATE_END_TIME), DATE(rosters.ESTIMATE_START_TIME)) as INOUT_DATE"),
                    DB::raw("IF(rosters.TRIP_TYPE = 'P', TIME(rosters.ESTIMATE_END_TIME), TIME(rosters.ESTIMATE_START_TIME)) as INOUT_TIME"),
                ])
                ->join('rosters',          'rosters.ROSTER_ID',       '=', 'toll_payment.ROSTER_ID')
                ->join('branch',           'branch.BRANCH_ID',        '=', 'rosters.BRANCH_ID')
                ->join('vendors',          'vendors.VENDOR_ID',       '=', 'rosters.VENDOR_ID')
                ->join('cab',              'cab.CAB_ID',              '=', 'rosters.CAB_ID')
                ->join('vehicles',         'vehicles.VEHICLE_ID',     '=', 'cab.VEHICLE_ID')
                ->join('vehicle_models',   'vehicle_models.VEHICLE_MODEL_ID', '=', 'vehicles.VEHICLE_MODEL_ID')
                ->where('rosters.BRANCH_ID', $branchId)
                ->where('rosters.ACTIVE',    $this->ACTIVE)
                ->where(function($q2) use ($fromDate, $toDate) {
                    $q2->whereBetween(DB::raw('DATE(rosters.ESTIMATE_END_TIME)'),   [$fromDate, $toDate])
                        ->orWhereBetween(DB::raw('DATE(rosters.ESTIMATE_START_TIME)'), [$fromDate, $toDate]);
                });


            if ($vendorId != $this->VENDORID) {
                $q->where('rosters.VENDOR_ID', $vendorId);
            }


            if ($tripType === $this->ALLREPORT) {
                $types = explode(',', $this->TTLTYPE);
                $q->whereIn('rosters.TRIP_TYPE', $types);
            } else {
                $q->where('rosters.TRIP_TYPE', $tripType);
            }


            if ($filterModel = $request->input('filterModel')) {
                foreach ($filterModel as $field => $filter) {
                    if (!empty($filter['filter'])) {
                        $val = $filter['filter'];
                        switch ($field) {
                            case 'ROSTER_ID':
                                $q->where('rosters.ROSTER_ID', 'like', "%{$val}%"); break;
                            case 'TRIP_TYPE':
                                $q->where('rosters.TRIP_TYPE', 'like', "%{$val}%"); break;
                            case 'VENDORNAME':
                                $q->where('vendors.NAME', 'like', "%{$val}%"); break;
                            case 'VEHICLE_REG_NO':
                                $q->where('vehicles.VEHICLE_REG_NO', 'like', "%{$val}%"); break;
                            case 'MODEL':
                                $q->where('vehicle_models.MODEL', 'like', "%{$val}%"); break;
                            case 'CAPACITY':
                                $q->where('vehicle_models.CAPACITY', 'like', "%{$val}%"); break;
                            case 'START_LOCATION':
                                $q->where('rosters.START_LOCATION', 'like', "%{$val}%"); break;
                            case 'END_LOCATION':
                                $q->where('rosters.END_LOCATION', 'like', "%{$val}%"); break;
                            case 'TOLLAMOUNT':
                                $q->where('toll_payment.TOLL_CHARGE', 'like', "%{$val}%"); break;
                            case 'remarks':
                                $q->where('toll_payment.remarks', 'like', "%{$val}%"); break;
                        }
                    }
                }
            }


            if ($request->filled('orderBy')) {
                $q->orderBy($request->input('orderBy'), $request->input('order', 'asc'));
            } else {
                $q->orderBy('rosters.ROSTER_ID', 'desc');
            }


            if ($perPage === MyHelper::$PAGINATION_PER_PAGE_ALL) {
                $paginated = $q->paginate($q->count());
            } else {
                $perPage = is_numeric($perPage) ? (int)$perPage : MyHelper::$PAGINATION_PER_PAGE;
                $paginated = $q->paginate($perPage);
            }

            return response([
                'success' => true,
                'status'  => 1,
                'result'  => $paginated,
            ], 200);

        } catch (\Throwable $e) {
            $this->commonFunction->logException($e);
            return response([
                'success'               => false,
                'message'               => 'Toll Payments Data Fetch Unsuccessful',
                'validation_controller' => true,
                'error'                 => $e->getMessage(),
            ], 500);
        }
    }

    public function tollPaymentDetails($from_date,$to_date,$trip_type)
    {
        $result = $this->tollPaymentDetail($from_date,$to_date,$trip_type);
        $json_arr = json_encode($result);
        $normal_array = json_decode($json_arr, true);
        $results = array();
        for ($i = 0; $i < count($normal_array); $i++) {
            $results[$i] = $normal_array[$i];
        }

        return $results;
    }

    public function tollPaymentDetail($from_date,$to_date,$trip_type)
    {        
        $vendor_id = auth::user()->vendor_id;
        $dbname = Auth::user()->dbname;
        if($vendor_id == $this->VENDORID){
            $vendor = "";
        }
        else{
            $vendor = "AND R.VENDOR_ID=$vendor_id";
        }
        if($trip_type == $this->ALLREPORT)
        {
            
            $RPTTLTYPE = explode(',', $this->TTLTYPE);
            $triptype = "AND R.TRIP_TYPE IN ('$RPTTLTYPE[0]','$RPTTLTYPE[1]')";
        }
        else
        {
            $triptype = "AND R.TRIP_TYPE ='$trip_type'";
        }
        $query = "SELECT R.ROSTER_ID,R.ROUTE_ID,R.TRIP_TYPE,
        VE.`NAME` VENDORNAME,V.VEHICLE_REG_NO,VM.MODEL,VM.CAPACITY,R.START_LOCATION,R.END_LOCATION,TP.remarks,
        if(R.TRIP_TYPE = 'P',DATE(R.ESTIMATE_END_TIME),DATE(R.ESTIMATE_START_TIME)) as INOUT_DATE,
        if(R.TRIP_TYPE = 'P',TIME(R.ESTIMATE_END_TIME),TIME(R.ESTIMATE_START_TIME)) AS INOUT_TIME,TP.TOLL_CHARGE as TOLLAMOUNT,TP.PAYMENT_ID
        FROM toll_payment TP
        INNER join rosters R on R.ROSTER_ID = TP.ROSTER_ID
        INNER JOIN branch B ON B.BRANCH_ID = R.BRANCH_ID
        INNER JOIN vendors VE ON VE.VENDOR_ID = R.VENDOR_ID
        INNER JOIN cab C ON C.CAB_ID = R.CAB_ID
        INNER JOIN vehicles V ON V.VEHICLE_ID = C.VEHICLE_ID
        INNER JOIN vehicle_models VM ON VM.VEHICLE_MODEL_ID = V.VEHICLE_MODEL_ID
        WHERE R.BRANCH_ID=$this->BRANCHID  AND R.ACTIVE = $this->ACTIVE AND (DATE(R.ESTIMATE_END_TIME) BETWEEN '$from_date' AND '$to_date' 
        OR DATE(R.ESTIMATE_START_TIME) BETWEEN '$from_date' AND '$to_date') $vendor $triptype
        GROUP BY TP.ROSTER_ID ORDER BY R.ROSTER_ID DESC "; 
        
        return  DB::connection("$dbname")->select($query);
    }

    public function edit_toll_amount(Request $request)
    {
        try {
            $payment_id = $request->payment_id;
            $remarks = $request->remarks;
            $toll_amount = $request->toll_amount;
            if ($toll_amount == '' || $toll_amount == null) {
                return ['error' => true, 'message' => 'Please add valid Amount'];
            } else if ($remarks == '' || $remarks == null) {
                return ['error' => true, 'message' => 'Remarks should not be empty'];
            } else if ($payment_id == '' || $payment_id == null) {
                return ['error' => true, 'message' => 'Invalid request'];
            } else {
                $curDateTime = date('Y-m-d H:i:s');
                $update_status_sql = "UPDATE toll_payment set TOLL_CHARGE = '$toll_amount', updated_at = '$curDateTime', remarks = '$remarks' where PAYMENT_ID = '$payment_id'";
                $update_status_result = DB::update($update_status_sql);

            }

            return response([
                'success' => true,
                'status' => 1,
                'message' => 'Toll Amount updated successfully',
            ], 200);
        } catch (\Throwable | \Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'message' => 'Toll Amount updated Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    private function generateYears(): array
    {
        $years = [];
        $currentYear = (int)date("Y");
        for($i = 0; $i < 10; $i++) {
            $year = $currentYear - $i;
            $years[] = [
                'value' => $year,
                'label' => (string)$year
            ];
        }
        return $years;
    }
 
    private function generateMonths(): array
    {
        $months = [];
        for ($i = 1; $i <= 12; $i++) {
            $months[] = [
                'value' => $i,
                'label' => date('F', mktime(0, 0, 0, $i, 1))
            ];
        }
        return $months;
    }

    private function getTariffType(): array
    {
        return [
            [
                'value' => 'all',
                'label' => 'All'
            ],
            [
                'value' => 'daytariff',
                'label' => 'US Tariff'
            ],
            [
                'value' => 'nighttariff',
                'label' => 'Night Tariff'
            ],
            [
                'value' => 'mondaytariff',
                'label' => 'Monday Tariff'
            ]
        ];
    }

    private function getExportType(): array
    {
        return [
            [
                'value' => 'payment',
                'label' => 'Payment'
            ],
            [
                'value' => 'summary',
                'label' => 'Summary'
            ],
        ];
    }

    public function drivermisreportgenerate()
    {
        try {
            $curdate = date('Y-m-d');
            $from_date = $curdate;
            $to_date = $curdate;
            //$from_date='2023-03-04';
            //$to_date='2023-03-30';
            $allvehicle = $this->GetCabVehicle();
            $misReport = $this->getMisReeportList();
 
            return response([
                'success' => true,
                'status' => 1,
                'result' => [
                    'drop_down_values' => [
                        'vehicles' => $allvehicle,
                        'year' => $this->generateYears(),
                        'month' => $this->generateMonths(),
			'export_type' => $this->getExportType(),
                        'tariff_type' => $this->getTariffType(),
                    ],
                    'misReport' => $misReport,
                ],                
		'message' => 'Driver MIS Report has been successfully Fetched.',
            ], 200);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'message' => 'Driver MIS Report Data Fetch Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function GetCabVehicle() {
 
        $user_type = Auth::user()->user_type;
        $vendor_id = Auth::user()->vendor_id;
        $dbname = Auth::user()->dbname;
        if ($user_type == 'ADMIN') {
            $vendor = "";
        } else {
            $vendor = "AND C.VENDOR_ID=" . $vendor_id . " ";
        }
 
        $sql = "SELECT C.CAB_ID,V.VEHICLE_REG_NO FROM cab C
            INNER JOIN vehicles V ON V.VEHICLE_ID = C.VEHICLE_ID
            WHERE C.BRANCH_ID = $this->BRANCHID  AND C.ACTIVE=1 $vendor";
        return DB::connection("$dbname")->select($sql);
    }

    public function getMisReeportList()
    {
        $dbname = Auth::user()->dbname;
        $BRANCH_ID = Auth::user()->BRANCH_ID;
        $summery_sql = "SELECT REPORT_ID,`MONTH`,`YEAR`,sum(TOTAL_KMS) as 'TOTAL_KMS',sum(TOTAL_AMOUNT) as 'TOTAL_AMOUNT',SEC_TO_TIME(SUM(TIME_TO_SEC(TOTAL_HOURS))) as 'TOTAL_HOURS', SUM(TOLL_AMT) as 'TOTAL_TOLL_AMT'
        FROM misreport WHERE Active = 1 AND BRANCH_ID = '$BRANCH_ID' group by YEAR,MONTH";
        $summery_result = DB::connection("$dbname")->select($summery_sql);
        $details_sql = "SELECT BR.BRANCH_NAME,rm.REASON as 'TARIFF_ID',mis.REPORT_ID,v.VEHICLE_REG_NO as 'VEHICLE_ID',vm.MODEL as 'VEHICLE_MODEL_ID',
        mis.id,mis.REPORT_ID,mis.DATE,mis.TOTAL_HOURS,mis.PACKAGE_PRICE,mis.TOTAL_KMS,mis.PACKAGE_HRS,mis.PACKAGE_KMS,mis.EXTRA_KMS_CHARGE,mis.EXTRA_HRS_CHARGE,mis.MINIMUM_MINUTES,mis.MONTH,mis.YEAR,mis.ROSTER_IDS,mis.TOTAL_AMOUNT,mis.EXTRA_HOURS,mis.EXTRA_MINUTES,mis.EXTRA_HOURS_AMOUNT,mis.EXTRA_MINUTES_AMOUNT,mis.EXTRA_KM,mis.EXTRA_KM_AMOUNT,mis.TOLL_AMT
        FROM misreport mis
        left join reason_master rm on mis.TARIFF_ID = rm.REASON_ID
        left join vehicles v on mis.VEHICLE_ID = v.VEHICLE_ID
        left join vehicle_models vm on mis.VEHICLE_MODEL_ID = vm.VEHICLE_MODEL_ID
        left join branch BR on BR.BRANCH_ID = '$BRANCH_ID'
        WHERE mis.Active = 1 and mis.BRANCH_ID = '$BRANCH_ID';";
        $details_result = DB::connection("$dbname")->select($details_sql);
        $reportArr = [];
        $summeryArr = [];
        if(count($summery_result) > 0)
        {
            foreach ($summery_result as $key => $value) {
                $unique = $value->YEAR . "-" . $value->MONTH;
                 $summeryArr[$unique]  = $value;
            }
        }
        if(count($details_result) > 0)
        {
           
            foreach($details_result as $val)
            {
                $REPORT_ID = $val->REPORT_ID;
                $uniqueval = $val->YEAR . "-" . $val->MONTH;
                $reportArr[$uniqueval]['data'][] = $val;
                $reportArr[$uniqueval]['REPORT_ID'] = $REPORT_ID;
                $reportArr[$uniqueval]['MONTH'] = isset($summeryArr[$uniqueval]) ? $this->getMonth($summeryArr[$uniqueval]->MONTH) : '';
                $reportArr[$uniqueval]['YEAR'] = isset($summeryArr[$uniqueval]) ? $summeryArr[$uniqueval]->YEAR : '';
                $reportArr[$uniqueval]['TOTAL_KMS'] = isset($summeryArr[$uniqueval]) ? $summeryArr[$uniqueval]->TOTAL_KMS : 0;
                $reportArr[$uniqueval]['TOTAL_AMOUNT'] = isset($summeryArr[$uniqueval]) ? $summeryArr[$uniqueval]->TOTAL_AMOUNT : 0;
                $reportArr[$uniqueval]['TOTAL_HOURS'] = isset($summeryArr[$uniqueval]) ? $summeryArr[$uniqueval]->TOTAL_HOURS : 0;
                $reportArr[$uniqueval]['TOTAL_TOLL_AMT'] = isset($summeryArr[$uniqueval]) ? $summeryArr[$uniqueval]->TOTAL_TOLL_AMT : 0;
                $reportArr[$uniqueval]['TOTAL_PACKAGE_AMT'] = isset($summeryArr[$uniqueval]) ? $summeryArr[$uniqueval]->TOTAL_AMOUNT - $summeryArr[$uniqueval]->TOTAL_TOLL_AMT : 0;
            }
        }
        $output = [];
        if(count($reportArr) > 0)
        {
            foreach($reportArr as $out)
            {
                $output[] = $out;
            }
        }
        return $output;
 
    }

    public function getMonth($month)
    {
        $monthsArray = array(
            1 => 'January',
            2 => 'February',
            3 => 'March',
            4 => 'April',
            5 => 'May',
            6 => 'June',
            7 => 'July',
            8 => 'August',
            9 => 'September',
            10 => 'October',
            11 => 'November',
            12 => 'December'
        );
        $result = isset($monthsArray[$month]) ? $monthsArray[$month] : '';
        return $result;
       
    }

    public function GenerateMisReport($request){
        try {
            $reportyear = $request->input('reportyear');
            $reportmonth = $request->input('reportmonth');
            $vehiclevise = $request->input('vehiclevise');
            $status = $this->GenerateMisReportdata($reportyear,$reportmonth,'ALL',$vehiclevise);
            if($status == 'empty')
            {
                return response([
                    'success'=> true,
                    'status'=> 1,
                    'data'=> $status,
                    'message' => "Generate MIS Report Empty status find Return."
                ],200);
            }else{
                $result = $this->getMisReeportList(); 
                return response([
                    'success' => true,
                    'status' => 1,
                    'data' => $result,
                    'message' => "MIS data Fetch Sucessfully."
                ],200);
            }
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Generete MIS Report Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ],500);
        }
    }

    public function GenerateMisReportdata($reportyear,$reportmonth,$trip_type,$vehicle_id = '') {
		ini_set('memory_limit', '1024M');
        date_default_timezone_set('Asia/Kolkata');
        
        $dbname = Auth::user()->dbname;
		$vendor = "";
		$mispropertysql = "SELECT PROPERTIE_NAME,PROPERTIE_VALUE from properties where BRANCH_ID = $this->BRANCHID and PROPERTIE_NAME in('REPORT START DATE','REPORT DURATION DATE') and ACTIVE = 1";
		$misproperties = DB::connection("$dbname")->select($mispropertysql);

		$RS_TOTALTRIPCLOSE      	= MyHelper::$RS_TOTALTRIPCLOSE;
		$RS_TOTALMANUALTRIPCLOSE    = MyHelper::$RS_TOTALMANUALTRIPCLOSE;
		$RS_TOTALTRIPSHEETACCEPT    = MyHelper::$RS_TOTALTRIPSHEETACCEPT;
		$RS_TOTALDELAYROUTES      	= MyHelper::$RS_TOTALDELAYROUTES;

		$startdate = 1;
		$duration = 30;
		if(count($misproperties) > 0)
		{
			foreach($misproperties as $propertyvalue)
			{
				if($propertyvalue->PROPERTIE_NAME == 'REPORT START DATE'){
					$startdate = $propertyvalue->PROPERTIE_VALUE;
				}else{
					$duration = $propertyvalue->PROPERTIE_VALUE;
				}
			}
		}


		$startmonth = $reportmonth - 1;
		$date = Carbon::create($reportyear, $startmonth, $startdate);
		$from_date = $date->format('Y-m-d');

		$carbonDate = Carbon::create($reportyear, $reportmonth, $duration);
		$to_date = $carbonDate->format('Y-m-d');
        
        if($trip_type =='ALL') {
            $RPTTLTYPE = explode(',', $this->TTLTYPE);
            $triptype = "AND R.TRIP_TYPE IN ('$RPTTLTYPE[0]','$RPTTLTYPE[1]')";
        } else {
            $triptype = "AND R.TRIP_TYPE ='$trip_type'";
        }

        $SWINGSTART_TIME = $this->commonFunction->GetPropertyValue('SWING START TIME');
        $SWINGEND_TIME = $this->commonFunction->GetPropertyValue('SWING END TIME');

        $vehicle_condition = $vehicle_id == 'all' ? "" : " AND C.CAB_ID IN ($vehicle_id)";
        $from_date_time = $from_date .' 15:00:00';
        $to_date_time = $to_date. ' 09:00:00';
        $kmquery = '2 * R.TOTAL_KM,';
        $queryttlamt = '';
        $querykm = ",R.TOTAL_KM,R.TRIP_APPROVED_KM,IF(TIME(R.ESTIMATE_END_TIME) <= '$SWINGSTART_TIME' OR TIME(R.ESTIMATE_END_TIME) >= '$SWINGEND_TIME',(RS.TRAVELEMPCNT-1),0) AS SWINGKM";

		$query = "SELECT R.ROSTER_ID,R.ROUTE_ID,RS.TTLEMPCNT,RS.TRAVELEMPCNT,RS.EMPNOSHOWCNT,R.TRIP_TYPE,V.VEHICLE_REG_NO,VM.MODEL,VM.CAPACITY,V.VEHICLE_MODEL_ID,C.VEHICLE_ID,C.CAB_ID,
		R.ACTUAL_START_TIME, R.ACTUAL_END_TIME,	TIME_FORMAT(SEC_TO_TIME(TIME_TO_SEC(TIMEDIFF(R.ACTUAL_END_TIME,R.ACTUAL_START_TIME))), '%H:%i') AS time_diff,
		IF(dbs.APPROVED_KM_TYPE = 'GoogleKm',dbs.GOOGLE_KM,dbs.DEVICE_KM) AS GOOGLE_KM,AD.APPROVED_DISTANCE,
        VE.`NAME` VENDORNAME,R.START_LOCATION,R.END_LOCATION,'--' as OTPLOCATION,
        if(R.TRIP_TYPE = 'P',DATE(R.ESTIMATE_END_TIME),DATE(R.ESTIMATE_START_TIME)) as INOUT_DATE,
        if(R.TRIP_TYPE = 'P',TIME(R.ESTIMATE_END_TIME),TIME(R.ESTIMATE_START_TIME)) AS INOUT_TIME,RM.REASON,$kmquery
        '0' AS TTLKMS,if(TP.ttlcost is NULL,0,TP.ttlcost) AS TOLLCOST,if(PY.TTLAMOUNT is NULL,0,PY.TTLAMOUNT) AS PENALTYCOST,$queryttlamt
        if(RE.ROSTER_ID IS NULL,'--',CONCAT(ES.ESCORT_ID,',',ES.ESCORT_NAME)) ESCORTROUTE,'--' AS ALLSTATUS,

        if(TF.PACKAGE_PRICE IS NULL,0,TF.PACKAGE_PRICE) AS TARIFF,TF.PACKAGE_KMS,TF.EXTRA_KMS_CHARGE,R.ROSTER_STATUS
         ,RS.START_LAT,RS.START_LONG,RS.END_LAT,RS.END_LONG $querykm 

         FROM rosters R
        INNER JOIN (SELECT EM.*,COUNT(*) AS TTLEMPCNT,
        sum(if(EM.ROSTER_PASSENGER_STATUS & 16,1,0)) AS EMPNOSHOWCNT,
        SUM(if((EM.ROSTER_PASSENGER_STATUS & 32 OR EM.ROSTER_PASSENGER_STATUS & 128),1,0)) AS TRAVELEMPCNT  
        FROM (SELECT RR.ROSTER_ID,RR.EMPLOYEE_ID,RR.ROUTE_ORDER,RR.ROSTER_PASSENGER_STATUS,RR.START_LAT,RR.START_LONG,RR.END_LAT,RR.END_LONG
        FROM rosters RU
        INNER JOIN roster_passengers RR ON RR.ROSTER_ID = RU.ROSTER_ID 
        WHERE RU.BRANCH_ID=$this->BRANCHID AND RU.ACTIVE = 1 AND RR.ACTIVE = 1 AND (RR.ESTIMATE_END_TIME BETWEEN '$from_date_time' AND '$to_date_time'
        OR RR.ESTIMATE_START_TIME BETWEEN '$from_date_time' AND '$to_date_time') ORDER BY RR.ROUTE_ORDER DESC)
        EM GROUP BY EM.ROSTER_ID) RS ON RS.ROSTER_ID = R.ROSTER_ID
        INNER JOIN branch B ON B.BRANCH_ID = R.BRANCH_ID
        INNER JOIN vendors VE ON VE.VENDOR_ID = R.VENDOR_ID
        INNER JOIN cab C ON C.CAB_ID = R.CAB_ID
        INNER JOIN vehicles V ON V.VEHICLE_ID = C.VEHICLE_ID
        INNER JOIN vehicle_models VM ON VM.VEHICLE_MODEL_ID = V.VEHICLE_MODEL_ID
        INNER JOIN reason_master RM ON RM.REASON_ID = C.TARIFF_TYPE
        LEFT JOIN tariff TF ON TF.TARIFF_TYPE = C.TARIFF_TYPE AND TF.BRANCH_ID = C.BRANCH_ID 
        AND TF.VENDOR_ID = C.VENDOR_ID AND TF.VEHICLE_MODEL_ID = V.VEHICLE_MODEL_ID
        LEFT JOIN (SELECT ROSTER_ID,SUM(TOLL_CHARGE) as ttlcost FROM toll_payment WHERE DATE(PAYMENT_DATE)
        BETWEEN '$from_date' AND '$to_date' GROUP BY ROSTER_ID) TP ON TP.ROSTER_ID = R.ROSTER_ID 
        LEFT JOIN (SELECT ROSTER_ID,SUM(AMOUNT) AS TTLAMOUNT FROM route_penalty 
        WHERE LOGIN_DATE BETWEEN '$from_date' AND '$to_date' GROUP BY ROSTER_ID) PY ON PY.ROSTER_ID = R.ROSTER_ID 
        LEFT JOIN route_escorts RE ON RE.ROSTER_ID = R.ROSTER_ID AND RE.BRANCH_ID = R.BRANCH_ID AND RE.STATUS NOT IN (5,6)
        LEFT JOIN escorts ES ON ES.ESCORT_ID = RE.ESCORT_ID
        LEFT JOIN driver_billing_summary dbs on dbs.ROSTER_ID = R.ROSTER_ID and dbs.ACTIVE = 1
        LEFT JOIN locations L on L.LOCATION_NAME =  if(R.TRIP_TYPE = 'P',R.START_LOCATION,R.END_LOCATION)  AND L.DIVISION_ID=B.DIVISION_ID
        LEFT JOIN  approve_distances AD  on AD.LOCATION_ID = L.LOCATION_ID AND AD.BRANCH_ID=$this->BRANCHID


        WHERE R.MIS_ID IS NULL AND R.BRANCH_ID=$this->BRANCHID AND R.ACTIVE = 1 AND R.isNonBillable= 1  $vendor 
		AND R.ROSTER_STATUS in ($RS_TOTALTRIPCLOSE,$RS_TOTALMANUALTRIPCLOSE,$RS_TOTALTRIPSHEETACCEPT,$RS_TOTALDELAYROUTES)
		AND (R.ESTIMATE_END_TIME BETWEEN '$from_date_time' AND '$to_date_time' 
        OR R.ESTIMATE_START_TIME BETWEEN '$from_date_time' AND '$to_date_time') $triptype $vehicle_condition 
		ORDER BY R.ROSTER_ID DESC";

		//$errorlogfile = "/var/www/html/TMS/storage/logs/" . date('Y-m-d') . "-DEBUG_LOGSS.log";
		//$this->commonFunction->debug_log($errorlogfile, 'Query : '. $query);

		
		$result = DB::connection("$dbname")->select($query);
		
		/** Getting Tariff data */
		$tariff_query = "SELECT REASON_ID,REASON,BRANCH_ID,SUPPORT_CATEGORY as 'tariff_start',SUB_SUPPORT_CATEGORY as 'tariff_end' from reason_master where BRANCH_ID = $this->BRANCHID and CATEGORY = 'TariffType' and ACTIVE = 1 AND REASON IN('US TARIFF','NIGHT TARIFF', 'MONDAY TARIFF')";
		$tariff_result = DB::connection("$dbname")->select($tariff_query);

		
		$outputData  = [];
		$parentData = [];
		if(empty($result))
		{
			return 'empty';
		}

		// Getting MONDAY TARIFF Details
		$moday_tariff_start = '';
		$moday_tariff_end = '';
		$moday_traiff_type = '';
		$moday_traiff_REASON_ID = '';

		foreach($tariff_result as $traiff_details)
		{
			if($traiff_details->REASON == 'MONDAY TARIFF')
			{
				$moday_tariff_start = $traiff_details->tariff_start;
				$moday_tariff_end = $traiff_details->tariff_end;
				$moday_traiff_type = $traiff_details->REASON;
				$moday_traiff_REASON_ID = $traiff_details->REASON_ID;
				break;
			}
		}
		

		foreach($result as $details)
		{				
			$INOUT_TIME = $details->INOUT_TIME;
			$INOUT_DATE = $details->INOUT_DATE;
			$TRIP_TYPE = $details->TRIP_TYPE;
			$REASON_ID = '';
			$traiff_type = '';			
			$start = '';
			$end = '';
			$day_start_time = "00:00:00";			
			$include_branches = [18,62,63];

			// Validating Monday Tariff
			$dayName = date("l", strtotime($INOUT_DATE));


			if($dayName == 'Monday' && $this->isTimeBetween($moday_tariff_start, $moday_tariff_end, $INOUT_TIME))
			{
				$INOUT_TIME = $details->INOUT_TIME;
				$traiff_type = $moday_traiff_type;
				$REASON_ID = $moday_traiff_REASON_ID;
				$start = $moday_tariff_start;
				$end = $moday_tariff_end;	
				
				$this->commonFunction->debug_log($errorlogfile, 'Monday Tariff  dayName: '. $dayName . ' INOUT_DATE :   ' . $INOUT_DATE . ' INOUT_TIME : ' . $INOUT_TIME);
			}else{

				$REASON_ID = '';
				$traiff_type = '';			
				$start = '';
				$end = '';
				foreach($tariff_result as $traiff_details)
				{	
					if($traiff_details->REASON == 'MONDAY TARIFF') {
						continue;
					}
					$tariff_start = $traiff_details->tariff_start;
					$tariff_end = $traiff_details->tariff_end;				
					if($INOUT_TIME == '21:30:00' || $INOUT_TIME == '21:35:00') {
						if($TRIP_TYPE == 'P'){
							if($traiff_details->REASON == 'NIGHT TARIFF'){
								continue;
							}else{
								$traiff_type = $traiff_details->REASON;
								$REASON_ID = $traiff_details->REASON_ID;
								
								$start = $traiff_details->tariff_start;
								$end = $traiff_details->tariff_end;
							}
						}else{
							if($traiff_details->REASON != 'NIGHT TARIFF'){
								continue;
							}else{

								$traiff_type = $traiff_details->REASON;
								$REASON_ID = $traiff_details->REASON_ID;
								
								$start = $traiff_details->tariff_start;
								$end = $traiff_details->tariff_end;
							}
						}
					}else{

                        if (($INOUT_TIME == '22:05:00' && $TRIP_TYPE == 'P') || ($INOUT_TIME == '22:00:00' && $TRIP_TYPE == 'P')) {
                            if ($traiff_details->REASON == 'NIGHT TARIFF') {
                                continue;
                            } else {
                                $traiff_type = $traiff_details->REASON;
                                $REASON_ID = $traiff_details->REASON_ID;

                                $start = $traiff_details->tariff_start;
                                $end = $traiff_details->tariff_end;
                            }
                        } else {
                            if ($this->isTimeBetween($tariff_start, $tariff_end, $INOUT_TIME)) {
                                $INOUT_TIME = $details->INOUT_TIME;
                                $traiff_type = $traiff_details->REASON;
                                $REASON_ID = $traiff_details->REASON_ID;
                                
                                $start = $traiff_details->tariff_start;
                                $end = $traiff_details->tariff_end;
                                if( in_array($this->BRANCHID, $include_branches)){
                                    if($traiff_type == 'NIGHT TARIFF'){							
                                        if ($this->isTimeBetween($day_start_time, $tariff_end, $INOUT_TIME)) {
                                            $INOUT_DATE =  date('Y-m-d', strtotime($INOUT_DATE . ' -1 day'));
                                        }
                                    }
                                }
						    } 
					    }
				    }
				}
			}
			
			$details->traiff_type = $traiff_type;
			$details->INOUT_DATE = $INOUT_DATE;
			$details->traiff_reson_id = $REASON_ID;
			$parentData['traiff_type'] = $traiff_type;
			$parentData['REASON_ID'] = $REASON_ID;
			$parentData['tariff_start'] = $start;
			$parentData['tariff_end'] = $end;
			$parentData['INOUT_DATE'] = $INOUT_DATE;
			$outputData[$INOUT_DATE]['parentData']  = $parentData;
			$outputData[$INOUT_DATE]['children'][]  = $details;
		}

		$this->commonFunction->debug_log($errorlogfile, 'outputData : '. json_encode($outputData));

		$output = [];
		foreach($outputData as $rawData)
		{
			$tableData = [];
			$total_trip_km = 0;
			$total_approved_km = 0;
			$VEHICLE_MODEL_ID = '';
			$total_hrs = '00:00:00';
			// $tableData['traiff_type'] = $rawData['parentData']['traiff_type'];
			$tableData['INOUT_DATE'] = $rawData['parentData']['INOUT_DATE'];
			$tableData['tariff_start'] = $rawData['parentData']['tariff_start'];
			$tableData['tariff_end'] = $rawData['parentData']['tariff_end'];
			$childrenData = [];
			foreach($rawData['children'] as $children)
			{
				$time_diff = $children->time_diff;
				$total_trip_km += $children->GOOGLE_KM; //$children->APPROVED_DISTANCE;

				$total_approved_km += $children->TRIP_APPROVED_KM;
				if($time_diff != null && $time_diff != '')
				{
					$total_hrs = $this->add_hours($total_hrs,$time_diff);
				}
				$childrenData[$children->traiff_type][$children->VEHICLE_ID]['data'][] = $children;
				$VEHICLE_MODEL_ID = $children->VEHICLE_MODEL_ID;
			}
			$tableData['total_hrs'] = $total_hrs;
			$tableData['total_trip_km'] = $total_trip_km;
			$tableData['total_approved_km'] = $total_approved_km;
			$tableData['VEHICLE_MODEL_ID'] = $VEHICLE_MODEL_ID;
			$tableData['children'] = $childrenData;
			$output[] = $tableData;
		}
        
		/** Getting total hour and total Km */
		foreach($output as &$outputDetails)
		{
			$childDetails = $outputDetails['children'];
			$updatedData = [];
			foreach($childDetails as $key => &$childData)
			{
				
				$updatedChildData = [];
				foreach($childData  as $vehicleModel => &$child)
				{
					$tarrif_total_google_km = 0;
					$tarrif_total_hrs = '00:00:00';
					$tarrif_reason_id = '';
					$VEHICLE_MODEL_ID = '';
					$VEHICLE_ID = '';
					$rosterIds = "";
					$INOUT_DATE = "";
					$TOLL_AMT = 0;
					foreach($child['data'] as $vehicleData)
					{
						$tarrif_total_google_km +=$vehicleData->GOOGLE_KM;  //$vehicleData->APPROVED_DISTANCE;

						$tarrif_time_diff = $vehicleData->time_diff;
						$tarrif_reason_id = $vehicleData->traiff_reson_id;
						$VEHICLE_MODEL_ID = $vehicleData->VEHICLE_MODEL_ID;
						$VEHICLE_ID = $vehicleData->VEHICLE_ID;
						$CAB_ID = $vehicleData->CAB_ID;
						$INOUT_DATE = $vehicleData->INOUT_DATE;
						$rosterIds = $rosterIds == "" ? $vehicleData->ROSTER_ID : $rosterIds . ",". $vehicleData->ROSTER_ID;
						$TOLL_AMT+=$vehicleData->TOLLCOST;
						if($tarrif_time_diff != null && $tarrif_time_diff != '')
						{
							$tarrif_total_hrs = $this->add_hours($tarrif_total_hrs,$tarrif_time_diff);
						}
					}
					$child = ['data' => $child['data'],'tarrif_total_google_km' => $tarrif_total_google_km,'tarrif_total_hrs'=> $tarrif_total_hrs,'tarrif_reason_id' =>$tarrif_reason_id,'VEHICLE_MODEL_ID' => $VEHICLE_MODEL_ID,"rosterIds"=>$rosterIds,'VEHICLE_ID' => $VEHICLE_ID,'CAB_ID' => $CAB_ID, 'INOUT_DATE' => $INOUT_DATE, 'TOLL_AMT' => $TOLL_AMT];
					$updatedChildData[$vehicleModel][] = $child;					
				}
				$updatedData[$key] = $updatedChildData;				
			}
			$outputDetails['children'] = $updatedData;			
		}

        $this->commonFunction->debug_log($errorlogfile, 'updatedData : '. json_encode($updatedData));

		$maxRepotId = $this->getmaxRepotId();
		$reportID =  $maxRepotId + 1;
		/** Getting Tarrif Amount */
		foreach($output as &$tarrif_outputDetails)
		{
			$tarrif_childDetails = $tarrif_outputDetails['children'];
			$total_amount = 0;
			foreach($tarrif_childDetails as $tarrif_key => &$tarrif_childData)
			{
				
				foreach($tarrif_childData as $vehiclemodel_id => &$innerData)
				{					
					foreach($innerData as &$vehicle_details)
					{
						$tarrif_total_google_km = $vehicle_details['tarrif_total_google_km'];
						$tarrif_total_hrs = $vehicle_details['tarrif_total_hrs'];
						$tarrif_reason_id = $vehicle_details['tarrif_reason_id'];
						$VEHICLE_MODEL_ID = $vehicle_details['VEHICLE_MODEL_ID'];
						list($amount,$PACKAGE_KMS, $PACKAGE_HRS, $PACKAGE_PRICE, $EXTRA_KMS_CHARGE, $EXTRA_HRS_CHARGE, $MINIMUM_MINUTES,$extra_hours,$extra_minutes,$extra_hours_amount,$extra_minutes_amount,$extra_km,$extra_km_amount) = $this->tarrif_amount_calc($tarrif_reason_id,$tarrif_total_google_km,$tarrif_total_hrs,$VEHICLE_MODEL_ID);
						$vehicle_details['tarrif_amount']  = $amount;	
						$vehicle_details['PACKAGE_KMS']  = $PACKAGE_KMS;	
						$vehicle_details['PACKAGE_HRS']  = $PACKAGE_HRS;	
						$vehicle_details['PACKAGE_PRICE']  = $PACKAGE_PRICE;	
						$vehicle_details['EXTRA_KMS_CHARGE']  = $EXTRA_KMS_CHARGE;	
						$vehicle_details['EXTRA_HRS_CHARGE']  = $EXTRA_HRS_CHARGE;	
						$vehicle_details['MINIMUM_MINUTES']  = $MINIMUM_MINUTES;

						$vehicle_details['extra_hours'] = $extra_hours;
						$vehicle_details['extra_minutes'] = $extra_minutes;
						$vehicle_details['extra_hours_amount'] = $extra_hours_amount;
						$vehicle_details['extra_minutes_amount'] = $extra_minutes_amount;
						$vehicle_details['extra_km'] = $extra_km;
						$vehicle_details['extra_km_amount'] = $extra_km_amount;
						$total_amount += $amount;	
						$this->addmisreport($vehicle_details,$reportyear,$reportmonth,$reportID);				

					}	
					
				}
				
			}
			$tarrif_outputDetails['children'] = $tarrif_childDetails;
			$tarrif_outputDetails['total_amount'] = $total_amount;
		}
        return 'success';
		
    }

    public function isTimeBetween($startTime, $endTime, $givenTime)
	{
		$startTime = Carbon::createFromFormat('H:i:s', $startTime);
		$endTime = Carbon::createFromFormat('H:i:s', $endTime);
		$givenTime = Carbon::createFromFormat('H:i:s', $givenTime);

		if ($endTime < $startTime) {
			// Time range spans midnight, so check if the given time is between start time and midnight
			// or between midnight and end time to correctly determine if it's between the range.
			return ($givenTime >= $startTime && $givenTime <= Carbon::createFromFormat('H:i:s', '23:59:59'))
				|| ($givenTime >= Carbon::createFromFormat('H:i:s', '00:00:00') && $givenTime <= $endTime);
		} else {
			// Normal scenario, check if the given time is between start and end time.
			return $givenTime >= $startTime && $givenTime <= $endTime;
		}
	}

    public function add_hours($time1,$time2)
	{
		
		// Explode the times to get hours and minutes
		list($hours1, $minutes1) = explode(':', $time1);
		list($hours2, $minutes2) = explode(':', $time2);

		// Convert the hours and minutes to integers
		$hours1 = intval($hours1);
		$minutes1 = intval($minutes1);
		$hours2 = intval($hours2);
		$minutes2 = intval($minutes2);

		// Calculate the total minutes for each time
		$totalMinutes1 = $hours1 * 60 + $minutes1;
		$totalMinutes2 = $hours2 * 60 + $minutes2;

		// Add the two times together
		$totalMinutesSum = $totalMinutes1 + $totalMinutes2;

		// Calculate the result hours and minutes
		$resultHours = floor($totalMinutesSum / 60);
		$resultMinutes = $totalMinutesSum % 60;
		$result = $resultHours. ":".$resultMinutes;

		return $result;
	}

    public function getmaxRepotId()
	{
		$dbname = Auth::user()->dbname;
		$reportSql = "SELECT max(REPORT_ID) as 'REPORT_ID' FROM misreport WHERE Active = 1";
		$result = DB::connection("$dbname")->select($reportSql);
		if(count($result) > 0)
		{
			$latestReporId = isset($result[0]) ? $result[0]->REPORT_ID : 0;
		}
		$reportId = $latestReporId != null && $latestReporId != '' ? $latestReporId : 0;
		return $reportId;
	}

    public function addmisreport($rosterdetails = [],$reportyear,$reportmonth,$reportID)
	{
		$dbname = Auth::user()->dbname;
		$userid = Auth::user()->id;
		$BRANCH_ID = Auth::user()->BRANCH_ID;
		if(count($rosterdetails) > 0)
		{
			$tarrif_total_google_km = isset($rosterdetails['tarrif_total_google_km']) ? $rosterdetails['tarrif_total_google_km'] : '';
			$tarrif_total_hrs = isset($rosterdetails['tarrif_total_hrs']) ? $rosterdetails['tarrif_total_hrs'] : '';
			$tarrif_reason_id = isset($rosterdetails['tarrif_reason_id']) ? $rosterdetails['tarrif_reason_id'] : '';
			$VEHICLE_MODEL_ID = isset($rosterdetails['VEHICLE_MODEL_ID']) ? $rosterdetails['VEHICLE_MODEL_ID'] : '';
			$rosterIds = isset($rosterdetails['rosterIds']) ? $rosterdetails['rosterIds'] : '';
			$tarrif_amount = isset($rosterdetails['tarrif_amount']) ? $rosterdetails['tarrif_amount'] : '';
			$PACKAGE_KMS = isset($rosterdetails['PACKAGE_KMS']) ? $rosterdetails['PACKAGE_KMS'] : '';
			$PACKAGE_HRS = isset($rosterdetails['PACKAGE_HRS']) ? $rosterdetails['PACKAGE_HRS'] : '';
			$PACKAGE_PRICE = isset($rosterdetails['PACKAGE_PRICE']) ? $rosterdetails['PACKAGE_PRICE'] : '';
			$EXTRA_KMS_CHARGE = isset($rosterdetails['EXTRA_KMS_CHARGE']) ? $rosterdetails['EXTRA_KMS_CHARGE'] : '';
			$EXTRA_HRS_CHARGE = isset($rosterdetails['EXTRA_HRS_CHARGE']) ? $rosterdetails['EXTRA_HRS_CHARGE'] : '';
			$MINIMUM_MINUTES = isset($rosterdetails['MINIMUM_MINUTES']) ? $rosterdetails['MINIMUM_MINUTES'] : '';
			$VEHICLE_ID = isset($rosterdetails['VEHICLE_ID']) ? $rosterdetails['VEHICLE_ID'] : '';
			$INOUT_DATE = isset($rosterdetails['INOUT_DATE']) ? $rosterdetails['INOUT_DATE'] : '';
			$CAB_ID = isset($rosterdetails['CAB_ID']) ? $rosterdetails['CAB_ID'] : '';

			$TOLL_AMT = isset($rosterdetails['TOLL_AMT']) ? $rosterdetails['TOLL_AMT'] : 0;
			$tarrif_amount+=$TOLL_AMT;
			
			$extra_hours = isset($rosterdetails['extra_hours']) ? $rosterdetails['extra_hours'] : '';
			$extra_minutes = isset($rosterdetails['extra_minutes']) ? $rosterdetails['extra_minutes'] : '';
			$extra_hours_amount = isset($rosterdetails['extra_hours_amount']) ? $rosterdetails['extra_hours_amount'] : '';
			$extra_minutes_amount = isset($rosterdetails['extra_minutes_amount']) ? $rosterdetails['extra_minutes_amount'] : '';
			$extra_km = isset($rosterdetails['extra_km']) ? $rosterdetails['extra_km'] : '';
			$extra_km_amount = isset($rosterdetails['extra_km_amount']) ? $rosterdetails['extra_km_amount'] : '';


			$exQuery = "SELECT * FROM misreport WHERE TARIFF_ID = '$tarrif_reason_id' AND BRANCH_ID = '$BRANCH_ID' AND VEHICLE_ID = '$VEHICLE_ID' AND CAB_ID = '$CAB_ID' AND `DATE` = '$INOUT_DATE'";
			$exxistData = DB::connection("$dbname")->select($exQuery);
			$lastInsertedId = null;
			if(count($exxistData) > 0){
				if(isset($exxistData[0])){
					$existingData = $exxistData[0];
					$lastInsertedId = $existingData->id;

					$updateData = [
						'TOTAL_HOURS' => $existingData->TOTAL_HOURS + $tarrif_total_hrs, 
						'TOTAL_KMS' => $existingData->TOTAL_KMS + $tarrif_total_google_km, 
						'ROSTER_IDS' => $existingData->ROSTER_IDS . ',' . $rosterIds, 
						'TOTAL_AMOUNT' => $existingData->TOTAL_AMOUNT + $tarrif_amount,
						'EXTRA_HOURS' => $existingData->EXTRA_HOURS + $extra_hours,
						'EXTRA_MINUTES' => $existingData->EXTRA_MINUTES + $extra_minutes,
						'EXTRA_HOURS_AMOUNT' => $existingData->EXTRA_HOURS_AMOUNT + $extra_hours_amount,
						'EXTRA_MINUTES_AMOUNT' => $existingData->EXTRA_MINUTES_AMOUNT + $extra_minutes_amount,
						'EXTRA_KM' => $existingData->EXTRA_KM + $extra_km,
						'EXTRA_KM_AMOUNT' => $existingData->EXTRA_KM_AMOUNT + $extra_km_amount,
						'TOLL_AMT' => $existingData->TOTAL_KMS + $TOLL_AMT,
					];

					DB::table('misreport')->where('id', $lastInsertedId)->update($updateData);
				}
			}else{
				$data = [
					'BRANCH_ID' => $BRANCH_ID, 
					'TARIFF_ID' => $tarrif_reason_id, 
					'REPORT_ID' => $reportID, 
					'VEHICLE_ID' => $VEHICLE_ID, 
					'CAB_ID' => $CAB_ID, 
					'VEHICLE_MODEL_ID' => $VEHICLE_MODEL_ID, 
					'DATE' => $INOUT_DATE, 
					'TOTAL_HOURS' => $tarrif_total_hrs, 
					'PACKAGE_PRICE' => $PACKAGE_PRICE, 
					'TOTAL_KMS' => $tarrif_total_google_km, 
					'PACKAGE_HRS' => $PACKAGE_HRS, 
					'PACKAGE_KMS' => $PACKAGE_KMS, 
					'EXTRA_KMS_CHARGE' => $EXTRA_KMS_CHARGE, 
					'EXTRA_HRS_CHARGE' => $EXTRA_HRS_CHARGE, 
					'MINIMUM_MINUTES' => $MINIMUM_MINUTES, 
					'MONTH' => $reportmonth, 
					'YEAR' => $reportyear, 
					'ROSTER_IDS' => $rosterIds, 
					'TOTAL_AMOUNT' => $tarrif_amount,
					'EXTRA_HOURS' => $extra_hours,
					'EXTRA_MINUTES' => $extra_minutes,
					'EXTRA_HOURS_AMOUNT' => $extra_hours_amount,
					'EXTRA_MINUTES_AMOUNT' => $extra_minutes_amount,
					'EXTRA_KM' => $extra_km,
					'EXTRA_KM_AMOUNT' => $extra_km_amount,
					'created_by' => $userid, 
					'TOLL_AMT' => $TOLL_AMT,
				];
				$lastInsertedId = DB::table('misreport')->insertGetId($data);
			}
			$this->updaterosterdetails($rosterIds,$lastInsertedId);
		}else{
			return false;
		}
		return true;
	}

    public function tarrif_amount_calc($tarrif_reason_id,$tarrif_total_km,$tarrif_total_hrs,$vehiclemodel_id)
	{
		$dbname = Auth::user()->dbname;
		$branch_id  = $this->BRANCHID;
		$tariff_query = "SELECT * from tariff where BRANCH_ID = $branch_id and ACTIVE = '1' and TARIFF_TYPE = '$tarrif_reason_id' and VEHICLE_MODEL_ID = '$vehiclemodel_id'";
		$tariff_result = DB::connection("$dbname")->select($tariff_query);
		$PACKAGE_KMS = '';
		$PACKAGE_HRS = '';
		$PACKAGE_PRICE = '';
		$EXTRA_KMS_CHARGE = '';
		$EXTRA_HRS_CHARGE = '';
		$MINIMUM_MINUTES = '';
		$amount = 0;
		$extra_km = 0;
		$extra_hours = 0;
		$extra_minutes = 0;
		$extra_km_amount = 0;
		$extra_hours_amount = 0;
		$extra_minutes_amount = 0;
		if(count($tariff_result) > 0)
		{
			$tariffData = $tariff_result[0];
			$PACKAGE_KMS = $tariffData->PACKAGE_KMS;
			$PACKAGE_HRS = $tariffData->PACKAGE_HRS;
			$PACKAGE_PRICE = $tariffData->PACKAGE_PRICE;
			$EXTRA_KMS_CHARGE = $tariffData->EXTRA_KMS_CHARGE;
			$EXTRA_HRS_CHARGE = $tariffData->EXTRA_HRS_CHARGE;
			$MINIMUM_MINUTES = $tariffData->MINIMUM_MINUTES;
			if($EXTRA_KMS_CHARGE > 0 || $EXTRA_HRS_CHARGE > 0)
			{
				
				$tarrif_minutes = $this->hours_to_minute($tarrif_total_hrs);
				$package_minutes = $this->hours_to_minute($PACKAGE_HRS);
				

				if($PACKAGE_KMS > $tarrif_total_km && $package_minutes > $tarrif_minutes)
				{
					$amount = $PACKAGE_PRICE;
				}else{
					$amount = $PACKAGE_PRICE;
					/** Amount calculated by Hours */
					if($tarrif_minutes > $package_minutes)
					{
						$balance_minutes = $tarrif_minutes - $package_minutes;
						list($hours,$remainingMinutes) = $this->minutes_to_hours($balance_minutes);
						$extra_hours = $hours;
						$extra_minutes = $remainingMinutes;
						$extra_hours_amount = $hours * $EXTRA_HRS_CHARGE;
						$amount += $extra_hours_amount;
						if($remainingMinutes >= $MINIMUM_MINUTES)
						{
							$extra_minutes_amount = 1 * $EXTRA_HRS_CHARGE;
							$amount += $extra_minutes_amount;
						}
					} 
					/** Amount calculated by Kms */
					if($tarrif_total_km > $PACKAGE_KMS)
					{
						$balance_kms = $tarrif_total_km - $PACKAGE_KMS;
						$extra_km = $balance_kms;
						$extra_km_amount = $balance_kms * $EXTRA_KMS_CHARGE;
						$amount += $extra_km_amount;
					}
					
				}
				

			}else{
				$amount = 0;
			}
		}else{
			$amount = 0;
		}

		return [$amount,$PACKAGE_KMS, $PACKAGE_HRS, $PACKAGE_PRICE, $EXTRA_KMS_CHARGE, $EXTRA_HRS_CHARGE, $MINIMUM_MINUTES,$extra_hours,$extra_minutes,$extra_hours_amount,$extra_minutes_amount,$extra_km,$extra_km_amount];
	}

    public function hours_to_minute($hrs)
	{
		
		list($hours, $minutes) = explode(":", $hrs);

		$totalMinutes = ($hours * 60) + $minutes;

		return $totalMinutes;
	}
 
	public function minutes_to_hours($minutes)
	{
		$hours = floor($minutes / 60);
		$remainingMinutes = $minutes % 60;

		return [$hours,$remainingMinutes];
	}

    public function updaterosterdetails($rosterIds = '',$misID)
	{
		$rosterArr = explode(",",$rosterIds);
		if(count($rosterIds) > 0 && $misID != '' && $misID != null)
		{			
			foreach($rosterArr as $rosterId)
			{
				DB::table('rosters')
				->where('ROSTER_ID', $rosterId)
				->update([
					'MIS_ID' => $misID
				]);
			}
		}else{
			return false;
		}
		return true;
	}

    public function exportmisreport($request)
    {
        try {
            $reportyear = $request->reportyear;
            $reportmonth = $request->reportmonth;
            $vehiclevise = $request->vehiclevise;
            $exporttype = $request->exporttype;
            $tarifftype = $request->tarifftype;
           
            if ($exporttype == 'payment') {  
                $data = $this->ExportSUmmaryData($reportyear, $reportmonth, $vehiclevise, $tarifftype);
            } else {
                $data = $this->ExportMisReports($reportyear, $reportmonth, $vehiclevise, $tarifftype);
            }
           
            if (count($data) > 0) {
                $filename = 'data.csv';
                $csvContent = '';              
               
                foreach ($data as $index => $row) {
                    if ($index === 0) {
                        $row = array_map(function ($value) {
                            return $value;
                        }, $row);
                    }        
                    $csvContent .= implode(',', $row) . "\n";
                }
               
                $headers = array(
                    'Content-Type' => 'text/csv',
                    'Content-Disposition' => 'attachment; filename="' . $filename . '"',
                );
       
                return response($csvContent, 200, $headers);
            }else{
                return response([
                    'success' => false,
                    'message' => 'No Report available for given details. Please generate a Report...',
                    'validation_controller' => true,
                ], 200);            }
           
            //return response([
            //    'success' => true,
            //    'status' => 1,
            //    'message' => 'MIS Report has been successfully exported.',
            //], 200);
           
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'message' => 'MIS Report export unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function ExportSUmmaryData($reportyear,$reportmonth,$vehiclevise, $tarifftype)
    {
        $dbname = Auth::user()->dbname;
        $branch_id=Auth::user()->BRANCH_ID;
        $tariff_condition = "";
        if($tarifftype != '' && $tarifftype != null){
            $tariff = '';
            switch($tarifftype){
                case 'daytariff':
                    $tariff = 'US TARIFF';
                    break;
                case 'nighttariff':
                    $tariff = 'NIGHT TARIFF';
                    break;
                case 'mondaytariff':
                    $tariff = 'MONDAY TARIFF';
                    break;
                default:
                    $tariff = '';
            }
            if($tariff != '' && $tariff !=null){
 
                $tariff_condition = " AND rm.REASON = '$tariff'";
            }
        }
        $vehicle_condition = $vehiclevise == 'all' ? "" : " AND mis.CAB_ID IN ($vehiclevise)";
        $query = "SELECT B.BRANCH_NAME,mis.id as 'MIS_ID',mis.DATE,mis.EXTRA_MINUTES_AMOUNT,mis.EXTRA_HOURS_AMOUNT,mis.PACKAGE_KMS,mis.start_time,mis.end_time,mis.PACKAGE_HRS,mis.TOTAL_KMS,mis.TOTAL_HOURS,mis.EXTRA_KM,mis.EXTRA_HOURS,mis.PACKAGE_PRICE,mis.EXTRA_KM_AMOUNT,mis.TOTAL_AMOUNT,mis.TOLL_AMT,rm.REASON as 'TARRIF',CONCAT(SUBSTRING(v.VEHICLE_REG_NO,6,2),' ',SUBSTRING(v.VEHICLE_REG_NO,8,2),' ',SUBSTRING(v.VEHICLE_REG_NO,10,2),' ',SUBSTRING(v.VEHICLE_REG_NO, 1, 4)) as 'VEHICLE_REG_NO',vm.MODEL as 'MODEL'
        FROM misreport mis
        LEFT JOIN reason_master rm on mis.TARIFF_ID = rm.REASON_ID
        LEFT JOIN branch B on B.BRANCH_ID = mis.BRANCH_ID
 
        LEFT JOIN vehicles v on mis.VEHICLE_ID = v.VEHICLE_ID
        LEFT JOIN vehicle_models vm on mis.VEHICLE_MODEL_ID = vm.VEHICLE_MODEL_ID
        WHERE mis.`MONTH` = '$reportmonth' AND mis.`YEAR` = '$reportyear' $vehicle_condition $tariff_condition";
        $result = DB::connection("$dbname")->select($query);
 
        $summery_header = ['Date','Tarrif Type','Branch','Vehicle Reg No','Vehicle Model','Actual KM','Actual Hrs','Extra KM','Extra Hrs','Starting Time','Closing Time','Package Kms','Package Hrs','Package Price','Extra Kms Charge','Extra Hrs Charge','Toll Amount','Total Amount'];
       
        $data = array(
            $summery_header
        );
 
        $startTime = '';
        $endTime = '';
        if(count($result) > 0)
        {  
 
            /** Getting Tariff data */
            $tariff_query = "SELECT REASON_ID,REASON,BRANCH_ID,SUPPORT_CATEGORY,SUPPORT_CATEGORY as 'tariff_start',SUB_SUPPORT_CATEGORY as 'tariff_end' from reason_master where BRANCH_ID = $this->BRANCHID and CATEGORY = 'TariffType' and ACTIVE = 1 AND REASON IN('US TARIFF','NIGHT TARIFF', 'MONDAY TARIFF')";
            $tariff_result = DB::connection("$dbname")->select($tariff_query);
            $tariffDetails = [];
            foreach($tariff_result as $trff){
                $tariffDetails[$trff->REASON] = $trff;
            }
            foreach($result as $row)
            {
 
                $startTime = isset($tariffDetails[$row->TARRIF]) ? $tariffDetails[$row->TARRIF]->tariff_start : null;
                $endTime = isset($tariffDetails[$row->TARRIF]) ? $tariffDetails[$row->TARRIF]->tariff_end : null;
 
                $actual_km = $row->TOTAL_KMS - $row->EXTRA_KM;
                $extra_hrs_amt = $row->EXTRA_MINUTES_AMOUNT > 0 ? $row->EXTRA_MINUTES_AMOUNT + $row->EXTRA_HOURS_AMOUNT : $row->EXTRA_MINUTES_AMOUNT;
                $row_data[] = $row->DATE;                
                $row_data[] = $row->TARRIF;
                $row_data[] = $row->BRANCH_NAME;            
                $row_data[] = $row->VEHICLE_REG_NO;              
                $row_data[] = $row->MODEL;              
                $row_data[] = $row->TOTAL_KMS;  
                $row_data[] = $row->TOTAL_HOURS;    
                $row_data[] = $row->EXTRA_KM;
                $row_data[] = $row->EXTRA_HOURS;
                $row_data[] = $startTime;
                $row_data[] = $endTime;
                $row_data[] = $row->PACKAGE_KMS;                
                $row_data[] = $row->PACKAGE_HRS;                
                $row_data[] = $row->PACKAGE_PRICE;              
                $row_data[] = $row->EXTRA_KM_AMOUNT;                
                $row_data[] = $extra_hrs_amt;  
                $row_data[] = $row->TOLL_AMT;            
                $row_data[] = $row->TOTAL_AMOUNT;            
                $data[] = $row_data;
                $row_data = [];
            }
        }else{
            $data = [];
        }
        return $data;
 
    }

    public function ExportMisReports($reportyear,$reportmonth,$vehiclevise,$tarifftype)
    {
        ini_set('memory_limit', '1024M');
        $dbname = Auth::user()->dbname;
 
        $kmquery = '2 * R.TOTAL_KM,';
        $queryttlamt = '';
        $vehicle_condition = $vehiclevise > 0 ? " AND MIS.CAB_ID = '$vehiclevise'" : "";
 
        $RS_TOTALTRIPCLOSE        = MyHelper::$RS_TOTALTRIPCLOSE;
        $RS_TOTALMANUALTRIPCLOSE  = MyHelper::$RS_TOTALMANUALTRIPCLOSE;
        $RS_TOTALTRIPSHEETACCEPT  = MyHelper::$RS_TOTALTRIPSHEETACCEPT;
        $RS_TOTALDELAYROUTES      = MyHelper::$RS_TOTALDELAYROUTES; 
 
        $tariff_condition = "";
        if($tarifftype != '' && $tarifftype != null){
            $tariff = '';
            switch($tarifftype){
                case 'daytariff':
                    $tariff = 'US TARIFF';
                    break;
                case 'nighttariff':
                    $tariff = 'NIGHT TARIFF';
                    break;
                case 'mondaytariff':
                    $tariff = 'MONDAY TARIFF';
                    break;
                default:
                    $tariff = '';
            }
            if($tariff != '' && $tariff !=null){
 
                $tariff_condition = " AND MISRM.REASON = '$tariff'";
            }
        }
       
        $query = "SELECT R.ROSTER_ID,R.ROUTE_ID,RS.TTLEMPCNT,RS.TRAVELEMPCNT,RS.EMPNOSHOWCNT,R.TRIP_TYPE, CONCAT(SUBSTRING(V.VEHICLE_REG_NO,6,2),' ',SUBSTRING(V.VEHICLE_REG_NO,8,2),' ',SUBSTRING(V.VEHICLE_REG_NO,10,2),' ',SUBSTRING(V.VEHICLE_REG_NO, 1, 4)) AS VEHICLE_REG_NO,VM.MODEL,VM.CAPACITY,V.VEHICLE_MODEL_ID,C.VEHICLE_ID,
            R.ACTUAL_START_TIME, R.ACTUAL_END_TIME, TIME_FORMAT(SEC_TO_TIME(TIME_TO_SEC(TIMEDIFF(R.ACTUAL_END_TIME,R.ACTUAL_START_TIME))), '%H:%i') AS time_diff,
            IF(dbs.APPROVED_KM_TYPE = 'GoogleKm',dbs.GOOGLE_KM,dbs.DEVICE_KM) AS GOOGLE_KM,AD.APPROVED_DISTANCE,
            VE.`NAME` VENDORNAME,R.START_LOCATION,R.END_LOCATION,'--' as OTPLOCATION,
            if(R.TRIP_TYPE = 'P',DATE(R.ESTIMATE_END_TIME),DATE(R.ESTIMATE_START_TIME)) as INOUT_DATE,
            if(R.TRIP_TYPE = 'P',TIME(R.ESTIMATE_END_TIME),TIME(R.ESTIMATE_START_TIME)) AS INOUT_TIME,RM.REASON,$kmquery
            '0' AS TTLKMS,if(TP.ttlcost is NULL,0,TP.ttlcost) AS TOLLCOST,if(PY.TTLAMOUNT is NULL,0,PY.TTLAMOUNT) AS PENALTYCOST,$queryttlamt
            if(RE.ROSTER_ID IS NULL,'--',CONCAT(ES.ESCORT_ID,',',ES.ESCORT_NAME)) ESCORTROUTE,'--' AS ALLSTATUS,
 
            if(TF.PACKAGE_PRICE IS NULL,0,TF.PACKAGE_PRICE) AS TARIFF,TF.PACKAGE_KMS,TF.EXTRA_KMS_CHARGE,R.ROSTER_STATUS
            ,RS.START_LAT,RS.START_LONG,RS.END_LAT,RS.END_LONG,B.BRANCH_NAME
 
            FROM rosters R
            INNER JOIN (SELECT EM.*,COUNT(*) AS TTLEMPCNT,
            sum(if(EM.ROSTER_PASSENGER_STATUS & 16,1,0)) AS EMPNOSHOWCNT,
            SUM(if((EM.ROSTER_PASSENGER_STATUS & 32 OR EM.ROSTER_PASSENGER_STATUS & 128),1,0)) AS TRAVELEMPCNT  
            FROM (SELECT RR.ROSTER_ID,RR.EMPLOYEE_ID,RR.ROUTE_ORDER,RR.ROSTER_PASSENGER_STATUS,RR.START_LAT,RR.START_LONG,RR.END_LAT,RR.END_LONG
            FROM rosters RU
            INNER JOIN roster_passengers RR ON RR.ROSTER_ID = RU.ROSTER_ID
            WHERE RU.BRANCH_ID=$this->BRANCHID AND RU.ACTIVE = 1 AND RR.ACTIVE = 1   ORDER BY RR.ROUTE_ORDER DESC)
            EM GROUP BY EM.ROSTER_ID) RS ON RS.ROSTER_ID = R.ROSTER_ID
            INNER JOIN branch B ON B.BRANCH_ID = R.BRANCH_ID
            INNER JOIN vendors VE ON VE.VENDOR_ID = R.VENDOR_ID
            INNER JOIN cab C ON C.CAB_ID = R.CAB_ID
            INNER JOIN vehicles V ON V.VEHICLE_ID = C.VEHICLE_ID
            INNER JOIN vehicle_models VM ON VM.VEHICLE_MODEL_ID = V.VEHICLE_MODEL_ID
            INNER JOIN reason_master RM ON RM.REASON_ID = C.TARIFF_TYPE
            LEFT JOIN tariff TF ON TF.TARIFF_TYPE = C.TARIFF_TYPE AND TF.BRANCH_ID = C.BRANCH_ID
            AND TF.VENDOR_ID = C.VENDOR_ID AND TF.VEHICLE_MODEL_ID = V.VEHICLE_MODEL_ID
            LEFT JOIN (SELECT ROSTER_ID,SUM(TOLL_CHARGE) as ttlcost FROM toll_payment  GROUP BY ROSTER_ID) TP ON TP.ROSTER_ID = R.ROSTER_ID
            LEFT JOIN (SELECT ROSTER_ID,SUM(AMOUNT) AS TTLAMOUNT FROM route_penalty  GROUP BY ROSTER_ID) PY ON PY.ROSTER_ID = R.ROSTER_ID
            LEFT JOIN route_escorts RE ON RE.ROSTER_ID = R.ROSTER_ID AND RE.BRANCH_ID = R.BRANCH_ID AND RE.STATUS NOT IN (5,6)
            LEFT JOIN escorts ES ON ES.ESCORT_ID = RE.ESCORT_ID
            LEFT JOIN misreport MIS ON MIS.id = R.MIS_ID
            LEFT JOIN reason_master MISRM on MIS.TARIFF_ID = MISRM.REASON_ID
            LEFT JOIN driver_billing_summary dbs on dbs.ROSTER_ID = R.ROSTER_ID and dbs.ACTIVE = 1
                     LEFT JOIN locations L on L.LOCATION_NAME =  if(R.TRIP_TYPE = 'P',R.START_LOCATION,R.END_LOCATION)  AND L.DIVISION_ID=B.DIVISION_ID
                     LEFT JOIN  approve_distances AD  on AD.LOCATION_ID = L.LOCATION_ID AND AD.BRANCH_ID=$this->BRANCHID
 
            WHERE  R.BRANCH_ID=$this->BRANCHID AND R.ACTIVE = 1 AND R.isNonBillable= 1 $vehicle_condition
            AND MIS.MONTH = $reportmonth AND MIS.YEAR = $reportyear
            AND R.ROSTER_STATUS in ($RS_TOTALTRIPCLOSE,$RS_TOTALMANUALTRIPCLOSE,$RS_TOTALTRIPSHEETACCEPT,$RS_TOTALDELAYROUTES)
            $tariff_condition
            ORDER BY R.ROSTER_ID DESC";
        $result = DB::connection("$dbname")->select($query);
 
        $header = ['Roster Id ','Route Id','Branch','Schedule Count','Actual Count','Noshow Count','Travel Type','Cab No','Cab Model','Capacity','Vendor Name','Start Location','End Location','OTP Location','Login/Logout Date','Login/Logout Time','Actual Start Time','Actual End Time','Time Difference','Trip KM'];
           
        $data = array(
            $header
        );
        if(count($result) > 0)
        {
            foreach($result as $row)
            {
                $row_data[] = $row->ROSTER_ID;
                $row_data[] = $row->ROUTE_ID;
                $row_data[] = $row->BRANCH_NAME;
 
                $row_data[] = $row->TTLEMPCNT;
                $row_data[] = $row->TRAVELEMPCNT;
                $row_data[] = $row->EMPNOSHOWCNT;
                $row_data[] = $row->TRIP_TYPE;
                $row_data[] = $row->VEHICLE_REG_NO;
                $row_data[] = $row->MODEL;
                $row_data[] = $row->CAPACITY;
                $row_data[] = $row->VENDORNAME;
                $row_data[] = $row->START_LOCATION;
                $row_data[] = $row->END_LOCATION;
                $row_data[] = $row->OTPLOCATION;
                $row_data[] = $row->INOUT_DATE;
                $row_data[] = $row->INOUT_TIME;
                $row_data[] = $row->ACTUAL_START_TIME;
                $row_data[] = $row->ACTUAL_END_TIME;
                $row_data[] = $row->time_diff;
                $row_data[] = $row->GOOGLE_KM; //$row->APPROVED_DISTANCE;
                $data[] = $row_data;
                $row_data = [];
            }    
        }
 
        return $data;  
    }

    public function MisReportDetails($request){
		try {
			$rosterIds = $request->input('rosterIds');
            $mis_id = $request->input('mis_id');

			if($rosterIds != null && $rosterIds != '') {
				$dbname = Auth::user()->dbname;
				$vendor = ""; 
				

				$kmquery = '2 * R.TOTAL_KM,';
				$queryttlamt = '';
				

				$query = "SELECT R.ROSTER_ID,R.ROUTE_ID,RS.TTLEMPCNT,RS.TRAVELEMPCNT,RS.EMPNOSHOWCNT,R.TRIP_TYPE,V.VEHICLE_REG_NO,VM.MODEL,VM.CAPACITY,V.VEHICLE_MODEL_ID,C.VEHICLE_ID,
				R.ACTUAL_START_TIME, R.ACTUAL_END_TIME,	TIME_FORMAT(SEC_TO_TIME(TIME_TO_SEC(TIMEDIFF(R.ACTUAL_END_TIME,R.ACTUAL_START_TIME))), '%H:%i') AS time_diff,
				IF(dbs.APPROVED_KM_TYPE = 'GoogleKm',dbs.GOOGLE_KM,dbs.DEVICE_KM) AS GOOGLE_KM,AD.APPROVED_DISTANCE,
				VE.`NAME` VENDORNAME,R.START_LOCATION,R.END_LOCATION,'--' as OTPLOCATION,
				if(R.TRIP_TYPE = 'P',DATE(R.ESTIMATE_END_TIME),DATE(R.ESTIMATE_START_TIME)) as INOUT_DATE,
				if(R.TRIP_TYPE = 'P',TIME(R.ESTIMATE_END_TIME),TIME(R.ESTIMATE_START_TIME)) AS INOUT_TIME,RM.REASON,$kmquery
				'0' AS TTLKMS,if(TP.ttlcost is NULL,0,TP.ttlcost) AS TOLLCOST,if(PY.TTLAMOUNT is NULL,0,PY.TTLAMOUNT) AS PENALTYCOST,$queryttlamt
				if(RE.ROSTER_ID IS NULL,'--',CONCAT(ES.ESCORT_ID,',',ES.ESCORT_NAME)) ESCORTROUTE,'--' AS ALLSTATUS,

				if(TF.PACKAGE_PRICE IS NULL,0,TF.PACKAGE_PRICE) AS TARIFF,TF.PACKAGE_KMS,TF.EXTRA_KMS_CHARGE,R.ROSTER_STATUS
				,RS.START_LAT,RS.START_LONG,RS.END_LAT,RS.END_LONG 

				FROM rosters R
				INNER JOIN (SELECT EM.*,COUNT(*) AS TTLEMPCNT,
				sum(if(EM.ROSTER_PASSENGER_STATUS & 16,1,0)) AS EMPNOSHOWCNT,
				SUM(if((EM.ROSTER_PASSENGER_STATUS & 32 OR EM.ROSTER_PASSENGER_STATUS & 128),1,0)) AS TRAVELEMPCNT  
				FROM (SELECT RR.ROSTER_ID,RR.EMPLOYEE_ID,RR.ROUTE_ORDER,RR.ROSTER_PASSENGER_STATUS,RR.START_LAT,RR.START_LONG,RR.END_LAT,RR.END_LONG
				FROM rosters RU
				INNER JOIN roster_passengers RR ON RR.ROSTER_ID = RU.ROSTER_ID 
				WHERE RU.BRANCH_ID=$this->BRANCHID AND RU.ACTIVE = 1 AND RR.ACTIVE = 1 AND  RR.ROSTER_ID IN($rosterIds) ORDER BY RR.ROUTE_ORDER DESC)
				EM GROUP BY EM.ROSTER_ID) RS ON RS.ROSTER_ID = R.ROSTER_ID
				INNER JOIN branch B ON B.BRANCH_ID = R.BRANCH_ID
				INNER JOIN vendors VE ON VE.VENDOR_ID = R.VENDOR_ID
				INNER JOIN cab C ON C.CAB_ID = R.CAB_ID
				INNER JOIN vehicles V ON V.VEHICLE_ID = C.VEHICLE_ID
				INNER JOIN vehicle_models VM ON VM.VEHICLE_MODEL_ID = V.VEHICLE_MODEL_ID
				INNER JOIN reason_master RM ON RM.REASON_ID = C.TARIFF_TYPE
				LEFT JOIN tariff TF ON TF.TARIFF_TYPE = C.TARIFF_TYPE AND TF.BRANCH_ID = C.BRANCH_ID 
				AND TF.VENDOR_ID = C.VENDOR_ID AND TF.VEHICLE_MODEL_ID = V.VEHICLE_MODEL_ID
				LEFT JOIN (SELECT ROSTER_ID,SUM(TOLL_CHARGE) as ttlcost FROM toll_payment WHERE ROSTER_ID IN($rosterIds) GROUP BY ROSTER_ID) TP ON TP.ROSTER_ID = R.ROSTER_ID 
				LEFT JOIN (SELECT ROSTER_ID,SUM(AMOUNT) AS TTLAMOUNT FROM route_penalty 
				WHERE ROSTER_ID IN($rosterIds) GROUP BY ROSTER_ID) PY ON PY.ROSTER_ID = R.ROSTER_ID 
				LEFT JOIN route_escorts RE ON RE.ROSTER_ID = R.ROSTER_ID AND RE.BRANCH_ID = R.BRANCH_ID AND RE.STATUS NOT IN (5,6)
				LEFT JOIN escorts ES ON ES.ESCORT_ID = RE.ESCORT_ID
				LEFT JOIN driver_billing_summary dbs on dbs.ROSTER_ID = R.ROSTER_ID and dbs.ACTIVE = 1
						LEFT JOIN locations L on L.LOCATION_NAME =  if(R.TRIP_TYPE = 'P',R.START_LOCATION,R.END_LOCATION)  AND L.DIVISION_ID=B.DIVISION_ID
						LEFT JOIN  approve_distances AD  on AD.LOCATION_ID = L.LOCATION_ID AND AD.BRANCH_ID=$this->BRANCHID
				WHERE  R.BRANCH_ID=$this->BRANCHID AND R.ACTIVE = 1 AND R.isNonBillable= 1 $vendor AND R.ROSTER_ID IN($rosterIds)  ORDER BY R.ROSTER_ID ASC";
				$result = DB::connection("$dbname")->select($query);

				$mis_sql = "select * from misreport where id = '$mis_id'";
				$mis_details = DB::connection("$dbname")->select($mis_sql);
                $total_value = [];
				if(count($mis_details) > 0)
				{
					$mis_data = isset($mis_details[0]) ? $mis_details[0] : [];
					
					if(isset($mis_data))
					{
						$extra_hours_charge = $mis_data->EXTRA_MINUTES_AMOUNT > 0 ? $mis_data->EXTRA_MINUTES_AMOUNT + $mis_data->EXTRA_HOURS_AMOUNT : $mis_data->EXTRA_HOURS_AMOUNT;
						$extra_hours = $mis_data->EXTRA_MINUTES_AMOUNT > 0 ? $mis_data->EXTRA_HOURS + 1 : $mis_data->EXTRA_HOURS;
						$extra_hrs_charge_explaination = $extra_hours_charge > 0 ? "(". $extra_hours . ' * ' .  $mis_data->EXTRA_HRS_CHARGE . ")" : '';

						$extra_km_explain =  $mis_data->EXTRA_KM_AMOUNT > 0 ? "(". $mis_data->EXTRA_KM . ' * ' .  $mis_data->EXTRA_KMS_CHARGE . ")" : '';
						
						$total_value['Package_Kms'] = $mis_data->PACKAGE_KMS;
						$total_value['Package_Hrs'] = $mis_data->PACKAGE_HRS;
						$total_value['Package_Price'] = $mis_data->PACKAGE_PRICE;
						$total_value['Extra_Kms_Charge'] = $mis_data->EXTRA_KM_AMOUNT. ' - ' . $extra_km_explain;
						$total_value['Extra_Hrs_Charge'] = $extra_hours_charge. ' - ' . $extra_hrs_charge_explaination;
						$total_value['Total_Amount'] = $mis_data->TOTAL_AMOUNT;
						$total_value['Total_Hours'] = $mis_data->TOTAL_HOURS;
						$total_value['Total_Kms'] = $mis_data->TOTAL_KMS;
					}
				}

				return response([
					'success' => true,
					'status' => 1,
					'result' => [
                                    'data' => $result,
                                    'total_value' => $total_value
                                ],
					'message' => 'MIS Report Details Fetch Sucessfully!!',
				],200);
			} else {
				return response([
					'success' => true,
					'status' => 1,
					'message' => 'No Records Available!!',
				],200);
			}
		} catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'MIS Report Details Fetch Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ],500);
        }
    }

    public function DriverMisReportGenerateNew(){
        try {
            $dbname = Auth::user()->dbname;
            $BRANCH_ID = Auth::user()->BRANCH_ID;
            $summery_sql = "SELECT REPORT_ID,`MONTH`, DATE_FORMAT(STR_TO_DATE(MONTH, '%m'), '%M') AS 'Month Name' ,`YEAR`,sum(TOTAL_KMS) as 'TOTAL_KMS',sum(TOTAL_AMOUNT) as 'TOTAL_AMOUNT',SEC_TO_TIME(SUM(TIME_TO_SEC(TOTAL_HOURS))) as 'TOTAL_HOURS', SUM(TOLL_AMT) as 'TOTAL_TOLL_AMT', sum(TOTAL_AMOUNT - TOLL_AMT) as 'TOTAL_PACKAGE_AMT'
            FROM misreport WHERE Active = 1 AND BRANCH_ID = '$BRANCH_ID' group by YEAR,MONTH";
            $summery_result = DB::connection("$dbname")->select($summery_sql);

            if (isset($summery_result)){
                return response([
					'success' => true,
					'status' => 1,
					'result' => $summery_result,
					'message' => 'MIS Report Details Fetch Sucessfully!!',
				],200);
            } else {
                return response([
					'success' => true,
					'status' => 1,
					'message' => 'No Result Found',
				],200);
            }
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'MIS Report Details Fetch Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ],500);
        }
    }

    public function DriverMisReportGenerateDetails($request){
        try {
            $year = $request->input('year');
            $month = $request->input('month');

            $dbname = Auth::user()->dbname;
            $BRANCH_ID = Auth::user()->BRANCH_ID;

            $details_sql = "SELECT BR.BRANCH_NAME,rm.REASON as 'TARIFF_ID',mis.REPORT_ID,v.VEHICLE_REG_NO as 'VEHICLE_ID',vm.MODEL as 'VEHICLE_MODEL_ID',
                            mis.id,mis.REPORT_ID,mis.DATE,mis.TOTAL_HOURS,mis.PACKAGE_PRICE,mis.TOTAL_KMS,mis.PACKAGE_HRS,mis.PACKAGE_KMS,mis.EXTRA_KMS_CHARGE,mis.EXTRA_HRS_CHARGE,mis.MINIMUM_MINUTES,mis.MONTH,mis.YEAR,mis.ROSTER_IDS,mis.TOTAL_AMOUNT,mis.EXTRA_HOURS,mis.EXTRA_MINUTES,mis.EXTRA_HOURS_AMOUNT,mis.EXTRA_MINUTES_AMOUNT,mis.EXTRA_KM,mis.EXTRA_KM_AMOUNT,mis.TOLL_AMT    FROM misreport mis    left join reason_master rm on mis.TARIFF_ID = rm.REASON_ID     left join vehicles v on mis.VEHICLE_ID = v.VEHICLE_ID     left join vehicle_models vm on mis.VEHICLE_MODEL_ID = vm.VEHICLE_MODEL_ID 
                            left join branch BR on BR.BRANCH_ID = '$BRANCH_ID' 
                            WHERE mis.Active = 1 
                            AND mis.BRANCH_ID = '$BRANCH_ID'
                            AND mis.YEAR = $year
                            AND mis.MONTH = $month";

            $details_result = DB::connection("$dbname")->select($details_sql);

            return response([
                'success' => true,
                'status' => 1,
                'result' => $details_result,
                'message' => 'MIS Report Details Fetch Sucessfully!!',
            ],200);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'MIS Report Details Fetch Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ],500);
        }
    }
}