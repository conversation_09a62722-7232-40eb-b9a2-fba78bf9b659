<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;

use App\Services\Admin\ReclubService;
use App\Http\Requests\Admin\ReclubRosterRequest;
use App\Http\Requests\Admin\ReclubingAddRequest;
use App\Http\Requests\Admin\ReclubRosterDetailsRequest;
use App\Http\Requests\Admin\ReclubedNewRosterRequest;

use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class ReclubController extends Controller
{
    protected ReclubService $reclubService;

    public function __construct(ReclubService $reclubService)
    {
        $this->reclubService = $reclubService;
    }
    
    public function reclub_roster(): FoundationApplication|Response|ResponseFactory
    {
        return $this->reclubService->reclub_roster();
    }
 
    public function reclub_roster_details(ReclubRosterDetailsRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->reclubService->reclub_roster_details($request);
    }
 
    public function reclubing_add(ReclubingAddRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->reclubService->reclubing_add($request);
    }
   
    public function reclubed_new_roster(ReclubedNewRosterRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->reclubService->reclubed_new_roster($request);
        
    }
   
    
 
}
