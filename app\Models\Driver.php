<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Crypt;

class Driver extends Model
{
    use HasFactory;

    protected $table = 'drivers';
    protected $primaryKey = 'DRIVERS_ID';
	public $timestamps = false;
	
	protected $appends = ['DRIVERS_ID_crypt', 'image_path'];
	
	protected $fillable = [
        'DRIVERS_NAME',
        'LAST_NAME',
        'DRIVERS_ADRESS',
        'DRIVERS_ADDR_LAT',
        'DRIVERS_ADDR_LONG',
        'DRIVER_LOC_LAT',
        'DRIVER_LOC_LONG',
        'DRIVER_MOBILE',
        'DRIVER_LICENSE',
        'LICENCE_EXPIRY',
        'BADGE_EXPIRY',
        'COMPLIANT_STATUS',
        'REMARKS',
        'MEDICAL_STATUS',
        'DRIVER_IMAGE',
        'LOCATION_NAME',
        'SHIFT_IN_TIME',
        'SHIFT_OUT_TIME',
        'ACTIVE',
        'CREATED_BY',
        'created_at',
        'UPDATED_BY',
        'updated_at',
        'ORG_ID',
    ];


  public function getImagePathAttribute(): ?string
    {
        if ($this->DRIVER_IMAGE && $this->DRIVER_IMAGE !== '--') 
		{
            return asset('driver-images/' . $this->DRIVER_IMAGE);
        }
        return null;
    }
	
	public function getDRIVERSIDCryptAttribute(): string
    {
        return Crypt::encryptString($this->DRIVERS_ID);
    }
}
