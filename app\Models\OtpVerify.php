<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class OtpVerify extends Model
{
    use HasFactory;

    protected $table = 'otp_verification';
    protected $primaryKey='OTP_VERIFICATION_ID';

    public $timestamps = false;

    protected $fillable = [
        'MOBILE_NO',
        'ROSTER_PASSENGER_ID',
        'OTP',
        'VERIFIED_STATUS',
        'SMS_RESPONSE',
        'OTP_CATEGORY',
        'CREATED_BY',
        'CREATED_DATE',
        'UPDATED_BY',
        'updated_at',
        'token_id',
        'request_coming_from',
    ];
}
