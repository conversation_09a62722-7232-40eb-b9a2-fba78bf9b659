<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class RouteDistance extends Model
{
    use HasFactory;

    protected $table = 'route_distance';
    protected $primaryKey = 'DISTANCE_ID';

    public $timestamps = false;

    protected $fillable = [
        'DISTANCE_ID',
        'ROSTER_ID',
        'TOLL_ROUTE_STATUS',
        'TRIP_TYPE',
        'ROUTE_DISTANCE',
        'ROUTE_TRAVELTIME',
        'START_DISTANCE',
        'START_TRAVELTIME',
        'ROSTER_STATUS',
        'ESTIMATE_START_TIME',
        'ESTIMATE_END_TIME',
        'EMP_LATLONG',
        'created_by',
        'updated_by',
        'updated_at'        
    ];
}
