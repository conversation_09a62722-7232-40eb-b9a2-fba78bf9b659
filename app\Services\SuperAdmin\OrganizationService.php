<?php

namespace App\Services\SuperAdmin;

use App\Helpers\CommonFunction;
use App\Helpers\MyHelper;
use App\Http\Controllers\ElasticController;
use App\Models\Organization;
use App\Models\User;
use App\Models\property;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Response;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;

class OrganizationService
{
    protected CommonFunction $commonFunction;

    public function __construct(CommonFunction $commonFunction)
    {
        $this->commonFunction = $commonFunction;
    }

    public function indexOrganization(): FoundationApplication|Response|ResponseFactory
    {
        try {
            $organizations = Organization::where('ACTIVE', MyHelper::$RS_ACTIVE)->get();
            return response([
                'success' => true,
                'organizations' => $organizations,
            ]);
        } catch (\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'message' => 'Organization Index Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function storeOrganization($request): FoundationApplication|Response|ResponseFactory
    {
        try {
            DB::beginTransaction();

            $organization = Organization::create([
                'NAME' => $request->input('name'),
                'LOCATION' => $request->input('location'),
                'LAT' => $request->input('lat'),
                'LONG' => $request->input('long'),
                'CREATED_BY' => Auth::user()->id,
                'ENTITY_PROPERTIE' => MyHelper::$RS_ACTIVE,
                'ACTIVE' => MyHelper::$RS_ACTIVE,
            ]);

            DB::commit();

            return response([
                'success' => true,
                'message' => 'Organization Created Successfully',
                'organization' => $organization,
            ]);
        } catch (\Throwable|\Exception $e) {
            DB::rollBack();
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'validation_controller' => true,
                'message' => 'Organization Created Unsuccessfully',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function paginationOrganization($request, bool $active = true): FoundationApplication|Response|ResponseFactory
    {
        try {
            $perPage = $request->input('per_page', MyHelper::$PAGINATION_PER_PAGE);
            $activeStatus = $active ? MyHelper::$RS_ACTIVE : MyHelper::$RS_INACTIVE;

            $organization = Organization::query()
                ->where('ACTIVE', $activeStatus);

            $filterModel = $request->input('filterModel');
            if ($filterModel) {
                foreach ($filterModel as $field => $filter) {
                    if (isset($filter['filter']) && $filter['filter'] !== '') {
                        $value = $filter['filter'];
                        switch ($field) {
                            case 'NAME':
                                $organization->where('NAME', 'like', "%{$value}%");
                                break;
                            case 'LOCATION':
                                $organization->where('LOCATION', 'like', "%{$value}%");
                                break;
                        }
                    }
                }
            }

            if ($request->has('orderBy') && !empty($request->input('orderBy'))) {
                $orderBy = $request->input('orderBy');
                $order = $request->input('order', 'asc');
                $organization->orderBy($orderBy, $order);
            } else {
                $organization->orderBy('created_at', 'desc');
            }

            $organization->with([
                'createdBy:id,name',
                'updatedBy:id,name'
            ]);

            if ($perPage === MyHelper::$PAGINATION_PER_PAGE_ALL) {
                $paginatedOrganizations = $organization->paginate($organization->count());
            } else {
                $perPage = is_numeric($perPage) ? (int)$perPage : MyHelper::$PAGINATION_PER_PAGE;
                $paginatedOrganizations = $organization->paginate($perPage);
            }

            return response([
                'success' => true,
                'organizations' => $paginatedOrganizations,
            ]);
        } catch (\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'message' => 'Organization Pagination Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function editOrganization($organizationIdCrypt): FoundationApplication|Response|ResponseFactory
    {
        try {
            $organizationId = Crypt::decryptString($organizationIdCrypt);
            $organization = Organization::with([
                'createdBy:id,name',
                'updatedBy:id,name'
            ])->where('ORGANIZATIONID', $organizationId)
                ->where('ACTIVE', MyHelper::$RS_ACTIVE)
                ->firstOrFail();
            return response([
                'success' => true,
                'organization' => $organization,
            ]);
        } catch (\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'message' => 'Organization Edit Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function updateOrganization($request, $organizationIdCrypt): FoundationApplication|Response|ResponseFactory
    {
        try {
            DB::beginTransaction();
            $organizationId = Crypt::decryptString($organizationIdCrypt);
            $organization = Organization::where('ORGANIZATIONID', $organizationId)
                ->where('ACTIVE', MyHelper::$RS_ACTIVE)
                ->firstOrFail();
            $organization->update([
                'NAME' => $request->input('name'),
                'LOCATION' => $request->input('location'),
                'LAT' => $request->input('lat'),
                'LONG' => $request->input('long'),
                'UPDATED_BY' => Auth::user()->id,
            ]);

            DB::commit();

            return response([
                'success' => true,
                'message' => 'Organization Updated Successfully',
            ]);
        } catch (\Throwable|\Exception $e) {
            DB::rollBack();
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'message' => 'Organization Update Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function deleteOrganization($request, $organizationIdCrypt): FoundationApplication|Response|ResponseFactory
    {
        try {
            DB::beginTransaction();
            $organizationId = Crypt::decryptString($organizationIdCrypt);
            $organization = Organization::where('ACTIVE', MyHelper::$RS_ACTIVE)->findOrFail($organizationId);
            $organization->update([
                'REMARKS' => $request->input('remark'),
                'ACTIVE' => MyHelper::$RS_INACTIVE,
                'UPDATED_BY' => Auth::user()->id,
            ]);

            DB::commit();

            return response([
                'success' => true,
                'message' => 'Organization Deleted Successfully',
            ]);
        } catch (\Throwable|\Exception $e) {
            DB::rollBack();
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'message' => 'Organization Delete Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function dataForCreateOrganization(): FoundationApplication|Response|ResponseFactory
    {
        return response([
            'success' => true,
            'data_for_create' => "No Data",
        ]);
    }

    public function Register(): FoundationApplication|Response|ResponseFactory
    {
        try {
            $admin = 'SUPERADMIN';
            $RS_ACTIVE = MyHelper::$RS_ACTIVE;
            $branch_id=Auth::user()->BRANCH_ID;
            if (Auth::user()->user_type == $admin) {
                $user_type = DB::table("reason_master")->select("REASON", "REASON_ID")
                                ->where("ACTIVE", "=", 1)->where("CATEGORY", "=", 'WebUserType')
                                ->where("BRANCH_ID", "=", $branch_id)
                                ->get();
                $branch = DB::table("branch")->select("BRANCH_ID", "BRANCH_NAME")
                                ->where("ACTIVE", "=", $RS_ACTIVE)
                                ->get();
                
                return response([
                    'success' => true,
                    'status' => 1,
                    'user_type' => $user_type,
                    'branch' => $branch,
                    'message' => 'Register Fetch Successfully',
                ],200);
            }else {
                return response([
                    'success' => true,
                    'status' => 0,
                    'message' => 'Unauthorized Access',
                ], 401);
            }
        } catch (\Throwable|\Exception $e) {
            DB::rollBack();
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'message' => 'Register User Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function GetRegister()
    {
        try {
            $RS_ACTIVE = MyHelper::$RS_ACTIVE;
			
			$data = DB::table("users as U")->select("U.name","U.id","U.BRANCH_ID","U.user_type","U.vendor_id","U.password","B.BRANCH_NAME")
			->join("branch as B","B.BRANCH_ID","=","U.BRANCH_ID")
			->where("active_status", "=", $RS_ACTIVE)->orderby('U.BRANCH_ID','ASC')->orderby('U.USER_TYPE','ASC')->get();
           
            return response([
               'success' => true,
                'data' => $data,
               'message' => 'Getregister Successfully',
            ],200);
        } catch (\Throwable|\Exception $e) {
            DB::rollBack();
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'message' => 'Getregister Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function BranchWiseVendor($request): FoundationApplication|Response|ResponseFactory
    {
        try {
            $branch_id=$request->branch_id;
            $res=DB::table("vendors")->select("VENDOR_ID","NAME")->where("BRANCH_ID","=",$branch_id)->where("ACTIVE","=",1)->get();
            if(count($res)>0)
            {
                return response([
                    'success' => true,
                    'status' => 1,
                    'vendor_list' => $res,
                    'message' => 'Vendor List Fetch Successfully',
                ],200);
            }else{
                return response([
                    'success' => true,
                    'status' => 0,
                   'message' => 'No Vendor found',
                ], 200);
            }
        } catch (\Throwable|\Exception $e) {
            DB::rollBack();
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'message' => 'Branchwisevendor Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function PostRegister($request): FoundationApplication|Response|ResponseFactory
    {
        try {
            $RS_ACTIVE = MyHelper::$RS_ACTIVE;
            $id = Auth::user()->id;
            $date = Carbon::now();
            $user_type = stripslashes(trim($request->user_type));
            $branch_id = stripslashes(trim($request->branch_id));
            $user_name = strtoupper(stripslashes(trim($request->name)));
            $email = stripslashes(trim($request->email));
            $password = stripslashes(trim($request->password));
            $selected_vendor_id = $request->vendor_id;
            $confirm_pwd = stripslashes(trim($request->password_confirmation));
            
            if($password!=$confirm_pwd)
            {
                return redirect("register")->with("status", "Does Not Match Password");
            }
            if ($user_type == 'Admin' || $user_type == 'Escort') {
                $vendor_id = 0;
                $arr = array("BRANCH_ID" => $branch_id, "name" => $user_name, "password" => bcrypt($password), "email" => $email, "user_type" => strtoupper($user_type), "vendor_id" => $vendor_id, "active_status" => $RS_ACTIVE, "CREATED_BY" => $id, "CREATED_DATE" => $date->format("Y-m-d H:i:s"),"created_password"=>$password);

                $result = User::insert($arr);
            } elseif ($user_type == 'Vendor' || $user_type == 'User' ) {
                $arr = array("BRANCH_ID" => $branch_id, "name" => $user_name, "password" => bcrypt($password), "email" => $email, "user_type" => strtoupper($user_type), "vendor_id" => $selected_vendor_id, "active_status" => $RS_ACTIVE, "CREATED_BY" => $id, "CREATED_DATE" => $date->format("Y-m-d H:i:s"),"created_at"=>date("Y-m-d H:i:s"),"updated_at"=>date("Y-m-d H:i:s"),"created_password"=>$password);
                $result = User::insert($arr);
            }
                /*  log */
                $date_f = $this->commonFunction->date_format_add();
                $elastic=new ElasticController();
                $log_arr=array("BRANCH_ID"=>$branch_id,"USER_TYPE"=>$user_type,"VENDOR_ID"=>$selected_vendor_id,"ACTION"=>'Register',"PROCESS_DATE"=>$date_f,"CATEGORY"=>'Register',"USER_ID"=>$id,"REGISTER_USER_NAME"=>$user_name,"REGISTER_PASSWORD"=>$password,"ENCRYPT_PASSWORDS"=> bcrypt($password));
                //$ret=$elastic->insertWebLogs($log_arr);
                /*  log End */
            
            if ($result == 1) {
                return response([
                    'success' => true,
                    'status' => 1,
                   'message' => 'User Inserted Successfully',
                ], 200);
            }else{
                return response([
                    'success' => true,
                    'status' => 0,
                   'message' => 'User Insertion Failed',
                ], 200);
            }
        } catch (\Throwable|\Exception $e) {
            DB::rollBack();
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'message' => 'Postregister Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function UpdateRegister($request): FoundationApplication|Response|ResponseFactory
    {
        try {
            $user_id=Auth::user()->id;
            $previous_password=Auth::user()->password;
            $branch_id = Auth::user()->BRANCH_ID;
            $id=stripslashes(trim($request->users_id));
            $edit_password=stripslashes(trim($request->edit_password));
            $array=array("password"=>bcrypt($edit_password),"created_password"=>$edit_password);
        
            $update=User::where('id',"=",$id)->update($array);
            /*  log */
            $date_f = $this->commonFunction->date_format_add();
            $log_arr=array("BRANCH_ID"=>$branch_id,"ACTION"=>'UPDATE REGISTERED',"PROCESS_DATE"=>$date_f,"CATEGORY"=>'Register',"USER_ID"=>$user_id,"PREVIOUS_PASSWORDS"=>$previous_password,"UPDATE_PASSWOEDS"=>$edit_password,"ENCRYPT_PASSWORDS"=> bcrypt($edit_password));
            //$ret = $this->commonFunction->weblogs($log_arr);
            /*  log End */
            if($update==1){
                return response([
                    'success' => true,
                    'status' => 1,
                   'message' => 'User Updated Successfully',
                ], 200);
            }else{
                return response([
                    'success' => true,
                    'status' => 0,
                   'message' => 'User Updation Failed',
                ], 200);
            }
        } catch (\Throwable|\Exception $e) {
            DB::rollBack();
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'message' => 'Updateregister Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function GetProperty(): FoundationApplication|Response|ResponseFactory
    {
        try {
            $RS_ACTIVE = MyHelper::$RS_ACTIVE;
            $branch_id=Auth::user()->BRANCH_ID;
			$propertie = DB::table("properties as P")->select("P.*")->where("ACTIVE","=",$RS_ACTIVE)->where("BRANCH_ID","=",$branch_id)->orderBy('P.updated_at','DESC')->get();
		
            return response([
                'success' => true,
                'status' => 1,
                'property' => $propertie,
                'message' => 'Property Fetch Successfully',
            ], 200);
        } catch (\Throwable|\Exception $e) {
            DB::rollBack();
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'message' => 'GetProperty Fetch Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function AddProperty($request): FoundationApplication|Response|ResponseFactory
    {
        try {
            $propertie = new property;

            $id = Auth::user()->id;
            $branch_id=Auth::user()->BRANCH_ID;
            $RS_ACTIVE = MyHelper::$RS_ACTIVE;
            $name=$request->PROPERTIE_NAME;
            $value=$request->PROPERTIE_VALUE;

            $query=DB::table("properties")->select("PROPERTIES_ID")->where("PROPERTIE_NAME","=",$name)->where("PROPERTIE_VALUE","=",$value)
            ->where("ACTIVE","=",$RS_ACTIVE)->get();
            $cnt=count($query);

            if($cnt>0)
            {
                return response([
                    'success' => true,
                    'status' => 0,
                    'message' => 'Already Exist This Property',
                ], 200);
                //return redirect('property')->with('error', "Already Exist This Property");
            }
            else
            {
                $propertie->BRANCH_ID = $branch_id;
                $propertie->PROPERTIE_NAME = $request->PROPERTIE_NAME;
                $propertie->PROPERTIE_VALUE = $request->PROPERTIE_VALUE;
                $propertie->CREATED_BY = $id;
                $propertie->ACTIVE = $RS_ACTIVE;
                $add=$propertie->save();
                /* LOG */	
                    $date_f = $this->commonFunction->date_format_add();
                    $log_arr=array("BRANCH_ID" => $branch_id, "PROPERTIE_NAME" => $request->PROPERTIE_NAME, "PROPERTIE_VALUE" => $request->PROPERTIE_VALUE, "CREATED_BY" => $id,"PROCESS_DATE"=>$date_f,"ACTION"=>"Property Added","CATEGORY"=>'Organization Form',"ADD_SUCCESS"=>$add);
                    //$res=$obj3->weblogs($log_arr);
                /* LOG */
                return response([
                    'success' => true,
                    'status' => 1,
                    'message' => 'Property Inserted Successfully!',
                ], 200);
                //return redirect('property')->with('status', "Successfully Inserted!");
            }
        } catch (\Throwable|\Exception $e) {
            DB::rollBack();
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'message' => 'AddProperty Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}
