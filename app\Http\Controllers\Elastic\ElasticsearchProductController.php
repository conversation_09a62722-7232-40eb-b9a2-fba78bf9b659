<?php

namespace App\Http\Controllers\Elastic;

use App\Http\Controllers\Controller;
use App\Services\Elastic\ElasticsearchProductService;
use Illuminate\Http\Request;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Response;

class ElasticsearchProductController extends Controller
{
    protected ElasticsearchProductService $elasticsearchProductService;

    public function __construct(ElasticsearchProductService $elasticsearchProductService)
    {
        $this->elasticsearchProductService = $elasticsearchProductService;
    }

    public function createIndex(): FoundationApplication|Response|ResponseFactory
    {
        return $this->elasticsearchProductService->createIndex();
    }

    public function index(): FoundationApplication|Response|ResponseFactory
    {
        return $this->elasticsearchProductService->indexProducts();
    }

    public function show($id): FoundationApplication|Response|ResponseFactory
    {
        return $this->elasticsearchProductService->showProduct($id);
    }

    public function store(Request $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->elasticsearchProductService->storeProduct($request);
    }

    public function update(Request $request, $id): FoundationApplication|Response|ResponseFactory
    {
        return $this->elasticsearchProductService->updateProduct($request, $id);
    }

    public function destroy($id): FoundationApplication|Response|ResponseFactory
    {
        return $this->elasticsearchProductService->deleteProduct($id);
    }

}
