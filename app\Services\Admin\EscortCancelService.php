<?php

namespace App\Services\Admin;

use App\Helpers\CommonFunction;
use App\Helpers\MyHelper;
use Carbon\Carbon;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use App\Models\property;
use App\Models\Branch;
use App\Models\Roster;
use App\Models\RouteEscorts;

class EscortCancelService
{
    protected CommonFunction $commonFunction;
    protected ?Branch $currentBranch = null;

    public function __construct(CommonFunction $commonFunction)
    {
        $this->commonFunction = $commonFunction;
    }


    public function fetch_EscortCancel_RouteData(bool $active = true): FoundationApplication|Response|ResponseFactory
    {

        try {

            $branch_id = Auth::user()->BRANCH_ID;
            $user_type = Auth::user()->user_type;
            $vendor_id = Auth::user()->vendor_id;

            if ($user_type == MyHelper::$ADMIN) {
                $vendorid = "";
            } else {
                $vendorid = $vendor_id;
            }

            $RS_ACTIVE = MyHelper::$RS_ACTIVE;

            $curdatetime = date('Y-m-d H:i:s');
            $predatetime = date('Y-m-d H:i:s', strtotime('-12 hours', strtotime($curdatetime)));

            $data = Roster::query()
                ->select(
                    'RE.ROUTE_ESCORT_ID',
                    'RE.ROSTER_ID',
                    'RE.EMPLOYEE_ID',
                    'RE.STATUS',
                    'RE.created_at',
                    'rosters.ROUTE_ID',
                    'E.NAME',
                    'E.MOBILE',
                    'rosters.TRIP_TYPE',
                    'rosters.ESTIMATE_END_TIME',
                    'rosters.ESTIMATE_START_TIME',
                    DB::raw("if(rosters.TRIP_TYPE='P',rosters.ESTIMATE_END_TIME,rosters.ESTIMATE_START_TIME) as in_out_time")
                )
                ->join('route_escorts as RE', 'RE.ROSTER_ID', '=', 'rosters.ROSTER_ID')
                ->join('employees as E', function ($join) use ($branch_id) {
                    $join->on('E.EMPLOYEES_ID', '=', 'RE.EMPLOYEE_ID')
                        ->where('E.BRANCH_ID',  '=', $branch_id);
                })
                ->where('rosters.BRANCH_ID', $branch_id)
                ->where('rosters.ACTIVE', $RS_ACTIVE)
                ->whereIn('RE.STATUS', ['1', '3', '4'])
                ->whereRaw("((rosters.ESTIMATE_END_TIME)>='$predatetime' or (rosters.ESTIMATE_START_TIME)>='$predatetime')")   
                ->when($vendorid, function ($query, $vendorid) {
                    return $query->where('rosters.VENDOR_ID', $vendorid);
                })
                ->get();

            return response([
                'success' => true,
                'status' => 3,
                'escortcancel_routedata' => $data
            ]);
        } catch (\Throwable | \Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'Escort Route Cancel Unsuccessful' : 'Escort Route Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ], 500);
        }
    }



    public function fetch_EscortCancel_RouteDetails($request, bool $active = true): FoundationApplication|Response|ResponseFactory
    {
        try {

            $perPage = $request->input('per_page', MyHelper::$PAGINATION_PER_PAGE);
            $branch_id = Auth::user()->BRANCH_ID;
            $user_type = Auth::user()->user_type;
            $vendor_id = Auth::user()->vendor_id;
            $route_escort_id = $request->route_escort_id;

            if ($user_type == MyHelper::$ADMIN) {
                $vendorid = "";
            } else {
                $vendorid = $vendor_id;
            }

            $RS_ACTIVE = MyHelper::$RS_ACTIVE;


            $data = Roster::query()
                ->select(
                    'RE.ROUTE_ESCORT_ID',
                    'RE.ROSTER_ID',
                    'RE.EMPLOYEE_ID',
                    'RE.STATUS',
                    'RE.created_at',
                    'rosters.ROUTE_ID',
                    'E.NAME',
                    'E.GENDER',
                    'E.MOBILE',
                    'rosters.TRIP_TYPE',
                    'rosters.ESTIMATE_END_TIME',
                    'rosters.ESTIMATE_START_TIME',
                    DB::raw("if(rosters.TRIP_TYPE='P',rosters.ESTIMATE_END_TIME,rosters.ESTIMATE_START_TIME) as in_out"),
                    'ES.ESCORT_NAME',
                )
                ->join('route_escorts as RE', 'RE.ROSTER_ID', '=', 'rosters.ROSTER_ID')
                ->join('employees as E', function ($join) use ($branch_id) {
                    $join->on('E.EMPLOYEES_ID', '=', 'RE.EMPLOYEE_ID')
                        ->where('E.BRANCH_ID',  '=', $branch_id);
                })
                ->leftJoin('escorts as ES', 'ES.ESCORT_ID', '=', 'RE.ESCORT_ID')
                ->where('rosters.BRANCH_ID', $branch_id)
                ->where('rosters.ACTIVE', $RS_ACTIVE)
                ->where('RE.ROUTE_ESCORT_ID', $route_escort_id)
                ->whereIn('RE.STATUS', ['1', '3', '4'])

                ->when($vendorid, function ($query, $vendorid) {
                    return $query->where('rosters.VENDOR_ID', $vendorid);
                });


            $filterModel = $request->input('filterModel');

            if ($filterModel) {
                foreach ($filterModel as $field => $filter) {
                    if (isset($filter['filter']) && $filter['filter'] !== '') {
                        $value = $filter['filter'];
                        $type = $filter['type'];

                        switch ($field) {
                            case 'EMPLOYEE_ID':
                                $data->where('E.EMPLOYEE_ID', 'like', "%{$value}%");
                                break;
                        }
                    }
                }
            }


            if ($request->has('orderBy') && !empty($request->input('orderBy'))) {
                $orderBy = $request->input('orderBy');
                $order = $request->input('order', 'DESC');
                $data->orderBy($orderBy, $order);
            } else {
                $data->orderBy('rosters.ROSTER_ID', 'DESC');
            }


            if ($perPage === MyHelper::$PAGINATION_PER_PAGE_ALL) {
                $paginateddata = $data->paginate($data->count());
            } else {
                $perPage = is_numeric($perPage) ? (int)$perPage : MyHelper::$PAGINATION_PER_PAGE;
                $paginateddata = $data->paginate($perPage);
            }

            return response([
                'success' => true,
                'status' => 3,
                'escortcancel_routedetails' => $paginateddata,
            ]);
        } catch (\Throwable | \Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'Escort Route Cancel Unsuccessful' : 'Escort Route Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ], 500);
        }
    }


    public function cancel_EscortRoute($request, bool $active = true): FoundationApplication|Response|ResponseFactory
    {
        try {
            $date = Carbon::now();
            $userid = Auth::user()->id;
            $route_escort_id = $request->route_escort_id;


            $arr = array("STATUS" => 6, "UPDATED_BY" => $userid, "updated_at" => $date->format("Y-m-d H:i:s"));
            RouteEscorts::where("ROUTE_ESCORT_ID", "=", $route_escort_id)->update($arr);

            return response([
                'success' => true,
                'status' => 3,
                'message' => "Route Escort Cancelled successfully",
            ]);
        } catch (\Throwable | \Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'Route Escort Cancelled Unsuccessful' : 'Route Escort Cancelled Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ], 500);
        }
    }
}
