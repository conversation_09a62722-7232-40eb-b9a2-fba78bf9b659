<?php

namespace App\Http\Requests\SuperAdmin;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Contracts\Validation\Validator;

class UpdateRegisterRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'users_id' => 'required',
            'edit_password' => ['required','min:8'],
            'edit_password_confirmation' => ['required','same:edit_password'],
        ];
    }

    public function messages()
    {
        return [
            'users_id.required' => 'User ID is required',
            'edit_password.required' => 'Password is required',
            'edit_password_confirmation.required' => 'Confirm Password is required',
            'edit_password.min' => 'Password must be at least 8 characters long',
            'edit_password_confirmation.same' => 'Password and Confirm Password must match',
        ];
    }

    public function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json([
           'success' => false,
            'validation_error' => true,
            'status' => 0,
            'data' => $validator->errors()
        ]));
    }
}
