<?php

namespace App\Services\Admin;

use App\Helpers\CommonFunction;
use App\Helpers\MyHelper;
use Validator;
use Redirect;

use Carbon\Carbon;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Crypt;

use Illuminate\Http\Request;

use App\Http\Controllers\ElasticController;


class UploadFormatService
{
    protected CommonFunction $commonFunction;
     

    public function __construct(CommonFunction $commonFunction)
    {
        $this->commonFunction = $commonFunction;
    }
     
    public function dataforcreate(): FoundationApplication|Response|ResponseFactory
    {
       

        try {
            $user = Auth::user();
            $RS_ACTIVE = MyHelper::$RS_ACTIVE;
            $branchId = $user->BRANCH_ID;
            $vendor_id = $user->vendor_id;
            $curdate = date("Y-m-d");
			
            $reason=DB::table("reason_master")->select('REASON','REASON_ID')
			->where([["active","=",$RS_ACTIVE],["CATEGORY","=",'WebOverSpeed'],["BRANCH_ID","=",$branchId]])->get();
               
                $roster_format=asset('storage/excel/fms_pickup_rost.csv');
                $employee_format=asset('storage/excel/employee_master.csv');
                $vehicle_format=asset('storage/excel/vehicle_master.csv');
                $driver_format=asset('storage/excel/driver_master.csv');
                $device_format=asset('storage/excel/device_master.csv');
                $sim_format=asset('storage/excel/sim_master.csv');
                $location_format=asset('storage/excel/location_master.csv');
                return response([
                'success' => true,
                'status' => 3,
                'rosterFormat' => $roster_format,
                'employeeFormat' => $employee_format,
                'vehicleFormat' => $vehicle_format,
                'driverFormat' => $driver_format,
                'deviceFormat' => $device_format,
                'simFormat' => $sim_format,
                'locationFormat' => $location_format,
                
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Data For Create Roster Upload Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ],500);
        }
    }
    
    
}
