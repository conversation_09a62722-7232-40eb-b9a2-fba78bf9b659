<?php
namespace App\Services\Admin;

use App\Helpers\CommonFunction;
use App\Helpers\MyHelper;
use App\Models\Branch;
use Carbon\Carbon;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;


class RealTimeTrackingService
{
    protected CommonFunction $commonFunction;
    protected ?Branch $currentBranch = null;

    public function __construct(CommonFunction $commonFunction)
    {
        $this->commonFunction = $commonFunction;
    }

    public function Index()
    {
        try {
            $curdatetime = date('Y-m-d H:i:s');
            $TRIP_EXECUTIVE_STSATUS = MyHelper::$RS_TOTALEXECUTE;       
            $RS_ACTIVE = MyHelper::$RS_ACTIVE;

            $BRANCH_ID = Auth::user()->BRANCH_ID;	
            $user_type = Auth::user()->user_type;
            $VENDOR_ID =Auth::user()->vendor_id;
            
            if($user_type == "VENDOR"){			    
                    $Cond=" AND R.VENDOR_ID='".$VENDOR_ID."'";
            }else{
                    $Cond = "";
            }
            
            $sel_sql = "SELECT RP.ROSTER_PASSENGER_ID,R.ROSTER_ID,R.ROUTE_ID,R.TRIP_TYPE,R.START_LOCATION,R.END_LOCATION,V.VEHICLE_REG_NO,VM.MODEL,D.DRIVER_MOBILE,VM.CAPACITY,B.BRANCH_NAME,B.LOCATION, 
                        VE.`NAME` as VENDOR_NAME,E.`NAME` as EMPLOYEE_NAME,RP.EMPLOYEE_ID,E.PROJECT_NAME,L.LOCATION_NAME,R.VENDOR_ID,R.CAB_ID, 
                        if(R.TRIP_TYPE = 'P',R.ESTIMATE_END_TIME,R.ESTIMATE_START_TIME) as LOGIN_DATETIME,(R.PASSENGER_ALLOT_COUNT + R.PASSENGER_CLUBING_COUNT) AS EMP_COUNT ,                    
                        RP.ROSTER_PASSENGER_STATUS, if(R.TRIP_TYPE = 'P',R.ESTIMATE_START_TIME,R.ESTIMATE_END_TIME) as SCHEDULE_CABALLOT, 
                        R.CAB_ALLOT_TIME, if(R.TRIP_TYPE = 'P',TIMEDIFF(R.CAB_ALLOT_TIME,R.ESTIMATE_START_TIME),
                        TIMEDIFF(R.CAB_ALLOT_TIME,R.ESTIMATE_END_TIME)) AS DELAYALLOT_TIME, RP.ESTIMATE_START_TIME, 
                        if(R.TRIP_TYPE = 'P',RP.DRIVER_ARRIVAL_TIME,R.ACTUAL_START_TIME) as DRIVER_ARRIVAL_TIME,RP.ACTUAL_START_TIME,RP.DRIVER_ARRIVAL_TIME,
                        R.ACTUAL_END_TIME,L.LATITUDE,L.LONGITUDE,B.LAT ,B.`LONG`,RP.ACTUAL_END_TIME AS ACTUAL_END_TIME1  FROM roster_passengers RP 
                        INNER JOIN rosters R ON R.ROSTER_ID = RP.ROSTER_ID AND R.ROSTER_STATUS IN($TRIP_EXECUTIVE_STSATUS)
                        INNER JOIN branch B ON B.BRANCH_ID = R.BRANCH_ID 
                        INNER JOIN vendors VE ON VE.VENDOR_ID = R.VENDOR_ID 
                        INNER JOIN cab C ON C.CAB_ID = R.CAB_ID 
                        INNER JOIN drivers D ON D.DRIVERS_ID = C.DRIVER_ID
                        INNER JOIN vehicles V ON V.VEHICLE_ID = C.VEHICLE_ID 
                        INNER JOIN vehicle_models VM ON VM.VEHICLE_MODEL_ID = V.VEHICLE_MODEL_ID 
                        INNER JOIN employees E ON E.EMPLOYEES_ID = RP.EMPLOYEE_ID AND E.BRANCH_ID = '$BRANCH_ID'
                        INNER JOIN locations L ON L.LOCATION_ID = E.LOCATION_ID 
                        WHERE R.BRANCH_ID='$BRANCH_ID' AND R.ACTIVE = '$RS_ACTIVE'  AND RP.ACTIVE='$RS_ACTIVE' AND R.CAB_ID IS NOT NULL AND (RP.ESTIMATE_END_TIME 
                        BETWEEN SUBTIME('" . $curdatetime . "',030000) AND ADDTIME('" . $curdatetime . "',030000)  OR RP.ESTIMATE_START_TIME 
                        BETWEEN SUBTIME('" . $curdatetime . "',030000) AND ADDTIME('" . $curdatetime . "',030000)) AND R.TRIP_TYPE IN ('P','D') $Cond GROUP BY R.ROSTER_ID ORDER BY R.ROSTER_ID,ROUTE_ORDER DESC";
            
            $result = DB::select($sel_sql);

            $employee_details_get = $this->EmploeeDetails($result);

            return response([
                'success' => true,
                'status' => 1,
                'tracking_list' => $employee_details_get,
                'message' => 'Real Time Tracking Data Fetch Successfully!',
            ], 200);
        }  catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'message' => 'Real Time Tracking Data Fetch Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function EmploeeDetails($request_data){
        $curdatetime = date('Y-m-d H:i:s');
        $TRIP_EXECUTIVE_STSATUS = MyHelper::$RS_TOTALEXECUTE;       
        $RS_ACTIVE = MyHelper::$RS_ACTIVE;

        $BRANCH_ID = Auth::user()->BRANCH_ID;	
        $user_type = Auth::user()->user_type;
        $VENDOR_ID =Auth::user()->vendor_id;
        
        if($user_type == "VENDOR"){			    
                $Cond=" AND R.VENDOR_ID='".$VENDOR_ID."'";
        }else{
                $Cond = "";
        }

        $routedata_final = [];

        foreach($request_data as $data){
            //dd($data->ROSTER_ID);

            $gps_track_control = new \App\Http\Controllers\ElasticController;
            $cab_live_location = $gps_track_control->getCabLiveStatus($BRANCH_ID, $VENDOR_ID, "1", $data->CAB_ID);

            if ($cab_live_location !="No Record") {
                $GPS_LOCATION = $cab_live_location[0]['LOCATION'];
                $cab_gps_position = $cab_live_location[0]['POSITION'];
                $GPS_DATE = $cab_live_location[0]['GPS_DATE'];

                $data->CAB_LIVE_STATUS = [
                    'GPS_LOCATION' => $GPS_LOCATION,
                    'GPS_DATE' => $GPS_DATE,
                    'POSITION' => $cab_gps_position
                ];
            }else{
                $data->CAB_LIVE_STATUS = [
                    'GPS_LOCATION' => 'No Record',
                    'GPS_DATE' => '',
                    'POSITION' => ''
                ];
            }

            $data->BRANCH_DETAILS = [
                'BRANCH_NAME' => $data->BRANCH_NAME,
                'LOCATION' => $data->LOCATION,
                'LAT' => $data->LAT,
                'LONG' => $data->LONG
            ];


            $sql = "SELECT RP.EMPLOYEE_ID,E.`NAME` as EMPLOYEE_NAME,L.LOCATION_NAME,E.PROJECT_NAME,R.START_LOCATION,R.END_LOCATION, 
                    if(R.TRIP_TYPE = 'P',R.ESTIMATE_END_TIME,R.ESTIMATE_START_TIME) as LOGIN_DATETIME,RP.ROSTER_PASSENGER_STATUS,RP.ESTIMATE_START_TIME, 
                    if(R.TRIP_TYPE = 'P',RP.DRIVER_ARRIVAL_TIME,R.ACTUAL_START_TIME) as DRIVER_ARRIVAL_TIME,RP.ACTUAL_START_TIME,RP.DRIVER_ARRIVAL_TIME,
                    R.ACTUAL_END_TIME,L.LATITUDE,L.LONGITUDE,B.LAT ,B.`LONG`,RP.ACTUAL_END_TIME AS ACTUAL_END_TIME1  FROM roster_passengers RP 
                    INNER JOIN rosters R ON R.ROSTER_ID = RP.ROSTER_ID AND R.ROSTER_STATUS IN($TRIP_EXECUTIVE_STSATUS)
                    INNER JOIN branch B ON B.BRANCH_ID = R.BRANCH_ID 
                    INNER JOIN vendors VE ON VE.VENDOR_ID = R.VENDOR_ID 
                    INNER JOIN cab C ON C.CAB_ID = R.CAB_ID 
                    INNER JOIN drivers D ON D.DRIVERS_ID = C.DRIVER_ID
                    INNER JOIN vehicles V ON V.VEHICLE_ID = C.VEHICLE_ID 
                    INNER JOIN vehicle_models VM ON VM.VEHICLE_MODEL_ID = V.VEHICLE_MODEL_ID 
                    INNER JOIN employees E ON E.EMPLOYEES_ID = RP.EMPLOYEE_ID AND E.BRANCH_ID = '$BRANCH_ID'
                    INNER JOIN locations L ON L.LOCATION_ID = E.LOCATION_ID 
                    WHERE R.BRANCH_ID='$BRANCH_ID' AND R.ACTIVE = '$RS_ACTIVE'  AND RP.ACTIVE='$RS_ACTIVE' AND RP.ROSTER_ID = $data->ROSTER_ID AND R.CAB_ID IS NOT NULL AND (RP.ESTIMATE_END_TIME 
                    BETWEEN SUBTIME('" . $curdatetime . "',030000) AND ADDTIME('" . $curdatetime . "',030000)  OR RP.ESTIMATE_START_TIME 
                    BETWEEN SUBTIME('" . $curdatetime . "',030000) AND ADDTIME('" . $curdatetime . "',030000)) AND R.TRIP_TYPE IN ('P','D') $Cond ORDER BY R.ROSTER_ID,ROUTE_ORDER DESC";

            $data->EMPLOYEE_DETAILS = DB::select($sql);

            $j = 0;
            $sno1 = 0;

            $roster_id_arr = array();
            $roster_waypoint_arr = array();
            $roster_waylocation_arr = array();
            $roster_lat_lng_string = "";
            $roster_location_string = "";

            $sno = 0;
            $i = 0;
            $str_roster_id = "";
            $roster_waypoint = "";
            $roster_waylocation = "";

            $destination = $data->LAT . ',' . $data->LONG;
            $BRANCH_NAME = $data->BRANCH_NAME;
            $LOGIN_DATETIME = $data->LOGIN_DATETIME;

            foreach ($data->EMPLOYEE_DETAILS as $employee)
            {
                //$dec_emp_name = $this->commonFunction->AES_DECRYPT($employee->EMPLOYEE_NAME, config('app.aes_encrypt_key'));
                //$employee->EMPLOYEE_NAME = $dec_emp_name;
                $status = $this->commonFunction->employee_status($employee->ROSTER_PASSENGER_STATUS);
                $employee->STATUS = $status;

                $ROSTER_PASSENGER_STATUS = $employee->ROSTER_PASSENGER_STATUS;
                $ROSTER_ID = $data->ROSTER_ID;
                $LATITUDE = $employee->LATITUDE;
                $LONGITUDE = $employee->LONGITUDE;
                $LOCATION_NAME = $employee->LOCATION_NAME;
                $TRIP_TYPE = $data->TRIP_TYPE;

                if (($employee->STATUS == env("RP_employee_delay_sts") && $data->TRIP_TYPE == "D") || $employee->STATUS == env("RP_Created_sts") || $employee->STATUS == env("RP_Arrival_sts") || $employee->STATUS == env("RP_Not_Enabled") || $employee->STATUS == env("RP_Not_Boarded_sts")) {

                    if ($roster_lat_lng_string != "") {
                        $roster_lat_lng_string = $roster_lat_lng_string . '|' . $LATITUDE . ',' . $LONGITUDE;
                        $roster_location_string = $roster_location_string . '|' . $LATITUDE . ',' . $LONGITUDE . '-' . $LOCATION_NAME;
                    } else {
                        $roster_lat_lng_string = $LATITUDE . ',' . $LONGITUDE;
                        $roster_location_string = $LATITUDE . ',' . $LONGITUDE . '-' . $LOCATION_NAME;
                    }
                } 
                // else {
                //     if ($sno1 > 0) {
                //         $roster_waypoint_arr[$j] = $roster_lat_lng_string;
                //         $roster_waylocation_arr[$j] = $roster_location_string;
                //     }
    
                //     $sno1 = 0;
                //     $roster_lat_lng_string = '';
                //     $roster_location_string = '';
                //     if (($employee->STATUS == env("RP_employee_delay_sts") && $data->TRIP_TYPE == "D") || $employee->STATUS == env("RP_Created_sts") || $employee->STATUS == env("RP_Arrival_sts") || $employee->STATUS == env("RP_Not_Enabled") || $employee->STATUS == env("RP_Not_Boarded_sts")) {
                //         $roster_lat_lng_string = $LATITUDE . ',' . $LONGITUDE;
                //         $roster_location_string = $LATITUDE . ',' . $LONGITUDE . '-' . $LOCATION_NAME;
                //     }
                //     if ($sno1 == 0) {
                //         $j++;
                //         $roster_id_arr[$j] = $ROSTER_ID;
                //     }
                //     $sno1 = 1;
                // }
            }
                $roster_waypoint_arr[$j] = $roster_lat_lng_string;
                $roster_waylocation_arr[$j] = $roster_location_string;

            if ($cab_live_location !="No Record") {

                $GPS_LOCATION = $cab_live_location[0]['LOCATION'];
                $cab_gps_position = $cab_live_location[0]['POSITION'];
                $GPS_DATE = $cab_live_location[0]['GPS_DATE'];

                foreach ($roster_waypoint_arr as $key => $row) {
                    if ($key == $ROSTER_ID) {
                        $roster_waypoint = $row;
                        break;
                    }
                }
                foreach ($roster_waylocation_arr as $key => $row) {
                    if ($key == $ROSTER_ID) {
                        $roster_waylocation = $row;
                        break;
                    }
                }

                if ($roster_waylocation != "") {
                    $path_value = $cab_gps_position . '-' . str_replace(" ", "*", $GPS_LOCATION) . '|' . str_replace(" ", "*", $roster_waylocation) . '|' . $destination . '-' . str_replace(" ", "*", $BRANCH_NAME);
                } else {
                    $path_value = $cab_gps_position . '-' . str_replace(" ", "*", $GPS_LOCATION) . '|' . $destination . '-' . str_replace(" ", "*", $BRANCH_NAME);
                }

                if ($roster_waypoint == "") {
                    //$GetKm = $this->commonFunction->Getkm_two_point($cab_gps_position, $destination);
		    $GetKm = 0;
                } else {
                    //$GetKm = $this->commonFunction->Getkm($cab_gps_position, $destination, $roster_waypoint);
		    $GetKm = 0;
                }

                if ($GetKm == 0) {
                    if ($GPS_DATE == "1900-01-01 00:00:00") {
                    $delaycolor = '#DF0101';
                    $sts = 'GPS Off';
                    $km = "0";
                    $expect_time = "--";
                    }else{
                        // $delaycolor = '#0404B4';
                        $sts = 'GPS On';
                        $km = "0";
                        $expect_time = "--";
                        
                        if ($TRIP_TYPE == 'P') {
                            $delaycolor = '#8A0886';
                            } else {
                            $delaycolor = '#0404B4';
                        }
                    }
                }else {
                    $GetKmList = explode("-", $GetKm);
                    $km = round(($GetKmList[0] / 1000), 2);
                    if ($GPS_DATE == "1900-01-01 00:00:00") {
                        $duration = $this->AddTimeDuration($LOGIN_DATETIME, $curdatetime, $GetKmList[1]);
                    } else {
                        $duration = $this->AddTimeDuration($LOGIN_DATETIME, $GPS_DATE, $GetKmList[1]);
                    }


                    $stsus_list = explode("|", $duration);

                    $expect_time = $stsus_list[0];
                    if ($stsus_list[2] == 'Expected Cabdelay') {
                        if ($TRIP_TYPE == 'P') {
                            $delaycolor = '#8A0886';
                            $sts = $stsus_list[2] . ' ' . $stsus_list[3];
                        } else {
                            $sts = 'Trip TravelTime ' . $stsus_list[3];
                            $delaycolor = '#0404B4';
                        }
                    } else {
                        if ($GPS_DATE == "1900-01-01 00:00:00") {
                            $delaycolor = '#DF0101';
                            $sts = 'No gps fixed';
                        } else {
                            if ($TRIP_TYPE == 'P') {
                                $delaycolor = '#21610B';
                                $sts = $stsus_list[2] . ' ' . $stsus_list[3];
                            } else {
                                $delaycolor = '#DF0101';
                                $sts = 'GPS Off';
                                $km = "0";
                                $expect_time = "--";
                            }
                        }
                    }
                }
            } else {
                $GPS_LOCATION = "--";
                $GPS_DATE = "1900-01-01 00:00:00";
                $delaycolor = '#DF0101';
                $sts = 'GPS Off';
                $km = "0";
                $expect_time = "--";
                $path_value ="--";
            }
            $data->STATUS = $sts;
            $data->EXPECT_TIME = $expect_time;
            $data->TKM = $km;
            $data->COLOR_CODE = $delaycolor;

            array_push($routedata_final, $data);
        }

        return $routedata_final;
    }

    public function AllotRealTimeRoute()
    {
        try {
            $checkdate = date("Y-m-d H:i:s", strtotime("+36 minutes"));
            $BRANCHID = Auth::user()->BRANCH_ID;
            $user_type = Auth::user()->user_type;
            $VENDOR_ID =Auth::user()->vendor_id;
            if($user_type == "VENDOR"){			    
                    $Cond=" AND R.VENDOR_ID='".$VENDOR_ID."'";
            }else{
                    $Cond = "";
            }
            
            $query = "SELECT R.ROSTER_ID,R.ROUTE_ID,R.TRIP_TYPE,if(R.TRIP_TYPE = 'P',DATE(R.ESTIMATE_END_TIME),DATE(R.ESTIMATE_START_TIME)) as LOGIN_DATE, 
            if(R.TRIP_TYPE = 'P',TIME(R.ESTIMATE_END_TIME),TIME(R.ESTIMATE_START_TIME)) as LOGIN_TIME,B.BRANCH_NAME,
            (R.PASSENGER_ALLOT_COUNT+R.PASSENGER_CLUBING_COUNT) AS EMPCOUNT,R.START_LOCATION,R.END_LOCATION FROM rosters R 
            INNER JOIN branch B ON B.BRANCH_ID = R.BRANCH_ID
            WHERE R.BRANCH_ID=$BRANCHID AND R.CAB_ID IS NULL 
            AND (R.ESTIMATE_END_TIME > '$checkdate' OR R.ESTIMATE_START_TIME > '$checkdate') $Cond";
            $result = DB::select($query);

            return response([
                'success' => true,
                'status' => 1,
                'route_list' => $result,
                'message' => 'Allot Real Time Route Data Fetch Successfully!',
            ], 200);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'message' => 'Allot Real Time Route Data Fetch Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function AddTimeDuration($login_datetime,$gps_datetime,$duration) {
        $sql = "SELECT  date_format(ADDTIME('" . $gps_datetime . "',SEC_TO_TIME('" . $duration . "')),'%Y-%m-%d %H:%i') as traveltime";
        $rs1 = DB::select($sql);        
        $traveltime= $rs1[0]->traveltime;
       
        $sql2 ="select  time_format(TIMEDIFF('" . $traveltime . "','" . $gps_datetime . "'), '%H:%i')   as total_time";
        $rs2 = DB::select($sql2);
        $total_time= $rs2[0]->total_time;
         
        $sql3 ="select  if(TIMEDIFF('" .$traveltime. "','".$login_datetime."')>'00:00:00','Expected Cabdelay','Expected OnTime') as cab_delay ,if(TIMEDIFF('" .$traveltime. "','".$login_datetime."')>'00:00:00',time_format(TIMEDIFF('" .$traveltime. "','".$login_datetime."'), '%H:%i'),'') as delay_time ";
        $rs3 = DB::select($sql3);
        
        $cab_delay= $rs3[0]->cab_delay;
        $delay_time= $rs3[0]->delay_time;
        
        return $traveltime.'|'.$total_time.'|'.$cab_delay.'|'.$delay_time;
    }
}