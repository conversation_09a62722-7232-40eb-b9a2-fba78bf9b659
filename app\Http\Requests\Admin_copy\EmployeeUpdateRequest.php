<?php

namespace App\Http\Requests\Admin;

use App\Helpers\CommonFunction;
use Closure;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;
use Illuminate\Contracts\Validation\Validator;

class EmployeeUpdateRequest extends FormRequest
{
    private CommonFunction $commonFunction;
    private ?object $employee = null;
    private ?int $employeeId = null;

    public function __construct(CommonFunction $commonFunction)
    {
        parent::__construct();
        $this->commonFunction = $commonFunction;
    }

    public function authorize(): bool
    {
        return true;
    }

    protected function prepareForValidation(): void
    {
        try {
            $this->employeeId = Crypt::decryptString($this->route('employeeAutoIdCrypt'));
        } catch (\Throwable $e) {
            throw new HttpResponseException(response()->json([
                'success' => false,
                'validation_error' => true,
                'status' => 1,
                'message' => 'Invalid employee ID',
            ], 500));
        }

        $this->employee = DB::table('employees')->where('id', $this->employeeId)->first();

        if (!$this->employee) {
            throw new HttpResponseException(response()->json([
                'success' => false,
                'validation_error' => true,
                'status' => 1,
                'message' => 'Employee not found',
            ], 500));
        }
    }

    public function rules(): array
    {
        $branchId = $this->input('BRANCH_ID');

        return [
            'EMPLOYEES_ID' => [
                'required',
                'string',
                Rule::unique('employees', 'EMPLOYEES_ID')
                    ->where('BRANCH_ID', $branchId)
                    ->ignore($this->employeeId),
            ],
            'BRANCH_ID' => 'required|integer',
            'NAME' => 'required|string',
            'LNAME' => 'required|string',
            'MOBILE' => [
                'required',
                'numeric',
                $this->uniqueMobileRule($branchId),
                Rule::unique('users', 'name')->ignore($this->employee->user_id),
            ],
            'GENDER' => 'required|string',
            'EMP_EMAIL' => 'nullable|email',
            'PROJECT_NAME' => 'required|string',
            'LOCATION' => 'required|integer',
            'ADDRESS' => 'required|string',
            'LATITUDE' => 'required|numeric',
            'LONGITUDE' => 'required|numeric',
            'addr_type' => 'required|string',
        ];
    }

    public function messages(): array
    {
        return [
            'EMPLOYEES_ID.required' => 'Employee ID field is required',
            'EMPLOYEES_ID.unique' => 'This Employee ID already exists',
            'NAME.required' => 'Name field is required',
            'MOBILE.numeric' => 'Mobile number must be numeric',
            'MOBILE.unique' => 'This mobile number is already registered',
            'BRANCH_ID.integer' => 'Branch ID must be an integer',
            'LOCATION.integer' => 'Location must be an integer',
            'EMP_EMAIL.email' => 'Invalid email format',
            'PROJECT_NAME.required' => 'Project name field is required',
            'ADDRESS.required' => 'Address field is required',
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json([
            'success' => false,
            'validation_error' => true,
            'status' => 2,
            'message' => 'Validation errors',
            'data' => $validator->errors()
        ]));
    }

    private function uniqueMobileRule($branchId): Closure
    {
        return function (string $attribute, mixed $value, Closure $fail) use ($branchId) {
            $mobileEncrypt = $this->commonFunction->AES_ENCRYPT($value,config('app.aes_encrypt_key'));

            $exists = DB::table('employees')
                ->where('BRANCH_ID', $branchId)
                ->where('id', '!=', $this->employeeId)
                ->where('MOBILE', $mobileEncrypt)
                ->exists();

            if ($exists) {
                $fail('The mobile number has already been taken.');
            }
        };
    }
}
