<?php

namespace App\Services\Admin;

use App\Helpers\CommonFunction;
use App\Helpers\MyHelper;
use Carbon\Carbon;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use App\Models\Vehicle;

class VehicleService
{
    protected CommonFunction $commonFunction;
    protected ?Branch $currentBranch = null;

    public function __construct(CommonFunction $commonFunction)
    {
        $this->commonFunction = $commonFunction;
    }
	
	 public function indexVehicle(): FoundationApplication|Response|ResponseFactory
    {
        $authUser = Auth::user();

        try 
		{
            $vehicle = Vehicle::select('vehicles.VEHICLE_ID',
                'vehicles.VEHICLE_MODEL_ID',
                'vehicles.VEHICLE_REG_NO',
                'vehicles.VEHICLE_JOIN_DATE',
                'vehicles.REG_DATE',
                'vehicles.REG_STATUS',
                'vehicles.PERMIT_EXPIRY',
                'vehicles.INSURANCE_EXPIRY',
                'vehicles.FC_EXPIRY',
                'vehicles.TAX_EXPIRY',
                'vehicles.COMPLIANT_STATUS',
                'vehicles.MILEAGE_KM',
                'vehicles.REMARKS',
               'O.NAME as ORG_NAME',
				'O.LOCATION'
            )
                ->where('vehicles.ACTIVE', MyHelper::$RS_ACTIVE)
               ->where('vehicles.ORG_ID', "=","O.ORGANIZATIONID")
			    ->join("branch", "branch.BRANCH_ID", "=", DB::Raw($authUser->BRANCH_ID))
                ->Join("organization as O", "O.ORGANIZATIONID", "=", "branch.ORG_ID")
                ->Join("vehicle_models as VM", "VM.VEHICLE_MODEL_ID", "=", "vehicles.VEHICLE_MODEL_ID")
                ->get();

            return response([
                'success' => true,
                'status' => 3,
                'vehicles' => $vehicle,
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Vehicle Index Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ]);
        }
    }

	

    public function storeVehicle($request): FoundationApplication|Response|ResponseFactory
    {

        try {

            DB::beginTransaction();
            $auth_user = Auth::user();

            $vehicleData = $this->prepareVehicleData($request, $auth_user, MyHelper::$RS_ACTIVE, $auth_user->id);
            $vehicleResult = Vehicle::create($vehicleData);



            return response([
                'success' => true,
                'status' => 3,
                'message' => 'Vehicle Created Successfully',

            ]);

        } catch (\Throwable|\Exception $e) 
		{
            DB::rollBack();
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'validation_controller' => true,
                'status' => 4,
                'message' => 'Vehicle Created UnSuccessfully',
                'error' => $e->getMessage(),
            ],500);
        } finally {
            DB::commit();
        }


    }
	
    /**
     * @throws ConnectionException
     */
    private function prepareVehicleData($request, $auth_user, $active, $userId): array
    {
        $date = Carbon::now();
	$org_id=$this->commonFunction->getOrgId($auth_user->BRANCH_ID);
        return [
		
			'VEHICLE_MODEL_ID' => $request->VEHICLE_MODEL_ID,
            'VEHICLE_REG_NO' => $request->VEHICLE_REG_NO,
            'REG_DATE' => $request->REG_DATE,
			'VEHICLE_JOIN_DATE' => $request->VEHICLE_JOIN_DATE,
            'PERMIT_EXPIRY' => $request->PERMIT_EXPIRY,
            'POLLUTION_EXPIRY' => $request->POLLUTION_EXPIRY,
            'INSURANCE_EXPIRY' => $request->INSURANCE_EXPIRY,
            'FC_EXPIRY' => $request->FC_EXPIRY,
            'TAX_EXPIRY' => $request->TAX_EXPIRY,
            'COMPLIANT_STATUS' => 1,
            'MILEAGE_KM' => $request->MILEAGE_KM,
         	"ACTIVE" => $active,
         	"ORG_ID" => $org_id,
			"CREATED_BY" => $auth_user->id,
            "created_at" => $date->format("Y-m-d H:i:s"),
		
                     
        ];
    }

    public function deleteVehicle($request, $vehcleAutoIdCrypt): FoundationApplication|Response|ResponseFactory
    {
        $authUser = Auth::user();
       
        try {
            DB::beginTransaction();

            $vehicleId = Crypt::decryptString($vehcleAutoIdCrypt);

            $vehicle = Vehicle::where('ACTIVE', MyHelper::$RS_ACTIVE)
               // ->where('BRANCH_ID', $authUser->BRANCH_ID)
                ->findOrFail($vehicleId);

            $vehicle->update([
                'REMARKS' => $request->input('remark'),
                'ACTIVE' => MyHelper::$RS_INACTIVE,
                'UPDATED_BY' => Auth::user()->id,
            ]);

            DB::commit();

            return response([
                'success' => true,
                'status' => 3,
                'message' => 'Vehicle Deleted Successfully',
            ]);
        } catch (\Throwable|\Exception $e) {
            DB::rollBack();
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Vehicle Delete Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ]);
        }
    }



    public function editVehicle($vehicleAutoIdCrypt): FoundationApplication|Response|ResponseFactory
    {
        $authUser = Auth::user();

        try {
            $vehicleAutoId = Crypt::decryptString($vehicleAutoIdCrypt);
			
			 $vehicle = Vehicle::select('vehicles.VEHICLE_ID',
                'vehicles.VEHICLE_MODEL_ID',
                'vehicles.VEHICLE_REG_NO',
                'vehicles.VEHICLE_JOIN_DATE',
                'vehicles.REG_DATE',
                'vehicles.REG_STATUS',
                'vehicles.PERMIT_EXPIRY',
                'vehicles.INSURANCE_EXPIRY',
                'vehicles.FC_EXPIRY',
                'vehicles.TAX_EXPIRY',
                'vehicles.COMPLIANT_STATUS',
                'vehicles.MILEAGE_KM',
                'vehicles.REMARKS',
               'O.NAME as ORG_NAME','VM.MODEL',
				'O.LOCATION'
            )
                ->where('vehicles.ACTIVE', MyHelper::$RS_ACTIVE)
               ->where('vehicles.ORG_ID', "=","O.ORGANIZATIONID")
			    ->where('vehicles.VEHICLE_ID', $vehicleAutoId)
			    ->join("branch", "branch.BRANCH_ID", "=", DB::Raw($authUser->BRANCH_ID))
                ->Join("organization as O", "O.ORGANIZATIONID", "=", "branch.ORG_ID")
                ->Join("vehicle_models as VM", "VM.VEHICLE_MODEL_ID", "=", "vehicles.VEHICLE_MODEL_ID")
                ->firstOrFail();
			
            return response([
                'success' => true,
                'status' => 3,
                'sim' => $vehicle,
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Vehicle Edit Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ]);
        }
    }

    public function updateVehicle($request, $vehicleAutoIdCrypt): FoundationApplication|Response|ResponseFactory
    {
        $authUser = Auth::user();
        try {
            DB::beginTransaction();
            $vehicleAutoId = Crypt::decryptString($vehicleAutoIdCrypt);
            $vehicle = Vehicle::findOrFail($vehicleAutoId);
            
            $auth_user = Auth::user();

            $vehicleData = $this->prepareVehicleDataForUpdate($request, $auth_user, $vehicle);
            $vehicle->update($vehicleData);

            DB::commit();


            return response([
                'success' => true,
                'status' => 3,
                'message' => 'Vehicle Updated Successfully',
            ]);

        } catch (\Throwable|\Exception $e) {
            DB::rollBack();
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'message' => 'Vehicle Update Unsuccessful',
                'status' => 4,
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * @throws ConnectionException
     */
    private function prepareVehicleDataForUpdate($request, $auth_user, $driver): array
    {
        $date = Carbon::now();

        return [
		
           'VEHICLE_MODEL_ID' => $request->VEHICLE_MODEL_ID,
            'VEHICLE_REG_NO' => $request->VEHICLE_REG_NO,
            'REG_DATE' => $request->REG_DATE,
            'VEHICLE_JOIN_DATE' => $request->VEHICLE_JOIN_DATE,
            'PERMIT_EXPIRY' => $request->PERMIT_EXPIRY,
            'POLLUTION_EXPIRY' => $request->POLLUTION_EXPIRY,
            'INSURANCE_EXPIRY' => $request->INSURANCE_EXPIRY,
            'FC_EXPIRY' => $request->FC_EXPIRY,
            'TAX_EXPIRY' => $request->TAX_EXPIRY,
            'COMPLIANT_STATUS' => 1,
            'MILEAGE_KM' => $request->MILEAGE_KM,
         	"ACTIVE" => MyHelper::$RS_ACTIVE,
			"CREATED_BY" => $auth_user->id,
            "created_at" => $date->format("Y-m-d H:i:s"),
		
        ];
    }

    public function dataForCreateVehicle(): FoundationApplication|Response|ResponseFactory
    {

        try {
            $user = Auth::user();
            $rsActive = MyHelper::$RS_ACTIVE;
            $branchId = $user->BRANCH_ID;

            $models = DB::table('vehicle_models')
                ->select('VEHICLE_MODEL_ID', 'BRAND','MODEL','CAPACITY')
              //  ->where('ACTIVE', $rsActive)
                ->get();

          
            return response([
                'success' => true,
                'status' => 3,
                'cab_models' => $models,
                
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Data For Create for Vehicle Models Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ]);
        }
    }
	
 
    public function paginationVehicle($request, bool $active = true): FoundationApplication|Response|ResponseFactory
    {
        try {
			 $user = Auth::user();
            $perPage = $request->input('per_page', MyHelper::$PAGINATION_PER_PAGE);
            $branch_id = Auth::user()->BRANCH_ID;
            $activeStatus = $active ? MyHelper::$RS_ACTIVE : MyHelper::$RS_INACTIVE;

            
			
			$vehicle = Vehicle::select('vehicles.VEHICLE_ID',
                'vehicles.VEHICLE_MODEL_ID',
                'vehicles.VEHICLE_REG_NO',
                'vehicles.VEHICLE_JOIN_DATE',
                'vehicles.REG_DATE',
                'vehicles.REG_STATUS',
                'vehicles.PERMIT_EXPIRY',
                'vehicles.INSURANCE_EXPIRY',
                'vehicles.FC_EXPIRY',
                'vehicles.POLLUTION_EXPIRY',
                'vehicles.TAX_EXPIRY',
                'vehicles.COMPLIANT_STATUS',
                'vehicles.MILEAGE_KM',
                'vehicles.REMARKS',
                'vehicles.created_at AS created_on',
                'users.name AS CREATED_BY',
               'O.NAME as ORG_NAME','VM.MODEL',
				'O.LOCATION'
            )
                ->where('vehicles.ACTIVE', MyHelper::$RS_ACTIVE)
				->where('vehicles.ORG_ID', "=","O.ORGANIZATIONID")
			    ->join("branch", "branch.BRANCH_ID", "=", DB::Raw($user->BRANCH_ID))
                ->join("users", "users.id", "=", "vehicles.CREATED_BY")
                ->Join("organization as O", "O.ORGANIZATIONID", "=", "branch.ORG_ID")
                ->Join("vehicle_models as VM", "VM.VEHICLE_MODEL_ID", "=", "vehicles.VEHICLE_MODEL_ID");
              //  ->get();
                $filterModel = $request->input('filterModel');
                if ($filterModel) {
                    foreach ($filterModel as $field => $filter) {
                        if (isset($filter['filter']) && $filter['filter'] !== '') {
                            $value = $filter['filter'];
                            $type = $filter['type'];
     
                            switch ($field) {
                                case 'MODEL':
                                    $vehicle->where('VM.MODEL', 'like', "%{$value}%");
                                    break;
                                case 'VEHICLE_REG_NO':
                                    $vehicle->where('vehicles.VEHICLE_REG_NO', 'like', "%{$value}%");
                                    break;
                                 
                            }
                        }
                    }
                }
     
     
                if ($request->has('orderBy') && !empty($request->input('orderBy'))) {
                    $orderBy = $request->input('orderBy');
                    $order = $request->input('order', 'asc');
                    $vehicle->orderBy($orderBy, $order);
                } else {
                    $vehicle->orderBy("vehicles.created_at");
                }
     
     
                if ($perPage === MyHelper::$PAGINATION_PER_PAGE_ALL) {
                    $paginatedData = $vehicle->paginate($vehicle->count());
                } else {
                    $perPage = is_numeric($perPage) ? (int)$perPage : MyHelper::$PAGINATION_PER_PAGE;
                    $paginatedData = $vehicle->paginate($perPage);
                }


            return response([
                'success' => true,
                'status' => 3,
                'Vehicle' => $paginatedData,
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'Vehcle Pagination Unsuccessful' : 'Deactivate Vehicle Pagination Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ]);
        }
    } 

}
