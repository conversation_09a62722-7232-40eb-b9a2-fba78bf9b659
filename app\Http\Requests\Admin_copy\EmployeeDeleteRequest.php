<?php

namespace App\Http\Requests\Admin;

use Illuminate\Contracts\Encryption\DecryptException;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Support\Facades\Crypt;

class EmployeeDeleteRequest extends FormRequest
{

    public $employeeId;

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'remark' => 'required|string',
        ];
    }

    protected function prepareForValidation(): void
    {
        try {
            $this->employeeId = Crypt::decryptString($this->route('employeeAutoIdCrypt'));
        } catch (DecryptException $e) {
            throw new HttpResponseException(response()->json([
                'success' => false,
                'validation_error' => true,
                'status' => 1,
                'message' => 'Invalid employee ID',
            ], 500));
        }
    }


    public function messages(): array
    {
        return [
            'remark.required' => 'A remark is required when deleting an employee.',
            'remark.string' => 'The remark must be a string.',
        ];
    }

    public function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json([
            'success' => false,
            'validation_error' => true,
            'status' => 2,
            'message' => 'Validation errors',
            'data' => $validator->errors()
        ]));
    }

}
