<?php
namespace App\Services\Admin;

use App\Helpers\CommonFunction;
use App\Helpers\MyHelper;
use App\Http\Controllers\ElasticController;
use App\Models\AlertSignalFailure;
use App\Models\Branch;
use App\Models\Cab;
use App\Models\Cab_allocation;
use App\Models\CabAttendance;
use App\Models\Driver;
use App\Models\email_notification;
use App\Models\Employee;
use App\Models\File_upload;
use App\Models\Input_data;
use App\Models\Location;
use App\Models\OtpVerify;
use App\Models\property;
use App\Models\Reason_Log;
use App\Models\Roster;
use App\Models\RosterPassenger;
use App\Models\RouteEscorts;
use App\Models\Sms;
use App\Models\Vehicle;
use App\Models\VehicleModel;
use App\Models\Vendor;
use App\Models\ApproveDistance;
use App\Models\EmployeesRosterRequest;
use Carbon\Carbon;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Redirect;
use Validator;

class AutorouteUploadService
{
    protected CommonFunction $commonFunction;
    protected ?Branch $currentBranch = null;

    public function __construct(CommonFunction $commonFunction)
    {
        $this->commonFunction = $commonFunction;
    }

    private function getPropertyValue()
    {
        $branch_id = Auth::user()->BRANCH_ID;
        //    exit;
        $property = property::where('BRANCH_ID', $branch_id)
            ->where('ACTIVE', MyHelper::$RS_ACTIVE)
            // ->where('PROPERTIE_NAME', $propertyName)
            ->get();

        return $property;
    }

    public function auto_route_upload($request): FoundationApplication|Response|ResponseFactory
    {
        try {
            $dbname = Auth::user()->dbname;
            $branch_id = Auth::user()->BRANCH_ID;
            // getting all of the post data
            $request->file('file');
            $file = array('file' => $request->file('file'));
            // echo "test";exit;
            // setting up rules
            $rules = array(
                'file' => 'required|max:10000',
            );  // mimes:csv,bmp,png and for max size max:10000
            // doing the validation, passing post data, rules and the messages
            $validator = Validator::make($file, $rules);

            if ($validator->fails()) {
                // send back to the page with the input data and errors
                return response([
                    'success' => true,
                    'status' => 3,
                    'message' => 'Invalid input',
                ]);

                //  return Redirect::to('upload')->withInput()->withErrors($validator);
            } else {
                // checking file is valid.
                if ($request->file('file')->isValid()) {
                    // $destinationPath = 'public/uploads'; // upload path

                    $extension = $request->file('file')->getClientOriginalExtension();  // getting image extension
                    $filename = $request->file('file')->getClientOriginalName();
                    $size = $request->file('file')->getSize();

                    // Input::file('file')->move($destinationPath, $filename); // uploading file to given path

                    if ($filename == MyHelper::$AUTO_ROUTE_UPLOAD_NAME) {
                        $user_id = Auth::user()->id;  // get session user name
                        $file_upload = new File_upload;
                        // $file_upload=$file_upload1->setConnection("$dbname");
                        $file_upload->FILE_NAME = $filename;
                        $file_upload->FILE_SIZE = $size;
                        $file_upload->FILE_TYPE = $extension;
                        $file_upload->CREATED_BY = $user_id;
                        $file_upload->UPDATED_BY = $user_id;
                        $file_upload->BRANCH_ID = $branch_id;
                        $file_upload->STATUS = 0;
                        $file_upload->save();
                        $FILE_ID = $file_upload->FILE_ID;
                        //
                        $customerArr = $this->importAutoRouteCsv($request->file('file')->getRealPath(), $FILE_ID);
                        // $customerArr = $this->importCsv('D://eclipse development/' . $filename, $FILE_ID);
                        if ($customerArr) {
                            return $customerArr;
                           // return $this->emp_locationupdate();
                        } else {
                            // $this->employee_input_datas();
                            Session::flash('autoroute', 'success');
                            return Redirect::to('upload');
                        }
                    } else {
                        return response([
                            'success' => false,
                            'status' => 3,
                            'message' => 'Invalid file name',
                        ]);
                    }
                }
            }
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'No data',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function importAutoRouteCsv($file, $FILE_ID)
    {
       
        return $this->csvToautorouteArray($FILE_ID, $file);
    }

    public function csvToautorouteArray($FILE_ID, $filename = '', $delimiter = ',')
    {
       
        $dbname = Auth::user()->dbname;

        try {
            if (!file_exists($filename) || !is_readable($filename))
                return false;
                
            $header = null;
            $data = array();
            $j = 0;
            $isCorrectLocation = false;
            $input_Arr = array();
            $location_list_Arr = array();
            $employees_list_Arr = array();
            $approve_dis_array = array();
            $is_employee_roster_req_data_Arr = array();
            $vendor_list_Arr = array();
            $DURATION = '';
           // $obj2 = new CommonController();

            $branch_id = Auth::user()->BRANCH_ID;  // get session branch id
            $TRAVEL_TYPE_PICKUP = MyHelper::$RP_PICKROUTE;
            $TRAVEL_TYPE_DROP = MyHelper::$RP_DROPROUTE;

            $branch_det = DB::connection("$dbname")->table('branch')->select('BRANCH_ID', 'BRANCH_NAME', 'DIVISION_ID')->where('BRANCH_ID', '=', $branch_id)->get();
            $site_name = $branch_det[0]->BRANCH_NAME;
            $DIVISION_ID = $branch_det[0]->DIVISION_ID;
           
            $location_rs_list = location::on("$dbname")->where([['ACTIVE', '=', 1], ['DIVISION_ID', '=', $DIVISION_ID]])->get();
            foreach ($location_rs_list as $location_list) {
                $location_list_Arr[] = $location_list->LOCATION_NAME;
            }

            $employees_rs_list = Employee::on("$dbname")->where([
                ['ACTIVE', '=', 1],
                ['BRANCH_ID', '=', $branch_id],
            ])->get();
            foreach ($employees_rs_list as $employees) {
                $employees_list_Arr[] = $employees->EMPLOYEES_ID;
            }

            $approve_rs_list = ApproveDistance::on("$dbname")->where([
                ['BRANCH_ID', '=', $branch_id],
            ])->get();

            foreach ($approve_rs_list as $value) {
                $approve_dis_array[] = $value->LOCATION_ID;
            }

            $vendor_rs_list = vendor::on("$dbname")->where([
                ['ACTIVE', '=', 1],
                ['BRANCH_ID', '=', $branch_id],
            ])->get();
            foreach ($vendor_rs_list as $vendors) {
                $vendor_list_Arr[] = $vendors->NAME;
            }
             $sql = "select * from employees_roster_request where  BRANCH_ID='" . $branch_id . "' and date(ESTIMATE_START_TIME)>='" . date('Y-m-d') . "'";
            
            $employee_roster_req = DB::connection("$dbname")->select($sql);
            // $employee_roster_req = employees_roster_request::on("$dbname")->where([['BRANCH_ID', '=', $branch_id],])
            // ->whereDate('ESTIMATE_START_TIME', '>=', Carbon::now()->format("Y-m-d"))
            // ->get();
            /*  print_r($employee_roster_req);
              exit; */
            foreach ($employee_roster_req as $input_datas) {
                /* $is_input_data_Arr[] = $input_datas->EMPLOYEE_ID . '|' . $input_datas->ROUTE_ID . '|' . $input_datas->TRIP_TYPE . '|' . $input_datas->ESTIMATE_START_TIME; */
                $is_employee_roster_req_data_Arr[] = $input_datas->EMPLOYEE_ID . '|' . $input_datas->TRIP_TYPE . '|' . $input_datas->ESTIMATE_START_TIME;
            }

            if (($handle = fopen($filename, 'r')) !== false) {
                while (($row = fgetcsv($handle, 1000, $delimiter)) !== false) {
                    if (!$header) {
                        $header = $row;
                        $err_msg = '';
                        for ($i = 0; $i < count($row); $i++) {
                            /*  if ($i == 0) {
                              if ($row[$i] != 'RouteId') {
                              $err_msg = "Don't Change or Replace the " . $row[$i] . ", It Should be in Cell No " . $i . "th Position";
                              return Session::flash('error', $err_msg);
                              }
                              } else */
                            if ($i == 0) {
                                if ($row[$i] != 'EmpId') {
                                    $err_msg = "Don't Change or Replace the " . $row[$i] . ', It Should be in Cell No ' . $i . 'th Position';
                                }
                            } else if ($i == 2) {
                                if ($row[$i] != 'LoginDate') {
                                    $err_msg = "Don't Change or Replace the " . $row[$i] . ', It Should be in Cell No ' . $i . 'th Position';
                                }
                            } else if ($i == 1) {
                                if ($row[$i] != 'TripType') {
                                    $err_msg = "Don't Change or Replace the " . $row[$i] . ', It Should be in Cell No ' . $i . 'th Position';
                                }
                            }

                            if ($err_msg != '') {
                                return response([
                                    'success' => false,
                                    'status' => 3,
                                    'message' => $err_msg,
                                ]);
                            }
                        }
                    } else {
                        // $data[] = array_combine($header, $row);

                        for ($i = 0; $i < count($row); $i++) {
                            /* if ($i == 0) {
                              if ($row[$i] == '') {
                              $err_msg = "Don't upload empty rows";
                              return Session::flash('error', $err_msg);
                              }
                              //$route_id = $row[$i];
                              $route_id = '';
                              } else */
                            if ($i == 0) {
                                $emp_id = trim($row[$i]);
                            }
                            if ($i == 1) {
                                $trip_type = strtoupper($row[$i]);
                                if ($trip_type != $TRAVEL_TYPE_PICKUP && $trip_type != $TRAVEL_TYPE_DROP) {
                                    $err_msg = "Don't upload invalid trip type upload P or D  ";
                                    return response([
                                        'success' => false,
                                        'status' => 3,
                                        'message' => $err_msg,
                                    ]);
                                }
                            } else if ($i == 2) {
                                $estimate_start_time = date('Y-m-d H:i:s', strtotime($row[$i]));
                                $estimate_date = date('Y-m-d', strtotime($row[$i]));
                                $dt = Carbon::parse($estimate_start_time);
                                if ($dt->isPast()) {
                                    $err_msg = 'please upload future datetime roster Details ';
                                    return response([
                                        'success' => false,
                                        'status' => 3,
                                        'message' => $err_msg,
                                    ]);
                                }


                                if ($dt->diffInDays(Carbon::now()) > MyHelper::$FILE_UPLOAD_DAYS_LIMIT) 
                                {
                                    echo $dt->diffInDays(Carbon::now());
                                    $err_msg = "Don't upload future Days roster Details ";
                                    return response([
                                        'success' => false,
                                        'status' => 3,
                                        'message' => $err_msg,
                                    ]);
                                }
                            }
                        }
                        $is_already_route = $emp_id . '|' . $trip_type . '|' . $estimate_start_time;

                        $date = Carbon::now();
                        $current_datetime = $date->format('Y-m-d H:i:s');
                        $user_id = Auth::user()->id;  // get session user name
                        $branch_id = Auth::user()->BRANCH_ID;  // get session branch id

                        if (in_array($emp_id, $employees_list_Arr, true)) {
                        } else {
                            $err_msg = '(' . $emp_id . ')' . ' invalid employee user , please upload valid employee user ';
                            return response([
                                'success' => false,
                                'status' => 3,
                                'message' => $err_msg,
                            ]);
                        }

                        if (in_array($is_already_route, $is_employee_roster_req_data_Arr, true)) {
                            $err_msg = 'Already Uploaded Data ' . $trip_type . '/' . $emp_id . '/' . $estimate_start_time;
                            return response([
                                'success' => false,
                                'status' => 3,
                                'message' => $err_msg,
                            ]);
                        } else {
                            $status = 1;
                            $DISTANCE = 0;
                            $gender = '';
                            foreach ($employees_rs_list as $employees) {
                                if ($employees->EMPLOYEES_ID == $emp_id) {
                                    $emp_name = "Selvam";
                                    $emp_mobile = "9543417214";
                                    //$emp_name = $this->commonFunction->AES_DECRYPT($employees->NAME, env('AES_ENCRYPT_KEY'));
                                    //$emp_mobile = $this->commonFunction->AES_DECRYPT($employees->MOBILE, env('AES_ENCRYPT_KEY'));
                                    $project_name = $employees->PROJECT_NAME;
                                    $address = $employees->ADDRESS;
                                    $gender = $employees->GENDER;
                                    $DISTANCE = $employees->DISTANCE;
                                    foreach ($location_rs_list as $location_list) {
                                        if ($location_list->LOCATION_ID == $employees->LOCATION_ID) {
                                            $location = $location_list->LOCATION_NAME;
                                            break;
                                        }
                                    }
                                    foreach ($approve_rs_list as $approve_dis_res) {
                                        if ($approve_dis_res->LOCATION_ID == $employees->LOCATION_ID) {
                                            $DURATION = $approve_dis_res->DURATION;
                                            if ($DISTANCE == '' || $DISTANCE == '0') {
                                                $DISTANCE = $approve_dis_res->APPROVED_DISTANCE;
                                            }
                                            break;
                                        }
                                    }
                                }
                            }
                            if ($trip_type == 'P') {
                                $start_loc = $location;
                                $end_loc = $site_name;
                                $DURATION_ARR = explode(':', $DURATION);
                                $picktime = date('Y-m-d H:i:s', strtotime("-$DURATION_ARR[0] hours -$DURATION_ARR[1] minute -$DURATION_ARR[2] second", strtotime($estimate_start_time)));
                            } else {
                                $start_loc = $site_name;
                                $end_loc = $location;
                                $picktime = '';
                            }

                            // $empID_Arr[]="'".$emp_id."'";
                            $input_Arr[$j] = array(
                                'BRANCH_ID' => $branch_id,
                                'TRIP_TYPE' => $trip_type,
                                'ESTIMATE_START_TIME' => $estimate_start_time,
                                'ESTIMATE_TIME' => $picktime,
                                'START_LOCATION' => $start_loc,
                                'END_LOCATION' => $end_loc,
                                'EMPLOYEE_ID' => $emp_id,
                                'GENDER' => $gender,
                                'STATUS' => $status,
                                'CREATED_BY' => $user_id,
                                'UPDATED_BY' => $user_id,
                                'DISTANCE' => $DISTANCE,
                                'CATEGORY' => 'auto_roster',
                                'created_at' => $current_datetime,
                                'updated_at' => $current_datetime
                            );
                        }
                        $j++;
                    }
                }
                fclose($handle);
            }
            $isinserted = EmployeesRosterRequest::on("$dbname")->insert($input_Arr);

            if ($isinserted) {
                if ($isCorrectLocation) {
                    return $isCorrectLocation;
                } else {
                    return response([
                        'success' => true,
                        'status' => 3,
                        'message' => 'success ' . $j . ' Rows Uploaded successfully',
                    ]);
                }
            }
        } catch (Exception $exc) {
            echo $exc->getTraceAsString();
        }
    }
}
