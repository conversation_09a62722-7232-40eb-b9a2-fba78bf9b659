<?php

namespace App\Services\Admin;

use App\Helpers\CommonFunction;
use App\Helpers\MyHelper;
use Carbon\Carbon;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use App\Models\Vendor;
use App\Models\Cab_allocation;
use App\Models\Roster;
use App\Models\RosterPassenger;
use App\Models\Cab;
use App\Models\Vehicle;
use App\Models\VehicleModel;
use App\Models\Driver;
use App\Models\property;
use App\Models\OtpVerify;
use App\Models\RouteEscorts;
use App\Models\Driver_Billing_Summary;
use App\Models\Sms;
use App\Http\Controllers\MaskNumberClearController;
use App\Http\Controllers\ElasticController;

class ReclubService
{
    protected CommonFunction $commonFunction;
    protected ?Branch $currentBranch = null;

    public function __construct(CommonFunction $commonFunction)
    {
        $this->commonFunction = $commonFunction;
    }
	         
    public function reclub_roster(): FoundationApplication|Response|ResponseFactory
    {
        try {
            $branch_id = Auth::user()->BRANCH_ID;
            $userid = Auth::user()->id;
            $db_name = Auth::user()->dbname;
            $RS_ACTIVE=MyHelper::$RS_ACTIVE;
            $curdate=date('Y-m-d');
            $active=true;
           $authUser = Auth::user();
           $vendor_id = Auth::user()->vendor_id;
		    $user_type = Auth::user()->user_type;
           $RS_NEWROSTER=MyHelper::$RS_NEWROSTER;
            $RS_TOTALALLOT=MyHelper::$RS_TOTALALLOT;
            $RS_TOTALACCEPT=MyHelper::$RS_TOTALACCEPT;
            $RS_TOTALEXECUTE=MyHelper::$RS_TOTALEXECUTE;
			$buffer_time=10800;
			$livetime = date('Y-m-d H:i:s', time() - $buffer_time); 
            if($user_type==MyHelper::$ADMIN)
			{
                $vendor_id='';
			}
			else
			{
				//$cond='and RS.VENDOR_ID='.$vendor_id;
                $vendor_id=$vendor_id;
			}


            $roster_data = DB::connection($db_name)->table('rosters as RS')
                ->join('roster_passengers as RP', 'RP.ROSTER_ID', '=', 'RS.ROSTER_ID')
                ->where('RS.BRANCH_ID', $branch_id)
                ->where('RS.ACTIVE', $RS_ACTIVE)
                ->where('RP.ACTIVE', $RS_ACTIVE)
                ->whereIn('RS.ROSTER_STATUS', [$RS_NEWROSTER,$RS_TOTALALLOT,$RS_TOTALACCEPT,$RS_TOTALEXECUTE])
                ->where(function($query) use ($livetime) {
                    $query->where('RS.ESTIMATE_START_TIME', '>=', $livetime)
                        ->orWhere('RS.ESTIMATE_END_TIME', '>=', $livetime);
                })
                ->when($vendor_id, function ($query, $vendor_id) {
                    return $query->where('RS.VENDOR_ID', $vendor_id);
                })
                ->select(
                    'RS.ROSTER_ID',
                    'RS.ROUTE_ID',
                    'RS.TRIP_TYPE',
                    'RS.ESTIMATE_END_TIME',
                    'RS.ESTIMATE_START_TIME',
                    DB::raw("GROUP_CONCAT(RP.EMPLOYEE_ID SEPARATOR ' ') as emp_id"),
                    DB::raw("if(RS.TRIP_TYPE='P',RS.ESTIMATE_END_TIME,RS.ESTIMATE_START_TIME) as shift_time"),
                )
                ->groupBy('RS.ROSTER_ID')
                ->get();
        
            return response([
                'success' => true,
                'status' => 3,
                'roster_data' => $roster_data
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'Reclub status Unsuccessful' : 'Reclub status Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ],500);
        }
    } 
    public function reclubing_add($request): FoundationApplication|Response|ResponseFactory
    {
        try {
            $branch_id = Auth::user()->BRANCH_ID;
            $userid = Auth::user()->id;
            $db_name = Auth::user()->dbname;
            $RS_ACTIVE=MyHelper::$RS_ACTIVE;
            $curdate=date('Y-m-d');
            $active=true;
           $vendor_id = Auth::user()->vendor_id;
		   $user_type = Auth::user()->user_type;
		   $buffer_time=6000;
			$current_date_time = date('Y-m-d H:i:s', time() - $buffer_time); 

           $RS_NEWROSTER=MyHelper::$RS_NEWROSTER;
            $RS_TOTALALLOT=MyHelper::$RS_TOTALALLOT;
            $RS_TOTALACCEPT=MyHelper::$RS_TOTALACCEPT;
            $RS_TOTALEXECUTE=MyHelper::$RS_TOTALEXECUTE;

            $selected_roster_id=$request->selected_roster_id;
            $selected_passenger_id=$request->selected_passenger_id;
            $trip_type=$request->trip_type;

            if($user_type==MyHelper::$ADMIN)
			{
                $vendor_id='';
			}
			else
			{
				//$cond='and RS.VENDOR_ID='.$vendor_id;
                $vendor_id=$vendor_id;
			}

            $propertie = property::where([['BRANCH_ID', '=', $branch_id],['PROPERTIE_NAME', '=', 'VENDOR_RECLUB_OPTION_SET'], ['ACTIVE', '=', $RS_ACTIVE]])->get();
			$PROPERTIE_VALUE = $propertie[0]->PROPERTIE_VALUE;


           $data = DB::table('rosters as RS')
            ->join('vendors as V', 'V.VENDOR_ID', '=', 'RS.VENDOR_ID') // INNER JOIN vendors
            ->leftJoin('cab as C', 'C.CAB_ID', '=', 'RS.CAB_ID') // LEFT JOIN cab
            ->leftJoin('vehicles as VH', 'VH.VEHICLE_ID', '=', 'C.VEHICLE_ID') // LEFT JOIN vehicles
            ->select(
                'RS.ROSTER_ID',
                'RS.ROSTER_STATUS',
                'RS.DRIVER_MASK_NUMBER',
                'RS.ROUTE_ID',
                'RS.CAB_ID as assign_cab_id',
                'RS.ESTIMATE_END_TIME',
                'RS.TRIP_TYPE',
                DB::raw('SUM(RS.PASSENGER_ALLOT_COUNT + RS.PASSENGER_CLUBING_COUNT) AS Passenger_cnt'),
                DB::raw("IF(RS.TRIP_TYPE = 'P', RS.ESTIMATE_END_TIME, RS.ESTIMATE_START_TIME) AS loginTime"),
                'RS.START_LOCATION',
                'RS.END_LOCATION',
                'RS.CAB_CAPACITY_COUNT',
                'V.NAME',
                'VH.VEHICLE_REG_NO'
            )
            ->when($vendor_id, function ($query, $vendor_id) {
                return $query->where('RS.VENDOR_ID', $vendor_id);
            })
            ->where(function($query) use ($current_date_time) {
                // Combining conditions for ESTIMATE_END_TIME and ESTIMATE_START_TIME
                $query->where('RS.ESTIMATE_END_TIME', '>=', $current_date_time)
                    ->orWhere('RS.ESTIMATE_START_TIME', '>=', $current_date_time);
            })
            ->where('RS.BRANCH_ID', $branch_id) // BRANCH_ID condition
            ->where('RS.ACTIVE', $RS_ACTIVE) // ACTIVE condition
            ->whereNotIn('RS.ROSTER_ID', [$selected_roster_id]) // Exclude the ROSTER_ID in the condition
            ->whereIn('RS.TRIP_TYPE', [$trip_type]) // TRIP_TYPE condition
            ->whereIn('RS.ROSTER_STATUS', [$RS_NEWROSTER,$RS_TOTALALLOT,$RS_TOTALACCEPT,$RS_TOTALEXECUTE]) // ROSTER_STATUS condition
            ->groupBy('RS.ROSTER_ID'); // Group by ROSTER_ID
           // ->get(); // Fetch results


			$perPage = $request->input('per_page', MyHelper::$PAGINATION_PER_PAGE);
            $filterModel = $request->input('filterModel');
            if ($filterModel) {
                foreach ($filterModel as $field => $filter) {
                    if (isset($filter['filter']) && $filter['filter'] !== '') {
                        $value = $filter['filter'];
                        $type = $filter['type'];

                        switch ($field) {
                            case 'ROUTE_ID':
                                $data->where('RS.ROUTE_ID', 'like', "%{$value}%");
                                break;
                            case 'EMPLOYEE_ID':
                                $data->where('RS.START_LOCATION', 'like', "%{$value}%");
                                break;
                        }
                    }
                }
            }


            if ($request->has('orderBy') && !empty($request->input('orderBy'))) {
                $orderBy = $request->input('orderBy');
                $order = $request->input('order', 'asc');
                $data->orderBy($orderBy, $order);
            } else {
                $data->orderBy('RS.ROSTER_ID');
            }


            if ($perPage === MyHelper::$PAGINATION_PER_PAGE_ALL) {
                $paginateddata = $data->paginate($data->count());
            } else {
                $perPage = is_numeric($perPage) ? (int)$perPage : MyHelper::$PAGINATION_PER_PAGE;
                $paginateddata = $data->paginate($perPage);
            }
                
            return response([
                'success' => true,
                'status' => 3,
                'roster_details' => $paginateddata
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'Roster Details Unsuccessful' : 'Roster Details status Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ],500);
        }
    } 
    public function reclub_roster_details($request): FoundationApplication|Response|ResponseFactory
    {
        try {
            $branch_id = Auth::user()->BRANCH_ID;
            $userid = Auth::user()->id;
            $db_name = Auth::user()->dbname;
            $RS_ACTIVE=MyHelper::$RS_ACTIVE;
            $curdate=date('Y-m-d');
            $active=true;
            $authUser = Auth::user();
            $selected_roster_id=$request->selected_roster_id;

            $trip_roster=DB::connection($db_name)->table("rosters")->select("TRIP_TYPE")->where("ROSTER_ID","=",$selected_roster_id)->get();
            $trip_type=$trip_roster[0]->TRIP_TYPE;
            $category=$trip_type=='P'?'Pickup':'Drop';
           $data = $result = DB::connection($db_name)->table("roster_passengers as RP")->select('RS.TRIP_TYPE', 'RS.ROUTE_ID', 'RS.CAB_ID', 'RS.ESTIMATE_START_TIME', 'RS.ESTIMATE_END_TIME', 'RS.PASSENGER_ALLOT_COUNT', 'RP.ROSTER_ID', 'RP.ROSTER_PASSENGER_ID', 'RP.PASSENGER_MASK_NUMBER','RP.EMPLOYEE_ID', 'EM.EMPLOYEES_ID','EM.GENDER','EM.NAME', 'EM.MOBILE','V.VEHICLE_REG_NO',
		   DB::raw("if(RS.TRIP_TYPE='P',RS.ESTIMATE_END_TIME,RS.ESTIMATE_START_TIME) as shift_time")
		   )
           ->join('rosters as RS', 'RS.ROSTER_ID', '=', 'RP.ROSTER_ID')
           ->join('employees as EM', 'EM.EMPLOYEES_ID', '=', 'RP.EMPLOYEE_ID')
           ->leftjoin('cab as C', 'C.CAB_ID', '=', 'RS.CAB_ID')
           ->leftjoin('vehicles as V', 'V.VEHICLE_ID', '=', 'C.VEHICLE_ID')
           ->WHERE('RP.ROSTER_ID', '=', $selected_roster_id)
           ->WHERE('EM.BRANCH_ID', '=', $branch_id)
           
           ->WHERE('RP.ACTIVE', '=',$RS_ACTIVE );
          // ->get();

		   $perPage = $request->input('per_page', MyHelper::$PAGINATION_PER_PAGE);
		   $filterModel = $request->input('filterModel');
		   if ($filterModel) {
			   foreach ($filterModel as $field => $filter) {
				   if (isset($filter['filter']) && $filter['filter'] !== '') {
					   $value = $filter['filter'];
					   $type = $filter['type'];

					   switch ($field) {
						   case 'ROUTE_ID':
							   $data->where('RS.ROUTE_ID', 'like', "%{$value}%");
							   break;
						   case 'EMPLOYEE_ID':
							   $data->where('RP.EMPLOYEE_ID', 'like', "%{$value}%");
							   break;
					   }
				   }
			   }
		   }


		   if ($request->has('orderBy') && !empty($request->input('orderBy'))) {
			   $orderBy = $request->input('orderBy');
			   $order = $request->input('order', 'asc');
			   $data->orderBy($orderBy, $order);
		   } else {
			   $data->orderBy('RP.ROSTER_ID');
		   }


		   if ($perPage === MyHelper::$PAGINATION_PER_PAGE_ALL) {
			   $paginateddata = $data->paginate($data->count());
		   } else {
			   $perPage = is_numeric($perPage) ? (int)$perPage : MyHelper::$PAGINATION_PER_PAGE;
			   $paginateddata = $data->paginate($perPage);
		   }

		   foreach($paginateddata as $data_val){
				$data_val->NAME = $this->commonFunction->AES_DECRYPT($data_val->NAME, config('app.aes_encrypt_key'));
				$data_val->MOBILE =$this->commonFunction->AES_DECRYPT($data_val->MOBILE, config('app.aes_encrypt_key'));
			}

            return response([
                'success' => true,
                'status' => 3,
                'roster_details' => $paginateddata,
            ]);
        } catch (\Throwable|\Exception $e) {
            DB::rollBack();
            $this->commonFunction->logException($e);
            $message = $active ? 'Roster Reclub  details Unsuccessful' : 'Roster Reclub details Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ],500);
        }
    } 
    
   
    
    public function reclubed_new_roster($request,$active=true): FoundationApplication|Response|ResponseFactory
    {
        try {
            $curdatetime = date('Y-m-d H:i:s');
					$branch_id = Auth::user()->BRANCH_ID;
					$RS_ACTIVE = MyHelper::$RS_ACTIVE;
					$RS_INACTIVE = MyHelper::$RS_INACTIVE;
					$date = Carbon::now();
					$id = Auth::user()->id;
					$ESCORT_REMOVE = MyHelper::$ESCORT_REMOVE;
					$RS_FGENDER = MyHelper::$RS_FGENDER;
					$ESCORT_NEW = MyHelper::$ESCORT_NEW;
					$old_route = $request->selected_old_roster_id;
					$updated_route = $request->selected_new_roster_id;
					$passenger_id = $request->selected_passenger_id;
					$old_cab_id = $request->old_cab_id;
					//$assign_cab_id = stripslashes(trim($request->new_cab_id));
					
					$AES_KEY = env("AES_ENCRYPT_KEY");
					$RP_PICKROUTE = MyHelper::$RP_PICKROUTE;
					$RP_DROPROUTE = MyHelper::$RP_DROPROUTE;
					$PR_HELPLINENO = MyHelper::$PR_HELPLINENO;
					$PR_SMSTAG = MyHelper::$PR_SMSTAG;
					$propertie = property::where([['BRANCH_ID', '=', $branch_id], ['ACTIVE', '=', $RS_ACTIVE]])->get();
						
						for ($ii = 0; $ii < count($propertie); $ii++) 
                        {
							
							$PROPERTIE_NAME = $propertie[$ii]->PROPERTIE_NAME;
							$PROPERTIE_VALUE = $propertie[$ii]->PROPERTIE_VALUE;
							
							switch ($PROPERTIE_NAME) {
								case $PR_HELPLINENO:
								$HELP_LINE = $PROPERTIE_VALUE;
								break;
								case $PR_SMSTAG:
								$SMSTAG = $PROPERTIE_VALUE;
								break;
								case 'SMS_ALERT':
								$PR_SMS_ALERT = $PROPERTIE_VALUE;
								break;
								case 'SMS_LAST_TAG':
								$SMS_LAST_TAG = $PROPERTIE_VALUE;
								break;
								default:
								break;
							}
						}
                    $mask_enable = $this->commonFunction->GetPropertyValue('CALL MASKING OPTION');
                    $emp_details = DB::table('roster_passengers as R')
                    ->join('rosters as RR', 'RR.ROSTER_ID', '=', 'R.ROSTER_ID')
                    ->join('employees as E', 'E.EMPLOYEES_ID', '=', 'R.EMPLOYEE_ID')
                    ->join('locations as L', 'L.LOCATION_ID', '=', 'R.LOCATION_ID')
                    ->where('E.BRANCH_ID', '=', $branch_id)
                    ->where('R.ROSTER_PASSENGER_ID', '=', $passenger_id)
                    ->where('R.ACTIVE', '=', $RS_ACTIVE)
                    ->select('E.NAME as EMPNAME','E.MOBILE_CATEGORY','E.MOBILE','E.EMAIL','E.SMS_ALERT','E.GENDER','E.EMPLOYEES_ID','E.PROJECT_NAME', 'E.ADDRESS','E.MOBILE_GCM','L.LOCATION_ID','L.LOCATION_NAME','L.LATITUDE','L.LONGITUDE','R.ESTIMATE_START_TIME','R.ACTUAL_START_TIME','R.ESTIMATE_END_TIME','R.DRIVER_ARRIVAL_TIME','R.ROSTER_PASSENGER_STATUS','R.START_LAT','R.START_LONG', 'R.ACTUAL_END_TIME','R.PASSENGER_MASK_NUMBER','R.END_LAT','R.END_LONG','R.ROUTE_ORDER','RR.ROUTE_ID','RR.DRIVER_MASK_NUMBER' )->get();

                    if ($emp_details->isEmpty()) 
                    {

                        return response([
                            'success' => false,
                            'status' => 4,
                            'message' => 'No passenger found',
                            'validation_controller' => true,
                            
                        ],500);
                    }
					//$EMPNAME=$this->commonFunction->AES_DECRYPT($emp_details[0]->EMPNAME,$AES_KEY);
					//$empmobile=$this->commonFunction->AES_DECRYPT($emp_details[0]->MOBILE,$AES_KEY);
					$EMPNAME=" RA";
					//$empmobile=7200389436;
					$empmobile=9543417214;
					$LOCATION_NAME = $emp_details[0]->LOCATION_NAME;
					$ESTIMATE_START_TIME=$emp_details[0]->ESTIMATE_START_TIME;
					$ESTIMATE_END_TIME=$emp_details[0]->ESTIMATE_END_TIME;
					$DRIVER_ARRIVAL_TIME=$emp_details[0]->DRIVER_ARRIVAL_TIME;
					$ACTUAL_START_TIME=$emp_details[0]->ACTUAL_START_TIME;
					$START_LAT=$emp_details[0]->START_LAT;
					$START_LONG=$emp_details[0]->START_LONG;
					$ACTUAL_END_TIME=$emp_details[0]->ACTUAL_END_TIME;
					$END_LAT=$emp_details[0]->END_LAT;
					$END_LONG=$emp_details[0]->END_LONG;
					$ROUTE_ORDER=$emp_details[0]->ROUTE_ORDER;
					$ROSTER_PASSENGER_STATUS=$emp_details[0]->ROSTER_PASSENGER_STATUS;
					$EMPLOYEES_ID=$emp_details[0]->EMPLOYEES_ID;
					$LOCATION_ID=$emp_details[0]->LOCATION_ID!=''?$emp_details[0]->LOCATION_ID:'';
					$MOBILE_GCM = $emp_details[0]->MOBILE_GCM;
					$MOBILE_CATEGORY = $emp_details[0]->MOBILE_CATEGORY;
					$previous_roster_passenger_status=$emp_details[0]->ROSTER_PASSENGER_STATUS;
					$SMS_ALERT=$emp_details[0]->SMS_ALERT;
					$ROUTE_ID=$emp_details[0]->ROUTE_ID;
					
					
					
			
					if($mask_enable=='Y')
					{
						
						$mask= new MaskNumberClearController();
						//$res=$mask->Clear_MaskNumber($passenger_id,$branch_id,$old_route,'Passenger');
					
					}
					//exit;
					$deactive_passenger=DB::table("roster_passengers")->select("ROSTER_PASSENGER_ID")->where("ROSTER_PASSENGER_ID","=",$passenger_id)->update(array("ACTIVE" => $RS_INACTIVE, "UPDATED_BY" => $id,"updated_at"=>date('Y-m-d H:i:s')));
					$insert_passanger = array("EMPLOYEE_ID" => $EMPLOYEES_ID, "ROSTER_ID" => $updated_route, "ESTIMATE_START_TIME" => $ESTIMATE_START_TIME,
					"DRIVER_ARRIVAL_TIME" => $DRIVER_ARRIVAL_TIME, "ACTUAL_START_TIME" => $ACTUAL_START_TIME, "START_LAT" => $START_LAT,
					"START_LONG" => $START_LONG, "ACTUAL_END_TIME" => $ACTUAL_END_TIME, "END_LAT" => $END_LAT, "END_LONG" => $END_LONG, "ACTIVE" => $RS_ACTIVE, 
					"ESTIMATE_END_TIME" => $ESTIMATE_END_TIME, "ROUTE_ORDER" => $ROUTE_ORDER, "CREATED_BY" => $id, "CREATED_DATE" => date('Y-m-d H:i:s'),"LOCATION_ID"=>$LOCATION_ID);
					$last_passenger_id=DB::table("roster_passengers")->insertGetId($insert_passanger);
					
					//$last_id=Roster_passanger::insert($insert_passanger);
					
					/* $passenger_update = Roster_passanger::where("ROSTER_PASSENGER_ID", "=", $passenger_id)
					->update(array("ROSTER_ID" => $updated_route,"UPDATED_BY"=>$id)); */
					/* if($driver_mask_no!='' && $driver_mask_no!='--')
					{
						$obj->clubbing_property_check($passenger_id,$updated_route);
					} */
					
					
					$RS_AUTOCANCEL = MyHelper::$RS_AUTOCANCEL;
					$picktype = "SELECT TRIP_TYPE,CAB_ID FROM rosters WHERE ROSTER_ID= '" . $updated_route . "'";
					$pick_drop = DB::select($picktype);
					$triptype = $pick_drop[0]->TRIP_TYPE;
					$assign_cab_id=$pick_drop[0]->CAB_ID!=NULL?$pick_drop[0]->CAB_ID:'';
					
					$OTP_CATEGORY = $triptype == 'P' ? MyHelper::$SMS_PICKUP : MyHelper::$SMS_DROP;
					$SMS_PICKDROP = $triptype == 'P' ? MyHelper::$SMS_PICKUP : MyHelper::$SMS_DROP;
					$otp_update='';
					$otp_verified_status='';
					$otp_insert_array=array();
					$result_OTP_Insert='';
					if ($old_cab_id != '' && $assign_cab_id == '') {
						$otp_update = OtpVerify::where("ROSTER_PASSENGER_ID", "=", $passenger_id)->update(array("VERIFIED_STATUS" => 2,"UPDATED_BY"=>$id));
						$otp_verified_status=2;
						} elseif (($old_cab_id == '' && $assign_cab_id != '') OR ($old_cab_id != '' && $assign_cab_id != '') ) {
						$EMP_OTP = substr(number_format(time() * rand(), 0, '', ''), 0, 4);
						$otp_update = OtpVerify::where("ROSTER_PASSENGER_ID", "=", $passenger_id)->update(array("VERIFIED_STATUS" => 2,"UPDATED_BY"=>$id));
						$arr = array("MOBILE_NO" => $empmobile, "ROSTER_PASSENGER_ID" => $last_passenger_id, 'OTP' => $EMP_OTP, "VERIFIED_STATUS" => env('VERIFIED_STATUS'), "OTP_CATEGORY" => $OTP_CATEGORY, "CREATED_BY" => $id, "CREATED_DATE" => $date->format("Y-m-d H:i:s"));
						array_push($otp_insert_array,$arr);
						$result_OTP_Insert = OtpVerify::insert($arr);
						$otp_verified_status=2;
						
						//$mask_enable = $obj->GetPropertyValue('CALL MASKING OPTION');
		
						if($mask_enable=='Y')
						{
							$mask= new MaskNumberController_new();
							//$mask->masknumber_clubbing_reallot($updated_route,$EMPLOYEES_ID,$branch_id,'clubbing');
							//($rosterid,$empid,$branchid,$category)
						} 
						
                        $cab_details = DB::table('cab as C')
                        ->join('vehicles as V', 'V.VEHICLE_ID', '=', 'C.VEHICLE_ID')
                        ->join('vehicle_models as VM', 'VM.VEHICLE_MODEL_ID', '=', 'V.VEHICLE_MODEL_ID')
                        ->join('branch as B', 'B.BRANCH_ID', '=', 'C.BRANCH_ID')
                        ->leftJoin('devices as D', 'D.DEVICE_ID', '=', 'C.DEVICE_ID')
                        ->leftJoin('drivers as DR', 'DR.DRIVERS_ID', '=', 'C.DRIVER_ID')
                        ->where('C.CAB_ID', '=', $assign_cab_id)
                        ->select('D.MOBILE_GCM as driver_gcm','V.VEHICLE_REG_NO','VM.MODEL','VM.CAPACITY','B.BRANCH_NAME','DR.DRIVER_MOBILE',
                            'DR.DRIVERS_NAME','C.CAB_ID')
                        ->get();

						$VEHICLE_REG_NO = $cab_details[0]->VEHICLE_REG_NO;
						$MODEL = $cab_details[0]->MODEL;
						$SITENAME = $cab_details[0]->BRANCH_NAME;
						$DRIVER_GCM = $cab_details[0]->driver_gcm;
						$DRIVER_MOBILE = $cab_details[0]->DRIVER_MOBILE;
						$DRIVERS_NAME = $cab_details[0]->DRIVERS_NAME;
						$CAB_ID = $cab_details[0]->CAB_ID;
						$ESTIMATESTARTDATE = date('Y-m-d',strtotime($ESTIMATE_START_TIME));
						$ESTIMATESTARTTIME = date('H:i:s',strtotime($ESTIMATE_START_TIME));
						$date=$ESTIMATESTARTDATE." ".$ESTIMATESTARTTIME;
						$sms_fmt= date('jS M g:ia', strtotime($date));
						$VEHICLE_REG_NO=str_replace(' ', '', $VEHICLE_REG_NO);
						
						$EMPNAME=substr(str_replace(' ', '', $EMPNAME), 0, 5);
						$DRIVERS_NAME=substr(str_replace(' ', '', $DRIVERS_NAME), 0, 5);
						
						if($branch_id==32 )
						{
							if($triptype=='P')
							{
								//$message="Hi $EMPNAME, your estimated pickup time on ".$ESTIMATESTARTDATE." is ".$ESTIMATESTARTTIME.". Cab ($MODEL) $VEHICLE_REG_NO, $DRIVERS_NAME, $DRIVER_MOBILE, OTP - $EMP_OTP. Helpline - $HELP_LINE";
								$message="Hi $EMPNAME, your estimated pickup time on ".date('d/m/Y',strtotime($ESTIMATESTARTDATE))." is ".$ESTIMATESTARTTIME.". Cab $VEHICLE_REG_NO-$DRIVER_MOBILE, OTP - $EMP_OTP. Helpline - $HELP_LINE.$SMS_LAST_TAG";
							}
							else
							{
								//$message="Hi $EMPNAME, your drop to $LOCATION_NAME is scheduled for ".$ESTIMATESTARTTIME." on ".$ESTIMATESTARTDATE." Cab ($MODEL) $VEHICLE_REG_NO, $DRIVERS_NAME, $DRIVER_MOBILE and your Route no. is $ROUTE_ID. OTP - $EMP_OTP. Reporting time ".$ESTIMATESTARTTIME.". Helpline - $HELP_LINE";
								$message="Hi $EMPNAME, your drop to $LOCATION_NAME is scheduled for ".date('H:i',strtotime($ESTIMATESTARTTIME))." on ".date('d/m/Y',strtotime($ESTIMATESTARTDATE))." Cab $VEHICLE_REG_NO-$DRIVER_MOBILE and your Route no. is $ROUTE_ID. OTP - $EMP_OTP. Reporting time ".date('H:i',strtotime($ESTIMATESTARTTIME)).". Helpline - $HELP_LINE.$SMS_LAST_TAG";
							}
						}
						else
						{
							$message="Hi, $SMS_PICKDROP at $LOCATION_NAME on $sms_fmt Cab Details: $DRIVERS_NAME-$DRIVER_MOBILE-$VEHICLE_REG_NO OTP-$EMP_OTP Help-$HELP_LINE. $SMS_LAST_TAG";
							
						}
						
						if( $PR_SMS_ALERT==1)
						{
							$insert_sms = array("BRANCH_ID" => $branch_id, "ORIGINATOR" => $SMSTAG, "RECIPIENT" => $empmobile, "MESSAGE" => $message,
							"SENT_DATE" => '1900-01-01 00:00:00', "REF_NO" => '--', "CREATED_BY" => $id, "CREATED_DATE" => $curdatetime);
							if($SMS_ALERT==1)
							{
								Sms::insert($insert_sms);
							}
						}
						/*Mobile GCM */
						if($MOBILE_GCM != '' && $DRIVER_GCM!='')
						{
							$emp_heading='New Cab Details';
							$driver_heading='New Club';
							$cur_date_time=date('Y-m-d H:i:s');
							$emp_message="Your cab details for Pickup - ".$VEHICLE_REG_NO."( ".$DRIVER_MOBILE." ) on $ESTIMATESTARTDATE  $ESTIMATESTARTTIME at $LOCATION_NAME. Enter OTP $EMP_OTP, use TOPSA App to track your cab";
							$driver_message="$EMPNAME - $LOCATION_NAME  was clubbed in your trip";
							
							$emp_notification=DB::insert("INSERT INTO `notification` (`branch_id`, `empid_cabid`, `fcm_id`, `heading`, `message`, `category_value`,`app_category`,`created_at`) VALUES ('".$branch_id."','".$EMPLOYEES_ID."','".$MOBILE_GCM."','".$emp_heading."','".$emp_message."','3','".$MOBILE_CATEGORY."','".$cur_date_time."')");
							
							$cab_notification=DB::insert("INSERT INTO `notification` (`branch_id`, `empid_cabid`, `fcm_id`, `heading`, `message`, `category_value`,`app_category`,`created_at`) VALUES ('".$branch_id."','".$CAB_ID."','".$DRIVER_GCM."','".$driver_heading."','".$driver_message."','3','ANDROID','".$cur_date_time."')");
						}
						/* End */
					}
					
					/*$sql = "SELECT 	if(R.PASSENGER_CLUBING_COUNT > 0,'PASSENGER_CLUBING_COUNT','PASSENGER_ALLOT_COUNT') AS decrementfield	FROM rosters R 
					WHERE R.ACTIVE = '".$RS_ACTIVE."' and R.ROSTER_ID= '".$old_route."' ";
					$rosterstatus = DB::select($sql); */

                    $rosterstatus = DB::table('rosters as R')
                    ->selectRaw("IF(R.PASSENGER_CLUBING_COUNT > 0, 'PASSENGER_CLUBING_COUNT', 'PASSENGER_ALLOT_COUNT') as decrementfield")
                    ->where('R.ACTIVE', '=', $RS_ACTIVE)
                    ->where('R.ROSTER_ID', '=', $old_route)
                    ->get();

					$decrementfield = $rosterstatus[0]->decrementfield;
					$RPS_TTLNOSHOWS=explode(',',MyHelper::$RPS_TTLNOSHOW);
					if(in_array($previous_roster_passenger_status,$RPS_TTLNOSHOWS)==false)
					{
						$roster_dec = Roster::where([["ROSTER_ID", "=", $old_route], ["ACTIVE", "=", $RS_ACTIVE]])->decrement("$decrementfield");
					}
					else
					{
						$roster_dec='1';
					}
					// $roster_dec = Roster::where([["ROSTER_ID", "=", $old_route], ["ACTIVE", "=", $RS_ACTIVE]])->decrement("$decrementfield");
					
					$roster_cancel = Roster::select("PASSENGER_ALLOT_COUNT","PASSENGER_CLUBING_COUNT","CAB_ID","ROSTER_STATUS","ROSTER_ID")->where([["ROSTER_ID", "=", $old_route], ["ACTIVE", "=", $RS_ACTIVE]])->get();
					$cnt = count($roster_cancel);
					$auto_cancel_sts='';
					$roster_update_cancel='';
					if ($cnt > 0) {
						$cab_id=$roster_cancel[0]->CAB_ID;
						$roster_sts=$roster_cancel[0]->ROSTER_STATUS;
						$passenger_allot_cnt = $roster_cancel[0]->PASSENGER_ALLOT_COUNT;
						$PASSENGER_CLUBING_COUNT = $roster_cancel[0]->PASSENGER_CLUBING_COUNT;
						$tot_cnt=$passenger_allot_cnt+$PASSENGER_CLUBING_COUNT;
						$RS_AUTOCANCEL = MyHelper::$RS_AUTOCANCEL;
						if ($tot_cnt==0 && $cab_id=='') {
							$sql = "SELECT conv(bin(R.ROSTER_STATUS)+bin(" . $RS_AUTOCANCEL . "),2,10) as upstatus,R.ROSTER_ID FROM (SELECT ROSTER_STATUS,ROSTER_ID	FROM rosters WHERE ROSTER_ID='".$old_route."') R";
							$roster_status = DB::select($sql);
							$up_sts=$roster_status[0]->upstatus;
							$auto_cancel_sts=$up_sts;
							$roster_update_cancel=DB::update("update rosters set ROSTER_STATUS='".$up_sts."',ACTIVE='".$RS_INACTIVE."',UPDATED_BY='".$id."' where ROSTER_ID='".$old_route."'");
							
							DB::update("UPDATE `cab_allocation`  SET ACCEPTANCE_REJECT_STATE = ACCEPTANCE_REJECT_STATE + $RS_AUTOCANCEL,UPDATED_BY='".$id."'  WHERE ROSTER_ID = '".$old_route."' ");
						}
						else if($tot_cnt==0 && $cab_id!='' )
						{
							$sql = "SELECT conv(bin(R.ROSTER_STATUS)+bin(" . $RS_AUTOCANCEL . "),2,10) as upstatus,R.ROSTER_ID FROM (SELECT ROSTER_STATUS,ROSTER_ID	FROM rosters WHERE ROSTER_ID='".$old_route."') R";
							$roster_status = DB::select($sql);
							$up_sts=$roster_status[0]->upstatus;
							$auto_cancel_sts=$up_sts;
							$roster_update_cancel =DB::update("update rosters set ROSTER_STATUS='".$up_sts."',ACTIVE='".$RS_INACTIVE."',UPDATED_BY='".$id."' where ROSTER_ID='".$old_route."'");
							
							DB::update("UPDATE `cab_allocation`  SET ACCEPTANCE_REJECT_STATE = ACCEPTANCE_REJECT_STATE + $RS_AUTOCANCEL,UPDATED_BY='".$id."'  WHERE ROSTER_ID = '".$old_route."' ");
							
							/* $roster_update_cancel = Roster::where("ROSTER_ID", "=", $old_route)->update(array("ACTIVE" => $RS_INACTIVE,"UPDATED_BY"=>$id)); */
							
						}
					}
					$roster_inc = Roster::where([["ROSTER_ID", "=", $updated_route], ["ACTIVE", "=", $RS_ACTIVE]])->increment("PASSENGER_CLUBING_COUNT");

					
					/*$already_emp="select RP.EMPLOYEE_ID,RP.ROSTER_ID,RP.ROSTER_PASSENGER_ID,RE.ROUTE_ESCORT_ID,EM.GENDER,RP.ROUTE_ORDER from roster_passengers as RP
					inner join route_escorts as RE ON RE.ROSTER_ID='".$old_route."' and RP.EMPLOYEE_ID=RE.EMPLOYEE_ID
					inner join employees as EM ON EM.EMPLOYEES_ID=RP.EMPLOYEE_ID
					where RP.ROSTER_PASSENGER_ID='".$passenger_id."' and EM.BRANCH_ID='".$branch_id."' and RE.`STATUS`='".$ESCORT_NEW."' and RE.ROSTER_ID='".$old_route."'  ";
					$isemp=DB::select($already_emp);
                    */

                    $isemp = DB::table('roster_passengers as RP')
                    ->join('route_escorts as RE', function ($join) use ($old_route) {
                        $join->on('RP.EMPLOYEE_ID', '=', 'RE.EMPLOYEE_ID')
                            ->where('RE.ROSTER_ID', '=', $old_route);
                    })
                    ->join('employees as EM', 'EM.EMPLOYEES_ID', '=', 'RP.EMPLOYEE_ID')
                    ->where('RP.ROSTER_PASSENGER_ID', '=', $passenger_id)
                    ->where('EM.BRANCH_ID', '=', $branch_id)
                    ->where('RE.STATUS', '=', $ESCORT_NEW)
                    ->where('RE.ROSTER_ID', '=', $old_route)
                    ->select('RP.EMPLOYEE_ID', 'RP.ROSTER_ID', 'RP.ROSTER_PASSENGER_ID', 'RE.ROUTE_ESCORT_ID', 'EM.GENDER', 'RP.ROUTE_ORDER')
                    ->get();

					$ccnt=count($isemp);
					$gender=$ccnt>0?$isemp[0]->GENDER:'';
					if($ccnt > 0 && $gender==$RS_FGENDER)
					{
						$employeeID=$isemp[0]->EMPLOYEE_ID;
						$remove_escort=RouteEscorts::where([["ROSTER_ID", "=", $old_route],["EMPLOYEE_ID", "=", $employeeID]])->update(array("STATUS" => $ESCORT_REMOVE,"ESCORT_ID" => NULL,"UPDATED_BY"=>$id )); 
					}
					
					/*Escort Enable */
					$RPS_TTLNOSHOW=MyHelper::$RPS_TTLNOSHOW;
					$RP_DROPROUTE = MyHelper::$RP_DROPROUTE;
					$PR_ESCORT_ENABLE=MyHelper::$PR_ESCORT_ENABLE;
					$PR_ESCORT_START_TIME=MyHelper::$PR_ESCORT_START_TIME;
					$PR_ESCORT_END_TIME=MyHelper::$PR_ESCORT_END_TIME;
					
					$escort_check = "SELECT R.TRIP_TYPE,if(R.CAB_ID is NULL,0,R.CAB_ID) AS CABID,rp.ROSTER_ID,rp.EMPLOYEE_ID,e.BRANCH_ID,e.GENDER,R.ESTIMATE_START_TIME,R.ESTIMATE_END_TIME,
					rp.EMPLOYEE_ID,rp.ROSTER_PASSENGER_STATUS,R.PASSENGER_ALLOT_IN_ROUT_COUNT FROM roster_passengers as rp
					INNER JOIN rosters R ON R.ROSTER_ID = '".$updated_route."'
					INNER JOIN employees as e on e.EMPLOYEES_ID=rp.EMPLOYEE_ID
					WHERE R.BRANCH_ID ='".$branch_id."' and e.BRANCH_ID ='".$branch_id."'  and rp.ROSTER_ID = '".$updated_route."' and rp.ACTIVE = '".$RS_ACTIVE."' and rp.ROSTER_PASSENGER_STATUS NOT IN ($RPS_TTLNOSHOW)
					ORDER BY rp.ROUTE_ORDER desc LIMIT 1"; 
					$escortcheck = DB::select($escort_check);
					$isEscort = '';
					$GENDER = $escortcheck[0]->GENDER;
					if($GENDER==MyHelper::$RS_MGENDER)
					{
						$remove_escort=RouteEscorts::where("ROSTER_ID", "=", $updated_route)->update(array("STATUS" => $ESCORT_REMOVE,"ESCORT_ID"=>NULL,"UPDATED_BY"=>$id));
					}
					if(count($escortcheck)>0)
					{
						if($escortcheck[0]->TRIP_TYPE==$RP_DROPROUTE)
						{
							$is_Escort_Time=date("H:i:s",strtotime($escortcheck[0]->ESTIMATE_START_TIME));
						}
						else
						{
							$is_Escort_Time=date("H:i:s",strtotime($escortcheck[0]->ESTIMATE_END_TIME));
						}
						
					}
					$propertie = property::where([['BRANCH_ID', '=', $branch_id], ['ACTIVE', '=', $RS_ACTIVE]])->get();
					for($i=0;$i<count($propertie);$i++)
					{
						$PROPERTIE_NAME = $propertie[$i]->PROPERTIE_NAME;
						$PROPERTIE_VALUE = $propertie[$i]->PROPERTIE_VALUE;
						
						switch ($PROPERTIE_NAME) {
							case $PR_ESCORT_ENABLE:
							$PR_ESCORTENABLE = $PROPERTIE_VALUE;
							break;
							case $PR_ESCORT_START_TIME:
							$PR_ESCORTSTART_TIME = $PROPERTIE_VALUE;
							break;
							case $PR_ESCORT_END_TIME:
							$PR_ESCORTEND_TIME = $PROPERTIE_VALUE;
							break;
							default:
							break;
						}
					}
					if ($PR_ESCORTENABLE == 'Y') {
						/*if (strtotime($is_Escort_Time) >= strtotime($PR_ESCORTSTART_TIME) && strtotime($PR_ESCORTEND_TIME) <= strtotime($is_Escort_Time)) {
							$isEscort = 'true';
							} else {
							$isEscort = 'false';
						}*/
						$isEscort = $this->commonFunction->check_time($PR_ESCORTSTART_TIME, $PR_ESCORTEND_TIME, $is_Escort_Time) ? "true" : "false";
					}
					$insert_escort=array();
					$escort_roster_id='';
					$already_escort_roster='';
					$remove_already_escort='';
					$ins='';
					if($isEscort == 'true')
					{
						if(count($escortcheck) > 0 )
						{
							$GENDER = $escortcheck[0]->GENDER;
							$EMPLOYEE_ID = $escortcheck[0]->EMPLOYEE_ID;
							$TRIP_TYPE = $escortcheck[0]->TRIP_TYPE;
							$CABID = $escortcheck[0]->CABID;
							if($GENDER == $RS_FGENDER)
							{
								$already_escort="select count(*) as cnt from route_escorts where ROSTER_ID='".$updated_route."'";
								$already_escort_res=DB::select($already_escort);
								$count=count($already_escort_res);
								if($count > 0)
								{
									$remove_already_escort=RouteEscorts::where("ROSTER_ID", "=", $updated_route)->update(array("STATUS" => $ESCORT_REMOVE,"ESCORT_ID"=>NULL,"UPDATED_BY"=>$id));
									$already_escort_roster=$updated_route;
									$remove_already_escort=1;
								}
								
								// $checkescort = "SELECT COUNT(*) as ttlcnt FROM route_escorts WHERE ROSTER_ID='".$updated_route."' AND EMPLOYEE_ID='".$EMPLOYEE_ID."' and STATUS='".$ESCORT_NEW."' and BRANCH_ID='".$branch_id."' ";
								// $checkescorts = DB::select($checkescort);
								// $ttlcnts = $checkescorts[0]->ttlcnt;

                                $ttlcnts = DB::table('route_escorts')
                                ->where('ROSTER_ID', $updated_route)
                                ->where('EMPLOYEE_ID', $EMPLOYEE_ID)
                                ->where('STATUS', $ESCORT_NEW)
                                ->where('BRANCH_ID', $branch_id)
                                ->count();

								if($ttlcnts == 0)
								{
									if($escortcheck[0]->TRIP_TYPE=='P' && $escortcheck[0]->PASSENGER_ALLOT_IN_ROUT_COUNT == 0)
									{ 
									    $insert_escort1 = array("BRANCH_ID" => $branch_id, "ROSTER_ID" => $updated_route, "EMPLOYEE_ID" => $EMPLOYEE_ID, "STATUS" => $ESCORT_NEW,"CREATED_BY" => $id, "created_at" => date('Y-m-d H:i:s'));
									    $ins=RouteEscorts::insert($insert_escort1);
									    $escort_roster_id=$updated_route;
									    array_push($insert_escort,$insert_escort1);
									}
									else if($RP_DROPROUTE == $escortcheck[0]->TRIP_TYPE)
									{
										$insert_escort1 = array("BRANCH_ID" => $branch_id, "ROSTER_ID" => $updated_route, "EMPLOYEE_ID" => $EMPLOYEE_ID, "STATUS" => $ESCORT_NEW,
										"CREATED_BY" => $id, "created_at" => date('Y-m-d H:i:s'));
										$ins=RouteEscorts::insert($insert_escort1);
										$escort_roster_id=$updated_route;
										array_push($insert_escort,$insert_escort1);
									}
								}
							}
						}
					}
					$this->commonFunction->change_roster_approve_km($old_route,$updated_route);
					$this->commonFunction->noshow_change_roster_approve_km($updated_route,$triptype);
					/* log */
					
					$date_f=$this->commonFunction->date_format_add();
					
					
					//$elastic=new ElasticController();
					//$log_arr=array("ROSTER_ID"=>$updated_route,"OLD_ROSTER_ID"=>$old_route,"BRANCH_ID"=>$branch_id,"ACTION"=>'Reclubbed',"PROCESS_DATE"=>$date_f,"CATEGORY"=>'Reclub',"USER_ID"=>$id,"PASSENGER_ID"=>$passenger_id,"ROSTER_PASSENGER_DEACTIVE_SUCCESS"=>$deactive_passenger,"LAST_INSERT_ID"=>$last_passenger_id,"EMPLOYEE_ID"=>$EMPLOYEES_ID,"OTP_INSERT_SUCCESS"=>//$result_OTP_Insert);
					/* $log_arr=array("ROSTER_ID"=>$updated_route,"OLD_ROSTER_ID"=>$old_route,"BRANCH_ID"=>$branch_id,"ACTION"=>'Reclubbed',"PROCESS_DATE"=>$date_f,"CATEGORY"=>'Reclub',"USER_ID"=>$id,"PASSENGER_ID"=>$passenger_id,"ROSTER_PASSENGER_DEACTIVE_SUCCESS"=>$deactive_passenger,"LAST_INSERT_ID"=>$last_passenger_id,"INSERT_ESCORT"=>$insert_escort,"ESCORT_INSERT_SUCCESS"=>$ins,"ESCORT_ROSTER_ID"=>$escort_roster_id,"ALREADY_ESCORT_ROSTER"=>$already_escort_roster,"REMOVE_ALREADY_ESCORT_SUCCESS"=>$remove_already_escort,"OTP_UPDATE"=>$otp_update,"OTP_VERIFIED_STATUS"=>$otp_verified_status,"OTP_INSERT_ARRAY"=>$otp_insert_array,"RESULT_OTP_INSERT"=>$result_OTP_Insert,"AUTOCANCEL_UPDATE_STATUS"=>$auto_cancel_sts,"AUTO_CANCEL_UPDATE_ROSTER_SUCCESS"=>$roster_update_cancel,"EMPLOYEE_ID"=>$EMPLOYEES_ID); */
					//$ret=$elastic->insertWebLogs($log_arr);
					/* log End */
					

            return response([
                'success' => true,
                'status' => 3,
                'message' => "Reclub successfully",
            ]);
        } catch (\Throwable|\Exception $e) {
            DB::rollBack();
            $this->commonFunction->logException($e);
            $message = $active ? 'Roster Reclub  details Unsuccessful' : 'Roster Reclub details Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ],500);
        }
    } 
    
   
    
   
    
    /**
     * @throws ConnectionException
     */
    public function dataForCreateManualOTP(): FoundationApplication|Response|ResponseFactory
    {

        try {
                $user = Auth::user();
                $RS_ACTIVE = MyHelper::$RS_ACTIVE;
            
                $branch_id = Auth::user()->BRANCH_ID;
                $vendor_id = Auth::user()->vendor_id;
                $reason = DB::table("reason_master")->select('REASON', 'REASON_ID')
                                ->where([["active", "=", $RS_ACTIVE], ["CATEGORY", "=", 'ManualOTP'], ["BRANCH_ID", "=", $branch_id]])->get();
                $reset_reason = DB::table("reason_master")->select('REASON', 'REASON_ID')
                                ->where([["active", "=", $RS_ACTIVE], ["CATEGORY", "=", 'NoshowReset'], ["BRANCH_ID", "=", $branch_id]])->get();
                if (Auth::user()->user_type == MyHelper::$ADMIN) {
                    $vendor_list = Vendor::where('ACTIVE', '=', $RS_ACTIVE)->where("BRANCH_ID", "=", $branch_id)->get();
                } else {
                    $vendor_list = Vendor::where('ACTIVE', '=', $RS_ACTIVE)->where("VENDOR_ID", "=", $vendor_id)->get();
                }
                
                return response([
                'success' => true,
                'status' => 3,
                'vendor_list' => $vendor_list,
                'reason' => $reason,
                'reset_reason' => $reset_reason,
                
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Data For Create for Manual OTP  Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ],500);
        }
    }
   
    
	

   
}
