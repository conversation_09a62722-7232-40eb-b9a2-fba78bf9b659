<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class LogRequestResponse
{
    /**
     * Handle an incoming request.
     *
     * @param \Closure(Request): (Response) $next
     */
    public function handle(Request $request, Closure $next): Response
    {

        $requestToLog = clone $request;

        $headers = $requestToLog->headers->all();
        unset($headers['authorization']);

        $body = $requestToLog->all();
        if (isset($body['password'])) {
            unset($body['password']);
        }


        $token = $request->bearerToken();
        $userInfo = 'guest';
        if ($token) {
            $user = Auth::guard('api')->user(); // Assuming you're using the 'api' guard
            if ($user) {
                $userInfo = $user->name;
            }
        }

        Log::channel('request_response')->info('Incoming Request', [
            'method' => $requestToLog->method(),
            'url' => $requestToLog->fullUrl(),
            'headers' => $headers,
            'body' => $body,
            'user' => $userInfo,
        ]);

        $response = $next($request);

        $bodyOR = $response->getContent();


        if ($response->headers->get('Content-Type') === 'application/json') {
            $bodyArray = json_decode($bodyOR, true);
            if (is_array($bodyArray) && isset($bodyArray['token'])) {
                unset($bodyArray['token']);
                $bodyOR = json_encode($bodyArray);
            }
        }

        Log::channel('request_response')->info('Outgoing Response', [
            'status' => $response->status(),
            'headers' => $response->headers->all(),
            'body' =>$bodyOR,
        ]);

        Log::channel('request_response')->info('-------------------------------------------');

        return $response;
    }
}
