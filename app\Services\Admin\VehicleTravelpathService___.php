<?php

namespace App\Services\Admin;

use App\Helpers\CommonFunction;
use App\Helpers\MyHelper;
use Carbon\Carbon;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use App\Models\Vendor;
use App\Models\Cab_allocation;
use App\Models\Roster;
use App\Models\RosterPassenger;
use App\Models\RouteEscorts;
use App\Models\Reason_Log;
use App\Models\Cab;
use App\Models\Vehicle;
use App\Models\VehicleModel;
use App\Models\Driver;
use App\Models\property;
use App\Models\Driver_Billing_Summary;
use App\Http\Controllers\MaskNumberClearController;
use App\Http\Controllers\ElasticController;

class VehicleTravelPathService
{
    protected CommonFunction $commonFunction;
    protected ?Branch $currentBranch = null;

    public function __construct(CommonFunction $commonFunction)
    {
        $this->commonFunction = $commonFunction;
    }
	     
    
    public function vendorwise_vehicle_find($request): FoundationApplication|Response|ResponseFactory
    {
        try {
            echo "ttt";exit;
            $branch_id = Auth::user()->BRANCH_ID;
            $userid = Auth::user()->id;
            $db_name = Auth::user()->dbname;
            $RS_ACTIVE=MyHelper::$RS_ACTIVE;
            $active=true;

           $authUser = Auth::user();

           $selected_date=$request->selected_date;
           $vendor_id=$request->vendor_id;

          
           $cab_data = DB::connection("$db_name")->table('cab as C')->select('C.CAB_ID','C.VENDOR_ID', 'V.VEHICLE_ID', 'V.VEHICLE_REG_NO')
						->join('vehicles as V', 'V.VEHICLE_ID', '=', 'C.VEHICLE_ID')
						->where('C.VENDOR_ID', '=', $vendor_id)
						->where('C.BRANCH_ID', '=', $branch_id) ->where('C.ACTIVE', '=', $RS_ACTIVE)->distinct('C.CAB_ID')->get();

        
            return response([
                'success' => true,
                'status' => 3,
                'cab_data' => $cab_data,
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'Manual otp  status Unsuccessful' : 'Manual otp  status Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ],500);
        }
    } 
   
    
    /**
     * @throws ConnectionException
     */
    public function dataForCreate(): FoundationApplication|Response|ResponseFactory
    {

        try {
                $user = Auth::user();
                $RS_ACTIVE = MyHelper::$RS_ACTIVE;
            
                $branch_id = Auth::user()->BRANCH_ID;
                $vendor_id = Auth::user()->vendor_id;
                
                if (Auth::user()->user_type == MyHelper::$ADMIN) {
                    $vendor_list = Vendor::where('ACTIVE', '=', $RS_ACTIVE)->where("BRANCH_ID", "=", $branch_id)->get();
                } else {
                    $vendor_list = Vendor::where('ACTIVE', '=', $RS_ACTIVE)->where("VENDOR_ID", "=", $vendor_id)->get();
                }
                
                return response([
                'success' => true,
                'status' => 3,
                'vendor_list' => $vendor_list
                
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Data For Create for Travelpath Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ],500);
        }
    }
   
    
	

   
}
