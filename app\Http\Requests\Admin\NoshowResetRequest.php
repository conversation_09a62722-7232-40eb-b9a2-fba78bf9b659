<?php

//namespace App\Http\Requests\Admin;
namespace App\Http\Requests\Admin;

use App\Helpers\CommonFunction;
use Closure;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;

class NoshowResetRequest extends FormRequest
{
    protected CommonFunction $commonFunction;

    public function __construct(CommonFunction $commonFunction)
    {
        
    }

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        
        return [
            'selected_passenger_id' => ['required','numeric'],
            'reset_remarks' => ['required','string'],
            'resetopt' => ['required','string'],
            'reset_noshow_reason' => ['required','numeric'],
            
        ];
    }

    public function messages(): array
    {
        return [
            'selected_passenger_id.required' => 'selected_passenger_id field is required',
            'reset_remarks.required' => 'reset_remarks field is required',
            'resetopt.required' => 'resetopt field is required',
            'reset_noshow_reason.required' => 'reset_noshow_reason field is required',
        ];
    }
   

    public function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json([
            'success' => false,
            'validation_error' => true,
            'message' => 'Validation errors',
            'data' => $validator->errors()
        ]));
    }
   
}
