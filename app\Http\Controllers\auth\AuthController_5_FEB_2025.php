<?php

namespace App\Http\Controllers\Auth;

use App\Helpers\MyHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\LoginRequest;
use App\Http\Requests\Auth\OtpCreationRequest;
use App\Http\Requests\Auth\OtpVerificationRequest;
use App\Models\OtpVerify;
use App\Models\User;
use Carbon\Carbon;
use Exception;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class AuthController extends Controller
{
    public function login(LoginRequest $request): FoundationApplication|Response|ResponseFactory
    {
        try {
            $credentials = $request->only('name', 'password');
            $credentials['active_status'] = MyHelper::$RS_ACTIVE;

            if (Auth::attempt($credentials)) {
                $user = Auth::user();

                $tokenResult = $user->createToken('app');
                $token = $tokenResult->accessToken;

                $selectedUser = $user->only(['name', 'BRANCH_ID','email','user_type']);

                return response([
                    'success' => true,
                    'message' => "Successfully Login",
                    'api_token' => $token,
                    'user' => $selectedUser,
                ], 200);
            }
        } catch (Exception $exception) {
            return response([
                'success' => false,
                'message' => $exception->getMessage()
            ], 500);
        }
        return response([
            'success' => false,
            'message' => 'Invalid Name Or Password'
        ], 401);
    }

    public function verifyToken(): FoundationApplication|Response|ResponseFactory
    {
        try {
            $user = Auth::user();
            $selectedUser = $user->only(['name', 'BRANCH_ID', 'email', 'user_type']);

            return response([
                'success' => true,
                'message' => "Successfully Login",
                'name' => $selectedUser['name'],
                'BRANCH_ID' => $selectedUser['BRANCH_ID'],
                'email' => $selectedUser['email'],
                'user_type' => $selectedUser['user_type'],
            ], 200);

        } catch (Exception $exception) {
            return response([
                'success' => false,
                'message' => $exception->getMessage()
            ], 500);
        }
    }

    public function otpCreation(OtpCreationRequest $request): FoundationApplication|Response|ResponseFactory
    {
        try {
            $user = User::where('name', $request->input('mobile'))
                ->where('active_status', MyHelper::$RS_ACTIVE)
                ->first();

            if ($user) {
                $tokenResult = $user->createToken('app');
                $token = $tokenResult->accessToken;
                $tokenId = $tokenResult->token->id;

                $otp = mt_rand(100000, 999999);
                $message = "Your OTP is: $otp";

                OtpVerify::create([
                    'MOBILE_NO' => $request->input('mobile'),
                    'ROSTER_PASSENGER_ID' => 0,
                    'OTP' => $otp,
                    'VERIFIED_STATUS' => '0',
                    'OTP_CATEGORY' => 'EmpLogin',
                    'token_id' => $tokenId,
                    'SMS_RESPONSE' => $message,
                    'request_coming_from' => $request->header('User-Agent'),
                    'CREATED_BY' => $user->id,
                    'CREATED_DATE' => Carbon::now()
                ]);

                $smsSent = $this->sendSms($request->input('mobile'), $message);

                if ($smsSent) {
                    return response([
                        'success' => true,
                        'message' => "OTP created and sent successfully",
                        'token' => $token,
                    ]);
                } else {
                    return response([
                        'success' => false,
                        'message' => "OTP created but failed to send SMS",
                        'token' => $token,
                    ]);
                }
            }
        } catch (Exception $exception) {
            Log::error("Error in otpCreation: " . $exception->getMessage());
            return response([
                'success' => false,
                'message' => $exception->getMessage()
            ]);
        }
        return response([
            'success' => false,
            'message' => 'Invalid mobile number'
        ]);
    }

    public function otpVerification(OtpVerificationRequest $request):FoundationApplication|Response|ResponseFactory
    {

        try {
            $user = Auth::guard('api')->user();
            $tokenId = $user->token()->id;

            $otpRecord = OtpVerify::where('OTP', $request->otp)
                ->where('token_id', $tokenId)
                ->where('VERIFIED_STATUS', '0')
                ->first();

            if (!$otpRecord) {
                return response([
                    'success' => false,
                    'message' => 'Invalid OTP',
                ]);
            }


            $expiryTime = Carbon::parse($otpRecord->CREATED_DATE)->addMinutes(MyHelper::$OTP_EXPIRE_TIME);
            if (Carbon::now()->isAfter($expiryTime)) {
                return response([
                    'success' => false,
                    'message' => 'OTP has expired',
                ]);
            }


            $otpRecord->update([
                'VERIFIED_STATUS' => '1',
                'UPDATED_BY' => Auth::id(),
                'updated_at' => Carbon::now(),
            ]);

            return response([
                'success' => true,
                'message' => 'OTP verified successfully',
            ]);

        } catch (Exception $exception) {
            return response([
                'success' => false,
                'message' => $exception->getMessage()
            ]);
        }
    }

    private function sendSms($mobileNumber, $message): bool
    {
        try {
            Log::info("Sending SMS to: $mobileNumber, Message: $message");
            Log::info("SMS sent successfully to: $mobileNumber");
            return true;
        } catch (Exception $e) {
            Log::error("Failed to send SMS to: $mobileNumber. Error: " . $e->getMessage());
            return false;
        }
    }


    public function logoutAllDevices(): FoundationApplication|Response|ResponseFactory
    {
        $userId = auth()->id();

        DB::table('oauth_access_tokens')
            ->where('user_id', $userId)
            ->delete();

        return response([
            'success' => true,
            'message' => 'Logout successful from all devices',
        ]);
    }

    public function logoutCurrentDevice()
    {

        $user = Auth::guard('api')->user();

        if ($user) {

            $tokenId = $user->token()->id;

            DB::table('oauth_access_tokens')->where('id', $tokenId)->delete();

            return response()->json([
                'success' => true,
                'message' => 'Successfully logged out from the current device',
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Unable to log out. User not found.',
        ], 404);
    }


}
