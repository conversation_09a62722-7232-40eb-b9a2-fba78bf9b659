<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class RouteEscorts extends Model
{
    use HasFactory;

    protected $table = 'route_escorts';
    protected $primaryKey = 'ROUTE_ESCORT_ID';

    public $timestamps = false;

    protected $fillable = [
        'ROUTE_ESCORT_ID',
        'ESCORT_ID',
        'BRANCH_ID',
        'ROSTER_ID',
        'EMPLOYEE_ID',
        'STATUS',
        'REASON',
        'REMARKS',
        'CREATED_BY',
        'UPDATED_BY'       
    ];
}
