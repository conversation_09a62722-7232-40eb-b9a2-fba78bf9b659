<?php

namespace App\Services\Admin;

use App\Helpers\CommonFunction;
use App\Helpers\MyHelper;
use App\Http\Controllers\ElasticController;
use App\Models\Cab_allocation;
use App\Models\Roster;
use App\Models\Employee;
use Carbon\Carbon;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use DateTime;

class AdhocService
{
    protected CommonFunction $commonFunction;
    protected ?Branch $currentBranch = null;

    public function __construct(CommonFunction $commonFunction)
    {
        $this->commonFunction = $commonFunction;
    }

    public function getemplocation(): FoundationApplication|Response|ResponseFactory
    {
        try {
            $user = Auth::user();

            $RS_ACTIVE = MyHelper::$RS_ACTIVE;
            $ADMIN = MyHelper::$ADMIN;
            $branch_id = Auth::user()->BRANCH_ID;
            $vendor_id = Auth::user()->vendor_id;
            $dbname = Auth::user()->dbname;
            $user_id = Auth::user()->id;

            $sql = "SELECT E.LOCATION_ID,L.LOCATION_NAME,B.BRANCH_NAME FROM employees E
            INNER JOIN branch B ON B.BRANCH_ID = E.BRANCH_ID
            INNER JOIN locations L ON L.DIVISION_ID = B.DIVISION_ID AND L.LOCATION_ID = E.LOCATION_ID
            WHERE E.BRANCH_ID = '$branch_id' AND E.user_id='$user_id' AND E.ACTIVE=$RS_ACTIVE";
            $result = DB::connection("$dbname")->select($sql);

            return response([
                'success' => true,
                'status' => 3,
                'EmpDetails' => $result
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => '  Employee Adhoc request Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function getadhocdatechange($request): FoundationApplication|Response|ResponseFactory
    {
        try {
            $user = Auth::user();

            $RS_ACTIVE = MyHelper::$RS_ACTIVE;
            $ADMIN = MyHelper::$ADMIN;
            $branch_id = Auth::user()->BRANCH_ID;
            $inouttime_date = $request->inouttime_date;
            $triptype = $request->triptype;
            // $emp_id = Session::get('emp_id');
            $user_id = Auth::user()->id;

            $GENDER_TYPE = 'all';
            $gender_based_qry = "SELECT PROPERTIE_VALUE  from properties where PROPERTIE_NAME = 'GENDER BASED SHIFT TIME' and PROPERTIE_VALUE = 'Y' and ACTIVE=1 and BRANCH_ID = '$branch_id'";
            $gender_based_res = DB::select($gender_based_qry);
            $g_based_shift_status = 'N';
            if (count($gender_based_res) > 0) {
                $g_based_shift_status = isset($gender_based_res[0]->PROPERTIE_VALUE) ? $gender_based_res[0]->PROPERTIE_VALUE : $g_based_shift_status;
            }

            if ($g_based_shift_status == 'Y') {
                $gendor_type_qry = "SELECT GENDER  from employees  where  user_id='$user_id' 
                and BRANCH_ID ='$branch_id' and ACTIVE ='1'";
                $gendor_type_res = DB::select($gendor_type_qry);
                $count = count($gendor_type_res);
                if ($count > 0) {
                    $GENDER_TYPE = $gendor_type_res[0]->GENDER;
                }
            }

            $current_date = date('Y-m-d');
            $current_time = date('H:i:s', time());

            $adhoc_enableoption = $this->commonFunction->GetPropertyValue('ADHOC_ENABLE_BUFFERTIME', $branch_id);

            $adhoc_opt = json_decode($adhoc_enableoption, TRUE);
            $pickup_adhoc = $adhoc_opt['pickup'];
            $drop_adhoc = $adhoc_opt['drop'];
            $category = "'ADHOC'";

            $usename = Auth::user()->name;
            $encrypt_username = $this->commonFunction->AES_ENCRYPT($usename, env('AES_ENCRYPT_KEY'));

            $sql = "SELECT special_employee from employees  where MOBILE = '$encrypt_username' and ACTIVE = 1 and BRANCH_ID = '$branch_id'";
            $result = DB::select($sql);
            $special_employee = 0;

            if (count($result) > 0) {
                $special_employee = isset($result[0]) ? $result[0]->special_employee : 0;
            }

            $category = $special_employee ? "'SPECIAL ADHOC','ADHOC'" : "'ADHOC'";

            if ($triptype == 'P') {
                if ($inouttime_date == $current_date) {
                    $cond = "and IN_TIME >=ADDTIME('" . $current_time . "','" . $pickup_adhoc . ":00:00')";
                } else {
                    $cond = '';
                }

                if ($GENDER_TYPE == 'all' || $GENDER_TYPE == 'F') {
                    $data = DB::select("SELECT IN_TIME from shift_time where ACTIVE=1 $cond  and BRANCH_ID=" . $branch_id . " and CATEGORY='ADHOC' group by IN_TIME");
                } else {
                    $data = DB::select("SELECT if(IN_TIME_GENDER = 'F','NA', IN_TIME) as IN_TIME  from shift_time where ACTIVE=1 $cond  and BRANCH_ID=" . $branch_id . " and CATEGORY='ADHOC' group by IN_TIME ");
                }
            } elseif ($triptype == 'D') {
                if ($inouttime_date == $current_date) {
                    $cond = "and OUT_TIME >=ADDTIME('" . $current_time . "','" . $drop_adhoc . ":00:00')";
                } else {
                    $cond = '';
                }

                if ($GENDER_TYPE == 'all' || $GENDER_TYPE == 'F') {
                    $data = DB::select("SELECT OUT_TIME from shift_time where ACTIVE=1 $cond  and BRANCH_ID=" . $branch_id . " and CATEGORY IN($category) group by OUT_TIME ");
                } else {
                    $data = DB::select("SELECT  if( OUT_TIME_GENDER = 'F','NA', OUT_TIME) as OUT_TIME from shift_time where ACTIVE=1 $cond  and BRANCH_ID=" . $branch_id . " and CATEGORY IN($category) group by OUT_TIME ");
                }
            }

            return response([
                'success' => true,
                'status' => 3,
                'EmpDetails' => $data
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => '  Employee Adhoc request Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function getrequestedAdhoc($request)
    {
		try
		{
        $branchid = Auth::user()->BRANCH_ID;
        $curdtime = date('Y-m-d H:i:s');
        $dbname = Auth::user()->dbname;
       // $emp_id = Session::get('emp_id');
		$user_id=Auth::user()->id;
		$emp_id=Employee::where("ACTIVE",1)->where("user_id",$user_id)->get();
        $emp_id=$emp_id[0]->EMPLOYEES_ID;
		
        $trip_type = $request->trip_type;
        $start_location = $request->start_location;
        $end_location = $request->end_location;
        $intime_date = $request->intime_date;
        $intime = $request->tripintime;
		$tripintime=$intime_date." ".$intime;
        $reason = $request->reason;
        $other_reason = $request->other_reason;
        $checkcnt1 = DB::connection("$dbname")->select("SELECT count(ROSTER_REQ_ID) as cnt FROM employees_roster_request WHERE EMPLOYEE_ID='$emp_id' AND date(ESTIMATE_START_TIME) = '".$intime_date."' AND TRIP_TYPE = '".$trip_type."' AND `STATUS` != 6 ");
//        $count = $checkcnt[0]->cnt;
        $count1 = $checkcnt1[0]->cnt;
       
		if($count1 > 0)
		{
			//echo "EXIST";
			return response([
                'success' => false,
                'status' => 4,
                'message' => "Already Requested"
            ]);
		}
        $ROSTREQEST = array();

				if($trip_type == 'P'){
                if($count1 == 0) {
                    $ROSTREQEST[] = "('$branchid', '$emp_id', '$trip_type', '$start_location', '$end_location', '$tripintime', '0000-00-00 00:00:00', '1', 'WEB', '$curdtime','$reason','$other_reason')";
                }
            }

            if($trip_type == 'D'){
                if($count1 == 0) {
                    $ROSTREQEST[] = "('$branchid', '$emp_id', '$trip_type', '$start_location', '$end_location', '$tripintime', '0000-00-00 00:00:00', '1', 'WEB', '$curdtime','$reason','$other_reason')";
                }
            }



        if(!empty($ROSTREQEST)) {
            $sql = "INSERT INTO `employees_roster_request` (`BRANCH_ID`, `EMPLOYEE_ID`, `TRIP_TYPE`, `START_LOCATION`, `END_LOCATION`, `ESTIMATE_START_TIME`, `ESTIMATE_END_TIME`, `STATUS`, `CREATED_BY`, `created_at`,`ADHOC_REASON_ID`,`ADHOC_OTHER_REASON`)  VALUES " . implode(',', $ROSTREQEST);
             $res= DB::connection("$dbname")->insert($sql);
        }
		return response([
                'success' => true,
                'status' => 3,
                'message' => "Successfully updated"
            ]);
		} catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => '  Employee Adhoc request Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function dataForCreate(): FoundationApplication|Response|ResponseFactory
    {
        try {
            $user = Auth::user();
            $RS_ACTIVE = MyHelper::$RS_ACTIVE;
            $ADMIN = MyHelper::$ADMIN;
            $branch_id = Auth::user()->BRANCH_ID;
            $vendor_id = Auth::user()->vendor_id;
            $dbname = Auth::user()->dbname;
            $date = carbon::now();
            $from_date = date('Y-m-d');
            $user_type = Auth::user()->user_type;

            $category = array(
                array(
                    'value' => 'P',
                    'name' => 'Login'
                ),
                array(
                    'value' => 'D',
                    'name' => 'Logout'
                )
            );
            $reason_list = DB::connection($dbname)
                ->table('reason_master')
                ->where('CATEGORY', 'EmpAdhoc')
                ->where('BRANCH_ID', $branch_id)
                ->where('ACTIVE', '1')
                ->get();

            return response([
                'success' => true,
                'status' => 3,
                'category' => $category,
                'reason_list' => $reason_list
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => '  Employee Adhocc request Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}
