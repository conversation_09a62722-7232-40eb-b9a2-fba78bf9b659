<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Crypt;

class Driver_Billing_Summary extends Model
{
    use HasFactory;

    protected $table = 'driver_billing_summary';
    protected $primaryKey = 'BILLING_ID';
	public $timestamps = false;
	
	//protected $appends = ['DRIVERS_ID_crypt', 'image_path'];
	
	protected $fillable = [
        'BRANCH_ID',
        'VENDOR_ID',
        'CAB_ID',
        'ROSTER_ID  ',
        'TRIP_TYPE',
        'TRIP_STATUS',
        'TRIP_EXECUTE_DATETIME',
        'TRIP_EXECUTE_GPS_DATETIME',
        'EXECUTE_LAT',
        'EXECUTE_LONG',
        'FIRST_POINT_DATETIME',
        'FIRST_POINT_GPS_DATETIME',
        'FIRST_POINT_LAT',
        'FIRST_POINT_LONG',
        'DEVICE_KM',
        'GOOGLE_KM',
        'DEVIATION_KM',
        'GOOGLE_SHED_KM',
        'GOOGLE_TOTAL_KM',
        'DEVICE_TOTAL_KM',
        'ROSTER_STATUS',
        'TRIP_CLOSED_GPS_DATETIME',
        'TRIP_CLOSED_DATETIME',
        'TRIP_CLOSE_LAT',
        'TRIP_CLOSE_LONG',
        'TARIFF_ID',
        'REMARKS',
        'TRIP_STATUS_REMARKS',
        'CREATED_AT',
        'CREATED_BY',
        'UPDATED_BY',
        'UPDATED_AT',
        'CATEGORY',
        'ACTIVE',
    ];


}
