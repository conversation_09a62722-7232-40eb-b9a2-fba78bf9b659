<?php

namespace App\Services\Admin;

use App\Helpers\CommonFunction;
use App\Helpers\MyHelper;
use Carbon\Carbon;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use App\Models\Vendor;
use App\Models\Cab_allocation;
use App\Models\Roster;
use App\Models\RosterPassenger;
use App\Models\RouteEscorts;
use App\Models\Reason_Log;
use App\Models\Cab;
use App\Models\Vehicle;
use App\Models\VehicleModel;
use App\Models\Driver;
use App\Models\property;
use App\Models\Driver_Billing_Summary;
use App\Http\Controllers\MaskNumberClearController;
use App\Http\Controllers\ElasticController;
use DateTime;

class TripTravelPathService
{
    protected CommonFunction $commonFunction;
    protected ?Branch $currentBranch = null;

    public function __construct(CommonFunction $commonFunction)
    {
        $this->commonFunction = $commonFunction;
    }
	     
    
    public function vendorwise_vehicle_find($request): FoundationApplication|Response|ResponseFactory
    {
        try {
            $branch_id = Auth::user()->BRANCH_ID;
            $userid = Auth::user()->id;
            $db_name = Auth::user()->dbname;
            $RS_ACTIVE=MyHelper::$RS_ACTIVE;
            $active=true;
           $authUser = Auth::user();

           $selected_date=$request->selected_date;
           $vendor_id=$request->vendor_id;
          
           $cab_data = DB::connection("$db_name")->table('cab as C')->select('C.CAB_ID','C.VENDOR_ID', 'V.VEHICLE_ID', 'V.VEHICLE_REG_NO')
						->join('vehicles as V', 'V.VEHICLE_ID', '=', 'C.VEHICLE_ID')
						->where('C.VENDOR_ID', '=', $vendor_id)
						->where('C.BRANCH_ID', '=', $branch_id) ->where('C.ACTIVE', '=', $RS_ACTIVE)->distinct('C.CAB_ID')->get();

        
            return response([
                'success' => true,
                'status' => 3,
                'cab_data' => $cab_data,
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'Manual otp  status Unsuccessful' : 'Manual otp  status Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ],500);
        }
    } 

    public function select_cab_wise_route($request, bool $active = true): FoundationApplication|Response|ResponseFactory
    {
        try {
            $branch_id = Auth::user()->BRANCH_ID;
            $userid = Auth::user()->id;
            $db_name = Auth::user()->dbname;
            $RS_ACTIVE=MyHelper::$RS_ACTIVE;

           $authUser = Auth::user();

           $selected_date=$request->selected_date;
           $vendor_id=$request->selected_vendor_id;
           $cab_id=$request->selected_cab_id;
           $check_sts = [MyHelper::$RS_NEWROSTER,MyHelper::$RS_TOTALALLOT,MyHelper::$RS_TOTALACCEPT,MyHelper::$RS_TOTALEXECUTE];
          
           $cab_route = DB::connection("$db_name")->table('rosters as R')
                    ->select('R.ROUTE_ID', 'R.ROSTER_ID', 'R.TRIP_TYPE', 'R.ESTIMATE_END_TIME', 'R.ESTIMATE_START_TIME')

                    ->where('R.CAB_ID', '=', $cab_id)
                    ->where('R.VENDOR_ID', '=', $vendor_id)
                    ->where('R.ACTIVE', '=', $RS_ACTIVE)
                    ->where(function($query) use ($selected_date) {
                        $query->whereDate('R.ESTIMATE_END_TIME', '=', $selected_date)
                            ->orWhereDate('R.ESTIMATE_START_TIME', '=', $selected_date);
                    })
                    //->whereIn('R.ROSTER_STATUS', $check_sts)
                    ->where('R.BRANCH_ID', '=', $branch_id)
                    ->get();
        
            return response([
                'success' => true,
                'status' => 3,
                'cab_route' => $cab_route,
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'Triptravel path cab wise status Unsuccessful' : 'Triptravel path cab wise status Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ],500);
        }
    } 

    public function selected_route_details($request, bool $active = true): FoundationApplication|Response|ResponseFactory
    {
        try {
            $branch_id = Auth::user()->BRANCH_ID;
            $userid = Auth::user()->id;
            $db_name = Auth::user()->dbname;
            $RS_ACTIVE=MyHelper::$RS_ACTIVE;
            $RPS_TTLNOSHOW=explode(',',MyHelper::$RPS_TTLNOSHOW);
            $RPS_TTLARRIVAL=explode(',',MyHelper::$RPS_TTLARRIVAL);
            $RPS_TTLCABDELAY=explode(',',MyHelper::$RPS_TTLCABDELAY);
            $arrived_array=array_merge($RPS_TTLCABDELAY,$RPS_TTLARRIVAL);
           $authUser = Auth::user();

            $selected_roster_id=$request->selected_roster_id;
          $datas= Roster::where("ROSTER_ID","=",$selected_roster_id)->get();
          $order = $datas[0]->TRIP_TYPE == 'P' ? 'DESC' : 'ASC';

        $data = RosterPassenger::on("$db_name")
        ->from('roster_passengers as RP')
        ->select(
        'RP.ROSTER_PASSENGER_STATUS', 
        'RP.ROSTER_ID', 
        'RP.ROUTE_ORDER', 
        'RP.ROSTER_PASSENGER_ID', 
        'RP.ESTIMATE_START_TIME', 
        'RP.DRIVER_ARRIVAL_TIME', 
        'RS.TRIP_APPROVED_KM', 
        'RS.ROUTE_ID', 
        'RS.TOTAL_KM', 
        'RS.TRIP_TYPE', 
        'RP.ACTUAL_START_TIME', 
        'RS.ACTUAL_START_TIME as driver_start_time', 
        'RS.ROSTER_STATUS', 
        'RS.ACTUAL_END_TIME as destination_time', 
        'RP.ACTUAL_END_TIME', 
        'L.LOCATION_NAME', 
        'BR.BRANCH_NAME', 
        'RP.ESTIMATE_END_TIME', 
        'RP.EMPLOYEE_ID', 
        'EM.NAME', 
        'EM.MOBILE', 
        'D.DRIVER_MOBILE', 
        'EM.GENDER'
    )
    ->join('employees as EM', 'EM.EMPLOYEES_ID', '=', 'RP.EMPLOYEE_ID')
    ->join('rosters as RS', 'RS.ROSTER_ID', '=', 'RP.ROSTER_ID')
    ->join('branch as BR', 'BR.BRANCH_ID', '=', 'RS.BRANCH_ID')
    ->join('locations as L', 'L.LOCATION_ID', '=', 'RP.LOCATION_ID')
    ->join('cab as C', 'C.CAB_ID', '=', 'RS.CAB_ID')
    ->join('drivers as D', 'D.DRIVERS_ID', '=', 'C.DRIVER_ID')
    ->where('RP.ROSTER_ID', '=', $selected_roster_id)
    ->where('RS.BRANCH_ID', '=', $branch_id)
    ->where('EM.BRANCH_ID', '=', $branch_id)
    ->where('RP.ACTIVE', '=', $RS_ACTIVE);
  //  ->orderBy('RP.ROUTE_ORDER', $order)
  //  ->get();
    $perPage = $request->input('per_page', MyHelper::$PAGINATION_PER_PAGE);
            $filterModel = $request->input('filterModel');
            if ($filterModel) {
                foreach ($filterModel as $field => $filter) {
                    if (isset($filter['filter']) && $filter['filter'] !== '') {
                        $value = $filter['filter'];
                        $type = $filter['type'];

                        switch ($field) {
                            case 'LOCATION_NAME':
                                $data->where('L.LOCATION_NAME', 'like', "%{$value}%");
                                break;
                            case 'EMPLOYEE_ID':
                                $data->where('E.EMPLOYEE_ID', 'like', "%{$value}%");
                                break;
                        }
                    }
                }
            }


            if ($request->has('orderBy') && !empty($request->input('orderBy'))) {
                $orderBy = $request->input('orderBy');
                $order = $request->input('order', 'asc');
                $data->orderBy($orderBy, $order);
            } else {
                $data->orderBy('RP.ROUTE_ORDER', $order);
            }


            if ($perPage === MyHelper::$PAGINATION_PER_PAGE_ALL) {
                $paginateddata = $data->paginate($data->count());
            } else {
                $perPage = is_numeric($perPage) ? (int)$perPage : MyHelper::$PAGINATION_PER_PAGE;
                $paginateddata = $data->paginate($perPage);
            }
           
            return response([
                'success' => true,
                'status' => 3,
                'route_details' => $paginateddata,
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'Manual otp route details Unsuccessful' : 'Manual otp route details Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ],500);
        }
    } 
    

    public function trip_travel_path($request): FoundationApplication|Response|ResponseFactory
		{
            try
            {
                $sss='';$ssss='';

			$travelpath_control = new \App\Http\Controllers\ElasticController;
			
			$selected_date=$request->selected_date;
			$selected_cab_id=$request->selected_cab_id;
			$selected_vendor_id=$request->selected_vendor_id;
			$selected_roster_id=$request->selected_roster_id;
            $branch_id=Auth::user()->BRANCH_ID;
           // $branch_id=48;

            $sql="select R.ROSTER_ID,R.ESTIMATE_START_TIME,R.ESTIMATE_END_TIME,ADDTIME(R.ACTUAL_START_TIME,'5:00:00')as actual_time,R.ACTUAL_START_TIME,R.ACTUAL_END_TIME,concat(RP.END_LAT,',',RP.END_LONG) as last_point from rosters R 
            inner join roster_passengers RP on RP.ROSTER_ID=R.ROSTER_ID where R.ROSTER_ID='".$selected_roster_id."'";
			$result=DB::select($sql);

			//$path=$travelpath_control->cabPathSearchFull($selected_cab_id,$selected_vendor_id,$fromdatetime,$todatetime);

           // $fromdatetime=str_replace(" ","T",$result[0]->ACTUAL_START_TIME);
            $fromdatetime=$result[0]->ACTUAL_START_TIME;
			$last_latlong=array();
			if($result[0]->last_point!='')
			{
				$last_latlong=array("last_lat"=>$result[0]->last_point);
			}
			
			if($result[0]->ACTUAL_END_TIME=='')
			{
				//$todatetime=str_replace(" ","T",$result[0]->actual_time);
				$todatetime=$result[0]->actual_time;
			}
			else
			{
				//$todatetime=str_replace(" ","T",$result[0]->ACTUAL_END_TIME);
				$todatetime=$result[0]->ACTUAL_END_TIME;
			}
			$path=$travelpath_control->cabTripPathSearch($branch_id,$selected_cab_id,$selected_roster_id,$fromdatetime,$todatetime);

          
            /* $path = collect($path)->map(function($item) use ($travelpath_control) {

                $item['addr_pos'] = $item['POSITION']; // You can calculate or assign any value here
                $gps_pos = explode(',', $item['POSITION']);
                $get_address=$travelpath_control->getGpsAddress($gps_pos[0],$gps_pos[1]);
				$item['address']=$get_address!='No Record'?$get_address[0]['ADDRESS']:'--';
                return $item;
            }); */
            
			if($path!='No Record')
			{
				$trip_data= $path;
			}
			else
			{
				$trip_data =array();
				//$trip_data ="No Record";
			}

            $datas = property::where([
                ['BRANCH_ID', '=', $branch_id],
                ['ACTIVE', '=', '1'],
                ])->get();
                for ($i = 0; $i < count($datas); $i++) {
                    $property_name = $datas[$i]['PROPERTIE_NAME'];
                    $property_value = $datas[$i]['PROPERTIE_VALUE'];
                    
                    switch ($property_name) {
                        case "ADDRESS TIME":
                        $ADDRESSTIME = $property_value;
                        break;
                        case "OVERSPEED TRACKING":
                        $OVERSPEED = $property_value;
                        break;
                        default:
                        break;
                    }
                }

            $result=DB::table("roster_passengers as RP")->select("RP.ROSTER_ID","L.LATITUDE","L.LONGITUDE","L.LOCATION_NAME","R.TRIP_TYPE","RP.START_LAT","RP.START_LONG","RP.END_LAT","RP.END_LONG","R.TRIP_TYPE","EM.ADDRESS","R.ESTIMATE_END_TIME","R.ESTIMATE_START_TIME","EM.EMPLOYEES_ID","EM.LATITUDE as emp_lat","EM.LONGITUDE as emp_long")
			->join("employees as EM","EM.EMPLOYEES_ID","=","RP.EMPLOYEE_ID")
			->join("rosters as R","R.ROSTER_ID","=","RP.ROSTER_ID")
			->join("locations as L","L.LOCATION_ID","=","RP.LOCATION_ID")
			->where("RP.ROSTER_ID","=",$selected_roster_id)->where("EM.BRANCH_ID","=",$branch_id)->get();

            $cnt=count($result);
            $mark2=array();
			$mark3=array();

			if($cnt>0)
			{
				for($j=0;$j<$cnt;$j++)
				{
					//$data2="<b>Location Name : </b>".$result[$j]->LOCATION_NAME;
					$estimate_start_time=$result[$j]->TRIP_TYPE=='P'?$result[$j]->ESTIMATE_END_TIME:$result[$j]->ESTIMATE_START_TIME;
					
					$checkdatatime = $this->commonFunction->address_time_check($ADDRESSTIME, $estimate_start_time);
					$EMPLOYEES_ID=$result[$j]->EMPLOYEES_ID;
					$TRIP_TYPE=$result[$j]->TRIP_TYPE;
					
					if($checkdatatime == 'LOCATION'){
						$address=$result[$j]->LOCATION_NAME;
						$loc_lat=$result[$j]->LATITUDE;
						$loc_long=$result[$j]->LONGITUDE;
					}
					else
					{
						$address=$result[$j]->ADDRESS;
						$loc_lat=$result[$j]->emp_lat;
						$loc_long=$result[$j]->emp_long;
					}
					
					$alat=array("trip_type"=>$TRIP_TYPE,"lat"=> $loc_lat,"lng"=> $loc_long,"address"=>$address,"employees_id"=>$EMPLOYEES_ID);

					array_push($mark2,$alat);
					//$img3='car.png';
					if($result[$j]->TRIP_TYPE=='P')
					{
						$lat=$result[$j]->START_LAT;
						$lng=$result[$j]->START_LONG;
					}
					else
					{
						$lat=$result[$j]->END_LAT;
						$lng=$result[$j]->END_LONG;
					}
					$alat3=array("trip_type"=>$TRIP_TYPE,"lat"=> $lat,"lng"=> $lng,"address"=>$address,"employees_id"=>$EMPLOYEES_ID);
					array_push($mark3,$alat3);
				}
				$sss= $mark2;
				$ssss= $mark3;
			}
			
           
            return response([
                'success' => true,
                'status' => 3,
                'trip_data' => $trip_data,
                'emp_data' => $sss,
                'punch_data' => $ssss,
                'overspeed' => $OVERSPEED,
                
                
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Data For Create for Travelpath Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ],500);
        }
			
		}
    public function vehicle_travel_path_excel($request): FoundationApplication|Response|ResponseFactory
		{
            try
            {

			$travelpath_control = new \App\Http\Controllers\ElasticController;
			
			$date=$request->selected_date;
			$from_time=$request->from_time;
			$to_time=$request->to_time;
			$fromdatetime=$date.' '.$from_time.':00';
			$todatetime=$date.' '.$to_time.':00';
			$VEHICLE_NO=$request->selected_vehicle;
			$vendor_id=$request->selected_vendor_id;
			$path=$travelpath_control->cabPathSearchFull($VEHICLE_NO,$vendor_id,$fromdatetime,$todatetime);
            //print_r($path);exit;
			if($path!='No Record')
			{
                $path = collect($path)->map(function($item) use ($travelpath_control) {

                $item['addr_pos'] = $item['POSITION']; // You can calculate or assign any value here
                $gps_pos = explode(',', $item['POSITION']);
                $get_address=$travelpath_control->getGpsAddress($gps_pos[0],$gps_pos[1]);
				$item['address']=$get_address!='No Record'?$get_address[0]['ADDRESS']:'--';
                return $item;
            }); 
            
			
				$vehicle_data= $path;
			}
			else
			{
				$vehicle_data ="No Record";
				
			}
           
            return response([
                'success' => true,
                'status' => 3,
                'vehicle_data' => $vehicle_data
                
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Data For Create for Travelpath Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ],500);
        }
			
		}
   
    
    /**
     * @throws ConnectionException
     */
    public function dataForCreate(): FoundationApplication|Response|ResponseFactory
    {

        try {
                $user = Auth::user();
                $RS_ACTIVE = MyHelper::$RS_ACTIVE;
            
                $branch_id = Auth::user()->BRANCH_ID;
                $vendor_id = Auth::user()->vendor_id;
                
                if (Auth::user()->user_type == MyHelper::$ADMIN) {
                    $vendor_list = Vendor::where('ACTIVE', '=', $RS_ACTIVE)->where("BRANCH_ID", "=", $branch_id)->get();
                } else {
                    $vendor_list = Vendor::where('ACTIVE', '=', $RS_ACTIVE)->where("VENDOR_ID", "=", $vendor_id)->get();
                }
                
                return response([
                'success' => true,
                'status' => 3,
                'vendor_list' => $vendor_list
                
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Data For Create for Travelpath Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ],500);
        }
    }
   
    /* public function gpsactionlist($request)
	{
        try {
            $route_no=$request->route_no;
            $cab_id=$request->cab_no;
            $vendor_id=$request->vendor_id;
            $branch_id=Auth::user()->BRANCH_ID;
            $GPS_LOG_KEY='AIzaSyACKus8X1z0xJ0k8hDZxgpOPeZwebpUEGY';

            $travelpath_control = new \App\Http\Controllers\ElasticController;
                        
            $sql="select ROSTER_ID,ESTIMATE_START_TIME,ESTIMATE_END_TIME,ADDTIME(ACTUAL_START_TIME,'5:00:00')as actual_time,ACTUAL_START_TIME,ADDTIME(ACTUAL_END_TIME,'00:01:00') as ACTUAL_END_TIME from rosters where ROSTER_ID='".$route_no."'";
            $result=DB::select($sql);
            
            $fromdatetime=str_replace(" ","T",$result[0]->ACTUAL_START_TIME);
            
            if($result[0]->ACTUAL_END_TIME==''){
                $todatetime=str_replace(" ","T",$result[0]->actual_time);
            } else {
                $todatetime=str_replace(" ","T",$result[0]->ACTUAL_END_TIME);
            }

            $data = $travelpath_control->getGpsActionAlert($branch_id,$vendor_id,$cab_id,$route_no,$fromdatetime,$todatetime);

            $sno=1;
            $address = "---";
            $old_pos='';

            if($data!='No Record'){
                $Result_data = array();
                for($i=0;$i<count($data);$i++){	

                    $alert_status=$data[$i]['ALERT_STATUS'];
                    $PROCESS_DATE=$data[$i]['PROCESS_DATE'];
                    
                    if($alert_status=='Internet ON' || $alert_status=='Gps First Fix' || $alert_status=='Gps On' || $alert_status=='Battery Normal' ){
                        $res=$travelpath_control->cabGpsRecordSingleFuture($branch_id,$vendor_id,$cab_id,$PROCESS_DATE);   
                    } else if($alert_status=='Internet OFF' || $alert_status=='Gps OFF' || $alert_status=='Gps Not Fix' 
                    || $alert_status=='Battery low' || $alert_status=='GPS_ONLY_SETTING' || $alert_status=='Gps Not Fixed' ){
                        $res=$travelpath_control->cabGpsRecordSinglePast($branch_id,$vendor_id,$cab_id,$PROCESS_DATE);   
                    } else {
                        //echo "test ".$alert_status;
                        //print_r($res);
                        //exit;
                    }
			
                    if($res!='No Record' && $res!=''){
                        $pos=$res[0]['POSITION'];
                        $gps_date=$res[0]['GPS_DATE'];
                        //if($old_pos!=$pos)
                        //{
			                //Google map API command
                            //$url = 'https://maps.googleapis.com/maps/api/geocode/json?latlng='.$pos.'&key='.$GPS_LOG_KEY.'&sensor=false';
                            //$json = json_decode(file_get_contents($url), true);
                            //$status = $json['results'];
			                //if(isset($json['results'][0])){
                            	//$address = ($status !='') ? $json['results'][0]['formatted_address'] : '';
			                //}  
                         //}
                        $old_pos=$pos; 
                    } else {
                        $pos='--';
                        $gps_date='--';
                        $address='';
                    }
                
                    $res_array = [];
                    $res_array['SNO'] = $sno++;
                    $res_array['ROSTER_ID'] = $data[$i]['ROSTER_ID'];
                    $res_array['CAB_NO'] = $data[$i]['CAB_NO'];
                    $res_array['ALERT_STATUS'] = $data[$i]['ALERT_STATUS'];
                    $res_array['GPS_DATE'] = $gps_date;
                    $res_array['POS'] = $pos;
                    $res_array['ADDRESS'] = $address;
                    
                    array_push($Result_data, $res_array);
                }

                return response([
                    'success' => true,
                    'status' => 3,
                    'message' => 'GPS Report Success',
                    'data' => $Result_data
                ]);
            } else {
                return response([
                'success' => false,
                'status' => 4,
                'message' => 'No Data Found',
                ]);
            }
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'GPS Report Data Fetch Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ],500);
        }
    }*/
	
  public function gpsactionlist($request)
    {
        try {

            $route_no  = $request->route_no;
            $cab_id    = $request->cab_no;
            $vendor_id = $request->vendor_id;
            $branch_id = Auth::user()->BRANCH_ID;
            $GPS_LOG_KEY = 'AIzaSyACKus8X1z0xJ0k8hDZxgpOPeZwebpUEGY';


            $page        = $request->input('page', 1);
            $per_page    = $request->input('per_page', 10);
            $orderBy     = $request->input('orderBy', 'GPS_DATE');
            $order       = $request->input('order', 'asc');
            $filterModel = $request->input('filterModel', []);


            $travelpath_control = new ElasticController;


            $sql = "SELECT ROSTER_ID,
                       ESTIMATE_START_TIME,
                       ESTIMATE_END_TIME,
                       ADDTIME(ACTUAL_START_TIME, '5:00:00') AS actual_time,
                       ACTUAL_START_TIME,
                       ADDTIME(ACTUAL_END_TIME, '00:01:00') AS ACTUAL_END_TIME
                FROM rosters
                WHERE ROSTER_ID = '" . $route_no . "'";
            $result = DB::select($sql);


            $fromdatetime = str_replace(" ", "T", $result[0]->ACTUAL_START_TIME);
            if ($result[0]->ACTUAL_END_TIME == '') {
                $todatetime = str_replace(" ", "T", $result[0]->actual_time);
            } else {
                $todatetime = str_replace(" ", "T", $result[0]->ACTUAL_END_TIME);
            }


            $data = $travelpath_control->getGpsActionAlert(
                $branch_id,
                $vendor_id,
                $cab_id,
                $route_no,
                $fromdatetime,
                $todatetime,
                $page,
                $per_page,
                $orderBy,
                $order,
                $filterModel
            );

            $sno     = 1;
            $old_pos = '';
            $Result_data = [];


            if (count($data->items()) > 0) {
                foreach ($data->items() as $item) {
                    $alert_status = $item['ALERT_STATUS'];
                    $PROCESS_DATE = $item['PROCESS_DATE'];
					$address = "---";


                    if (
                        $alert_status == 'Internet ON' ||
                        $alert_status == 'Gps First Fix' ||
                        $alert_status == 'Gps On' ||
                        $alert_status == 'Battery Normal'
                    ) {
                        $res = $travelpath_control->cabGpsRecordSingleFuture($branch_id, $vendor_id, $cab_id, $PROCESS_DATE);
                    } else if (
                        $alert_status == 'Internet OFF' ||
                        $alert_status == 'Gps OFF' ||
                        $alert_status == 'Gps Not Fix' ||
                        $alert_status == 'Battery low' ||
                        $alert_status == 'GPS_ONLY_SETTING' ||
                        $alert_status == 'Gps Not Fixed'
                    ) {
                        $res = $travelpath_control->cabGpsRecordSinglePast($branch_id, $vendor_id, $cab_id, $PROCESS_DATE);
                    } else {
                        $res = '';
                    }


                    if ($res != 'No Record' && $res != '') {
                        $pos = $res[0]['POSITION'];
                        $gps_date = $res[0]['GPS_DATE'];
                        /*if ($old_pos != $pos) {
                            $url = 'https://maps.googleapis.com/maps/api/geocode/json?latlng=' . $pos . '&key=' . $GPS_LOG_KEY . '&sensor=false';
                            $json = json_decode(file_get_contents($url), true);
                            $results = $json['results'];
                            if (isset($results[0])) {
                                $address = !empty($results) ? $results[0]['formatted_address'] : '';
                            } else {
                                $address = "---";
                            }
                        }*/
                        $old_pos = $pos;
                    } else {
                        $pos = '--';
                        $gps_date = '--';
                        $address = '';
                    }


                    $res_array = [];
                    $res_array['SNO']          = $sno++;
                    $res_array['ROSTER_ID']    = $item['ROSTER_ID'];
                    $res_array['CAB_NO']       = $item['CAB_NO'];
                    $res_array['ALERT_STATUS'] = $alert_status;
                    $res_array['GPS_DATE']     = $gps_date;
                    $res_array['POS']          = $pos;
                    $res_array['ADDRESS']      = $address;

                    $Result_data[] = $res_array;
                }


                $data->setCollection(collect($Result_data));


                return response([
                    'success' => true,
                    'status'  => 3,
                    'message' => 'GPS Report Success',
                    'gps_report'    => $data
                ]);
            } else {
                return response([
                    'success' => false,
                    'status'  => 4,
                    'message' => 'No Data Found',
                ]);
            }
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success'               => false,
                'status'                => 14,
                'message'               => 'GPS Report Data Fetch Unsuccessful',
                'validation_controller' => true,
                'error'                 => $e->getMessage(),
				'trace'                 => $e->getTrace(),
            ], 500);
        }
    }
   
}
