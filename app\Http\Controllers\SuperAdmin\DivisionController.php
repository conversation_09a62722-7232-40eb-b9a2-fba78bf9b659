<?php

namespace App\Http\Controllers\SuperAdmin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Services\SuperAdmin\DivisionService;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Response;
use App\Http\Requests\SuperAdmin\DivisionDeleteRequest;
use App\Http\Requests\SuperAdmin\DivisionRequest;


class DivisionController extends Controller
{
    protected DivisionService $divisionService;

    public function __construct(DivisionService $divisionService)
    {
        $this->divisionService =$divisionService;
    }


    public function index(): FoundationApplication|Response|ResponseFactory
    {
       return $this->divisionService->indexDivision();    

    }

    public function store(DivisionRequest $request): ResponseFactory|Application|Response
    {
        return $this->divisionService->storeDivision($request);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    public function edit($divisionAutoIdCrypt): Application|Response|ResponseFactory
    {
        return $this->divisionService->editDivision($divisionAutoIdCrypt);
    }

    public function update(DivisionRequest $request, $divisionAutoIdCrypt): Application|Response|ResponseFactory
    {
        return $this->divisionService->updateDivision($request, $divisionAutoIdCrypt);
    }


    public function delete(DivisionDeleteRequest $request, $divisionAutoIdCrypt): FoundationApplication|Response|ResponseFactory
    {
        return $this->divisionService->deleteDivision($request, $divisionAutoIdCrypt);
    }

    

    public function divisionlistpagination(Request $request): ResponseFactory|Application|Response
    {
        return $this->divisionService->paginationDivision($request);
    }
}
