<?php

namespace App\Services\Admin;

use App\Helpers\CommonFunction;
use App\Helpers\MyHelper;
use Carbon\Carbon;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use App\Models\Vendor;
use App\Models\Vehicle;
use App\Models\VehicleModel;
use App\Models\Driver;
use App\Models\property;
use App\Models\Driver_Billing_Summary;
use App\Http\Controllers\MaskNumberClearController;
use App\Http\Controllers\ElasticController;
use DateTime;

class LiveTrackingService
{
    protected CommonFunction $commonFunction;
    protected ?Branch $currentBranch = null;

    public function __construct(CommonFunction $commonFunction)
    {
        $this->commonFunction = $commonFunction;
    }
	
   
    public function live_travel_path($request): FoundationApplication|Response|ResponseFactory
		{
            try
            {
               
			$travelpath_control = new \App\Http\Controllers\ElasticController;
			$cab_id=$request->selected_cab_id;
                //$gpsDate='1900-01-01 00:00:00';
                //$gpsDate=date('Y-m-d 17:15:00');
                $thestime = date('Y-m-d H:i:s');
                $gpsDate = date("Y-m-d H:i:s",strtotime("-30 minutes",strtotime($thestime)));
                $path=$travelpath_control->getLiveTrack($cab_id,$gpsDate);
			
			if($path!='No Record')
			{
				$trip_data= $path;
			}
			else
			{
				$trip_data =array();
				//$trip_data ="No Record";
			}
           
            return response([
                'success' => true,
                'status' => 3,
                'live_data' => $trip_data,
                
                
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Data For Create for Travelpath Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ],500);
        }
			
		}
    
    /**
     * @throws ConnectionException
     */
    public function dataForCreate(): FoundationApplication|Response|ResponseFactory
    {

        try {
                $user = Auth::user();
                $RS_ACTIVE = MyHelper::$RS_ACTIVE;
            
                $branch_id = Auth::user()->BRANCH_ID;
                $vendor_id = Auth::user()->vendor_id;
                
                if (Auth::user()->user_type == MyHelper::$ADMIN) {
                    $cab_data = DB::table('cab as C')->select('C.CAB_ID','C.VENDOR_ID', 'V.VEHICLE_ID', 'V.VEHICLE_REG_NO','D.DRIVER_MOBILE')
                            ->join('vehicles as V', 'V.VEHICLE_ID', '=', 'C.VEHICLE_ID')
                            ->join('drivers as D', 'D.DRIVERS_ID', '=', 'C.DRIVER_ID')
                            // ->where('C.VENDOR_ID', '=', $vendor_id)
                            ->where('C.BRANCH_ID', '=', $branch_id)
                            ->where('C.ACTIVE', '=', $RS_ACTIVE)
                            ->distinct('C.CAB_ID')->get();
                } else {
                    $cab_data = DB::table('cab as C')->select('C.CAB_ID','C.VENDOR_ID', 'V.VEHICLE_ID', 'V.VEHICLE_REG_NO','D.DRIVER_MOBILE')
                    ->join('vehicles as V ', 'V.VEHICLE_ID', '=', 'C.VEHICLE_ID')
                    ->join('drivers as D ', 'D.DRIVERS_ID', '=', 'C.DRIVER_ID')
                    ->where('C.VENDOR_ID', '=', $vendor_id)
                    ->where('C.BRANCH_ID', '=', $branch_id)
                    ->where('C.ACTIVE', '=', $RS_ACTIVE)
                    ->distinct('C.CAB_ID')->get();
                }
                
                return response([
                'success' => true,
                'status' => 3,
                'cab_list' => $cab_data
                
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Data For Create for LiveTravelpath Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ],500);
        }
    }
   
    
	

   
}
