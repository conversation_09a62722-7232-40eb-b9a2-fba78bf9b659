<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;

use App\Services\Admin\LiveTrackingService;
use App\Http\Requests\Admin\LiveTrackingRequest;

use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class LiveTrackingController extends Controller
{
    protected LiveTrackingService $livetrackingService;

    public function __construct(LiveTrackingService $livetrackingService)
    {
        $this->livetrackingService = $livetrackingService;
    }
    
    public function dataForCreate(): FoundationApplication|Response|ResponseFactory
    {
        return $this->livetrackingService->dataForCreate();
    }
   
    public function live_travel_path(LiveTrackingRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->livetrackingService->live_travel_path($request);
        
    }
   
   
 
}
