<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;

use App\Services\Admin\NewRosterService;

use App\Http\Requests\Admin\NewRosterEmployeeFindRequest;
use App\Http\Requests\Admin\TripTypeWiseLoginTimeRequest;
use App\Http\Requests\Admin\PostRosterRequest;

use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class NewRosterController extends Controller
{
    protected NewRosterService $newRosterService;

    public function __construct(NewRosterService $newRosterService)
    {
        $this->newRosterService = $newRosterService;
    }
    
    public function dataForCreate(): FoundationApplication|Response|ResponseFactory
    {
        return $this->newRosterService->dataForCreate();
    }
   
    public function newrosteremployee(NewRosterEmployeeFindRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->newRosterService->newrosteremployee($request);
        
    }
   
    public function trip_type_wise_login_time(TripTypeWiseLoginTimeRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->newRosterService->trip_type_wise_login_time($request);
        
    }
   
    public function PostRosterRequest(PostRosterRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->newRosterService->PostRosterRequest($request);
        
    }
   
   
 
}
