<?php

namespace App\Http\Requests\Admin;

use App\Helpers\CommonFunction;
use Closure;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;

class EnableEditAddressRequest extends FormRequest
{
    protected CommonFunction $commonFunction;

    public function __construct(CommonFunction $commonFunction)
    {
        parent::__construct();
        $this->commonFunction = $commonFunction;
    }

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
       
        return [
            'type' => 'required|string',
            'emp_auto_id' => 'required|numeric'
        ];
    }

    public function messages(): array
    {
        return [
            'type' => 'Enable Address field is required',
            'emp_auto_id.numeric' => 'This Employee ID only numeric'
        ];
    }

    public function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json([
            'success' => false,
            'validation_error' => true,
            'message' => 'Validation errors',
            'data' => $validator->errors()
        ]));
    }

    private function checkDeactivatedEmployee($branchId): Closure
    {
        return function (string $attribute, mixed $value, Closure $fail) use ($branchId) {
            $deactivatedEmployee = DB::table("employees")
                ->where("BRANCH_ID", $branchId)
                ->where("EMPLOYEES_ID", $value)
                ->where("ACTIVE", 2)
                ->exists();

            if ($deactivatedEmployee) {
                $fail("User already exists and is deactivated.");
            }
        };
    }

    private function checkUniqueMobile($mobileEncrypt, $branchId): Closure
    {
        return function (string $attribute, mixed $value, Closure $fail) use ($mobileEncrypt, $branchId) {
            $exists = DB::table('employees')
                ->where('BRANCH_ID', $branchId)
                ->where("MOBILE", $mobileEncrypt)
                ->exists();

            if ($exists) {
                $fail('The mobile number has already been taken.');
            }
        };
    }
}
