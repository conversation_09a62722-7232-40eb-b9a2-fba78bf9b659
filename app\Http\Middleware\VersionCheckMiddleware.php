<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class VersionCheckMiddleware
{

    const MIN_SUPPORTED_VERSION = '2';
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $version = $request->header('Accept-Version', '1');

        if (version_compare($version, self::MIN_SUPPORTED_VERSION, '<')) {
            return response()->json([
                'error' => 'Unsupported version',
                'message' => 'Please update your app to the latest version.',
                'min_supported_version' => self::MIN_SUPPORTED_VERSION
            ], 426);
        }

        return $next($request);
    }
}
