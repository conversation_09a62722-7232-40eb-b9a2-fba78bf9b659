<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;

use App\Services\Admin\OTPSMSService;
use App\Http\Requests\Admin\OTPRosterSmsDetailsRequest;
use App\Http\Requests\Admin\OTPRosterSMSSendRequest;
use App\Http\Requests\Admin\DriverOTPSmsRequest;
use App\Http\Requests\Admin\DriverOTPSendSMSRequest;
use App\Http\Requests\Admin\EscortOTPSMSRequest;
use App\Http\Requests\Admin\EscortOTPSMSSendRequest;
use App\Http\Requests\Admin\EmployeeLoginOTPRequest;
use App\Http\Requests\Admin\MultiPushMessageRequest;


use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class OTPSMSController extends Controller
{
    protected OTPSMSService $otpsmsService;

    public function __construct(OTPSMSService $otpsmsService)
    {
        $this->otpsmsService = $otpsmsService;
    }
    public function index(): ResponseFactory|Application|Response
    {
        return $this->otpsmsService->otpsms();
    }
    
    public function dataForCreateManualOTP(): FoundationApplication|Response|ResponseFactory
    {
        return $this->manualotpService->dataForCreateManualOTP();
    }
    
    public function otp_sms(): FoundationApplication|Response|ResponseFactory
    {
        return $this->otpsmsService->otp_sms();
    }
 
    public function otp_roster_sms_details(OTPRosterSmsDetailsRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->otpsmsService->otp_roster_sms_details($request);
    }
    public function otp_roster_sms_send(OTPRosterSMSSendRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->otpsmsService->otp_roster_sms_send($request);
    }
 
    public function multi_push_message(MultiPushMessageRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->otpsmsService->multi_push_message($request);
    }
 
    public function driver_otp_sms(DriverOTPSmsRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->otpsmsService->driver_otp_sms($request);
    }
 
    public function driver_otp_send_sms(DriverOTPSendSMSRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->otpsmsService->driver_otp_send_sms($request);
    }
 
    public function escort_otp_sms(EscortOTPSMSRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->otpsmsService->escort_otp_sms($request);
    }
 
    public function escort_otp_sms_send(EscortOTPSMSSendRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->otpsmsService->escort_otp_sms_send($request);
    }
 
    public function employee_login_otp(EmployeeLoginOTPRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->otpsmsService->employee_login_otp($request);
    }
 
    
 
}
