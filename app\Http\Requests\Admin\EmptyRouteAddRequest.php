<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Contracts\Validation\Validator;

class EmptyRouteAddRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'vendor_name' => 'required',
            'trip_type' => 'required',
            'in_out' => 'required',
            'vehicle_no' => 'required',
            'empty_km' => 'required',
            'remarks' => 'required',
        ];
    }

    public function messages(): array
    {
        return [
            'vendor_name.required' => 'Vendor name is required',
            'trip_type.required' => 'Trip type is required',
            'in_out.required' => 'In/out is required',
            'vehicle_no.required' => 'Vehicle number is required',
            'empty_km.required' => 'Empty km is required',
            'remarks.required' => 'Remarks is required',
        ];
    }

    public function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json([
            'success' => false,
            'validation_error' => true,
            'message' => 'Validation errors',
            'data' => $validator->errors(),
        ]));
    }
}
