<?php


namespace App\Http\Requests\Admin;
use Closure;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;

class MisShiftTimeRequest extends FormRequest
{
    
    
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {        
        return [
            'vendor_id' => ['required'],
            'triptype_id' => ['required'],
            'selected_date' => 'required',
        ];
    }

    public function messages(): array
    {
        return [
            'triptype_id.required' => 'select trip type',
            'vendor_id.required' => 'select Vendor Name',
            'selected_date.required' => 'Shift Date field is required',
        ];
    }
   

    public function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json([
            'success' => false,
            'validation_error' => true,
            'message' => 'Validation errors',
            'data' => $validator->errors()
        ]));
    }
   
}
