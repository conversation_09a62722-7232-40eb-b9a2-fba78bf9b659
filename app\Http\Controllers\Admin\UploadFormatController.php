<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;

use App\Services\Admin\UploadFormatService;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application;
use Illuminate\Http\Response;
use Illuminate\Http\Request;

class UploadFormatController extends Controller
{
    protected UploadFormatService $uploadformatService;

    public function __construct(UploadFormatService $uploadformatService)
    {
        
        $this->uploadformatService = $uploadformatService;
    }


    public function dataforcreate(): Application|Response|ResponseFactory
    {
        
        return $this->uploadformatService->dataforcreate();
    }
	
}
