<?php

namespace App\Services\Admin;

use App\Helpers\CommonFunction;
use App\Helpers\MyHelper;
use Carbon\Carbon;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use App\Models\Vendor;
use App\Models\Cab_allocation;
use App\Models\Roster;
use App\Models\RosterPassenger;
use App\Models\RouteEscorts;
use App\Models\Reason_Log;
use App\Models\Cab;
use App\Models\Vehicle;
use App\Models\VehicleModel;
use App\Models\Driver;
use App\Models\property;
use App\Models\Driver_Billing_Summary;
use App\Http\Controllers\MaskNumberClearController;
use App\Http\Controllers\ElasticController;

class ManualOTPService
{
    protected CommonFunction $commonFunction;
    protected ?Branch $currentBranch = null;

    public function __construct(CommonFunction $commonFunction)
    {
        $this->commonFunction = $commonFunction;
    }
	     
    
    public function vendorwise_vehicle_list($request, bool $active = true): FoundationApplication|Response|ResponseFactory
    {
        try {
            $branch_id = Auth::user()->BRANCH_ID;
            $userid = Auth::user()->id;
            $db_name = Auth::user()->dbname;
            $RS_ACTIVE=MyHelper::$RS_ACTIVE;

           $authUser = Auth::user();

           $selected_date=$request->selected_date;
           $vendor_id=$request->vendor_id;
          
           $cab_data = DB::connection("$db_name")->table('cab as C')->select('C.CAB_ID','C.VENDOR_ID', 'V.VEHICLE_ID', 'V.VEHICLE_REG_NO')
						->join('vehicles as V', 'V.VEHICLE_ID', '=', 'C.VEHICLE_ID')
						->where('C.VENDOR_ID', '=', $vendor_id)
						->where('C.BRANCH_ID', '=', $branch_id) ->where('C.ACTIVE', '=', $RS_ACTIVE)->distinct('C.CAB_ID')->get();

        
            return response([
                'success' => true,
                'status' => 3,
                'cab_data' => $cab_data,
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'Manual otp  status Unsuccessful' : 'Manual otp  status Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ],500);
        }
    } 
   
    public function select_cab_wise_route($request, bool $active = true): FoundationApplication|Response|ResponseFactory
    {
        try {
            $branch_id = Auth::user()->BRANCH_ID;
            $userid = Auth::user()->id;
            $db_name = Auth::user()->dbname;
            $RS_ACTIVE=MyHelper::$RS_ACTIVE;

           $authUser = Auth::user();

           $selected_date=$request->selected_date;
           $vendor_id=$request->selected_vendor_id;
           $cab_id=$request->selected_cab_id;
           $check_sts = [MyHelper::$RS_NEWROSTER,MyHelper::$RS_TOTALALLOT,MyHelper::$RS_TOTALACCEPT,MyHelper::$RS_TOTALEXECUTE];
          
           $cab_route = DB::connection("$db_name")->table('rosters as R')
                    ->select('R.ROUTE_ID', 'R.ROSTER_ID', 'R.TRIP_TYPE', 'R.ESTIMATE_END_TIME', 'R.ESTIMATE_START_TIME')

                    ->where('R.CAB_ID', '=', $cab_id)
                    ->where('R.VENDOR_ID', '=', $vendor_id)
                    ->where('R.ACTIVE', '=', $RS_ACTIVE)
                    ->where(function($query) use ($selected_date) {
                        $query->whereDate('R.ESTIMATE_END_TIME', '>=', $selected_date)
                            ->orWhereDate('R.ESTIMATE_START_TIME', '>=', $selected_date);
                    })
                    ->whereIn('R.ROSTER_STATUS', $check_sts)
                    ->where('R.BRANCH_ID', '=', $branch_id)
                    ->get();
        
            return response([
                'success' => true,
                'status' => 3,
                'cab_route' => $cab_route,
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'Manual otp cab wise status Unsuccessful' : 'Manual otp cab wise status Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ],500);
        }
    } 
    
    public function arriveboarded($request, bool $active = true): FoundationApplication|Response|ResponseFactory
    {
        try {
            $branch_id = Auth::user()->BRANCH_ID;
            $id = Auth::user()->id;
            $db_name = Auth::user()->dbname;
            $RS_ACTIVE=MyHelper::$RS_ACTIVE;

           $authUser = Auth::user();

           $selected_roster_id=$request->selected_roster_id;
           $selected_passenger_id=$request->selected_passenger_id;
          
           $arrival_update_sts = MyHelper::$RPS_ARRIVAL;
           $ar1 = explode(',', MyHelper::$RPS_TTLARRIVAL);
           $ar2 = explode(',', MyHelper::$RPS_TTLCABDELAY);
           $arrival_sts = array_merge($ar1, $ar2);
           $update_sts =  $arrival_update_sts;
           $checked_status = $arrival_sts;
           $date = Carbon::now();
          
           $sql = "SELECT RS.CAB_ID,RS.TRIP_TYPE,R.EMPLOYEE_ID,E.NAME,E.MOBILE_CATEGORY,E.MOBILE_GCM,D.MOBILE_GCM as driver_gcm,R.ROSTER_PASSENGER_STATUS,conv(bin(R.ROSTER_PASSENGER_STATUS)+bin(".$update_sts."),2,10) as upstatus,R.ROSTER_ID,O.OTP,L.LOCATION_NAME FROM roster_passengers as R 
           inner join rosters as RS ON RS.ROSTER_ID =R.ROSTER_ID
           inner join employees as E ON E.EMPLOYEES_ID=R.EMPLOYEE_ID and E.BRANCH_ID='".$branch_id."'
           left join cab as C ON C.CAB_ID=RS.CAB_ID
           left join devices as D ON D.DEVICE_ID=C.DEVICE_ID
           left join otp_verification as O ON O.ROSTER_PASSENGER_ID='".$selected_passenger_id."'
           left join locations as L ON L.LOCATION_ID=R.LOCATION_ID
           WHERE R.ROSTER_PASSENGER_ID='".$selected_passenger_id."'";
           $roster_Pass_status =DB::connection($db_name)->select($sql);
           $upstatus = $roster_Pass_status[0]->upstatus;
           $ROSTER_ID = $roster_Pass_status[0]->ROSTER_ID;
           $cab_id=$roster_Pass_status[0]->CAB_ID;
           $mobile_category=$roster_Pass_status[0]->MOBILE_CATEGORY;
           $mobile_gcm=$roster_Pass_status[0]->MOBILE_GCM;
           $driver_gcm=$roster_Pass_status[0]->driver_gcm;
           $employee_id=$roster_Pass_status[0]->EMPLOYEE_ID;
           $location_name=$roster_Pass_status[0]->LOCATION_NAME;
           $otp=$roster_Pass_status[0]->OTP;
           $TRIP_TYPE=$roster_Pass_status[0]->TRIP_TYPE;
           
           $sts = $roster_Pass_status[0]->ROSTER_PASSENGER_STATUS;
           if ($TRIP_TYPE == 'P') {
               $arr = array("ROSTER_PASSENGER_STATUS" => $upstatus, "DRIVER_ARRIVAL_TIME" => $date->format("Y-m-d H:i:s"),"UPDATED_BY"=>$id);
           } else {
           $arr = array("ROSTER_PASSENGER_STATUS" => $upstatus, "ACTUAL_START_TIME" => $date->format("Y-m-d H:i:s"),"UPDATED_BY"=>$id);
           }
           if (in_array($upstatus, $checked_status) == true) {
               
               if($TRIP_TYPE=='D')
               {
                   //$obj3->property_check($passenger_id);
                   $update_roster=Roster::where("ROSTER_ID","=",$selected_roster_id)->increment('PASSENGER_ALLOT_IN_ROUT_COUNT', 1);
               }
               $result = RosterPassenger::where("ROSTER_PASSENGER_ID", "=", $selected_passenger_id)->update($arr);
               /* Arrived / Boarded web log */
               $date_f=$this->commonFunction->date_format_add();
             //  $elastic=new ElasticController();
               $log_arr=array("ROSTER_ID"=>$selected_roster_id,"BRANCH_ID"=>$branch_id,"ACTION"=>'Arrived/Boarded',"PROCESS_DATE"=>$date_f,"CATEGORY"=>'Manual OTP',"USER_ID"=>$id,"PASSENGER_ID"=>$selected_passenger_id,"UPDATED_STATUS"=>$upstatus,"PREVIOUS_STATUS"=>$sts,"TRIP_TYPE"=>$TRIP_TYPE);
               //$ret=$elastic->insertWebLogs($log_arr);
               /* Arrived / Boarded web log End */
               /*Mobile GCM */
               $empname=$this->commonFunction->AES_DECRYPT($roster_Pass_status[0]->NAME,env('AES_ENCRYPT_KEY'));
               if($mobile_gcm!='' && $driver_gcm!='')
               {
                   $PICKUP_MSG=" Dear Mr/Ms ".$empname.",Cab has arrived at your destination.Enter OTP ".$otp." in the GPS Device available with driver. Thanks";
                   $PICKUP_HEADING=env('PICKUP_HEADING');
                   $DROP_MSG="Dear Mr/Ms ".$empname.", Cab has started from ".$site_name.". Enter OTP ".$otp." in GPS device at your drop place. Thanks";
                   $DROP_HEADING=env('DROP_HEADING');
                   $HEADING=$TRIP_TYPE=='P'?$PICKUP_HEADING:$DROP_HEADING;
                   $MSG=$TRIP_TYPE=='P'?$PICKUP_MSG:$DROP_MSG;
                   $driver_msg=$TRIP_TYPE=='P'?'Employee - '.$empname.' ('.$location_name.') was manually Arrived':'Employee - '.$empname.' ('.$location_name.') was manually Boarded';
                   $driver_heading=$TRIP_TYPE=='P'?'Manually Arrived':'Manually Boarded';
                   $notification_cat='3';
                   $cur_date_time=date('Y-m-d H:i:s');
                   $emp_notification=DB::insert("INSERT INTO `notification` (`branch_id`, `empid_cabid`, `fcm_id`, `heading`, `message`, `category_value`,`app_category`,`created_at`) VALUES ('".$branch_id."','".$employee_id."','".$mobile_gcm."','".$HEADING."','".$MSG."','".$notification_cat."','".$mobile_category."','".$cur_date_time."')");
                   $cab_notification=DB::insert("INSERT INTO `notification` (`branch_id`, `empid_cabid`, `fcm_id`, `heading`, `message`, `category_value`,`app_category`,`created_at`) VALUES ('".$branch_id."','".$cab_id."','".$driver_gcm."','".$driver_heading."','".$driver_msg."','".$notification_cat."','ANDROID','".$cur_date_time."')");
               }
           }
        
            return response([
                'success' => true,
                'status' => 3,
                'msg' => "Cab Arrived/Boarded successfully updated"
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'Manual otp arrived Boared Unsuccessful' : 'Manual otp arrived Boared Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ],500);
        }
    } 
    
    public function manual_otp($request, bool $active = true): FoundationApplication|Response|ResponseFactory
    {
        try {
            $branch_id = Auth::user()->BRANCH_ID;
            $id = Auth::user()->id;
            $db_name = Auth::user()->dbname;
            $RS_ACTIVE=MyHelper::$RS_ACTIVE;

           $authUser = Auth::user();

           $selected_roster_id=$request->selected_roster_id;
           $selected_passenger_id=$request->selected_passenger_id;
           $remarks=$request->remarks;
          
                $update_manual_otp = MyHelper::$RPS_MANUALOTP;
                $rr1 = explode(',', MyHelper::$RPS_TTLSYSTEMOTP);
                $rr2 = explode(',', MyHelper::$RPS_TTLEMPLOYEEDELAY);
                $rr3 = explode(',', MyHelper::$RPS_TTLMANUALOTP);
                $rr4 = explode(',', MyHelper::$RPS_TTLCABEMPLOYEEDELAY);
                $rr5 = explode(',', MyHelper::$RPS_TTLCABSYSTEMOTP);
                $arry1 = array_merge($rr1, $rr2);
                $arry2 = array_merge($rr3, $rr4);
                $drop_otp_status=array_merge($rr1,$rr3);
                $arr = array_merge($arry1, $arry2);
                $pickup_otp_status=array_merge($arr,$rr5);
                $update_sts = $update_manual_otp;
               
           $date = Carbon::now();
          
           $sql="SELECT RS.CAB_ID,RS.TRIP_TYPE,R.EMPLOYEE_ID,E.MOBILE_CATEGORY,E.NAME,E.MOBILE_GCM,D.MOBILE_GCM as driver_gcm,R.ROSTER_PASSENGER_STATUS,conv(bin(R.ROSTER_PASSENGER_STATUS)+bin(".$update_sts."),2,10) as otpstatus,R.ROSTER_ID,L.LOCATION_NAME FROM roster_passengers as R 
           inner join rosters as RS ON RS.ROSTER_ID =R.ROSTER_ID
				left join cab as C ON C.CAB_ID=RS.CAB_ID
				left join devices as D ON D.DEVICE_ID=C.DEVICE_ID
				left join locations as L ON L.LOCATION_ID=R.LOCATION_ID
				inner join employees as E ON E.EMPLOYEES_ID=R.EMPLOYEE_ID and E.BRANCH_ID='".$branch_id."'
				WHERE R.ROSTER_PASSENGER_ID='".$selected_passenger_id."' ";
                $roster_Pass_status = DB::connection($db_name)->select($sql);
                $otpstatus = $roster_Pass_status[0]->otpstatus;
                $ROSTER_ID = $roster_Pass_status[0]->ROSTER_ID;
				$sts = $roster_Pass_status[0]->ROSTER_PASSENGER_STATUS;
				$employee_id=$roster_Pass_status[0]->EMPLOYEE_ID;
				$mobile_category=$roster_Pass_status[0]->MOBILE_CATEGORY;
				$mobile_gcm=$roster_Pass_status[0]->MOBILE_GCM;
				$driver_gcm=$roster_Pass_status[0]->driver_gcm;
				$location_name=$roster_Pass_status[0]->LOCATION_NAME;
				$cab_id=$roster_Pass_status[0]->CAB_ID;
				$TRIP_TYPE=$roster_Pass_status[0]->TRIP_TYPE;

                $all_otp_status=$TRIP_TYPE == 'P' ? $pickup_otp_status:$drop_otp_status;
                
                $checked_sts = $all_otp_status;
                if ($TRIP_TYPE == 'P') {
                    $arr = array("ROSTER_PASSENGER_STATUS" => $otpstatus, "REMARKS" => $remarks, "ACTUAL_START_TIME" => $date->format("Y-m-d H:i:s"),"UPDATED_BY"=>$id);
                }  else {
                    $arr = array("ROSTER_PASSENGER_STATUS" => $otpstatus, "REMARKS" => $remarks, "ACTUAL_END_TIME" => $date->format("Y-m-d H:i:s"),"UPDATED_BY"=>$id);
                }
				
                if (in_array($otpstatus, $checked_sts) == true) {
					if($TRIP_TYPE=='P')
					{
						$update_roster=Roster::where("ROSTER_ID","=",$selected_roster_id)->increment('PASSENGER_ALLOT_IN_ROUT_COUNT', 1);
					}

					//$obj2->property_check($passenger_id);
					$update = Rosterpassenger::where("ROSTER_PASSENGER_ID", "=", $selected_passenger_id)->update($arr);
					
					/* OTP web log */
					$date_f=$this->commonFunction->date_format_add();
					$elastic=new ElasticController();
					$log_arr=array("ROSTER_ID"=>$selected_roster_id,"BRANCH_ID"=>$branch_id,"ACTION"=>'OTP',"PROCESS_DATE"=>$date_f,"CATEGORY"=>'Manual OTP',"USER_ID"=>$id,"SELECTED_DATE"=>$selected_date,"PASSENGER_ID"=>$selected_passenger_id,"UPDATED_STATUS"=>$otpstatus,"PREVIOUS_STATUS"=>$sts,"TRIP_TYPE"=>$TRIP_TYPE,"REMARKS"=>$remarks);
					//$ret=$elastic->insertWebLogs($log_arr);
					/* OTP web log End */
					$empname=$this->commonFunction->AES_DECRYPT($roster_Pass_status[0]->NAME,env('AES_ENCRYPT_KEY'));
					if($mobile_gcm!='' && $driver_gcm!='')
					{
						$OTP_PICKUP_MSG='You have boarded the cab';
						$OTP_PICKUP_HEADING=env('OTP_PICKUP_HEADING');
						$OTP_DROP_MSG='You have deboarded from cab';
						$OTP_DROP_HEADING=env('OTP_DROP_HEADING');
						$MSG=$TRIP_TYPE=='P'?$OTP_PICKUP_MSG:$OTP_DROP_MSG;
						$driver_msg="Manual OTP given for employee - ".$empname ."( ".$location_name." )";
						$NOTIFICATION_CAT=env('NOTIFICATION_CAT');
						$cur_date_time=date('Y-m-d H:i:s');
						$emp_notification=DB::insert("INSERT INTO `notification` (`branch_id`, `empid_cabid`, `fcm_id`, `heading`, `message`, `category_value`,`app_category`,`created_at`) VALUES ('".$branch_id."','".$employee_id."','".$mobile_gcm."','OTP','".$MSG."','".$NOTIFICATION_CAT."','".$mobile_category."','".$cur_date_time."')");
						$cab_notification=DB::insert("INSERT INTO `notification` (`branch_id`, `empid_cabid`, `fcm_id`, `heading`, `message`, `category_value`,`app_category`,`created_at`) VALUES ('".$branch_id."','".$cab_id."','".$driver_gcm."','Manual OTP','".$driver_msg."','".$NOTIFICATION_CAT."','ANDROID','".$cur_date_time."')");
					}
					$mask_enable = $this->commonFunction->GetPropertyValue('CALL MASKING OPTION');
					if($mask_enable=='Y')
					{
						$mask= new MaskNumberClearController();
						//$mask->Clear_MaskNumber($passenger_id,$branch_id,$route_id,'Passenger');
					}
                }	
        
            return response([
                'success' => true,
                'status' => 3,
                'msg' => "Manual otp successfully updated"
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'Manual otp Unsuccessful' : 'Manual otp Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ],500);
        }
    } 
    
    public function noshow_reset($request, bool $active = true): FoundationApplication|Response|ResponseFactory
    {
        try {
            $branch_id = Auth::user()->BRANCH_ID;
            $id = Auth::user()->id;
            $db_name = Auth::user()->dbname;
            $RS_ACTIVE=MyHelper::$RS_ACTIVE;

                $authUser = Auth::user();
                $ESCORT_REMOVE = MyHelper::$ESCORT_REMOVE;
				$RS_FGENDER = MyHelper::$RS_FGENDER;
				$ESCORT_NEW = MyHelper::$ESCORT_NEW;
				$RS_ACTIVE=MyHelper::$RS_ACTIVE;
				$userid=Auth::user()->id;
				$branch_id=Auth::user()->BRANCH_ID;
				$RPS_TTLNOSHOW=MyHelper::$RPS_TTLNOSHOW;
				$PR_ESCORT_ENABLE=MyHelper::$PR_ESCORT_ENABLE;
				$PR_ESCORT_START_TIME=MyHelper::$PR_ESCORT_START_TIME;
				$PR_ESCORT_END_TIME=MyHelper::$PR_ESCORT_END_TIME;
                $RPS_NOSHOW=MyHelper::$RPS_NOSHOW;
          
                $selected_passenger_id=$request->selected_passenger_id;
                $reset_remarks=$request->reset_remarks;
                $resetopt=$request->resetopt;
                $reset_noshow_reason=$request->reset_noshow_reason;



           $sql = "SELECT R.ROSTER_ID,R.ROSTER_PASSENGER_STATUS,R.EMPLOYEE_ID,conv(bin(R.ROSTER_PASSENGER_STATUS)-bin(" . $RPS_NOSHOW . "),2,10) as upstatus,RS.PASSENGER_ALLOT_IN_ROUT_COUNT,RS.TRIP_TYPE	FROM roster_passengers as R
           inner join rosters as RS ON RS.ROSTER_ID=R.ROSTER_ID
           WHERE R.ROSTER_PASSENGER_ID='".$selected_passenger_id."' ";
           $roster_Pass_status = DB::select($sql);
           $upstatus = $roster_Pass_status[0]->upstatus;
           $ROSTER_ID = $roster_Pass_status[0]->ROSTER_ID;
           $employee_id = $roster_Pass_status[0]->EMPLOYEE_ID;
           $TRIP_TYPE = $roster_Pass_status[0]->TRIP_TYPE;
           $previous_status = $roster_Pass_status[0]->ROSTER_PASSENGER_STATUS;
           $passenger_allot_in_rout_count = $roster_Pass_status[0]->PASSENGER_ALLOT_IN_ROUT_COUNT!=''?$roster_Pass_status[0]->PASSENGER_ALLOT_IN_ROUT_COUNT:'0';
           $increment_roster='';
           $dec_roster='';
           if($resetopt=='noshowreset')
           {
               if($TRIP_TYPE=='P')
               {
                   $result1 = RosterPassenger::where("ROSTER_PASSENGER_ID", "=", $selected_passenger_id)
                   ->update(array("ROSTER_PASSENGER_STATUS" => $upstatus,"UPDATED_BY"=>$userid,"ACTUAL_START_TIME"=>NULL));
                   Roster::where([["ROSTER_ID", "=", $ROSTER_ID],["ACTIVE", "=", $RS_ACTIVE]])->decrement("PASSENGER_NOSHOW_COUNT",1);
                   $increment_roster=Roster::where([["ROSTER_ID", "=", $ROSTER_ID],["ACTIVE", "=", $RS_ACTIVE]])->increment("PASSENGER_ALLOT_COUNT");
                   /* if($passenger_allot_in_rout_count!=0)
                   {
                       $dec_roster=Roster::where([["ROSTER_ID", "=", $ROSTER_ID],["ACTIVE", "=", $RS_ACTIVE]])->decrement("PASSENGER_ALLOT_IN_ROUT_COUNT");
                   } */
               }
               else
               {
                   $result1 = RosterPassenger::where("ROSTER_PASSENGER_ID", "=", $selected_passenger_id)
                   ->update(array("ROSTER_PASSENGER_STATUS" => 1,"UPDATED_BY"=>$userid,"ACTUAL_END_TIME"=>Null));
                   $upstatus=1;
                   Roster::where([["ROSTER_ID", "=", $ROSTER_ID],["ACTIVE", "=", $RS_ACTIVE]])->decrement("PASSENGER_NOSHOW_COUNT",1);
                   $increment_roster=Roster::where([["ROSTER_ID", "=", $ROSTER_ID],["ACTIVE", "=", $RS_ACTIVE]])->increment("PASSENGER_ALLOT_COUNT");
                   /* if($passenger_allot_in_rout_count!=0)
                   {
                       $dec_roster=Roster::where([["ROSTER_ID", "=", $ROSTER_ID],["ACTIVE", "=", $RS_ACTIVE]])->decrement("PASSENGER_ALLOT_IN_ROUT_COUNT");
                   } */
               }
           }
           else
           {
               $result1 = RosterPassenger::where("ROSTER_PASSENGER_ID", "=", $selected_passenger_id)
                   ->update(array("ROSTER_PASSENGER_STATUS" => 1,"UPDATED_BY"=>$userid,"ACTUAL_END_TIME"=>Null));
               if($passenger_allot_in_rout_count!=0)
               {
                   $dec_roster=Roster::where([["ROSTER_ID", "=", $ROSTER_ID],["ACTIVE", "=", $RS_ACTIVE]])->decrement("PASSENGER_ALLOT_IN_ROUT_COUNT");
               } 
           }
           
           
           $check_escort = "SELECT COUNT(*) as ttlcnt FROM route_escorts WHERE ROSTER_ID='$ROSTER_ID'";
            //AND EMPLOYEE_ID='$employee_id'
               $checkescort = DB::select($check_escort);
               $ttlcnt = $checkescort[0]->ttlcnt;
               if($ttlcnt != 0)
               {
                   Route_Escort::where([["ROSTER_ID", "=", $ROSTER_ID]])->update(array("STATUS" => $ESCORT_REMOVE,"ESCORT_ID" =>NULL)); 
               }
               $escort_check = "SELECT R.TRIP_TYPE,if(R.CAB_ID is NULL,0,R.CAB_ID) AS CABID,rp.ROSTER_ID,rp.EMPLOYEE_ID,e.BRANCH_ID,e.GENDER,
               rp.EMPLOYEE_ID,rp.ROSTER_PASSENGER_STATUS,R.ESTIMATE_END_TIME,R.ESTIMATE_START_TIME FROM roster_passengers as rp
               INNER JOIN rosters R ON R.ROSTER_ID = '$ROSTER_ID'
               INNER JOIN employees as e on e.EMPLOYEES_ID=rp.EMPLOYEE_ID and e.BRANCH_ID=$branch_id
               WHERE R.BRANCH_ID = $branch_id and rp.ROSTER_ID = '$ROSTER_ID' and rp.ACTIVE = $RS_ACTIVE and rp.ROSTER_PASSENGER_STATUS NOT IN ($RPS_TTLNOSHOW)
                ORDER BY rp.ROUTE_ORDER desc LIMIT 1"; 
               $escortcheck = DB::select($escort_check);
               $is_Escort_Time='';
               if(count($escortcheck)>0)
               {
                   if($TRIP_TYPE=='D')
                   {
                       $is_Escort_Time=date("H:i:s",strtotime($escortcheck[0]->ESTIMATE_START_TIME));
                   }
                   else
                   {
                       $is_Escort_Time=date("H:i:s",strtotime($escortcheck[0]->ESTIMATE_END_TIME));
                   }
                   
               }
               //$escort_true =$obj2->getshifttime("ESCORT");
               $isEscort = '';
               $propertie = property::where([['BRANCH_ID', '=', $branch_id], ['ACTIVE', '=', $RS_ACTIVE]])->get();
               for($i=0;$i<count($propertie);$i++)
               {
                   $PROPERTIE_NAME = $propertie[$i]->PROPERTIE_NAME;
                   $PROPERTIE_VALUE = $propertie[$i]->PROPERTIE_VALUE;

                   switch ($PROPERTIE_NAME) {
                       case $PR_ESCORT_ENABLE:
                           $PR_ESCORTENABLE = $PROPERTIE_VALUE;
                           break;
                       case $PR_ESCORT_START_TIME:
                           $PR_ESCORTSTART_TIME = $PROPERTIE_VALUE;
                           break;
                       case $PR_ESCORT_END_TIME:
                           $PR_ESCORTEND_TIME = $PROPERTIE_VALUE;
                           break;
                       default:
                           break;
                   }
               }
                if ($PR_ESCORTENABLE == 'Y') {
                   /* if (strtotime($is_Escort_Time) >= strtotime($PR_ESCORTSTART_TIME) && strtotime($PR_ESCORTEND_TIME) <= strtotime($is_Escort_Time)) {
                       $isEscort = 'true';
                   } else {
                       $isEscort = 'false';
                   } */
                    $ret1 = $this->commonFunction->check_time($PR_ESCORTSTART_TIME, $PR_ESCORTEND_TIME, $is_Escort_Time) ? "yes" : "no";
                   
                       if ($ret1 == "yes") {
                           $isEscort = 'true';
                       }
               }
               $insert_escort="";
               $escort_roster_id='';
               $ins='';
               if($isEscort == 'true')
               {
                   if(count($escortcheck) > 0 )
                   {
                       $GENDER = $escortcheck[0]->GENDER;
                       $EMPLOYEE_ID = $escortcheck[0]->EMPLOYEE_ID;
                       $TRIP_TYPE = $escortcheck[0]->TRIP_TYPE;
                       if($GENDER == $RS_FGENDER)
                       {
                           $checkescort = "SELECT COUNT(*) as ttlcnt FROM route_escorts WHERE ROSTER_ID='$ROSTER_ID' AND EMPLOYEE_ID='$EMPLOYEE_ID' and STATUS='".$ESCORT_NEW."' ";
                           $checkescorts = DB::select($checkescort);
                           $ttlcnts = $checkescorts[0]->ttlcnt;
                           if($ttlcnts == 0)
                           {
                               $insert_escort = array("BRANCH_ID" => $branch_id, "ROSTER_ID" => $ROSTER_ID, "EMPLOYEE_ID" => $EMPLOYEE_ID, "STATUS" => $ESCORT_NEW,
                               "CREATED_BY" => $userid, "created_at" => date('Y-m-d H:i:s'));
                               $ins=RouteEscorts::insert($insert_escort);
                               $escort_roster_id=$ROSTER_ID;
                           }
                       }
                   }
               }
           
           
           
           $arr=array("ROSTER_PASSENGER_ID"=>$selected_passenger_id,"REASON_ID"=>$reset_noshow_reason,"CREATE_BY"=>$userid,"CREATED_DATE"=>date("Y-m-d H:i:s"),"REMARKS"=>$reset_remarks);
           $reason_log_ins=Reason_Log::insert($arr);
           /* Reset web log */
               
               $date_f=$this->commonFunction->date_format_add();
               $elastic=new ElasticController();
               $log_arr=array("ROSTER_ID"=>$ROSTER_ID,"BRANCH_ID"=>$branch_id,"ACTION"=>'Reset',"PROCESS_DATE"=>$date_f,"CATEGORY"=>'Manual OTP',"USER_ID"=>$userid,"PASSENGER_ID"=>$selected_passenger_id,"UPDATED_STATUS"=>$upstatus,"TRIP_TYPE"=>$TRIP_TYPE,"PREVIOUS_STATUS"=>$previous_status,"REMARKS"=>$reset_remarks,"REASON_ID"=>$reset_noshow_reason,"INCREMENT_ROSTER_SUCCESS"=>$increment_roster);	
        
            return response([
                'success' => true,
                'status' => 3,
                'msg' => "Manual Noshow reset successfully updated"
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'Manual Noshow reset Unsuccessful' : 'Manual noshow reset Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ],500);
        }
    } 
    
    public function selected_route_details($request, bool $active = true): FoundationApplication|Response|ResponseFactory
    {
        try {
            $branch_id = Auth::user()->BRANCH_ID;
            $userid = Auth::user()->id;
            $db_name = Auth::user()->dbname;
            $RS_ACTIVE=MyHelper::$RS_ACTIVE;
            $RPS_TTLNOSHOW=explode(',',MyHelper::$RPS_TTLNOSHOW);
            $RPS_TTLARRIVAL=explode(',',MyHelper::$RPS_TTLARRIVAL);
            $RPS_TTLCABDELAY=explode(',',MyHelper::$RPS_TTLCABDELAY);
            $arrived_array=array_merge($RPS_TTLCABDELAY,$RPS_TTLARRIVAL);
           $authUser = Auth::user();

            $selected_roster_id=$request->selected_roster_id;
          $datas= Roster::where("ROSTER_ID","=",$selected_roster_id)->get();
          $order = $datas[0]->TRIP_TYPE == 'P' ? 'DESC' : 'ASC';

        $data = RosterPassenger::on("$db_name")
        ->from('roster_passengers as RP')
        ->select(
        'RP.ROSTER_PASSENGER_STATUS', 
        'RP.ROSTER_ID', 
        'RP.ROUTE_ORDER', 
        'RP.ROSTER_PASSENGER_ID', 
        'RP.ESTIMATE_START_TIME', 
        'RP.DRIVER_ARRIVAL_TIME', 
        'RS.TRIP_APPROVED_KM', 
        'RS.ROUTE_ID', 
        'RS.TOTAL_KM', 
        'RS.TRIP_TYPE', 
        'RP.ACTUAL_START_TIME', 
        'RS.ACTUAL_START_TIME as driver_start_time', 
        'RS.ROSTER_STATUS', 
        'RS.ACTUAL_END_TIME as destination_time', 
        'RP.ACTUAL_END_TIME', 
        'L.LOCATION_NAME', 
        'BR.BRANCH_NAME', 
        'RP.ESTIMATE_END_TIME', 
        'RP.EMPLOYEE_ID', 
        'EM.NAME', 
        'EM.MOBILE', 
        'D.DRIVER_MOBILE', 
        'EM.GENDER'
    )
    ->join('employees as EM', 'EM.EMPLOYEES_ID', '=', 'RP.EMPLOYEE_ID')
    ->join('rosters as RS', 'RS.ROSTER_ID', '=', 'RP.ROSTER_ID')
    ->join('branch as BR', 'BR.BRANCH_ID', '=', 'RS.BRANCH_ID')
    ->join('locations as L', 'L.LOCATION_ID', '=', 'RP.LOCATION_ID')
    ->join('cab as C', 'C.CAB_ID', '=', 'RS.CAB_ID')
    ->join('drivers as D', 'D.DRIVERS_ID', '=', 'C.DRIVER_ID')
    ->where('RP.ROSTER_ID', '=', $selected_roster_id)
    ->where('RS.BRANCH_ID', '=', $branch_id)
    ->where('EM.BRANCH_ID', '=', $branch_id)
    ->where('RP.ACTIVE', '=', $RS_ACTIVE);
  //  ->orderBy('RP.ROUTE_ORDER', $order)
  //  ->get();
    $perPage = $request->input('per_page', MyHelper::$PAGINATION_PER_PAGE);
            $filterModel = $request->input('filterModel');
            if ($filterModel) {
                foreach ($filterModel as $field => $filter) {
                    if (isset($filter['filter']) && $filter['filter'] !== '') {
                        $value = $filter['filter'];
                        $type = $filter['type'];

                        switch ($field) {
                            case 'LOCATION_NAME':
                                $data->where('L.LOCATION_NAME', 'like', "%{$value}%");
                                break;
                            case 'EMPLOYEE_ID':
                                $data->where('E.EMPLOYEE_ID', 'like', "%{$value}%");
                                break;
                        }
                    }
                }
            }


            if ($request->has('orderBy') && !empty($request->input('orderBy'))) {
                $orderBy = $request->input('orderBy');
                $order = $request->input('order', 'asc');
                $data->orderBy($orderBy, $order);
            } else {
                $data->orderBy('RP.ROUTE_ORDER', $order);
            }


            if ($perPage === MyHelper::$PAGINATION_PER_PAGE_ALL) {
                $paginateddata = $data->paginate($data->count());
            } else {
                $perPage = is_numeric($perPage) ? (int)$perPage : MyHelper::$PAGINATION_PER_PAGE;
                $paginateddata = $data->paginate($perPage);
            }
            

            $paginateddata->getCollection()->transform(function ($dataV) use ($RPS_TTLNOSHOW,$arrived_array){
                $arrived_btn_is_enabled = false;
                $no_show_reset_btn_is_enabled=false;
                $no_show_and_otp_btn_is_enabled=false;

                $D_boarded_btn_is_enabled=false;
                $D_boarded_reset_btn_is_enabled=false;
                $D_noshow_btn_is_enabled=false;
                $D_noshow_reset_btn_is_enabled=false;
                $D_otp_btn_is_enabled=false;

                $boarded_no_show_btn_is_enabled=false;

                // $no_show_reset_btn_is_enabled=false;
 
                 $boarded_reset_btn_is_enabled=false;
               //  $otp_btn_is_enabled=false;

                if($dataV->TRIP_TYPE=='P')
                {              
                if (in_array($dataV->ROSTER_PASSENGER_STATUS, [1]) && $dataV->CAB_ID !== '') {
                    $arrived_btn_is_enabled = true;
                   // $boarded_no_show_btn_is_enabled = true;
                }
            
                // If the action button is enabled for any passenger, disable manual trip close
                if (in_array($dataV->ROSTER_PASSENGER_STATUS, $arrived_array) && $dataV->CAB_ID !== '') {
                    $no_show_and_otp_btn_is_enabled = true;
                   // $boarded_reset_btn_is_enabled = true;
                }
            
                if (in_array($dataV->ROSTER_PASSENGER_STATUS, $RPS_TTLNOSHOW) && $dataV->CAB_ID !== '') {
                    $no_show_reset_btn_is_enabled = true;
                }

            
                $dataV->arrived_btn_is_enabled = $arrived_btn_is_enabled;
                $dataV->no_show_and_otp_btn_is_enabled = $no_show_and_otp_btn_is_enabled;
                $dataV->no_show_reset_btn_is_enabled = $no_show_reset_btn_is_enabled;
               // $dataV->boarded_no_show_btn_is_enabled = $boarded_no_show_btn_is_enabled;
               // $dataV->boarded_reset_btn_is_enabled = $boarded_reset_btn_is_enabled;
            }
            else
            {
                if (in_array($dataV->ROSTER_PASSENGER_STATUS, [1]) && $dataV->CAB_ID !== '') {
                    $D_boarded_btn_is_enabled = true;
                    $D_noshow_btn_is_enabled = true;
                   
                }
            
                // If the action button is enabled for any passenger, disable manual trip close
                if (in_array($dataV->ROSTER_PASSENGER_STATUS, $arrived_array) && $dataV->CAB_ID !== '') {
                    $D_boarded_reset_btn_is_enabled = true;
                    $D_otp_btn_is_enabled = true;
                }
            
                if (in_array($dataV->ROSTER_PASSENGER_STATUS, $RPS_TTLNOSHOW) && $dataV->CAB_ID !== '') {
                    $D_noshow_reset_btn_is_enabled = true;
                }

            
                $dataV->D_noshow_reset_btn_is_enabled = $D_noshow_reset_btn_is_enabled;
                $dataV->D_otp_btn_is_enabled = $D_otp_btn_is_enabled;
                $dataV->D_boarded_reset_btn_is_enabled = $D_boarded_reset_btn_is_enabled;
                $dataV->D_noshow_btn_is_enabled = $D_noshow_btn_is_enabled;
                $dataV->D_boarded_btn_is_enabled = $D_boarded_btn_is_enabled;

            }
            
                return $dataV;
            });
    
          
        
            return response([
                'success' => true,
                'status' => 3,
                'route_details' => $paginateddata,
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'Manual otp route details Unsuccessful' : 'Manual otp route details Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ],500);
        }
    } 
    
    /**
     * @throws ConnectionException
     */
    public function dataForCreateManualOTP(): FoundationApplication|Response|ResponseFactory
    {

        try {
                $user = Auth::user();
                $RS_ACTIVE = MyHelper::$RS_ACTIVE;
            
                $branch_id = Auth::user()->BRANCH_ID;
                $vendor_id = Auth::user()->vendor_id;
                $reason = DB::table("reason_master")->select('REASON', 'REASON_ID')
                                ->where([["active", "=", $RS_ACTIVE], ["CATEGORY", "=", 'ManualOTP'], ["BRANCH_ID", "=", $branch_id]])->get();
                $noshow_reason = DB::table("reason_master")->select('REASON', 'REASON_ID')
                                ->where([["active", "=", $RS_ACTIVE], ["CATEGORY", "=", 'WebNoShow'], ["BRANCH_ID", "=", $branch_id]])->get();
                $reset_reason = DB::table("reason_master")->select('REASON', 'REASON_ID')
                                ->where([["active", "=", $RS_ACTIVE], ["CATEGORY", "=", 'NoshowReset'], ["BRANCH_ID", "=", $branch_id]])->get();
                if (Auth::user()->user_type == MyHelper::$ADMIN) {
                    $vendor_list = Vendor::where('ACTIVE', '=', $RS_ACTIVE)->where("BRANCH_ID", "=", $branch_id)->get();
                } else {
                    $vendor_list = Vendor::where('ACTIVE', '=', $RS_ACTIVE)->where("VENDOR_ID", "=", $vendor_id)->get();
                }
                
                return response([
                'success' => true,
                'status' => 3,
                'vendor_list' => $vendor_list,
                'reason' => $reason,
                'noshow_reason' => $noshow_reason,
                'reset_reason' => $reset_reason,
                
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Data For Create for Manual OTP  Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ],500);
        }
    }
   
    
	

   
}
