<?php

namespace App\Services\Admin;

use App\Helpers\CommonFunction;
use App\Helpers\MyHelper;
use Carbon\Carbon;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use App\Models\property;
use App\Models\Branch;
use App\Models\File_upload;
use App\Models\Input_data;

class FileDownloadService
{
    protected CommonFunction $commonFunction;
    protected ?Branch $currentBranch = null;

    public function __construct(CommonFunction $commonFunction)
    {
        $this->commonFunction = $commonFunction;
    }


    public function fetch_UploadFileData($request, bool $active = true): FoundationApplication|Response|ResponseFactory
    {

        try {

            $perPage = $request->input('per_page', MyHelper::$PAGINATION_PER_PAGE);
            $branch_id = Auth::user()->BRANCH_ID;
            $user_type = Auth::user()->user_type;
            $vendor_id = Auth::user()->vendor_id;

            if ($user_type == MyHelper::$ADMIN) {
                $vendorid = "";
            } else {
                $vendorid = $vendor_id;
            }

            $from_date = Carbon::parse($request->from_date)->startOfDay()->toDateTimeString();
            $to_date = Carbon::parse($request->to_date)->endOfDay()->toDateTimeString();


            $data = File_upload::query()
                ->select(
                    'file_uploads.FILE_ID',
                    'file_uploads.FILE_NAME',
                    'file_uploads.FILE_SIZE',
                    'file_uploads.FILE_TYPE',
                    'file_uploads.STATUS',
                    'file_uploads.created_at',
                    'U.name',
                    'I.TRIP_TYPE',
                    'I.ESTIMATE_START_TIME'
                )
                ->join("users as U", 'U.id', "=", "file_uploads.CREATED_BY")
                ->join("input_datas as I", 'I.FILE_ID', "=", "file_uploads.FILE_ID")

                ->whereDate("file_uploads.created_at", ">=", $from_date)
                ->whereDate("file_uploads.created_at", "<=", $to_date)
                ->whereIn("file_uploads.FILE_NAME", ['fms_pickup_rost.csv', 'AutoRoute'])
                ->where("file_uploads.BRANCH_ID", "=", $branch_id)
                ->where("I.BRANCH_ID", "=", $branch_id)
                ->groupBy('I.FILE_ID');

                

            $filterModel = $request->input('filterModel');
            if ($filterModel) {
                foreach ($filterModel as $field => $filter) {
                    if (isset($filter['filter']) && $filter['filter'] !== '') {
                        $value = $filter['filter'];
                        $type = $filter['type'];

                        switch ($field) {
                            case 'created_at':
                                $data->where('file_uploads.created_at', 'like', "%{$value}%");
                                break;
                        }
                    }
                }
            }


            if ($request->has('orderBy') && !empty($request->input('orderBy'))) {
                $orderBy = $request->input('orderBy');
                $order = $request->input('order', 'DESC');
                $data->orderBy($orderBy, $order);
            } else {
                $data->orderBy('file_uploads.updated_at', 'DESC');
            }


            if ($perPage === MyHelper::$PAGINATION_PER_PAGE_ALL) {
                $paginateddata = $data->paginate($data->count());
            } else {
                $perPage = is_numeric($perPage) ? (int)$perPage : MyHelper::$PAGINATION_PER_PAGE;
                $paginateddata = $data->paginate($perPage);
            }

            return response([
                'success' => true,
                'status' => 3,
                'file_download_list' => $paginateddata,
            ]);
        } catch (\Throwable | \Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'File DownLoad Unsuccessful' : 'File DownLoad Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ], 500);
        }
    }

   public function fetch_UploadFileDetails($request,bool $active = true): FoundationApplication|Response|ResponseFactory
    {

        try {

            $branch_id = Auth::user()->BRANCH_ID;
            $user_type = Auth::user()->user_type;
            $vendor_id = Auth::user()->vendor_id;

            if ($user_type == MyHelper::$ADMIN) {
                $vendorid = "";
            } else {
                $vendorid = $vendor_id;
            }

            $file_id = $request->file_id;
            $data = Input_data::query()
                ->select(
                    'input_datas.ROUTE_ID',
                    'input_datas.TRIP_TYPE',
                    'input_datas.ESTIMATE_START_TIME',
                    'input_datas.SITE_NAME',
                    'input_datas.LOCATION',
                    'input_datas.EMPLOYEE_ID',
                    'input_datas.EMPLOYEE_NAME',
                    'input_datas.EMPLOYEE_MOBILE',
                    'input_datas.GENDER',
                    'input_datas.PROJECT_NAME',
                    'input_datas.ADDRESS',
                    'input_datas.VENDOR_NAME',
                    'input_datas.TARIFF_TYPE',
                    DB::raw("if(input_datas.TRIP_TYPE = 'P',TIME(input_datas.ESTIMATE_TIME),'') AS ESTIMATE_TIME"),
                )               
                ->where('input_datas.BRANCH_ID', $branch_id)
                ->where('input_datas.FILE_ID', $file_id)               
                ->get();

            return response([
                'success' => true,
                'status' => 3,
                'download_inputdata' => $data
            ]);
        } catch (\Throwable | \Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'File DownLoad Unsuccessful' : 'File DownLoad Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ], 500);
        }
    }
}
