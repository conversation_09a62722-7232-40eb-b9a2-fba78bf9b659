<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TollPayment extends Model
{
    use HasFactory;

    protected $table = 'toll_payment';
    protected $primaryKey = 'PAYMENT_ID';

    public $timestamps = false;

    protected $fillable = [
        'BRANCH_ID',
        'ROSTER_ID',
        'TOLL_ID',
        'CAB_ID',
        'TOLL_CHARGE',
        'PAYMENT_DATE',
        'TICKET_NO',
        'CREATED_DATE',
        'CREATED_BY',
        'updated_at',
        'created_at',
        'remarks',
    ];
}
