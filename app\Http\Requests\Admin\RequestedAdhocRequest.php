<?php

//namespace App\Http\Requests\Admin;
namespace App\Http\Requests\Admin;

use App\Helpers\CommonFunction;
use Closure;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;

class RequestedAdhocRequest extends FormRequest
{
    protected CommonFunction $commonFunction;

    public function __construct(CommonFunction $commonFunction)
    {
        
    }

    public function authorize(): bool
    {
        return true;
    }
    
    public function rules(): array
    {   
        return [
                'trip_type' => ['required','string'],
                'start_location' => ['required','string'],
                'end_location' => ['required','string'],
                'reason' => ['required','string'],
                'other_reason' => ['nullable'],
                'intime_date' => ['required','date'],
                'tripintime' => ['required','date_format:H:i'],
            ];
    }

    public function messages(): array
    {
        return [
               'trip_type.required' => 'Trip Type field is required',
               'start_location.required' => 'Start Location field is required',
                'end_location.required' => 'End Location field is required',
                'reason.required' => 'Reason field is required',
                'intime_date.required' => 'Intime Date field is required',
                'tripintime.required' => 'Trip Intime field is required',
        ];
    }
   

    public function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json([
            'success' => false,
            'validation_error' => true,
            'message' => 'Validation errors',
            'data' => $validator->errors()
        ]));
    }
   
}
