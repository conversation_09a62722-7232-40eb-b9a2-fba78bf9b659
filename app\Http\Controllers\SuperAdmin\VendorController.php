<?php

namespace App\Http\Controllers\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Services\SuperAdmin\VendorService;
use Illuminate\Http\Request;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Response;
use App\Http\Requests\SuperAdmin\VendorDeleteRequest;
use App\Http\Requests\SuperAdmin\VendorRequest;

class VendorController extends Controller
{
    protected VendorService $vendorService;

    public function __construct(VendorService $vendorService)
    {
        $this->vendorService = $vendorService;
    }


    public function index(): FoundationApplication|Response|ResponseFactory
    {
        return $this->vendorService->indexVendor();
    }

    public function store(VendorRequest $request): ResponseFactory|Application|Response
    {
        return $this->vendorService->storeVendor($request);
    }


    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    public function edit($vendorAutoIdCrypt): Application|Response|ResponseFactory
    {
        return $this->vendorService->editVendor($vendorAutoIdCrypt);
    }

    public function update(VendorRequest $request, $vendorAutoIdCrypt): Application|Response|ResponseFactory
    {
        return $this->vendorService->updateVendor($request, $vendorAutoIdCrypt);
    }
    public function delete(VendorDeleteRequest $request, $vendorAutoIdCrypt): FoundationApplication|Response|ResponseFactory
    {
        return $this->vendorService->deleteVendor($request, $vendorAutoIdCrypt);
    }

    public function vendorlistpagination(Request $request): ResponseFactory|Application|Response
    {
        return $this->vendorService->paginationVendor($request);
    }


    public function dataForCreate(): FoundationApplication|Response|ResponseFactory
    {
        
        return $this->vendorService->dataForCreateVendor();
    }
}
