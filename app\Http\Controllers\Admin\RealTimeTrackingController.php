<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Services\Admin\RealTimeTrackingService;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class RealTimeTrackingController extends Controller
{
    protected RealTimeTrackingService $realtimetrackingService;

    public function __construct(RealTimeTrackingService $realtimetrackingService)
    {
        $this->realtimetrackingService = $realtimetrackingService;
    }

    public function Index(): Application|Response|ResponseFactory
    {
        return $this->realtimetrackingService->Index();
    }

    public function AllotRealTimeRoute(): Application|Response|ResponseFactory
    {
        return $this->realtimetrackingService->AllotRealTimeRoute();
    }
}
