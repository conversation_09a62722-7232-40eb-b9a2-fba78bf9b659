<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\NonInductRequest;
use App\Services\Admin\NonInductService;

use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class NonInductController extends Controller
{
    protected VehicleService $vehicleService;

    public function __construct(NonInductService $NonInductService)
    {
        $this->NonInductService = $NonInductService;
    }

    public function index(): ResponseFactory|Application|Response
    {
        return $this->NonInductService->indexNonInduct();
    }

   public function store(nonInductRequest $request): ResponseFactory|Application|Response
    {
        return $this->NonInductService->storeNonInduct($request);
    }
   


    public function activeNonInductPagination(Request $request): ResponseFactory|Application|Response
    {
        return $this->NonInductService->paginationNonInduct($request);
    }
	public function dataForCreate(): FoundationApplication|Response|ResponseFactory
    {
        return $this->NonInductService->dataForCreateNonInduct();
    }

}
