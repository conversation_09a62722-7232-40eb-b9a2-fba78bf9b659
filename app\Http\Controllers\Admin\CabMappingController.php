<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\CabMappingRequest;
use App\Http\Requests\Admin\CabMappingDeleteRequest;
use App\Http\Requests\Admin\CabMappingUpdateRequest;
use App\Http\Requests\Admin\SimChangeRequest;
use App\Http\Requests\Admin\DeviceChangeRequest;
use App\Services\Admin\CabMappingService;

use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class CabMappingController extends Controller
{
    protected CabMappingService $cabService;

    public function __construct(CabMappingService $cabService)
    {
        $this->cabService = $cabService;
    }

    public function index(): ResponseFactory|Application|Response
    {
        return $this->cabService->indexCab();
    }

   public function store(CabMappingRequest $request): ResponseFactory|Application|Response
    {
        return $this->cabService->storeCab($request);
    }
    public function delete(CabMappingDeleteRequest $request, $cabAutoIdCrypt): FoundationApplication|Response|ResponseFactory
    {
        return $this->cabService->deleteCab($request, $cabAutoIdCrypt);
    }
	
	 public function edit($cabAutoIdCrypt): Application|Response|ResponseFactory
    {
        return $this->cabService->editcab($cabAutoIdCrypt);
    }

    public function update(CabMappingUpdateRequest $request, $cabAutoIdCrypt): Application|Response|ResponseFactory
    {
        return $this->cabService->updateCab($request, $cabAutoIdCrypt);
    }


      public function activeCabPagination(Request $request): ResponseFactory|Application|Response
    {
        return $this->cabService->paginationcab($request);
    }
    public function simchange(SimChangeRequest $request,$cabAutoIdCrypt): ResponseFactory|Application|Response
    {
        return $this->cabService->simchange($request,$cabAutoIdCrypt);
    }
	public function devicechange(DeviceChangeRequest $request,$cabAutoIdCrypt): ResponseFactory|Application|Response
    {
        return $this->cabService->devicechange($request,$cabAutoIdCrypt);
    }
    public function dataForCreate(): FoundationApplication|Response|ResponseFactory
    {
        return $this->cabService->dataForCreateCab();
    } 


}
