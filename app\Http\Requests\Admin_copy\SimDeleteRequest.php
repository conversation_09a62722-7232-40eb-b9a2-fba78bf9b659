<?php

namespace App\Http\Requests\Admin;

use Illuminate\Contracts\Encryption\DecryptException;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Support\Facades\Crypt;

class SimDeleteRequest extends FormRequest
{

    public $simId;

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array<mixed>|string>
     */
    
     public function rules(): array
     {
         return [
             'remark' => 'required',
         ];
     }

     protected function prepareForValidation(): void
     {
         try {
             $this->simId = Crypt::decryptString($this->route('simAutoIdCrypt'));
         } catch (DecryptException $e) {
             throw new HttpResponseException(response()->json([
                 'success' => false,
                 'validation_error' => true,
                 'status' => 1,
                 'message' => 'Invalid SIM ID',
             ], 400));
         }
     }
 
 
     public function failedValidation(Validator $validator)
     {
         throw new HttpResponseException(response()->json([
             'success' => false,
             'validation_error' => true,
             'message' => 'Validation errors',
             'data' => $validator->errors()
         ]));
     }


}

