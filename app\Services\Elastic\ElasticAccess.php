<?php
    
namespace App\Services\Elastic;

use App\Services\Elastic\CommonFn;

class ElasticAccess {

    public function eindex($index, $type) {
        $itemsindex = [
            'index' => [
                '_index' => $index,
                '_type' => $type,           
            ]
        ];
        return $itemsindex;
    }


    public function matchPhrase($field, $value) {
        $matchphrase = [
            'match_phrase' => [
                $field => $value
            ]
        ];
        return $matchphrase;
    }

	
	 public function wildCard($field, $value) {
        $wildcard = [
            'wildcard' => [
                $field => $value
            ]
        ];
        return $wildcard;
    }
	
	public function Filter($field, $value) {
        $filter = [
            'terms' => [
                $field => $value
            ]
        ];
        return $filter;
    }
	
    public function singleRange($ope, $field, $value) {
        $item = [
            'range' => [
                $field => [
                    $ope => $value
                ]
            ]
        ];
        return $item;
    }

    public function doubleRange($greaterope, $greatervalue, $lesserope, $lesservalue, $field) {
        $item = [
            'range' => [
                $field => [
                    $greaterope => $greatervalue,
                    $lesserope => $lesservalue
                ]
            ]
        ];
        return $item;
    }

    public function distinctCount($name, $field) {
        $item = [
            $name => [
                'cardinality' => [
                    'field' => $field
                ]
            ]
        ];
        return $item;
    }
    
    public function groupBy($field,$sortfield,$sortorder) {
        $item = [
                    "top-uids" => [
                        "terms" => [
                            "field" => $field,
                            "size" => env('ES_SIZE_MAX')
                        ],
                        "aggs" => [
                            "top_uids_hits" => [
                                "top_hits" => [
                                    "sort" => [
                                        [
                                            $sortfield => [
                                                "order" => $sortorder
                                            ]
                                        ]
                                    ],
                                    "size" => env('ES_SIZE_ONE')
                                ]
                            ]
                        ]
                    ]
                ];
        return $item;
    }


    public function updateIndex($index, $type, $id) {
        $updateindex = [
            'update' => [
                '_index' => $index,
                '_type' => $type,
                '_id' => $id,
            ]
        ];
        return $updateindex;
    }

    public function updateRecord($reason_id, $remark) {
        $updaterecord = [
            'doc' => [
                'ACTION_REMARK' => $remark,
                'REASON_ID' => $reason_id
            ]
			];
        return $updaterecord;
    }
    
    public function cabLiveUpdateRecord($status) {
        $updaterecord = [
            'doc' => [
                'SERVICE_STATUS' => $status                
            ]
        ];
        return $updaterecord;
    }

    public function gpsLocation($latitude, $longitude, $Address) {
        $cur_datetime = CommonFn::date_time();
        $itemsLocation = [
            'POSITION' => $latitude . ',' . $longitude,
            'ADDRESS' => $Address
        ];
        return $itemsLocation;
    }

    public function routePath($route_id, $branch_id, $position, $duration,$route_date) {
        $cur_datetime = CommonFn::date_time();
        $itemsPath = [
            'ROSTER_ID' => $route_id,
            'BRANCH_ID' => $branch_id,
            'POSITION' => $position,
            'APROX_DURATION' => $duration,
            'ROUTE_DATE' => $route_date,
            'PROCESS_DATE' => $cur_datetime
        ];
        return $itemsPath;
    }

    //***************************search****************

    public function match_all() {
        $itemsGpsPath = [
            "scroll" => env('ES_SCROLL_TIME'), // how long between scroll requests. should be small!
            "size" => env('ES_SIZE_MAX'), // how many results *per shard* you want back
            'index' =>env('ES_INDEX'),
            'type' => env('ES_TYPE_SPEED_ALERT'),
            'body' => [
                'query' => [
                    'match_all' => [
                    ]
                ]
            ]
        ];
    }

    public function gpsCabPath($branch_id, $cab_id, $route_id, $from_date, $to_date) {

        $must[] = $this->matchPhrase('CAB_ID', $cab_id);
        $must[] = $this->matchPhrase('BRANCH_ID', $branch_id);
        $must[] = $this->matchPhrase('ROSTER_ID', $route_id);
        $must[] = $this->doubleRange('gte', $from_date, 'lte', $to_date, 'GPS_DATE');        

        $itemsGpsPath = [
            "scroll" => env('ES_SCROLL_TIME'), // how long between scroll requests. should be small!
            "size" =>env('ES_SIZE_MAX'), // how many results *per shard* you want back
            'index' =>env('ES_INDEX'),
            'type' => env('ES_TYPE_GPS_LOGGING'),
            'body' => [
                'query' => [
                    'bool' => [
                        'must' => $must
                    ]
                ],
                "sort" => [
                    [
                        "GPS_DATE" => [
                            "order" => "asc"
                        ]
                    ]
                ]
            ]
        ];
        return $itemsGpsPath;
    }
	
	
	public function gpsCabTripPath($branch_id, $cab_id, $route_id, $from_date, $to_date) {

        $must[] = $this->matchPhrase('CAB_ID', $cab_id);
        $must[] = $this->matchPhrase('BRANCH_ID', $branch_id);
        $must[] = $this->matchPhrase('ROSTER_ID', $route_id);
        $must[] = $this->doubleRange('gte', $from_date, 'lte', $to_date, 'GPS_DATE');        

        $itemsGpsPath = [
            "scroll" => env('ES_SCROLL_TIME'), // how long between scroll requests. should be small!
            "size" =>env('ES_SIZE_MAX'), // how many results *per shard* you want back
            'index' =>env('ES_INDEX'),
            'type' => env('ES_TYPE_GPS_LOGGING'),
            'body' => [
                'query' => [
                    'bool' => [
                        'must' => $must
                    ]
                ],
                "sort" => [
                    [
                        "GPS_DATE" => [
                            "order" => "asc"
                        ]
                    ]
                ]
            ]
        ];
        return $itemsGpsPath;
    }

    public function gpsCabPathFull($cab_no, $vendor_id, $from_date, $to_date) {
        $must[] = $this->matchPhrase('CAB_NO', $cab_no);
        $must[] = $this->matchPhrase('VENDOR_ID', $vendor_id);
        $must[] = $this->doubleRange('gte', $from_date, 'lte', $to_date, 'GPS_DATE');
        $itemsGpsPath = [
            "scroll" => env('ES_SCROLL_TIME'), // how long between scroll requests. should be small!
            "size" =>env('ES_SIZE_MAX'), // how many results *per shard* you want back
            'index' =>env('ES_INDEX'),
            'type' => env('ES_TYPE_GPS_LOGGING'),
            'body' => [
                'query' => [
                    'bool' => [
                        'must' => $must
                    ]
                ],
                "sort" => [
                    [
                        "GPS_DATE" => [
                            "order" => "asc"
                        ]
                    ]
                ]
            ]
        ];
        return $itemsGpsPath;
    }
	
	public function cabPath($cab_id, $from_date, $to_date) {

        $must[] = $this->matchPhrase('CAB_ID', $cab_id);        
        $must[] = $this->doubleRange('gte', $from_date, 'lte', $to_date, 'GPS_DATE');        

        $itemsGpsPath = [
            "scroll" => env('ES_SCROLL_TIME'), // how long between scroll requests. should be small!
            "size" =>env('ES_SIZE_MAX'), // how many results *per shard* you want back
            'index' =>env('ES_INDEX'),
            'type' => env('ES_TYPE_GPS_LOGGING'),
            'body' => [
                'query' => [
                    'bool' => [
                        'must' => $must
                    ]
                ],
                "sort" => [
                    [
                        "GPS_DATE" => [
                            "order" => "asc"
                        ]
                    ]
                ]
            ]
        ];
        return $itemsGpsPath;
    }

    public function queryCount($type, $branchId, $vendorId, $date) {
        $must[] = $this->matchPhrase('BRANCH_ID', $branchId);
        if ($vendorId != '0') {
            $must[] = $this->matchPhrase('VENDOR_ID', $vendorId);
        }
        $must[] = $this->singleRange('gte', 'PROCESS_DATE', $date);
        $distCount = $this->distinctCount('distinct_count', 'CAB_ID');       
        $itemsCount = [
            "size" => env('ES_SIZE_ZERO'),
            'index' =>env('ES_INDEX'),
            'type' => $type,
            'body' => [
                'query' => [
                    'bool' => [
                        'must' => $must
                    ]
                ],
                'aggs' => $distCount
            ]
        ];
        //print_r($itemsGpsSpeedCount);
        return $itemsCount;
    }
    
    public function queryIndividualSpeedCount($type, $branchId, $vendorId, $cab_id,$from_date,$to_date) {
        $must[] = $this->matchPhrase('BRANCH_ID', $branchId);
        if ($vendorId != '0') {
            $must[] = $this->matchPhrase('VENDOR_ID', $vendorId);
        }
        $must[] = $this->matchPhrase('CAB_ID', $cab_id);
        $must[] = $this->doubleRange('gte', $from_date, 'lte', $to_date, 'PROCESS_DATE');    
        $distCount = $this->distinctCount('distinct_count', 'PROCESS_DATE');       
        $itemsCount = [
            "size" => env('ES_SIZE_ZERO'),
            'index' =>env('ES_INDEX'),
            'type' => $type,
            'body' => [
                'query' => [
                    'bool' => [
                        'must' => $must
                    ]
                ],
                'aggs' => $distCount
            ]
        ];        
        return $itemsCount;
    }

    public function gpsCabNotLiveCount($branchId, $vendorId, $sdate, $edate) {
        $must[] = $this->matchPhrase('BRANCH_ID', $branchId);
        $must[] = $this->matchPhrase('ROUTE_STATUS', '1');
        if ($vendorId != '0') {
            $must[] = $this->matchPhrase('VENDOR_ID', $vendorId);
        }
        $must[] = $this->doubleRange('gte', $sdate, 'lte', $edate, 'GPS_DATE');
        $distCount = $this->distinctCount('cab_not_live', 'CAB_ID');
        $itemsCabNotLiveCount = [
            "size" => env('ES_SIZE_ZERO'),
            'index' =>env('ES_INDEX'),
            'type' => env('ES_TYPE_CAB_LIVE'),
            'body' => [
                'query' => [
                    'bool' => [
                        'must' => $must
                    ]
                ],
                'aggs' => $distCount
            ]
        ];
        return $itemsCabNotLiveCount;
    }
 
        
        public function cabNotLiveData($date,$edate) {            
        $must[] = $this->doubleRange('gte', $date, 'lte', $edate, 'GPS_DATE');
        $must[] = $this->matchPhrase('LOGIN_STATUS', env('ES_LOGIN'));
        $must[] = $this->matchPhrase('ROUTE_STATUS', env('ES_ONTRIP'));
        $must[] = $this->matchPhrase('SERVICE_STATUS',env('ES_NOT_SENT'));             
            $itemsCabNotLiveData = [
                'size' => env('ES_SIZE_MAX'),
                'index' =>env('ES_INDEX'),
                'type' => env('ES_TYPE_CAB_LIVE'),
                'body' => [
                    'query' => [
                        'bool' => [
                            'must' => $must
                        ]
                    ]
                ]
            ];     

        return $itemsCabNotLiveData;
    }
    
     
    
    public function cabLiveData($branchId, $vendorId) {
		$cdate=date("Y-m-d");
        $must[] = $this->matchPhrase('BRANCH_ID', $branchId);
		$must[] = $this->matchPhrase('ACTIVE_STATUS', '1');
        $must[] = $this->matchPhrase('PROCESS_DATE', $cdate);
        if ($vendorId != '0') {
            $must[] = $this->matchPhrase('VENDOR_ID', $vendorId);
        }           
            $itemsCabLiveData = [
                'size' => env('ES_SIZE_MAX'),
                'index' =>env('ES_INDEX'),
                'type' => env('ES_TYPE_CAB_LIVE'),
                'body' => [
                    'query' => [
                        'bool' => [
                            'must' => $must
                        ]
                    ]
                ]
            ];     
        return $itemsCabLiveData;
    }
	
	public function cabOnDuty($branchId, $vendorId,$sdate) {
        $must[] = $this->matchPhrase('BRANCH_ID', $branchId);
	    $must[] = $this->matchPhrase('ACTIVE_STATUS', '1');   
        $must[] = $this->matchPhrase('ROUTE_STATUS', '1'); 
		$must[] = $this->singleRange('gte', env('ES_FIELD_GPS_DATE'), $sdate);
        if ($vendorId != '0') {
            $must[] = $this->matchPhrase('VENDOR_ID', $vendorId);
        }           
            $itemsCabLiveData = [
                'size' => env('ES_SIZE_MAX'),
                'index' =>env('ES_INDEX'),
                'type' => env('ES_TYPE_CAB_LIVE'),
                'body' => [
                    'query' => [
                        'bool' => [
                            'must' => $must
                        ]
                    ]
                ]
            ];     
        return $itemsCabLiveData;
    }
	
	
    public function singleCabLiveData($branchId, $vendorId,$cabId) {
        $must[] = $this->matchPhrase('BRANCH_ID', $branchId);   
		$must[] = $this->matchPhrase('CAB_ID', $cabId);
		$must[] = $this->matchPhrase('ACTIVE_STATUS', '1');
       // $must[] = $this->matchPhrase('PROCESS_DATE', $cdate);
        if ($vendorId != '0') {
            $must[] = $this->matchPhrase('VENDOR_ID', $vendorId);
        }           
            $itemsCabLiveData = [
                'size' => env('ES_SIZE_MAX'),
                'index' =>env('ES_INDEX'),
                'type' => env('ES_TYPE_CAB_LIVE'),
                'body' => [
                    'query' => [
                        'bool' => [
                            'must' => $must
                        ]
                    ]
                ]
            ];     
        return $itemsCabLiveData;
    }
	
	
	public function multipleCabLiveData($branchId, $vendorId,$cabId) {
        $must[] = $this->matchPhrase('BRANCH_ID', $branchId);   
		$filter = $this->Filter('CAB_ID', $cabId);
		$must[] = $this->matchPhrase('ACTIVE_STATUS', '1');
       // $must[] = $this->matchPhrase('PROCESS_DATE', $cdate);
        if ($vendorId != '0') {
            $must[] = $this->matchPhrase('VENDOR_ID', $vendorId);
        }           
            $itemsCabLiveData = [
                'size' => env('ES_SIZE_MAX'),
                'index' =>env('ES_INDEX'),
                'type' => env('ES_TYPE_CAB_LIVE'),
                'body' => [
                    'query' => [
                        'bool' => [
                            'must' => $must,
							'filter'=>$filter
                        ]
                    ]
                ]
            ];     
        return $itemsCabLiveData;
    }
	
	
    public function gpsCabNavigation($cabid, $gps_date) {
         $must[] = $this->matchPhrase(env('ES_FIELD_CAB_ID'), $cabid);
        if ($gps_date == env('ES_DEFAULT_DATE')) {
            $size=env('ES_SIZE_SINGLE');
        }else{
            $size=env('ES_SIZE_THIRTY');            
            $must[] = $this->singleRange('gte', env('ES_FIELD_GPS_DATE'), $gps_date);
        }       
            $itemsGpsSearch = [
                "size" => $size,
                'index' => env('ES_INDEX'),
                'type' => env('ES_TYPE_GPS_LOGGING'),
                'body' => [
                   // '_source'=> [env('ES_FIELD_POSITION'),env('ES_FIELD_GPS_DATE')],
                    'query' => [
                        'bool' => [
                            'must' => $must
                        ]
                    ],
                    "sort" => [
                        [
                            "GPS_DATE" => [
                                "order" => "desc"
                            ]
                        ]
                    ]
                ]
            ];
        
        return $itemsGpsSearch;
    }
	
	public function gpsCabNavigationPath($cabid, $gps_date) {
         $must[] = $this->matchPhrase(env('ES_FIELD_CAB_ID'), $cabid);
        if ($gps_date == env('ES_DEFAULT_DATE')) {
            $size=env('ES_SIZE_SINGLE');
        }else{
            $size=env('ES_SIZE_MAX');            
            $must[] = $this->singleRange('gte', env('ES_FIELD_GPS_DATE'), $gps_date);
        }       
            $itemsGpsSearch = [
                "size" => $size,
                'index' => env('ES_INDEX'),
                'type' => env('ES_TYPE_GPS_LOGGING'),
                'body' => [
                   // '_source'=> [env('ES_FIELD_POSITION'),env('ES_FIELD_GPS_DATE')],
                    'query' => [
                        'bool' => [
                            'must' => $must
                        ]
                    ],
                    "sort" => [
                        [
                            "GPS_DATE" => [
                                "order" => "desc"
                            ]
                        ]
                    ]
                ]
            ];
        
        return $itemsGpsSearch;
    }
    
     public function cabStatusData($branchId, $vendorId ,$cabid) {
        $must[] = $this->matchPhrase('BRANCH_ID', $branchId);
		$must[] = $this->matchPhrase('ACTIVE_STATUS', '1');
        if ($vendorId != '0') {
            $must[] = $this->matchPhrase('VENDOR_ID', $vendorId);
        }
         if ($cabid != '0') {
            $must[] = $this->matchPhrase('CAB_ID', $cabid);
        } 
            $itemsCabLiveData = [
                'size' => env('ES_SIZE_MAX'),
                'index' =>env('ES_INDEX'),
                'type' => env('ES_TYPE_CAB_LIVE'),
                'body' => [
                    'query' => [
                        'bool' => [
                            'must' => $must
                        ]
                    ]
                ]
            ];     
        return $itemsCabLiveData;
    }
    
    public function cabLiveStatusData($branchId, $vendorId,$cabid,$field,$status) {
        $must[] = $this->matchPhrase('BRANCH_ID', $branchId);       
		$must[] = $this->matchPhrase('ACTIVE_STATUS', '1');
        $must[] = $this->matchPhrase($field, $status);
        if ($vendorId != '0') {
            $must[] = $this->matchPhrase('VENDOR_ID', $vendorId);
        }  
        if ($cabid != '0') {
            $must[] = $this->matchPhrase('CAB_ID', $cabid);
        } 
            $itemsCabLiveData = [
                'size' => env('ES_SIZE_MAX'),
                'index' =>env('ES_INDEX'),
                'type' => env('ES_TYPE_CAB_LIVE'),
                'body' => [
                    'query' => [
                        'bool' => [
                            'must' => $must
                        ]
                    ]
                ]
            ];     
        return $itemsCabLiveData;
    }
	
	public function cabOnDutyData($branchId, $vendorId,$cabid,$field,$status,$stime) {
        $must[] = $this->matchPhrase('BRANCH_ID', $branchId);  
		$must[] = $this->matchPhrase('ACTIVE_STATUS', '1');		
        $must[] = $this->matchPhrase($field, $status);
		$must[] = $this->singleRange('gte', env('ES_FIELD_GPS_DATE'), $stime);
        if ($vendorId != '0') {
            $must[] = $this->matchPhrase('VENDOR_ID', $vendorId);
        }  
        if ($cabid != '0') {
            $must[] = $this->matchPhrase('CAB_ID', $cabid);
        } 
            $itemsOnDutyData = [
                'size' => env('ES_SIZE_MAX'),
                'index' =>env('ES_INDEX'),
                'type' => env('ES_TYPE_CAB_LIVE'),
                'body' => [
                    'query' => [
                        'bool' => [
                            'must' => $must
                        ]
                    ]
                ]
            ];     
        return $itemsOnDutyData;
    }
	
	public function cabOverSpeedData($branchId, $vendorId,$cabid,$field,$status,$speed,$sdate) {
        $must[] = $this->matchPhrase('BRANCH_ID', $branchId);  
       // $must[] = $this->matchPhrase('SPEED', $branchId); 
        $must[] = $this->singleRange('gte','SPEED',$speed);	
		$must[] = $this->singleRange('gte','GPS_DATE',$sdate);
		$must[] = $this->matchPhrase('ACTIVE_STATUS', '1');
        $must[] = $this->matchPhrase($field, $status);
        if ($vendorId != '0') {
            $must[] = $this->matchPhrase('VENDOR_ID', $vendorId);
        }  
        if ($cabid != '0') {
            $must[] = $this->matchPhrase('CAB_ID', $cabid);
        } 
            $itemsCabLiveData = [
                'size' => env('ES_SIZE_MAX'),
                'index' =>env('ES_INDEX'),
                'type' => env('ES_TYPE_CAB_LIVE'),
                'body' => [
                    'query' => [
                        'bool' => [
                            'must' => $must
                        ]
                    ]
                ]
            ];     
        return $itemsCabLiveData;
    }
	public function cabIdleStatusData($branchId, $vendorId,$cabid,$field,$status,$from_date,$to_date) {
        $must[] = $this->matchPhrase('BRANCH_ID', $branchId);       
		$must[] = $this->matchPhrase('ACTIVE_STATUS', '1');
        $must[] = $this->matchPhrase($field, $status);
        if ($vendorId != '0') {
            $must[] = $this->matchPhrase('VENDOR_ID', $vendorId);
        }  
        if ($cabid != '0') {
            $must[] = $this->matchPhrase('CAB_ID', $cabid);
        } 
        if($from_date!='0'){
            $must[] = $this->doubleRange('gt', $from_date, 'lt', $to_date, 'GPS_DATE');
        }else{
            $must[] = $this->singleRange('lt', $to_date, 'GPS_DATE');
        }
        
            $itemsCabIdleData = [
                'size' => env('ES_SIZE_MAX'),
                'index' =>env('ES_INDEX'),
                'type' => env('ES_TYPE_CAB_LIVE'),
                'body' => [
                    'query' => [
                        'bool' => [
                            'must' => $must
                        ]
                    ]
                ]
            ];     
        return $itemsCabIdleData;
    }
    
    public function queryRemarkCount($type, $branchId, $vendorId, $date) {
        $must[] = $this->matchPhrase('BRANCH_ID', $branchId);
        $must[] = $this->matchPhrase('ACTION_REMARK', '--');
        if ($vendorId != '0') {
            $must[] = $this->matchPhrase('VENDOR_ID', $vendorId);
        }
        $must[] = $this->singleRange('gte', 'PROCESS_DATE', $date);
        $distCount = $this->distinctCount('distinct_remark', 'CAB_ID');
        $itemsRemarkSpeedCount = [
            'size' => env('ES_SIZE_ZERO'),'index' =>env('ES_INDEX'),'type' => $type,
            'body' => [
                'query' => [
                    'bool' => [
                        'must' => $must
                    ]
                ],
                'aggs' => $distCount
            ]
        ];      

        return $itemsRemarkSpeedCount;
    }

    public function queryCountGroup($type, $branchId, $vendorId,$date,$edate) {
        $must[] = $this->matchPhrase('BRANCH_ID', $branchId);
        if ($vendorId != '0') {
            $must[] = $this->matchPhrase('VENDOR_ID', $vendorId);
        }
        $must[] = $this->doubleRange('gte', $date, 'lte', $edate, 'PROCESS_DATE');
        $groupby = $this->groupBy('CAB_ID', 'PROCESS_DATE', 'desc');
        $itemsCountGroup = [
            "size" => env('ES_SIZE_ZERO'), 'index' =>env('ES_INDEX'), 'type' => $type,
            'body' => [
                'query' => [
                    'bool' => [
                        'must' => $must
                    ]
                ],
                "aggs" => $groupby
            ]
        ];
        return $itemsCountGroup;
    }

    public function queryGroup($type, $branchId, $cabId, $date) {
        $must[] = $this->matchPhrase('BRANCH_ID', $branchId);
        $must[] = $this->matchPhrase('CAB_ID', $cabId);
        $must[] = $this->singleRange('gte', 'PROCESS_DATE', $date);
        $itemsGroup = [
            "size" => env('ES_SIZE_MAX'), 'index' =>env('ES_INDEX'), 'type' => $type,
            'body' => [
                'query' => [
                    'bool' => [
                        'must' => $must
                    ]
                ],
                "sort" => [
                    [
                        "GPS_DATE" => [
                            "order" => "desc"
                        ]
                    ]
                ]
            ]
        ];
        return $itemsGroup;
    }
	public function queryGroup_OverSpeed_Report($type, $branchId, $from_date,$to_date) {
        $must[] = $this->matchPhrase('BRANCH_ID', $branchId);
        $must[] = $this->doubleRange('gte', $from_date, 'lte', $to_date, 'PROCESS_DATE');
        $itemsGroup = [
            "size" => env('ES_SIZE_MAX'), 'index' =>env('ES_INDEX'), 'type' => $type,
            'body' => [
                'query' => [
                    'bool' => [
                        'must' => $must
                    ]
                ],
                "sort" => [
                    [
                        "PROCESS_DATE" => [
                            "order" => "desc"
                        ]
                    ]
                ]
            ]
        ];
        return $itemsGroup;
    }
	
	public function queryMaxSpeed($branchId,$cab_id,$from_date,$to_date) {
        $must[] = $this->matchPhrase('BRANCH_ID', $branchId);        
        $must[] = $this->matchPhrase('CAB_ID', $cab_id);
        $must[] = $this->doubleRange('gte', $from_date, 'lte', $to_date, 'GPS_DATE');               
        $maxSpeed = [
            "size" => env('ES_SIZE_ZERO'),
            'index' =>env('ES_INDEX'),
            'type' => env('ES_TYPE_GPS_LOGGING'),
            'body' => [
                'query' => [
                    'bool' => [
                        'must' => $must
                    ]
                ],                
                "aggs" => [
                    'max_speed' => [
                        'max' => ['field'=>"SPEED"]
                    ]
                ]
            ]
        ];        
        return $maxSpeed;
    }


    public function queryUpdateGroup($type, $branchId, $cabId, $date) {
        $must[] = $this->matchPhrase('BRANCH_ID', $branchId);
        $must[] = $this->matchPhrase('CAB_ID', $cabId);
        $must[] = $this->matchPhrase('ACTION_REMARK', '--');
        $must[] = $this->singleRange('gte', 'PROCESS_DATE', $date);
        $itemsUpdateGroup = [
            "size" => env('ES_SIZE_MAX'),'index' =>env('ES_INDEX'),'type' => $type,
            'body' => [
                'query' => [
                    'bool' => [
                        'must' => $must
                    ]
                ],
                "sort" => [
                    [
                        "GPS_DATE" => [
                            "order" => "desc"
                        ]
                    ]
                ]
            ]
        ];
        return $itemsUpdateGroup;
    }

    public function gpsSpeedUpdate($index, $type, $id, $field, $value) {
        $paramsupdate = [
            'index' => $index,'type' => $type,'id' => $id,
            'body' => [
                'doc' => [
                    $field => $value,
                    'ROSTER_ID' => $value
                ]
            ]
        ];

        // print_r($paramsupdate);
        return $paramsupdate;
    }  
    
    public function actionRadius($rosterId,$branchId,$passengerId) {
        $must[] = $this->matchPhrase('ROSTER_ID', $rosterId);
        $must[] = $this->matchPhrase('BRANCH_ID', $branchId);
        $must[] = $this->wildCard('EMP_ID', "*".$passengerId."*");
        $itemsEmpDistanceData = [
            'size' => env('ES_SIZE_ONE'),
            '_source' => ['DISTANCE','CURRENT_POSITION','COM_EMP_POSITION','ROSTER_ID'],
            'index' => env('ES_INDEX'),
            'type' => env('ES_TYPE_DISTANCE_ALERT'),
            'body' => [
                'query' => [
                    'bool' => [
                        'must' => $must
                    ]
                ],
                "sort" => [
                    [
                        "PROCESS_DATE" => [
                            "order" => "desc"
                        ]
                    ]
                ]
            ]
        ];
        return $itemsEmpDistanceData;
    }
	public function distanceAlert($branchId,$vendorId,$date,$edate) {       
        $must[] = $this->matchPhrase('BRANCH_ID', $branchId);
		if ($vendorId != '0') {
            $must[] = $this->matchPhrase('VENDOR_ID', $vendorId);
        }
		$must[] = $this->doubleRange('gte', $date, 'lte', $edate, 'PROCESS_DATE');
        $itemsEmpDistanceData = [
            'size' => env('ES_SIZE_MAX'),
            '_source' => ['DISTANCE','CURRENT_POSITION','COM_EMP_POSITION','CAB_ID','EMP_ID','ACTION_REMARK','PROCESS_DATE'],
            'index' => env('ES_INDEX'),
            'type' => env('ES_TYPE_DISTANCE_ALERT'),
            'body' => [
                'query' => [
                    'bool' => [
                        'must' => $must
                    ]
                ],
                "sort" => [
                    [
                        "PROCESS_DATE" => [
                            "order" => "desc"
                        ]
                    ]
                ]
            ]
        ];
        return $itemsEmpDistanceData;
    } 
    
    public function driverMsg($roster_id,$branch_id, $vendor_id,$cab_id, $action,$msg,$status,$speed_interval,
            $request_time,$acknowledge_time) {
        $cur_datetime = $this->date_time();
        $itemsMsg = [
            'ROUTE_ID' => $roster_id,
            'BRANCH_ID' => $branch_id, 
            'VENDOR_ID' => $vendor_id,
            'CAB_ID' => $cab_id,            
            'ACTION' => $action,
            'MESSAGE' => $msg,
            'STATUS' => $status,
            'SPEED_INTERVAL' => $speed_interval,  
            'REQUEST_TIME' => $request_time, 
            'ACKNOWLEDGE_TIME' => $acknowledge_time,           
            'PROCESS_DATE' => $cur_datetime,          
        ];
        return $itemsMgs;
    }
    

    public function queryStopId($id) {
        $must[] = $this->matchPhrase('STOP_STATUS', '1');        
        $must[] = $this->matchPhrase('ROUTE_ID', $id);        
        $groupby = $this->groupBy('STOP_ID','PROCESS_DATE', 'asc');
        $itemsCountGroup = [
            "size" => env('ES_SIZE_ZERO'), 'index' =>env('ES_INDEX'), 'type' => 'shuttle_route_path',
            'body' => [
                'query' => [
                    'bool' => [
                        'must' => $must
                    ]
                ],
                "aggs" => $groupby
            ]
        ];
        return $itemsCountGroup;
    }
    
    
      public function queryGpsDistance($cab_id,$from_date_time,$to_date_time) {
        $must[] = $this->matchPhrase('CAB_ID',$cab_id);        
        $must[] = $this->doubleRange('gte', $from_date_time, 'lte', $to_date_time, 'GPS_DATE');      
        $itemsDistance = [
            "size" => env('ES_SIZE_ZERO'), 'index' =>env('ES_INDEX'), 'type' => env('ES_TYPE_GPS_LOGGING'),
            'body' => [
                'query' => [
                    'bool' => [
                        'must' => $must
                    ]
                ],
                "aggs" => [
                    'return' => [
                        'sum' => ['field'=>"KILOMETER"]
                    ]
                ]
            ]
        ];
        return $itemsDistance;
    }
    
     public function getGoogleAddress($lat, $lng) {
       return $itemsAddressSearch = [
            "size" => 1,
            'index' => env('ES_INDEX'),
            'type' =>  env('ES_TYPE_GOOGLE_ADDR'),
            'body' => [
                '_source' =>env('ES_FIELD_ADDRESS'),
                'query' => [
                    'bool' => [
                        "filter" => [
                            "geo_distance" => [
                                "distance" => env('ES_DEFAULT_KM'),
                                env('ES_FIELD_POSITION') => [
                                    "lat" => $lat,
                                    "lon" => $lng
                                ]
                            ]
                        ]

                    ]
                ],
                "sort" => [
                    [
                        "_geo_distance" => [
                            env('ES_FIELD_POSITION') => [
                                "lat" => $lat,
                                "lon" => $lng
                            ],
                            "order" => "asc",
                            "unit" => "km",
                            "distance_type" => "plane"
                        ]
                    ]]
            ]
        ];       
    }
	public function close_onduty($status) {
        $updaterecord = [
            'doc' => [
                'ROUTE_STATUS' => $status, 
	        ]
        ];
        return $updaterecord;
    }	
	public function cabUpdateLoginStatus($status) {
        $updaterecord = [
            'doc' => [
                'LOGIN_STATUS' => $status,                
                'ROUTE_STATUS' => $status,                
            ]
        ];
        return $updaterecord;
    }	
	public function cabMappingDeactiveStatus($status) {
        $updaterecord = [
            'doc' => [
                'ACTIVE_STATUS' => $status                
            ]
        ];
        return $updaterecord;
    }	
	public function getCabLiveId($cab_id) {       
        $must[] = $this->matchPhrase(env('ES_FIELD_CAB_ID'), $cab_id);        
        $must[] = $this->matchPhrase(env('ES_FIELD_ACTIVE_STATUS'), '1');        
        $itemsCabSearch = [
            "size" => env('ES_SIZE_SINGLE'),
            'index' => env('ES_INDEX'),
            'type' => env('ES_TYPE_CAB_LIVE'),
            'body' => [
                'query' => [
                    'bool' => [
                        'must' => $must
                    ]
                ]
            ]
        ];
        return $itemsCabSearch;
    }
	public function cabGpsAlert($branchId, $vendorId,$cabId, $rosterId,$date,$edate) 
{
        $must[] = $this->matchPhrase('BRANCH_ID', $branchId);
		$must[] = $this->matchPhrase('CAB_ID', $cabId); 
	    $must[] = $this->matchPhrase('ROSTER_ID', $rosterId);  		
        if ($vendorId != '0') {
            $must[] = $this->matchPhrase('VENDOR_ID', $vendorId);
        }   
        $must[] = $this->doubleRange('gte', $date, 'lte', $edate, 'PROCESS_DATE');        
            $itemsCabLiveData = [
                'size' => env('ES_SIZE_MAX'),
                'index' =>env('ES_INDEX'),
                'type' => env('ES_TYPE_GPS_ALERT'),
                'body' => [
                    'query' => [
                        'bool' => [
                            'must' => $must
                        ]
                    ],
			    "sort" => [
                    [
                        "PROCESS_DATE" => [
                            "order" => "asc"
                        ]
                    ]
                ]
                ]
            ];     
        return $itemsCabLiveData;
    }
	
	 public function cabGpsAlertWithPagination($branchId, $vendorId, $cabId, $rosterId, $date, $edate, $page , $per_page, $orderBy , $order , $filterModel)
    {
        $must[] = $this->matchPhrase('BRANCH_ID', $branchId);
        $must[] = $this->matchPhrase('CAB_ID', $cabId);
        $must[] = $this->matchPhrase('ROSTER_ID', $rosterId);

        if ($vendorId != '0') {
            $must[] = $this->matchPhrase('VENDOR_ID', $vendorId);
        }

        $must[] = $this->doubleRange('gte', $date, 'lte', $edate, 'PROCESS_DATE');


        if (!empty($filterModel) && is_array($filterModel)) {
            foreach ($filterModel as $field => $filter) {
                if (isset($filter['filter']) && $filter['filter'] !== '') {
                    $value = $filter['filter'];
                    $type = $filter['type'];

                    switch ($field) {
                        case 'ALERT_STATUS':
                            $must[] = $this->matchPhrase($field, $value);
                            break;
                    }
                }
            }
        }

        $from = ($page - 1) * $per_page;

        return [
            'size' => $per_page,
            'from' => $from,
            'index' => env('ES_INDEX'),
            'type' => env('ES_TYPE_GPS_ALERT'),
            'body' => [
                'query' => [
                    'bool' => [
                        'must' => $must
                    ]
                ],
                'sort' => [
                    [
                        "PROCESS_DATE" => [
                            "order" => "asc"
                        ]
                    ]
                ]
            ]
        ];
    }
	
	public function cabGpsProcessdatePast($branchId,$vendorId,$cabId,$date) 
{
        $must[] = $this->matchPhrase('BRANCH_ID', $branchId);
		$must[] = $this->matchPhrase('CAB_ID', $cabId);        
	      
        if ($vendorId != '0') {
            $must[] = $this->matchPhrase('VENDOR_ID', $vendorId);
        }   
        $must[] = $this->singleRange('lt','PROCESS_DATE', $date );        
            $itemsCabProcessData = [
                'size' => env('ES_SIZE_SINGLE'),
                'index' =>env('ES_INDEX'),
                'type' => env('ES_TYPE_GPS_LOGGING'),
                'body' => [
                    'query' => [
                        'bool' => [
                            'must' => $must
                        ]
                    ],
                "sort" => [
                    [
                        "PROCESS_DATE" => [
                            "order" => "desc"
                        ]
                    ]
                ]
              ]
            ];     
        return $itemsCabProcessData;
    }
	public function cabGpsProcessdateFuture($branchId,$vendorId,$cabId,$date) 
{
        $must[] = $this->matchPhrase('BRANCH_ID', $branchId);
		$must[] = $this->matchPhrase('CAB_ID', $cabId);        
        if ($vendorId != '0') {
            $must[] = $this->matchPhrase('VENDOR_ID', $vendorId);
        }   
        $must[] = $this->singleRange('gt','PROCESS_DATE', $date );        
            $itemsCabProcessData = [
                'size' => env('ES_SIZE_SINGLE'),
                'index' =>env('ES_INDEX'),
                'type' => env('ES_TYPE_GPS_LOGGING'),
                'body' => [
                    'query' => [
                        'bool' => [
                            'must' => $must
                        ]
                    ],
                "sort" => [
                    [
                        "PROCESS_DATE" => [
                            "order" => "asc"
                        ]
                    ]
                ]
              ]
            ];     
        return $itemsCabProcessData;
    }


	public function getTollCrossedPath($branch_id, $cab_id, $route_id, $from_date, $to_date,$latitude,$longitude) {

        $must[] = $this->matchPhrase('CAB_ID', $cab_id);
        $must[] = $this->matchPhrase('BRANCH_ID', $branch_id);
        $must[] = $this->matchPhrase('ROSTER_ID', $route_id);
        $must[] = $this->doubleRange('gte', $from_date, 'lte', $to_date, 'PROCESS_DATE');        

        $itemsGpsPath = [
            "scroll" => env('ES_SCROLL_TIME'), // how long between scroll requests. should be small!
            "size" =>env('ES_SIZE_MAX'), // how many results *per shard* you want back
            'index' =>env('ES_INDEX'),
            'type' => env('ES_TYPE_GPS_LOGGING'),
            'body' => [
                'query' => [
                    'bool' => [
                        'must' => $must,
                        'filter' => [
                            'geo_distance' => [
                                'distance' => '100m',
                                'POSITION' => implode(',', [$latitude, $longitude]),
                            ],
                        ],
                    ]
                ],
                "sort" => [
                    [
                        "GPS_DATE" => [
                            "order" => "asc"
                        ]
                    ]
                ]
            ]
        ];
        return $itemsGpsPath;
    }
	
}
