<?php

namespace App\Http\Requests\auth;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Contracts\Validation\Validator;

class EmployeeOtpVerificationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'regex:/^[0-9]{10}$/','exists:users,name'],
            'otp' => ['required','digits:4'],
            'password' => ['required','min:8'],
            'confirmed_password' => ['required','same:password'],
        ];
    }

    public function messages(): array
    {
        return [
            'name.required' => 'The mobile number is required.',
            'name.regex' => 'The mobile number must be exactly 10 digits.',
            'name.exists' => 'The mobile number does not exist.',
            'otp.required' => 'The OTP is required.',
            'otp.digits' => 'The OTP must be exactly 4 digits.',
            'password.required' => 'The password is required.',
            'password.digits' => 'The password must be minimum 8 digits.',
            'confirmed_password.required' => 'The confirmed password is required.',
            'confirmed_password.same' => 'The password and confirmed password do not match.',
        ];
    }

    public function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json([
            'success' => false,
            'validation_error' => true,
            'status'=> 0,
            'message' => 'Validation errors',
            'data' => $validator->errors()
        ]));
    }
}
