<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;

use App\Services\Admin\VehicleTrackingGPSService;
use App\Http\Requests\Admin\GetRosterTimeRequest;
use App\Http\Requests\Admin\VehicleTrackingGPSVehicleStatusRequest;
use App\Http\Requests\Admin\PostVehicletrackingRequest;
use App\Http\Requests\Admin\TrackingRosterDetailsRequest;

use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class VehicleTrackingGPSController extends Controller
{
    protected VehicleTrackingGPSService $vehicletrackinggpsService;

    public function __construct(VehicleTrackingGPSService $vehicletrackinggpsService)
    {
        $this->vehicletrackinggpsService = $vehicletrackinggpsService;
    }
    
    public function dataForCreate(): FoundationApplication|Response|ResponseFactory
    {
        return $this->vehicletrackinggpsService->dataForCreate();
    }
   
    public function get_roster_shifttime(GetRosterTimeRequest $request): FoundationApplication|Response|ResponseFactory
    {
        
        return $this->vehicletrackinggpsService->get_roster_shifttime($request);
        
    }
   
    public function vehicle_status(VehicleTrackingGPSVehicleStatusRequest $request): FoundationApplication|Response|ResponseFactory
    {

        return $this->vehicletrackinggpsService->vehicle_status($request);
        
    }

    public function postvehicletracking(PostVehicletrackingRequest $request): FoundationApplication|Response|ResponseFactory
    {

        return $this->vehicletrackinggpsService->postvehicletracking($request);
        
    }
   
    public function tracking_roster_details(TrackingRosterDetailsRequest $request): FoundationApplication|Response|ResponseFactory
    {

        return $this->vehicletrackinggpsService->tracking_roster_details($request);
        
    }
   
   
 
}
