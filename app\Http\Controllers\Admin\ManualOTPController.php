<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;

use App\Services\Admin\ManualOTPService;
use App\Http\Requests\Admin\ManualOTPRequest;
use App\Http\Requests\Admin\VendorwiseVehicleShowRequest;
use App\Http\Requests\Admin\SelectCabWiseRouteRequest;
use App\Http\Requests\Admin\SelectedRouteDetailsRequest;
use App\Http\Requests\Admin\ArriveboardedRequest;
use App\Http\Requests\Admin\Manual_OTPRequest;
use App\Http\Requests\Admin\NoshowResetRequest;

use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class ManualOTPController extends Controller
{
    protected ManualOTPService $manualotpService;

    public function __construct(ManualOTPService $manualotpService)
    {
        $this->manualotpService = $manualotpService;
    }
    public function index(): ResponseFactory|Application|Response
    {
        return $this->manualotpService->indexTripclose();
    }
    
    public function dataForCreateManualOTP(): FoundationApplication|Response|ResponseFactory
    {
        return $this->manualotpService->dataForCreateManualOTP();
    }
    
    public function vendorwise_vehicle_list(VendorwiseVehicleShowRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->manualotpService->vendorwise_vehicle_list($request);
    }
 
    public function select_cab_wise_route(SelectCabWiseRouteRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->manualotpService->select_cab_wise_route($request);
    }
 
    public function selected_route_details(SelectedRouteDetailsRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->manualotpService->selected_route_details($request);
    }
    public function arriveboarded(ArriveboardedRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->manualotpService->arriveboarded($request);
    }
    public function manual_otp(Manual_OTPRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->manualotpService->manual_otp($request);
    }
    public function noshow_reset(NoshowResetRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->manualotpService->noshow_reset($request);
    }
 
}
