<?php
namespace App\Http\Requests\Admin;

use App\Helpers\CommonFunction;
use Closure;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;

class DriverRequest extends FormRequest
{
    protected CommonFunction $commonFunction;

    public function __construct(CommonFunction $commonFunction)
    {
        
    }

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        
        return [
            'DRIVERS_NAME' => 'required',
            'DRIVER_MOBILE' => [
                'required',
                'numeric',
                 Rule::unique('drivers', 'DRIVER_MOBILE')
            ],
            'DRIVERS_ADRESS' => 'required',
            'DRIVERS_ADDR_LAT' => 'required',
            'DRIVERS_ADDR_LONG' => 'required',
            'DRIVER_LICENSE' => 'required',
            'LICENCE_EXPIRY' => 'required',
            'BADGE_EXPIRY' => 'required',
            'MEDICAL_STATUS' => 'required',
            'LOCATION_NAME' => 'required',
            'SHIFT_IN_TIME' => 'required',
            'SHIFT_OUT_TIME' => 'required',
           
        ];
    }
   

    public function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json([
            'success' => false,
            'validation_error' => true,
            'message' => 'Validation errors',
            'data' => $validator->errors()
        ]));
    }

    private function checkDeactivatedDriver($branchId): Closure
    {
        return function (string $attribute, mixed $value, Closure $fail) use ($branchId) {
            $deactivatedSim = DB::table("drivers")
                ->where("DRIVERS_ID", $value)
                ->where("ACTIVE", 2)
                ->exists();

            if ($deactivatedSim) {
                $fail("Driver already exists and is deactivated.");
            }
        };
    }

   
}
