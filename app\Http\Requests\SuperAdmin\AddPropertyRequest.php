<?php

namespace App\Http\Requests\SuperAdmin;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Contracts\Validation\Validator;

class AddPropertyRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'propertie_name' => 'required',
            'propertie_value' => 'required',
        ];
    }

    public function messages()
    {
        return [
            'propertie_name.required' => 'Propertie Name is required',
            'propertie_value.required' => 'Propertie Value is required',
        ];
    }

    public function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json([
           'success' => false,
            'validation_error' => true,
            'status' => 0,
            'data' => $validator->errors()
        ]));
    }
}
