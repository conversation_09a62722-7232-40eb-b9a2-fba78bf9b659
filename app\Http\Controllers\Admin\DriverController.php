<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\DriverRequest;
use App\Http\Requests\Admin\DriverDeleteRequest;
use App\Http\Requests\Admin\DriverUpdateRequest;
use App\Services\Admin\DriverService;

use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class DriverController extends Controller
{
    protected DriverService $driverService;

    public function __construct(DriverService $driverService)
    {
        $this->driverService = $driverService;
    }

    public function index(): ResponseFactory|Application|Response
    {
        return $this->driverService->indexDriver();
    }

   public function store(driverRequest $request): ResponseFactory|Application|Response
    {
        return $this->driverService->storeDriver($request);
    }
    public function delete(DriverDeleteRequest $request, $driverAutoIdCrypt): FoundationApplication|Response|ResponseFactory
    {
        return $this->driverService->deleteDriver($request, $driverAutoIdCrypt);
    }
	
	 public function edit($driverAutoIdCrypt): Application|Response|ResponseFactory
    {
        return $this->driverService->editdriver($driverAutoIdCrypt);
    }

    public function update(DriverUpdateRequest $request, $driverAutoIdCrypt): Application|Response|ResponseFactory
    {
        return $this->driverService->updateDriver($request, $driverAutoIdCrypt);
    }


      public function activeDriverPagination(Request $request): ResponseFactory|Application|Response
    {
        return $this->driverService->paginationDriver($request);
    }
    public function dataForCreate(): FoundationApplication|Response|ResponseFactory
    {
        return $this->driverService->dataForCreateDriver();
    } 

}
