<?php

namespace App\Http\Requests\Admin;

use App\Helpers\CommonFunction;
use Closure;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;
use Illuminate\Contracts\Validation\Validator;

class LocationUpdateRequest extends FormRequest
{
    private CommonFunction $commonFunction;
    private ?object $location = null;
    private ?int $locationId = null;

    public function __construct(CommonFunction $commonFunction)
    {
        parent::__construct();
        $this->commonFunction = $commonFunction;
    }

    public function authorize(): bool
    {
        return true;
    }

    protected function prepareForValidation(): void
    {
        try {
            $this->locationId = Crypt::decryptString($this->route('locationAutoIdCrypt'));
        } catch (\Throwable $e) {
            throw new HttpResponseException(response()->json([
                'success' => false,
                'validation_error' => true,
                'status' => 1,
                'message' => 'Invalid Location ID',
            ], 400));
        }

        $this->location = DB::table('locations')->where('LOCATION_ID', $this->locationId)->first();

        if (!$this->location) {
            throw new HttpResponseException(response()->json([
                'success' => false,
                'validation_error' => true,
                'status' => 1,
                'message' => 'Locatiion not found',
            ], 404));
        }
    }

    public function rules(): array
    {
       

        return [
           
            'LOCATION_NAME' => ['required', Rule::unique('locations', 'LOCATION_NAME')
            ->ignore($this->locationId, 'LOCATION_ID')],
            
            'LATITUDE' => ['required'],
            'LONGITUDE' => ['required'],
        ];
    }

    public function messages(): array
    {
        return [
            'LOCATION_ID.required' => 'Location ID field is required',
            'LOCATION_NAME.required' => 'Location name field is required',
            'LATITUDE.required' => 'Latitude field is required',
            'LONGITUDE.required' => 'Longitude field is required',
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json([
            'success' => false,
            'validation_error' => true,
            'status' => 2,
            'message' => 'Validation errors',
            'data' => $validator->errors()
        ], 422));
    }

    
}
