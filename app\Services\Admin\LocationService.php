<?php

namespace App\Services\Admin;

use App\Helpers\CommonFunction;
use App\Helpers\MyHelper;
use Carbon\Carbon;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use App\Models\Location;

class LocationService
{
    protected CommonFunction $commonFunction;
    protected ?Branch $currentBranch = null;

    public function __construct(CommonFunction $commonFunction)
    {
        $this->commonFunction = $commonFunction;
		$this->auth = Auth::user();
		
		$this->division_id=$this->commonFunction->getDivisionId($this->auth->BRANCH_ID);
		
    }
	
	 public function indexLocation(): FoundationApplication|Response|ResponseFactory
    {
        $authUser = Auth::user();
		

        try 
		{
            $location = Location::select('locations.LOCATION_ID',
                'locations.DIVISION_ID',
                'locations.LOCATION_NAME',
                'locations.LATITUDE',
                'locations.LONGITUDE',
                'locations.ACTIVE'
            )
                ->where('locations.ACTIVE', MyHelper::$RS_ACTIVE)
				->where('locations.DIVISION_ID', "=", DB::Raw($this->division_id))
			    ->join("branch", "branch.BRANCH_ID", "=", DB::Raw($authUser->BRANCH_ID))
               // ->Join("division as D", "D.DIVISIONN_ID", "=", DB::Raw($this->division_id))
                ->get();

            return response([
                'success' => true,
                'status' => 3,
                'locations' => $location,
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'location Index Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ]);
        }
    }

	

    public function storeLocation($request): FoundationApplication|Response|ResponseFactory
    {

        try {

            DB::beginTransaction();
            $auth_user = Auth::user();

            $locationData = $this->prepareLocationData($request, $auth_user, MyHelper::$RS_ACTIVE, $auth_user->id);
            $locationResult = Location::create($locationData);

            return response([
                'success' => true,
                'status' => 3,
                'message' => 'location Created Successfully',

            ]);

        } catch (\Throwable|\Exception $e) 
		{
            DB::rollBack();
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'validation_controller' => true,
                'status' => 4,
                'message' => 'Location Created UnSuccessfully',
                'errorM' => $e->getMessage(),
            ],500);
        } finally {
            DB::commit();
        }


    }
	
    /**
     * @throws ConnectionException
     */
    private function prepareLocationData($request, $auth_user, $active, $userId): array
    {
        $date = Carbon::now();
        $authUser = Auth::user();
        $division_id=$this->commonFunction->getDivisionId($authUser->BRANCH_ID);

        return [
            "DIVISION_ID" => $division_id,
            "LOCATION_NAME" => $request->LOCATION_NAME,
            "LATITUDE" => $request->LATITUDE,
            "LONGITUDE" => $request->LONGITUDE,
            "ACTIVE" => $active,
            "CREATED_BY" => $auth_user->id,
            "CREATED_DATE" => $date->format("Y-m-d H:i:s"),
          
        ];
    }

    public function deleteLocation($request, $locationAutoIdCrypt): FoundationApplication|Response|ResponseFactory
    {
        $authUser = Auth::user();
       
        try {
            DB::beginTransaction();

            $locationId = Crypt::decryptString($locationAutoIdCrypt);

            $location = location::where('ACTIVE', MyHelper::$RS_ACTIVE)
               // ->where('BRANCH_ID', $authUser->BRANCH_ID)
                ->findOrFail($locationId);

            $location->update([
                'ACTIVE' => MyHelper::$RS_INACTIVE,
                'UPDATED_BY' => Auth::user()->id,
                'UPDATETED_DATE' => $date->format("Y-m-d H:i:s"),
            ]);

            DB::commit();

            return response([
                'success' => true,
                'status' => 3,
                'message' => 'Location Deleted Successfully',
            ]);
        } catch (\Throwable|\Exception $e) {
            DB::rollBack();
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Location Delete Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ],500);
        }
    }

   

    public function paginationLocation($request, bool $active = true): FoundationApplication|Response|ResponseFactory
    {
        try {
            $perPage = $request->input('per_page', MyHelper::$PAGINATION_PER_PAGE);
            $branch_id = Auth::user()->BRANCH_ID;
            $activeStatus = $active ? MyHelper::$RS_ACTIVE : MyHelper::$RS_INACTIVE;

            $authUser = Auth::user();

         $location = Location::select('locations.LOCATION_ID',
                'locations.DIVISION_ID',
                'locations.LOCATION_NAME',
                'locations.LATITUDE',
                'locations.LONGITUDE',
                'locations.ACTIVE'
            )
                ->where('locations.ACTIVE', MyHelper::$RS_ACTIVE)
				->where('locations.DIVISION_ID', "=", DB::Raw($this->division_id))
			    ->join("branch", "branch.BRANCH_ID", "=", DB::Raw($authUser->BRANCH_ID));
               // ->Join("division as D", "D.DIVISION_ID", "=", "locations.DIVISION_ID");
               
               
            $filterModel = $request->input('filterModel');
            if ($filterModel) {
                foreach ($filterModel as $field => $filter) {
                    if (isset($filter['filter']) && $filter['filter'] !== '') {
                        $value = $filter['filter'];
                        $type = $filter['type'];

                        switch ($field) {
                            case 'LOCATION_NAME':
                                $location->where('locations.SIM_MOBILE_NO', 'like', "%{$value}%");
                                break;
                         case 'LATITUDE':
                                $location->where('locations.LATITUDE', 'like', "%{$value}%");
                                break;
                         case 'LONGITUDE':
                                $location->where('locations.LONGITUDE', 'like', "%{$value}%");
                                break;
                        }
                    }
                }
            }


            if ($request->has('orderBy') && !empty($request->input('orderBy'))) {
                $orderBy = $request->input('orderBy');
                $order = $request->input('order', 'asc');
                $location->orderBy($orderBy, $order);
            } else {
                $location->orderBy('locations.CREATED_DATE', 'desc');
            }


            if ($perPage === MyHelper::$PAGINATION_PER_PAGE_ALL) {
                $paginatedLocation = $location->paginate($location->count());
            } else {
                $perPage = is_numeric($perPage) ? (int)$perPage : MyHelper::$PAGINATION_PER_PAGE;
                $paginatedLocation = $location->paginate($perPage);
            }

            return response([
                'success' => true,
                'status' => 3,
                'locations' => $paginatedLocation,
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'Location Pagination Unsuccessful' : 'Location Pagination Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ],500);
        }
    } 

    public function editLocation($locationAutoIdCrypt): FoundationApplication|Response|ResponseFactory
    {
        $authUser = Auth::user();

        try {
            $locationAutoId = Crypt::decryptString($locationAutoIdCrypt);
            $location = Location::where('locations.ACTIVE', MyHelper::$RS_ACTIVE)
                 ->where('locations.LOCATION_ID', $locationAutoId)
			   
                ->select('locations.LOCATION_ID',
                'locations.DIVISION_ID',
                'locations.LOCATION_NAME',
                'locations.LATITUDE',
                'locations.LONGITUDE',
                'locations.ACTIVE'
            )
                ->where('locations.ACTIVE', MyHelper::$RS_ACTIVE)
				//->where('locations.DIVISION_ID', "=","D.DIVISION_ID")
			    ->join("branch", "branch.BRANCH_ID", "=", DB::Raw($authUser->BRANCH_ID))
                ->Join("division as D", "D.DIVISION_ID", "=", "locations.DIVISION_ID")
                ->firstOrFail();

            return response([
                'success' => true,
                'status' => 3,
                'sim' => $location,
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Location Edit Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ],500);
        }
    }

    public function updateLocation($request, $locationAutoIdCrypt): FoundationApplication|Response|ResponseFactory
    {
        $authUser = Auth::user();
        try {
            DB::beginTransaction();
            $locationAutoId = Crypt::decryptString($locationAutoIdCrypt);
            $location = Location::findOrFail($locationAutoId);
            $auth_user = Auth::user();

            $locationData = $this->prepareLocationDataForUpdate($request, $auth_user, $location);
            $location->update($locationData);

            DB::commit();
			
            return response([
                'success' => true,
                'status' => 3,
                'message' => 'Location Updated Successfully',
            ]);

        } catch (\Throwable|\Exception $e) {
            DB::rollBack();
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'message' => 'Location Update Unsuccessful',
                'status' => 4,
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ],500);
        }
    }

    /**
     * @throws ConnectionException
     */
    private function prepareLocationDataForUpdate($request, $auth_user, $sim): array
    {
        $date = Carbon::now();

        return [
            "LOCATION_NAME" => $request->LOCATION_NAME,
            "LATITUDE" => $request->LATITUDE,
            "LONGITUDE" => $request->LONGITUDE,
            "UPDATED_BY" => $auth_user->id,
            "updated_at" => $date->format("Y-m-d H:i:s"),
        ];
    }

    

    public function dataForCreateLocation(): FoundationApplication|Response|ResponseFactory
    {

        try {
            $user = Auth::user();
            $rsActive = MyHelper::$RS_ACTIVE;
            $branchId = $user->BRANCH_ID;
			
            $division = DB::table('division')
                ->select('DIVISION_ID', 'NAME','LOCATION')
                ->where('ACTIVE', $rsActive)
                ->get();
				
            return response([
                'success' => true,
                'status' => 3,
                'locations' => $division,
                
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Data For Create for Location  Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ],500);
        }
    }

}
