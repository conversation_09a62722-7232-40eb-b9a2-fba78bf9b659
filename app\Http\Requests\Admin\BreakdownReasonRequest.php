<?php

//namespace App\Http\Requests\Admin;
namespace App\Http\Requests\Admin;

use App\Helpers\CommonFunction;
use Closure;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;

class BreakdownReasonRequest extends FormRequest
{
    protected CommonFunction $commonFunction;

    public function __construct(CommonFunction $commonFunction)
    {
        
    }

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {   
        return [
            'breakdown_rosterid' => ['required','numeric'],
            'breakdown_cabid' => ['required','numeric'],
            'breakdown_type' => ['required','string'],
            'breakdown_reasonid' => ['required','numeric'],
            'vehicle_subreasonid' => ['nullable','string'],
            'vehicle_remarks' => ['nullable','string'],
            'driver_remarks' => ['nullable','string'],
            'incidentselect' => ['nullable','string'],
            ];
    }

    public function messages(): array
    {
        return [
            'breakdown_rosterid.required' => 'Breakdown Roster Id field is required',   
            'breakdown_cabid.required' => 'Breakdown Cab ID field is required',   
            'breakdown_type.required' => 'Breakdown Type field is required',   
            'breakdown_reasonid.required' => 'Breakdown Reason Id field is required',           
        ];
    }
   

    public function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json([
            'success' => false,
            'validation_error' => true,
            'message' => 'Validation errors',
            'data' => $validator->errors()
        ]));
    }
   
}
