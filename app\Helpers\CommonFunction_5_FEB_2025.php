<?php

namespace App\Helpers;

use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Client\Factory as HttpClient;
use Illuminate\Support\Facades\Log;
use DB;
use Illuminate\Support\Facades\Auth;

class CommonFunction
{

    protected ?HttpClient $httpClient;
    protected string $apiDirections;
    protected string $apiKey;
    protected string $apiKeyTripClose;

    public function __construct(?HttpClient $httpClient = null)
    {
        $this->httpClient = $httpClient ?? new HttpClient();
        $this->apiDirections = MyHelper::$API_DIRECTIONS;
        $this->apiKey = config('app.api_key');
        $this->apiKeyTripClose = config('app.trip_close_api_key');
    }


    /**
     * @throws ConnectionException
     */
    public function getKmMasterEmp(string $origin, string $destination, string $waypoints): string
    {
        if (empty($origin) || empty($destination) || empty($waypoints)) {
            return '0';
        }

        $response = $this->httpClient->get($this->apiDirections, [
            'origin' => $origin,
            'destination' => $destination,
            'waypoints' => $waypoints,
            'key' => $this->apiKey,
        ]);

        Log::info("getKmMasterEmp Google Maps API response", ['response' => $response]);

        if ($response->failed()) {
            return '0';
        }

        $json = $response->json();

        if (empty($json['routes'])) {
            return '0';
        }

        $route = $json['routes'][0]['legs'];
        $distance = 0;
        $timeMins = 0;

        foreach ($route as $leg) {
            $distance += $leg['distance']['value'];
            $timeMins += $leg['duration']['value'];
        }

        return "{$distance}-{$timeMins}";
    }

    /**
     * @throws ConnectionException
     */
    public function getKmTwoPointMasterEmp(string $origin, string $destination): string
    {
        if (empty($origin) || empty($destination)) {
            return '0';
        }

        $response = $this->httpClient->get($this->apiDirections, [
            'origin' => $origin,
            'destination' => $destination,
            'key' => $this->apiKeyTripClose,
        ]);

        Log::info("Google Maps API request", ['url' => $response->effectiveUri()]);
        Log::info("getKmTwoPointMasterEmp Google Maps API response", ['response' => $response]);

        if ($response->successful()) {
            $data = $response->json();

            if (!empty($data['routes'][0]['legs'] ?? null)) {
                $legs = $data['routes'][0]['legs'];
                $distance = collect($legs)->sum('distance.value');
                $duration = collect($legs)->sum('duration.value');

                return "{$distance}-{$duration}";
            }
        } else {
            Log::error("Google Maps API error", ['status' => $response->status(), 'body' => $response->body()]);
        }

        return '0';
    }

   /* public function AES_ENCRYPT($value, $secret): string
    {
        $key = substr(hash('sha256', $secret, true), 0, 32);
        $padded = $this->pkcs7Pad($value, 32);
        $encrypted = openssl_encrypt($padded, 'aes-256-ecb', $key, OPENSSL_RAW_DATA);
        return base64_encode($encrypted);
    }
*/
public function AES_ENCRYPT($value, $secret): ?string
    {
        try {
            $key = substr(hash('sha256', $secret, true), 0, 32);
            $padded = $this->pkcs7Pad($value, 32);
            $encrypted = openssl_encrypt($padded, 'aes-256-ecb', $key, OPENSSL_RAW_DATA);
            return base64_encode($encrypted);
        } catch (\Exception $e) {
            $this->logException($e);
            return null;
        }
    }
    private function pkcs7Pad($data, $blockSize): string
    {
        $padding = $blockSize - (strlen($data) % $blockSize);
        $padChar = chr($padding);
        return $data . str_repeat($padChar, $padding);
    }
	public function AES_DECRYPT($encryptedValue, $secret): ?string
    {
        try {
            $key = substr(hash('sha256', $secret, true), 0, 32);
            $encrypted = base64_decode($encryptedValue);
            $decrypted = openssl_decrypt($encrypted, 'aes-256-ecb', $key, OPENSSL_RAW_DATA);
            return $this->pkcs7Unpad($decrypted);
        } catch (\Exception $e) {
            $this->logException($e);
            return null;
        }
    }

    // public function AES_DECRYPT($encryptedValue, $secret): string
    // {
    //     $key = substr(hash('sha256', $secret, true), 0, 32);
    //     $encrypted = base64_decode($encryptedValue);
    //     $decrypted = openssl_decrypt($encrypted, 'aes-256-ecb', $key, OPENSSL_RAW_DATA);
    //     return $this->pkcs7Unpad($decrypted);
    // }

    private function pkcs7Unpad($data): string
    {
        $padding = ord($data[strlen($data) - 1]);
        return substr($data, 0, -$padding);
    }

    public function logException($e): void
    {
        Log::error($e->getMessage());
        Log::error($e->getTraceAsString());
    }
	
	 public function getOrgId($branch_id): int
    {
		ini_set('memory_limit', '-1');
		
		$org_id = DB::table('branch')->select("ORG_ID")->where('BRANCH_ID',$branch_id)->get();
		
        return $org_id[0]->ORG_ID;
    }
    public function getDivisionId($branch_id): int
    {
        $div_id = DB::table('branch')->select("DIVISION_ID")->where('BRANCH_ID', $branch_id)->get();
        return $div_id[0]->DIVISION_ID;
    }
   
     public function getReasonList($categoryName,$branch_id)
    {
        $reasons  = DB::table('reason_master as r')
      
        ->where('r.CATEGORY', $categoryName)
        ->where('r.BRANCH_ID', $branch_id)
        ->where('r.ACTIVE',  MyHelper::$RS_ACTIVE)
        ->select('r.REASON_ID','r.REASON')
        ->get();

        return $reasons;
    }	
    public function employee_status($sts)
	{
		$RP_safe_drop = explode(',',MyHelper::$RP_safe_drop);
		$RPS_SAFE_DROP_OTP = explode(',',MyHelper::$RPS_SAFE_DROP_OTP);
		$RP_create = explode(',',MyHelper::$RPS_CREATE);
		$RP_club = explode(',',MyHelper::$RPS_CLUBBING);
		$RP_arrival = explode(',',MyHelper::$RPS_TTLARRIVAL);
		$RPS_TTLCABSYSTEMOTP = explode(',',MyHelper::$RPS_TTLCABSYSTEMOTP);
		$RP_cab_delay = explode(',',MyHelper::$RPS_TTLCABDELAY);
		$RP_noshow = explode(',',MyHelper::$RPS_TTLNOSHOW);
		$RP_system_otp = explode(',',MyHelper::$RPS_TTLSYSTEMOTP);
		$RP_employee_delay = explode(',',MyHelper::$RPS_TTLEMPLOYEEDELAY);
		$RP_cabemployee_delay =explode(',',MyHelper::$RPS_TTLCABEMPLOYEEDELAY);
		$RP_manual_otp = explode(',',MyHelper::$RPS_TTLMANUALOTP);
		$RPS_TTLEMPLOYEEDELAY_SYSTEMOTP = explode(',',MyHelper::$RPS_TTLEMPLOYEEDELAY_SYSTEMOTP);

        $RP_Boarded_sts=MyHelper::$RP_Boarded_sts;
        $RP_Not_Boarded_sts=MyHelper::$RP_Not_Boarded_sts;
        $RP_Not_Enabled=MyHelper::$RP_Not_Enabled;
        $RP_Created_sts=MyHelper::$RP_Created_sts;
        $RP_Club_sts=MyHelper::$RP_Club_sts;
        $RP_Arrival_sts=MyHelper::$RP_Arrival_sts;
        $RP_cab_delay_sts=MyHelper::$RP_cab_delay_sts;
        $RP_Noshow_sts=MyHelper::$RP_Noshow_sts;
        $RP_system_otp_sts=MyHelper::$RP_system_otp_sts;
        $RP_Manual_otp_sts=MyHelper::$RP_Manual_otp_sts;
        $RP_employee_delay_sts=MyHelper::$RP_employee_delay_sts;
        $RP_cabemployee_delay_sts=MyHelper::$RP_cabemployee_delay_sts;
        $RP_manualclosed=MyHelper::$RP_manualclosed;
        $RP_employee_delay_system_otp=MyHelper::$RP_employee_delay_system_otp;
        $RP_employee_safe_drop=MyHelper::$RP_employee_safe_drop;
        $RP_cab_delay_system_otp_sts=MyHelper::$RP_cab_delay_system_otp_sts;

		if (in_array($sts, $RP_create) == true) {
			$stsus = $RP_Created_sts;
		} else if (in_array($sts, $RP_club) == true) {
			$stsus = $RP_Club_sts;
		} else if (in_array($sts, $RP_arrival) == true) {
			$stsus = $RP_Arrival_sts;
		} else if (in_array($sts, $RPS_TTLCABSYSTEMOTP) == true) {
			$stsus = $RP_cab_delay_system_otp_sts;
		} else if (in_array($sts, $RP_cab_delay) == true) {
			$stsus = $RP_cab_delay_sts;
		} else if (in_array($sts, $RP_noshow) == true) {
			$stsus = $RP_Noshow_sts;
		} else if (in_array($sts, $RP_system_otp) == true) {
			$stsus = $RP_system_otp_sts;
		
		} else if (in_array($sts, $RP_employee_delay) == true) {
			$stsus = $RP_employee_delay_sts;
		} else if (in_array($sts, $RP_cabemployee_delay) == true) {
			$stsus = $RP_cabemployee_delay_sts;
		} else if (in_array($sts, $RP_manual_otp) == true) {

			$stsus = $RP_Manual_otp_sts;
			/* if($branch_id==48)
					 {
						 $stsus='Employee Safe Drop';
					 } */

		} else if (in_array($sts, $RPS_TTLEMPLOYEEDELAY_SYSTEMOTP) == true) {
			$stsus = $RP_employee_delay_system_otp;
		} else if (in_array($sts,$RPS_SAFE_DROP_OTP)==true) {
			$stsus = $RP_employee_safe_drop;
		} else {
			$stsus = $RP_Not_Enabled;
		}
		
		return $stsus;
	}
    public function TripStatus($sts) {
        $Trip_created = MyHelper::$RS_NEWROSTER;
        $Trip_allot = MyHelper::$RS_TOTALALLOT;
        $Trip_accepted = MyHelper::$RS_TOTALACCEPT;
        $Trip_rejectd = MyHelper::$RS_TOTALREJECT;
        $Trip_executed = MyHelper::$RS_TOTALEXECUTE;
        $Trip_breakdown = MyHelper::$RS_TOTALBREAKDOWN;
        $Trip_close = MyHelper::$RS_TOTALTRIPCLOSE;
        $Trip_manual_close = MyHelper::$RS_TOTALMANUALTRIPCLOSE;
        $Tripsheet_accepted = MyHelper::$RS_TOTALTRIPSHEETACCEPT;
        $Tripsheet_rejected = MyHelper::$RS_TOTALTRIPSHEETREJECT;
        $RS_TOTALDELAYROUTES = MyHelper::$RS_TOTALDELAYROUTES;
        $RS_TOTALTRIPSHEET_CANCEL = MyHelper::$RS_TOTALTRIPSHEET_CANCEL;
        $RS_TOTAL_AUTOCANCEL = MyHelper::$RS_TOTAL_AUTOCANCEL;

        $RS_Created_sts = MyHelper::$RS_Created_sts;
        $RS_Alloted_sts = MyHelper::$RS_Alloted_sts;
        $RS_ACCEPTed_sts = MyHelper::$RS_ACCEPTed_sts;
        $RS_Rejected_sts = MyHelper::$RS_Rejected_sts;
        $RS_Execute_sts = MyHelper::$RS_Execute_sts;
        $RS_Breakdown_sts = MyHelper::$RS_Breakdown_sts;
        $RS_Closed_sts = MyHelper::$RS_Closed_sts;
        $RS_Closed_Delay_sts = MyHelper::$RS_Closed_Delay_sts;
        $RS_Manual_Closed_sts = MyHelper::$RS_Manual_Closed_sts;
        $RS_Tripsheet_Accept_sts = MyHelper::$RS_Tripsheet_Accept_sts;
        $RS_Tripsheet_Reject_sts = MyHelper::$RS_Tripsheet_Reject_sts;
        $RS_Tripsheet_cancelled_sts = MyHelper::$RS_Tripsheet_cancelled_sts;
        $RS_Autocancelled_sts = MyHelper::$RS_Autocancelled_sts;
        $trip_status = "";
		$branch_id=Auth::user()->BRANCH_ID;
        switch ($sts) {
            case preg_match('/' . $sts . '/', $Trip_created) ? true : false:
                $trip_status = MyHelper::$RS_Created_sts;
                break;

            case preg_match('/' . $sts . '/', $Trip_allot) ? true : false:
                $trip_status = MyHelper::$RS_Alloted_sts;
                break;

            case preg_match('/' . $sts . '/', $Trip_accepted) ? true : false:
                $trip_status = MyHelper::$RS_ACCEPTed_sts;
                break;

            case preg_match('/' . $sts . '/', $Trip_rejectd) ? true : false:
                $trip_status = MyHelper::$RS_Rejected_sts;
                break;

            case preg_match('/' . $sts . '/', $Trip_executed) ? true : false:
                $trip_status = MyHelper::$RS_Execute_sts;
                break;

            case preg_match('/' . $sts . '/', $Trip_breakdown) ? true : false:
                $trip_status = MyHelper::$RS_Breakdown_sts;
                break;

            case preg_match('/' . $sts . '/', $Trip_close) ? true : false:
                $trip_status = MyHelper::$RS_Closed_sts;
				  if($branch_id==48)
				{
					$trip_status='TripClosed';
				} 
                break;
            case preg_match('/' . $sts . '/', $RS_TOTALDELAYROUTES) ? true : false:
                $trip_status = MyHelper::$RS_Closed_Delay_sts;

                         /*if($branch_id==48)
				{
					$trip_status='TripClosed';
				} */

                break;
            case preg_match('/' . $sts . '/', $Trip_manual_close) ? true : false:
			    $trip_status = MyHelper::$RS_Manual_Closed_sts;
				/* if($branch_id==48)
				{
					$trip_status='TripClosed';
				} */
                break;

            case preg_match('/' . $sts . '/', $Tripsheet_accepted) ? true : false:
                $trip_status = MyHelper::$RS_Tripsheet_Accept_sts;
                break;

            case preg_match('/' . $sts . '/', $Tripsheet_rejected) ? true : false:
                $trip_status = MyHelper::$RS_Tripsheet_Reject_sts;
                break;

            case preg_match('/' . $sts . '/', $RS_TOTALTRIPSHEET_CANCEL) ? true : false:
                $trip_status = MyHelper::$RS_Tripsheet_cancelled_sts;
                break;

            case preg_match('/' . $sts . '/', $RS_TOTAL_AUTOCANCEL) ? true : false:
                $trip_status = MyHelper::$RS_Autocancelled_sts;
                break;
            default:
                $trip_status = '--';
                break;
        }
        return $trip_status;
    }
    public function date_format_add()
	{
		date_default_timezone_set('Asia/Kolkata');
		$cur_date = date('Y-m-d');
		$cur_time = date('H:i:s');
		return $cur_date . 'T' . $cur_time;
	}
    public function weblogs($log_arr)
	{
		// print_r($log_arr);exit;
		$elastic = new ElasticController();
		// return $elastic->insertWebLogs($log_arr); 
	}
	public function check_time($t1, $t2, $tn) {
        $t1 = +str_replace(":", "", $t1);
        $t2 = +str_replace(":", "", $t2);
        $tn = +str_replace(":", "", $tn);
        if ($t2 <= $t1) {
            $ret = $t2 <= $tn && $tn >= $t1;
            if ($ret) {
                return $ret;
            } else {
                return $t1 >= $tn && $tn <= $t2;
            }
        }
    }
    public function GetPropertyValue($propname)
	{
		$BRANCH_ID = Auth::user()->BRANCH_ID;
		$dbname = Auth::user()->dbname;
		$sql = "SELECT PROPERTIE_VALUE FROM properties WHERE PROPERTIE_NAME = '$propname' AND BRANCH_ID = $BRANCH_ID";
		$rs1 = DB::connection("$dbname")->select($sql);
		return $rs1[0]->PROPERTIE_VALUE;
	}
    public function noshow_change_roster_approve_km($roster_id, $trip_type)
	{
        
		$branch_id = Auth::user()->BRANCH_ID;
		//$ElasticController = new ElasticController();
		$user_id = Auth::user()->id;
		$dbname = Auth::user()->dbname;
		$RPS_TTLNOSHOW = MyHelper::$RPS_TTLNOSHOW;
		if ($trip_type == 'P') {
			$cond = '';
		} else {
			$cond = "and RP.ROSTER_PASSENGER_STATUS not in(" . $RPS_TTLNOSHOW . ")";
		}
		$sql1 = " SELECT RP.ROSTER_ID,RP.LOCATION_ID,L.LOCATION_ID,L.LOCATION_NAME,R.END_LOCATION as previous_location,RP.ROUTE_ORDER,R.TRIP_APPROVED_KM,R.TRIP_TYPE,RP.ROSTER_PASSENGER_STATUS
		-- ,if(RP.ROUTE_ORDER>=R.TRIP_APPROVED_KM,'Notsame','0') as km_update
		FROM roster_passengers  RP
		INNER JOIN locations L ON L.LOCATION_ID=RP.LOCATION_ID
		inner join rosters R ON R.ROSTER_ID='" . $roster_id . "'
		where RP.ROSTER_ID ='" . $roster_id . "' and RP.ACTIVE=1 " . $cond . " ORDER BY RP.ROUTE_ORDER DESC limit 1 ";
		$roster_id_res = DB::connection("$dbname")->select($sql1);
		if (count($roster_id_res) > 0) {
			$route_order = $roster_id_res[0]->ROUTE_ORDER;
			//$km_update=$roster_id_res[0]->km_update;
			$previous_trip_approved_km = $roster_id_res[0]->TRIP_APPROVED_KM;
			$LOCATION_NAME = $roster_id_res[0]->LOCATION_NAME;
			$UPDATE_ROSTER_SUCCESS = '';

			// if($km_update=='Notsame')
			// {
			if ($trip_type == 'D') {
				$UPDATE_ROSTER_SUCCESS = DB::connection("$dbname")->update("update rosters set END_LOCATION='" . $LOCATION_NAME . "' where ROSTER_ID='" . $roster_id . "'");
			}
			$UPDATE_ROSTER_SUCCESS = DB::connection("$dbname")->update("update rosters set TRIP_APPROVED_KM='" . $route_order . "' where ROSTER_ID='" . $roster_id . "'");
			//}

			/* log */
			$date_f = $this->date_format_add();
			$log_arr = array("BRANCH_ID" => $branch_id, "CREATED_BY" => $user_id, "PROCESS_DATE" => $date_f, "ACTION" => "MANUAL_OTP_NOSHOW_ROSTER_KM_CHANGED", "CATEGORY" => 'ROSTER_APPROVE_KM_CHANGED', "ROSTER_ID" => $roster_id, "UPDATE_ROSTER_SUCCESS" => $UPDATE_ROSTER_SUCCESS, "PREVIOUS_TRIP_APPROVE_KM" => $previous_trip_approved_km, "NEW_TRIP_APPROVE_KM_UPDATE" => $route_order);
			// $this->weblogs($log_arr);

			/* End Log */
		}
	}
	public function change_roster_approve_km($old_roster_id, $new_roster_id) {
        $branch_id = Auth::user()->BRANCH_ID;
        $user_id = Auth::user()->id;
		$dbname = Auth::user()->dbname;
        $sql1 = " SELECT RP.ROSTER_ID,RP.LOCATION_ID,L.LOCATION_ID,L.LOCATION_NAME,R.END_LOCATION as previous_location,RP.ROUTE_ORDER,R.TRIP_APPROVED_KM,R.TRIP_TYPE,RP.ROSTER_PASSENGER_STATUS,if(RP.ROUTE_ORDER=R.TRIP_APPROVED_KM,'0','Notsame') as km_update
		FROM roster_passengers  RP
		INNER JOIN locations L ON L.LOCATION_ID=RP.LOCATION_ID
		inner join rosters R ON R.ROSTER_ID='" . $old_roster_id . "'
		where RP.ROSTER_ID ='" . $old_roster_id . "' and RP.ACTIVE=1  ORDER BY RP.ROUTE_ORDER DESC limit 1 ";
        $old_roster_id_res = DB::connection("$dbname")->select($sql1);
        $old_roster_previous_end_location = '';
        $UPDATE_OLD_ROSTER_SUCCESS = '';
        $UPDATE_NEW_ROSTER_SUCCESS = '';
        $LOCATION_NAME = '';
        $new_roster_previous_location = '';
        $NEW_LOCATION_NAME = '';
        $PREVIOUS_TRIP_APPROVED_KM = '';
        $MAX_ROUTE_ORDER = '';
        $PREVIOUS_NEW_ROSTER_TRIP_APPROVED_KM = '';
        $NEW_MAX_ROUTE_ORDER = '';
        if (count($old_roster_id_res) > 0) {
            $LOCATION_NAME = $old_roster_id_res[0]->LOCATION_NAME;
            $MAX_ROUTE_ORDER = $old_roster_id_res[0]->ROUTE_ORDER;
            $KM_UPDATE = $old_roster_id_res[0]->km_update;
            $PREVIOUS_TRIP_APPROVED_KM = $old_roster_id_res[0]->TRIP_APPROVED_KM;
            $old_roster_previous_end_location = $old_roster_id_res[0]->previous_location;
            $TRIP_TYPE = $old_roster_id_res[0]->TRIP_TYPE;
            if ($KM_UPDATE == 'Notsame') {
                if ($TRIP_TYPE == 'D') {
                    $UPDATE_OLD_ROSTER_SUCCESS = DB::update("update rosters set END_LOCATION='" . $LOCATION_NAME . "',TRIP_APPROVED_KM='" . $MAX_ROUTE_ORDER . "' where ROSTER_ID='" . $old_roster_id . "'");
                } else {
                    $UPDATE_OLD_ROSTER_SUCCESS = DB::update("update rosters set START_LOCATION='" . $LOCATION_NAME . "',TRIP_APPROVED_KM='" . $MAX_ROUTE_ORDER . "' where ROSTER_ID='" . $old_roster_id . "'");
                }
            }
        }
        $sql2 = "SELECT RP.ROSTER_ID,RP.LOCATION_ID,L.LOCATION_ID,L.LOCATION_NAME,R.END_LOCATION as new_roster_previous_location,RP.ROUTE_ORDER,R.TRIP_APPROVED_KM,R.TRIP_TYPE,RP.ROSTER_PASSENGER_STATUS,if(RP.ROUTE_ORDER=R.TRIP_APPROVED_KM,'0','Notsame') as km_update
		FROM roster_passengers  RP
		INNER JOIN locations L ON L.LOCATION_ID=RP.LOCATION_ID
		inner join rosters R ON R.ROSTER_ID='" . $new_roster_id . "'
		where RP.ROSTER_ID ='" . $new_roster_id . "' and RP.ACTIVE=1  ORDER BY RP.ROUTE_ORDER DESC limit 1 ";
        $new_roster_id_res = DB::connection("$dbname")->select($sql2);
        if (count($new_roster_id_res) > 0) {
            $NEW_LOCATION_NAME = $new_roster_id_res[0]->LOCATION_NAME;
            $NEW_MAX_ROUTE_ORDER = $new_roster_id_res[0]->ROUTE_ORDER;
            $NEW_KM_UPDATE = $new_roster_id_res[0]->km_update;
            $PREVIOUS_NEW_ROSTER_TRIP_APPROVED_KM = $new_roster_id_res[0]->TRIP_APPROVED_KM;
            $new_roster_previous_location = $new_roster_id_res[0]->new_roster_previous_location;
            $TRIP_TYPE = $new_roster_id_res[0]->TRIP_TYPE;
            if ($NEW_KM_UPDATE == 'Notsame') {
                if ($TRIP_TYPE == 'D') {
                    $UPDATE_NEW_ROSTER_SUCCESS = DB::update("update rosters set END_LOCATION='" . $NEW_LOCATION_NAME . "',TRIP_APPROVED_KM='" . $NEW_MAX_ROUTE_ORDER . "' where ROSTER_ID='" . $new_roster_id . "'");
                } else {
                    $UPDATE_NEW_ROSTER_SUCCESS = DB::update("update rosters set START_LOCATION='" . $NEW_LOCATION_NAME . "',TRIP_APPROVED_KM='" . $NEW_MAX_ROUTE_ORDER . "' where ROSTER_ID='" . $new_roster_id . "'");
                }
            }
        }
        $date_f = $this->date_format_add();
        $log_arr = array("BRANCH_ID" => $branch_id, "CREATED_BY" => $user_id, "PROCESS_DATE" => $date_f, "ACTION" => "RECLUB_ROSTER_KM_CHANGED", "CATEGORY" => 'ROSTER_APPROVE_KM_CHANGED', "NEW_ROSTER_ID" => $new_roster_id, "OLD_ROSTER_ID" => $old_roster_id, "UPDATE_OLD_ROSTER_SUCCESS" => $UPDATE_OLD_ROSTER_SUCCESS, "UPDATE_NEW_ROSTER_SUCCESS" => $UPDATE_NEW_ROSTER_SUCCESS, "PREVIOUS_OLD_ROSTER_END_LOCATON_NAME" => $old_roster_previous_end_location, "NEW_END_LOCATON_OLD_ROSTER" => $LOCATION_NAME, "PREVIOUS_NEW_ROSTER_END_LOCATON_NAME" => $new_roster_previous_location, "NEW_END_LOCATON_NEW_ROSTER" => $NEW_LOCATION_NAME, "PREVIOUS_TRIP_APPROVE_KM_OLD_ROSTER" => $PREVIOUS_TRIP_APPROVED_KM, "NEW_TRIP_APPROVE_KM_OLD_ROSTER" => $MAX_ROUTE_ORDER, "PREVIOUS_TRIP_APPROVE_KM_NEW_ROSTER" => $PREVIOUS_NEW_ROSTER_TRIP_APPROVED_KM, "NEW_TRIP_APPROVE_KM_NEW_ROSTER" => $NEW_MAX_ROUTE_ORDER);
       // $this->weblogs($log_arr);
    }

    public function RRD_CalcDeviceKm($rosterid,$trip_type,$tripstatus) {
        $rosterid = $rosterid;
       // $ElasticController = new ElasticController();
        $user_id = Auth::user()->id;
        $branchid = Auth::user()->BRANCH_ID;
        $dbname = Auth::user()->dbname;
       
		$order=$trip_type=='P'?'DESC':'ASC';
		if($tripstatus=='FT' || $tripstatus=='LT')
		{
			//$cond="and DS.TRIP_STATUS='".$tripstatus."' and D.DRIVERS_ADDR_LAT is not null";
			$cond="";
		}
		else 
		{
			$cond='';
		}
		
		  $sql = "SELECT RP.LOCATION_ID,RP.ROSTER_ID,RP.ROUTE_ORDER,concat(L.LATITUDE,L.LONGITUDE) as latlong,DS.CAB_ID,B.LAT,B.`LONG`,L.LATITUDE as lat,L.LONGITUDE as lng,DS.TRIP_STATUS,D.DRIVERS_ADDR_LAT,D.DRIVERS_ADDR_LONG,
		  E.LATITUDE,E.LONGITUDE
		  from  roster_passengers RP
						inner join employees E on E.EMPLOYEES_ID=RP.EMPLOYEE_ID and E.BRANCH_ID='".$branchid."' 
						inner join driver_billing_summary DS on DS.ROSTER_ID=RP.ROSTER_ID
						inner join locations L on L.LOCATION_ID=RP.LOCATION_ID
						inner join branch as B ON B.BRANCH_ID='" . $branchid . "' 
						inner join cab C on C.CAB_ID=DS.CAB_ID
						inner join drivers D on D.DRIVERS_ID=C.DRIVER_ID
						where (RP.ROSTER_PASSENGER_STATUS & 32 or RP.ROSTER_PASSENGER_STATUS & 128 or RP.ROSTER_PASSENGER_STATUS & 64)  
						and RP.ROSTER_ID='".$rosterid."' $cond  ORDER BY RP.ROUTE_ORDER ".$order." ";
				$data = DB::connection("$dbname")->select($sql);
				
				if (count($data) > 0) {
					$cabid = $data[0]->CAB_ID;
					$branch_lat = $data[0]->LAT;
					$branch_long = $data[0]->LONG;
					$TRIP_STATUS = $data[0]->TRIP_STATUS;
				   // $previous_km = $data[0]->previous_km != 0 ? $data[0]->previous_km : '0';
					$result = '';
					$update_type = '';
					$update = '';
							$waypoints = '';
							$cnt = count($data);
							if($tripstatus=='N')
							{
								for ($i = 0; $i < count($data); $i++) {
									/* if ($cnt == 1) {
										$waypoints .= $data[$i]->LATITUDE . ',' . $data[$i]->LONGITUDE;
									} */
									if ($i == 0) {
										/* $emp_lat = $data[$i]->LATITUDE;
										$emp_long = $data[$i]->LONGITUDE; */
										$waypoints .= $data[$i]->LATITUDE . ',' . $data[$i]->LONGITUDE;
									} else {
										$waypoints .= '|'.$data[$i]->LATITUDE . ',' . $data[$i]->LONGITUDE ;
										/* if ($i == $cnt - 1) {
											$waypoints .= $data[$i]->LATITUDE . ',' . $data[$i]->LONGITUDE;
										} else {
											$waypoints .= $data[$i]->LATITUDE . ',' . $data[$i]->LONGITUDE . '|';
										} */
									}
								}
								$orgin = $branch_lat . ',' . $branch_long;
								$destination = $branch_lat . ',' . $branch_long;
							}
							else if($tripstatus=='FT')
							{
								for ($i = 0; $i < count($data); $i++) {
									if ($cnt == 1) {
										$waypoints = $data[$i]->LATITUDE . ',' . $data[$i]->LONGITUDE;
									}
									if ($i == 0) {
										$emp_lat = $data[$i]->LATITUDE;
										$emp_long = $data[$i]->LONGITUDE;
									} else {
										if ($i == $cnt - 1) {
											$waypoints .= $data[$i]->LATITUDE . ',' . $data[$i]->LONGITUDE;
										} else {
											$waypoints .= $data[$i]->LATITUDE . ',' . $data[$i]->LONGITUDE . '|';
										}
									}
								}
								$orgin = $emp_lat . ',' . $emp_long;
								$destination = $branch_lat . ',' . $branch_long;
							}
							else if($tripstatus=='LT')
							{
								for ($i = 0; $i < count($data); $i++) {
									if ($cnt == 1) {
										//$waypoints = $data[$i]->LATITUDE . ',' . $data[$i]->LONGITUDE;
											$emp_lat = $data[$i]->LATITUDE;
											$emp_long = $data[$i]->LONGITUDE;
									}
									if ($i == 0) {
										$waypoints = $data[$i]->LATITUDE . ',' . $data[$i]->LONGITUDE;
									} else {
										if ($i == $cnt - 1) {
											$emp_lat = $data[$i]->LATITUDE;
											$emp_long = $data[$i]->LONGITUDE;
											//$waypoints .= $data[$i]->LATITUDE . ',' . $data[$i]->LONGITUDE;
										} else {
											$waypoints .= '|'.$data[$i]->LATITUDE . ',' . $data[$i]->LONGITUDE ;
										}
									}
								}
								$orgin = $branch_lat . ',' . $branch_long;
								$destination = $emp_lat . ',' . $emp_long;
								//$destination = $DRIVERS_ADDR_LAT . ',' . $DRIVERS_ADDR_LONG;
							}
							else if($tripstatus=='DP')
							{
								if($trip_type=='D')
								{
									for ($i = 0; $i < count($data); $i++)
									{
										if ($cnt == 1) {
											$destination= $data[$i]->LATITUDE . ',' . $data[$i]->LONGITUDE;
										}
										if ($i == 0) {
											$waypoints = $data[$i]->LATITUDE . ',' . $data[$i]->LONGITUDE;
										}
										else 
										{
											if ($i == $cnt - 1) {
												$destination= $data[$i]->LATITUDE . ',' . $data[$i]->LONGITUDE;
											} else {
												$waypoints .= '|'.$data[$i]->LATITUDE . ',' . $data[$i]->LONGITUDE ;
											}
										}
									}
									$orgin = $branch_lat . ',' . $branch_long;
									$destination = $destination;
								}
								else if($trip_type=='P')
								{
									$sql="select DS.*, 
									CASE 
									WHEN DS.TRIP_TYPE='P' and DS.EXECUTE_LAT is not NULL THEN CONCAT(DS.EXECUTE_LAT,',',DS.EXECUTE_LONG) 
									ELSE CONCAT(E.LATITUDE,',',E.LONGITUDE)
									END AS orgin_lat_lng
									from roster_passengers RP
									inner join employees E on E.EMPLOYEES_ID=RP.EMPLOYEE_ID and E.BRANCH_ID='".$branchid."'
									inner join driver_billing_summary DS on DS.ROSTER_ID=RP.ROSTER_ID
									inner join locations L on L.LOCATION_ID=RP.LOCATION_ID
									where DS.ROSTER_ID='".$rosterid."' and RP.ACTIVE=1 ORDER BY RP.ROUTE_ORDER DESC limit 1";
									$res=DB::select($sql);
									for ($i = 0; $i < count($data); $i++)
									{
										/* if ($cnt == 1) {
											$orgin= $data[$i]->LATLNG1;
											
										} */
										
										if ($i == 0) 
										{
											if(count($res)>0)
											{
												$orgin=$res[0]->orgin_lat_lng;
											}
											else
											{
												$orgin= $data[$i]->LATITUDE . ',' . $data[$i]->LONGITUDE;
											}
										}
										else 
										{
											if($i == $cnt - 1)
											{
												$waypoints .=$data[$i]->LATITUDE . ',' . $data[$i]->LONGITUDE ;
											} 
											else
											{
												/* if($i==1)
												{
													$waypoints .= $data[$i]->LATITUDE . ',' . $data[$i]->LONGITUDE ;
												}
												else
												{ */
													$waypoints .=$data[$i]->LATITUDE . ',' . $data[$i]->LONGITUDE.'|' ;
												//}
											}
										}
									}
									$orgin = $orgin;
									$destination= $branch_lat . ',' . $branch_long;
								}
							}
									
						   
							$kms = $this->RRDGetkm($orgin, $destination, $waypoints,$rosterid);
							$distance_split = explode("-", $kms);
							$result = round($distance_split[0] / 1000);
							$update = DB::connection("$dbname")->table("rosters")->WHERE("ROSTER_ID", "=", $rosterid)->WHERE("ACTIVE", "=",1)->update(array("TOTAL_KM" => $result, "updated_at" => date("Y-m-d H:i:s")));
					
							$update2 = DB::connection("$dbname")->table("driver_billing_summary")
							->WHERE("ROSTER_ID", "=", $rosterid)
							->WHERE("ACTIVE", "=", 1)
							->update(array("GOOGLE_KM" => $result, "UPDATED_AT" => date("Y-m-d H:i:s")));
							//echo "<br/> UPDATE driver_billing_summary SET GOOGLE_KM='".$result."',UPDATED_AT='".date("Y-m-d H:i:s")."' WHERE ROSTER_ID='".$rosterid."' and BRANCH_ID = '".$branchid."';";
				}
    }
	public function RRDGetkm($origin, $destination, $waypoints,$rosterid) {
        if ($origin != '' && $destination != '' && $waypoints != '') {
            $API_DIRECTIONS = MyHelper::$API_DIRECTIONS;
            $API_CLIENTID = MyHelper::$API_CLIENTID;
            $TRIPCLOSE_API_KEY = MyHelper::$TRIP_CLOSE_API_KEY;
//            $url = $this->signUrl($API_DIRECTIONS . "origin=" . $origin . "&waypoints=" . $waypoints . "&destination=" . $destination . "&sensor=false&client=" . $API_CLIENTID, $API_KEY);
//            $url = $API_DIRECTIONS . "origin=" . $origin . "&waypoints=" . $waypoints . "&destination=" . $destination . "&sensor=false&client=" . $API_CLIENTID;
           // $url = $this->API_DIRECTIONS . "origin=" . $origin . "&waypoints=" . $waypoints . "&destination=" . $destination . "&sensor=false";
            $url = $this->API_DIRECTIONS . "origin=" . $origin . "&waypoints=" . $waypoints . "&destination=" . $destination . "&key=".$TRIPCLOSE_API_KEY;
			
			$errorlogpath = pathinfo(ini_get('error_log'));
						//$errorlogfile = "D:/xampp/htdocs/TMS_BACKUP/storage/logs/" . date('Y-m-d') . "-upload.log";
						$errorlogfile = "/var/www/html/TMS/storage/logs/" . date('Y-m-d') . "-API_LOGS.log";
						if (file_exists($errorlogfile)) {
							ini_set('error_log', $errorlogfile);
						} else {
							$errfh = fopen($errorlogfile, "a+");
							if (is_resource($errfh)) {
								ini_set('error_log', $errorlogfile);
								fclose($errfh);
							}
						}
						error_log("~~~~~~~  URL   ".$url." ~~~~~~ ".$rosterid, 0); 
		
           // $json = json_decode(file_get_contents($url, null), true);
		   $ch = curl_init();
				curl_setopt($ch, CURLOPT_URL, $url);
				curl_setopt($ch,CURLOPT_USERAGENT,"Mozilla/5.0 (Windows; U; Windows NT 5.1; en-US; rv:********) Gecko/20080311 Firefox/********");
				curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
				$result = curl_exec($ch);
				curl_close($ch);
				$json = json_decode($result,true);
				 
            if (count($json['routes']) > 0) {
                $route = $json['routes'][0]['legs'];
                $count = count($route);
                $distance = 0;
                $time_mins = 0;
                for ($i = 0; $i < $count; $i++) {
                    $distance = $distance + $json['routes'][0]['legs'][$i]['distance']['value'];
                    $time_mins = $time_mins + $json['routes'][0]['legs'][$i]['duration']['value'];
                }
				error_log("~~~~~~~  Test Result   ".$url." ~~~~~~ ". $distance. "~~~~~~~~~ ".$rosterid, 0);
 
                return $distance . '-' . $time_mins;
                // return $distance;
            } else {
                return 0;
            }
        } else {
            return 0;
        }
    }
	public function First_Last_UpdateShedDistance($rosterid,$tripstatus) 
	{
		   // First_Last_UpdateShedDistance
			ini_set("max_execution_time","0");
			$dbname = Auth::user()->dbname;
     			$branchid=Auth::user()->BRANCH_ID;
				$sql = "SELECT DS.BILLING_ID,RP.LOCATION_ID,RP.ROSTER_ID,RP.ROUTE_ORDER,concat(L.LATITUDE,',',L.LONGITUDE) as first_last_point,DS.CAB_ID,B.LAT,B.`LONG`,L.LATITUDE,L.LONGITUDE,CONCAT(D.DRIVERS_ADDR_LAT,',',D.DRIVERS_ADDR_LONG) as driver_start,D.DRIVERS_ADDR_LAT,D.DRIVERS_ADDR_LONG from roster_passengers RP
				inner join driver_billing_summary DS on DS.ROSTER_ID=RP.ROSTER_ID
				inner join locations L on L.LOCATION_ID=RP.LOCATION_ID
				inner join cab C on C.CAB_ID=DS.CAB_ID
				inner join drivers D on D.DRIVERS_ID=C.DRIVER_ID
				inner join branch as B ON B.BRANCH_ID='" . $branchid . "' 
				where (RP.ROSTER_PASSENGER_STATUS & 32 || RP.ROSTER_PASSENGER_STATUS & 128 || RP.ROSTER_PASSENGER_STATUS & 64)  
				and RP.ROSTER_ID='".$rosterid."' and D.DRIVERS_ADDR_LAT !=0  ORDER BY RP.ROUTE_ORDER desc limit 1 ";
					
				$data = DB::connection("$dbname")->select($sql);
				if(count($data)>0)
				{
					foreach ($data as $val) 
					{
						$id = $val->BILLING_ID;
						if($tripstatus=='FT')
						{
							$source = $val->DRIVERS_ADDR_LAT.",".$val->DRIVERS_ADDR_LONG;
							$destination = $val->LATITUDE.",".$val->LONGITUDE;
						}
						else if($tripstatus=='LT')
						{						
							$source = $val->LATITUDE.",".$val->LONGITUDE;
							$destination = $val->DRIVERS_ADDR_LAT.",".$val->DRIVERS_ADDR_LONG;
						}
						if(MyHelper::$RRD_SHED_DEFAULT_ENABLE=='Y')
						{
							$km=MyHelper::$RRD_SHED_DEFAULT_KM;
						}
						else
						{
							$distance = $this->Getkm_two_point_shed($source, $destination);
						    $km = $distance / 1000;
						}
						
						if($km != 0)
						{
							return $update2 = DB::connection("$dbname")->table("driver_billing_summary")->WHERE("ROSTER_ID", "=", $rosterid)->WHERE("ACTIVE", "=", 1)->update(array("GOOGLE_SHED_KM" => $km,"SHED_KM"=>$km,"UPDATED_AT" => date("Y-m-d H:i:s")));
							//echo "<br/> UPDATE driver_billing_summary SET GOOGLE_SHED_KM='$km',SHED_KM='$km',UPDATED_AT='".date("Y-m-d H:i:s")."',UPDATED_BY='Manual' WHERE BILLING_ID=$id and BRANCH_ID = '".$branchid."';";
						}
					}
				}
	    
    } 
	public function Getkm_two_point_shed($origin, $destination) {
        if ($origin != '' && $destination != '') {
            $API_DIRECTIONS = MyHelper::$API_DIRECTIONS;
            $API_CLIENTID = MyHelper::$API_CLIENTID;
            $API_KEY = MyHelper::$API_KEY;
			$TRIPCLOSE_API_KEY = MyHelper::$TRIP_CLOSE_API_KEY;
//            $url = $this->signUrl($API_DIRECTIONS . "origin=" . $origin . "&destination=" . $destination . "&sensor=false&client=" . $API_CLIENTID, $API_KEY);
//            $url = $API_DIRECTIONS . "origin=" . $origin . "&destination=" . $destination . "&sensor=false&client=" . $API_CLIENTID;
           //  $url = $this->API_DIRECTIONS . "origin=" . $origin . "&destination=" . $destination . "&sensor=false";
		  
            $url = $this->API_DIRECTIONS . "origin=" . $origin . "&destination=" . $destination . "&key=".$API_KEY;
			$ch = curl_init();
				curl_setopt($ch, CURLOPT_URL, $url);
				curl_setopt($ch,CURLOPT_USERAGENT,"Mozilla/5.0 (Windows; U; Windows NT 5.1; en-US; rv:********) Gecko/20080311 Firefox/********");
				curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
				$result = curl_exec($ch);
				curl_close($ch);
				$json = json_decode($result,true);
            //$json = json_decode(file_get_contents($url), true);
            if (count($json['routes']) > 0) {
                $route = $json['routes'][0]['legs'];
                $count = count($route);
                $distance = 0;
                $time_mins = 0;
                for ($i = 0; $i < $count; $i++) {
                    $distance = $distance + $json['routes'][0]['legs'][$i]['distance']['value'];
                    $time_mins = $time_mins + $json['routes'][0]['legs'][$i]['duration']['value'];
                }
                return $distance ;
            } else {
                return 0;
            }
        } else {
            return 0;
        }
    }
	public function address_time_check($value,$checktime)
	{
		$ret="";
		$timevalue = unserialize($value);
		foreach ($timevalue as $test) {
			list($t1, $t2, $t3) = $test;
			$ret1 = $this->prop_check_time($t1, $t2, $checktime) ? "yes" : "no";
			if ($ret1 == "yes") {
				$ret = $t3;
			}
		}
		return $ret;
	}
	public function prop_check_time($t1, $t2, $tn) {
		if ($t2 >= $t1) {
			return $t1 <= $tn && $tn <= $t2;
			} else {
			return !($t2 <= $tn && $tn <= $t1);
		}
	}
  
	
	
}
