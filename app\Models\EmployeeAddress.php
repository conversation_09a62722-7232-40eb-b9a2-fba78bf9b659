<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EmployeeAddress extends Model
{
    use HasFactory;

    protected $table = 'employee_address';
    protected $primaryKey = 'ADDRESS_ID';

    public $timestamps = false;

    protected $fillable = [
        'ADDRESS_TYPE',
        'ADDRESS',
        'LATITUDE',
        'LONGITUDE',
        'DISTANCE',
        'SEC_ADDR_DURATION',
        'LOCATION_ID',
        'EMP_AUTO_ID',
        'ACTIVE',
        'CREATED_BY',
        'UPDATED_BY',
        'UPDATED_REMARKS',
        'CREATED_DATE',
        'UPDATED_AT',
    ];

    public function secondaryLocationName()
    {
        return $this->hasOne(Location::class,'LOCATION_ID','LOCATION_ID');
    }
}
