<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Contracts\Validation\Validator;

class BillkmApproveUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'roster_id' => 'required',
            'ApprovedKm' => 'required',
            'billkm_type' => 'required',
            'billkm_type_rem' => 'required',
        ];
    }

    public function messages(): array
    {
        return [
            'roster_id.required' => 'roster_id is required',
            'ApprovedKm.required' => 'ApprovedKm is required',
            'billkm_type.required' => 'billkm_type is required',
            'billkm_type_rem.required' => 'billkm_type_rem is required',
        ];
    }

    public function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json([
            'success' => false,
            'validation_error' => true,
            'message' => 'Validation errors',
            'data' => $validator->errors()
        ]));
    }
}
