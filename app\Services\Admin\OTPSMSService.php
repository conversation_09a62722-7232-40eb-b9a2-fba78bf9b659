<?php

namespace App\Services\Admin;

use App\Helpers\CommonFunction;
use App\Helpers\MyHelper;
use Carbon\Carbon;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use App\Models\Vendor;
use App\Models\Cab_allocation;
use App\Models\Roster;
use App\Models\RosterPassenger;
use App\Models\Cab;
use App\Models\Vehicle;
use App\Models\VehicleModel;
use App\Models\Driver;
use App\Models\property;
use App\Models\Driver_Billing_Summary;
use App\Http\Controllers\MaskNumberClearController;
use App\Http\Controllers\ElasticController;

class OTPSMSService
{
    protected CommonFunction $commonFunction;
    protected ?Branch $currentBranch = null;

    public function __construct(CommonFunction $commonFunction)
    {
        $this->commonFunction = $commonFunction;
    }
	     
    
    public function otp_sms(): FoundationApplication|Response|ResponseFactory
    {
        try {
            $branch_id = Auth::user()->BRANCH_ID;
            $userid = Auth::user()->id;
            $db_name = Auth::user()->dbname;
            $RS_ACTIVE=MyHelper::$RS_ACTIVE;
            $curdate=date('Y-m-d');
            $active=true;
           $authUser = Auth::user();
           $RS_NEWROSTER=MyHelper::$RS_NEWROSTER;
            $RS_TOTALALLOT=MyHelper::$RS_TOTALALLOT;
            $RS_TOTALACCEPT=MyHelper::$RS_TOTALACCEPT;
            $RS_TOTALEXECUTE=MyHelper::$RS_TOTALEXECUTE;

           $employee_roster = DB::connection($db_name)->table('rosters AS RS')
           ->select('RS.ROSTER_ID', 'RS.ROUTE_ID', 'RS.TRIP_TYPE', 'RS.ESTIMATE_END_TIME', 'RS.ESTIMATE_START_TIME')
           ->where('RS.BRANCH_ID', $branch_id)
           ->where('RS.ACTIVE', $RS_ACTIVE)
           ->whereIn('RS.ROSTER_STATUS', [$RS_NEWROSTER, $RS_TOTALEXECUTE, $RS_TOTALALLOT, $RS_TOTALACCEPT])
           ->where(function ($query) use ($curdate) {
               $query->whereDate('RS.ESTIMATE_END_TIME', $curdate)
                     ->orWhereDate('RS.ESTIMATE_START_TIME', $curdate);
           })->get();

           $escort_roster = DB::connection($db_name)->table('route_escorts as RE')
            ->select('ES.ESCORT_ID', 'ES.ESCORT_NAME', 'ES.ESCORT_MOBILE', 'RE.ROSTER_ID', 'RE.ESCORT_ID', 'RE.STATUS', 'R.ROUTE_ID')
            ->join('rosters as R', 'R.ROSTER_ID', '=', 'RE.ROSTER_ID')
            ->join('escorts as ES', 'ES.ESCORT_ID', '=', 'RE.ESCORT_ID')
            ->where(function($query) use ($curdate) {
                 $query->whereDate('R.ESTIMATE_END_TIME', $curdate)
                     ->orWhereDate('R.ESTIMATE_START_TIME', $curdate);
            })->where('ES.ACTIVE', '=', $RS_ACTIVE)
            ->where('ES.BRANCH_ID', '=', $branch_id)
            ->get();

            $driver_data = DB::connection($db_name)->table('drivers as D')
            ->select('D.DRIVERS_ID', 'D.DRIVERS_NAME', 'D.DRIVER_MOBILE', 'C.CAB_ID')
            ->join('cab as C', 'C.DRIVER_ID', '=', 'D.DRIVERS_ID')
            ->where('C.BRANCH_ID', '=', $branch_id)
            ->where('C.ACTIVE', '=', $RS_ACTIVE)
            ->where('D.ACTIVE', '=', $RS_ACTIVE)
            ->get();

            $employee_data=DB::table("employees")->select("EMPLOYEES_ID","NAME","MOBILE")
					->where("BRANCH_ID","=",$branch_id)
					->where("ACTIVE","=",'1')
					->get();

					foreach($employee_data as &$empdata)
					{
						// $EMPLOYEESS_ID = $empdata->EMPLOYEES_ID;
						//$empdata->NAME = $this->commonFunction->AES_DECRYPT($empdata->NAME,env('AES_ENCRYPT_KEY'));
						//$empdata->MOBILE = $this->commonFunction->AES_DECRYPT($empdata->MOBILE,env('AES_ENCRYPT_KEY'));
					}

        
            return response([
                'success' => true,
                'status' => 3,
                'Employee_Roster' => $employee_roster,
                'Escort_Roster' => $escort_roster,
                'driver_data' => $driver_data,
                'empdata' => $employee_data,
            ]);
        } catch (\Throwable|\Exception $e) {
            DB::rollBack();
            $this->commonFunction->logException($e);
            $message = $active ? 'Manual otp  status Unsuccessful' : 'Manual otp  status Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ],500);
        }
    } 
    public function otp_roster_sms_details($request): FoundationApplication|Response|ResponseFactory
    {
        try {
            $branch_id = Auth::user()->BRANCH_ID;
            $userid = Auth::user()->id;
            $db_name = Auth::user()->dbname;
            $RS_ACTIVE=MyHelper::$RS_ACTIVE;
            $curdate=date('Y-m-d');
            $active=true;
           $authUser = Auth::user();
            $selected_roster_id=$request->selected_roster_id;

        $trip_roster=DB::connection($db_name)->table("rosters")->select("TRIP_TYPE")->where("ROSTER_ID","=",$selected_roster_id)->get();
        $trip_type=$trip_roster[0]->TRIP_TYPE;
        $category=$trip_type=='P'?'Pickup':'Drop';
           $data = DB::connection($db_name)->table('roster_passengers as RP')
            ->select(
                'RS.TRIP_TYPE', 
                'RS.ROUTE_ID', 
                'RS.CAB_ID', 
                'RS.ESTIMATE_START_TIME', 
                'RS.ESTIMATE_END_TIME', 
                'RS.PASSENGER_ALLOT_COUNT', 
                'RP.ROSTER_ID', 
                'RP.ROSTER_PASSENGER_ID', 
                'RP.PASSENGER_MASK_NUMBER',
                'RP.EMPLOYEE_ID', 
                'EM.EMPLOYEES_ID',
                'EM.GENDER', 
                'EM.NAME', 
                'EM.MOBILE',
                'O.OTP', 
                'O.VERIFIED_STATUS'
            )
            ->join('rosters as RS', 'RS.ROSTER_ID', '=', 'RP.ROSTER_ID')
            ->join('employees as EM', 'EM.EMPLOYEES_ID', '=', 'RP.EMPLOYEE_ID')
            ->join('otp_verification as O', 'O.ROSTER_PASSENGER_ID', '=', 'RP.ROSTER_PASSENGER_ID')
            ->where('RP.ROSTER_ID', $selected_roster_id)
            ->where('O.OTP_CATEGORY', $category)
            ->where('EM.BRANCH_ID', $branch_id)
            ->where('O.VERIFIED_STATUS', '!=', 2)
            ->where('RP.ACTIVE', $RS_ACTIVE);
          //  ->get();

            $perPage = $request->input('per_page', MyHelper::$PAGINATION_PER_PAGE);
            $filterModel = $request->input('filterModel');
            if ($filterModel) {
                foreach ($filterModel as $field => $filter) {
                    if (isset($filter['filter']) && $filter['filter'] !== '') {
                        $value = $filter['filter'];
                        $type = $filter['type'];

                        switch ($field) {
                            case 'ROUTE_ID':
                                $data->where('RS.ROUTE_ID', 'like', "%{$value}%");
                                break;
                            case 'EMPLOYEE_ID':
                                $data->where('RP.EMPLOYEE_ID', 'like', "%{$value}%");
                                break;
                        }
                    }
                }
            }


            if ($request->has('orderBy') && !empty($request->input('orderBy'))) {
                $orderBy = $request->input('orderBy');
                $order = $request->input('order', 'asc');
                $data->orderBy($orderBy, $order);
            } else {
                $data->orderBy('RP.ROSTER_ID');
            }


            if ($perPage === MyHelper::$PAGINATION_PER_PAGE_ALL) {
                $paginateddata = $data->paginate($data->count());
            } else {
                $perPage = is_numeric($perPage) ? (int)$perPage : MyHelper::$PAGINATION_PER_PAGE;
                $paginateddata = $data->paginate($perPage);
            }
            
            return response([
                'success' => true,
                'status' => 3,
                'roster_sms_details' => $paginateddata,
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            DB::rollBack();
            $message = $active ? 'Roster Otp SMS  status Unsuccessful' : 'Roster Otp SMS  status Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ],500);
        }
    } 
   
    public function multi_push_message($request): FoundationApplication|Response|ResponseFactory
    {
        try {
            $branch_id = Auth::user()->BRANCH_ID;
            $userid = Auth::user()->id;
            $db_name = Auth::user()->dbname;
            $RS_ACTIVE=MyHelper::$RS_ACTIVE;
            $curdate=date('Y-m-d');
            $active=true;
            $authUser = Auth::user();
            $selected_passenger_id=implode(',',$request->selected_passenger_id);
            $date = Carbon::now();
            $currentTime = $date->format("Y-m-d H:i:s");

            // $res=DB::table("branch")->select("BRANCH_NAME")->where("BRANCH_ID","=",$branch_id)->get();
			// $sitename=$res[0]->BRANCH_NAME;

            $propertie = property::on($db_name)->where([['BRANCH_ID', '=', $branch_id], ['ACTIVE', '=', 1]])->get();
            for($i=0;$i<count($propertie);$i++)
            {
                $PROPERTIE_NAME = $propertie[$i]->PROPERTIE_NAME;
                $PROPERTIE_VALUE = $propertie[$i]->PROPERTIE_VALUE;

                switch ($PROPERTIE_NAME) {
                         case 'HELPLINE NO':
                        $HELP_LINE = $PROPERTIE_VALUE;
                        break;
                        case 'SMS_LAST_TAG':
                        $SMS_LAST_TAG = $PROPERTIE_VALUE;
                        break;
                        case 'SMS TAG':
                        $SMS_TAG = $PROPERTIE_VALUE;
                        break;
                    default:
                        break;
                }
            }
            $results = RosterPassenger::on($db_name)->select(
                'roster_passengers.ROSTER_PASSENGER_ID',
                'roster_passengers.ROSTER_ID',
                'vehicles.VEHICLE_REG_NO',
                'otp_verification.OTP',
                'locations.LOCATION_NAME',
                'vehicle_models.MODEL',
                'rosters.TRIP_TYPE',
                'employees.MOBILE'
            )
            ->join('rosters', 'rosters.ROSTER_ID', '=', 'roster_passengers.ROSTER_ID')
            ->join('cab', 'cab.CAB_ID', '=', 'rosters.CAB_ID')
            ->join('vehicles', 'vehicles.VEHICLE_ID', '=', 'cab.VEHICLE_ID')
            ->join('otp_verification', 'otp_verification.ROSTER_PASSENGER_ID', '=', 'roster_passengers.ROSTER_PASSENGER_ID')
            ->join('locations', 'locations.LOCATION_ID', '=', 'roster_passengers.LOCATION_ID')
            ->join('vehicle_models', 'vehicle_models.VEHICLE_MODEL_ID', '=', 'vehicles.VEHICLE_MODEL_ID')
            ->join('employees', function($join) use ($branch_id) {
                $join->on('employees.EMPLOYEES_ID', '=', 'roster_passengers.EMPLOYEE_ID')
                ->where('employees.BRANCH_ID', '=', $branch_id);
            })
            ->whereIn('roster_passengers.ROSTER_PASSENGER_ID', [$selected_passenger_id])
            ->where('rosters.BRANCH_ID', '=', $branch_id)
            ->get();
            $site_name=$authUser->branch->BRANCH_NAME;
            foreach($results as $value)
            {
                $triptype=$value->TRIP_TYPE;
                $otp=$value->OTP;

               $empmobile='7200389436';
              //  $empmobile=$this->commonFunction->AES_DECRYPT($value->MOBILE,env('AES_ENCRYPT_KEY'));
                if($value->count())
                {
                    $cab_det='cab details:'.$value->VEHICLE_REG_NO.'-'.$value->MODEL;
                }
                else
                {
                    $cab_det='';
                }
                if($triptype=='P')
                {
                    /* $message = 'Dear Mr/Ms ' .$empname . ' Cab has arrived at your destination.Board in cab within 3Mts. Enter OTP ' . $otp . ' in the GPS Device available with driver. Thanks'; */
                    $message = 'Hi,Cab has arrived at your destination.Board in cab within 3Mts.'.$cab_det.' Enter OTP ' . $otp . ' in driver App. Thanks.'.$SMS_LAST_TAG;
                }
                else
                {
                    $message = "Hi,Cab has started from " . $sitename . ".  Enter OTP " . $otp . " in driver App at your drop place.".$SMS_LAST_TAG;
                    /* $message = "Dear " . $empname . ", Cab has started from " . $sitename . ". Enter OTP " . $otp . " in GPS device at your drop place. Thanks"; */
                    //$message = "Hi,Cab has started from " . $sitename . ". ".$cab_det." Enter OTP " . $otp . " in driver App at your drop place. Thanks";
                }
                
                $qry = "INSERT INTO sms (BRANCH_ID,ORIGINATOR,RECIPIENT,MESSAGE,SENT_DATE,CREATED_BY,CREATED_DATE)VALUES('" . $branch_id . "','".$SMS_TAG."','" . $empmobile . "','" . $message . "','1900-01-01 00:00:00','$userid','$currentTime')";
                $res = DB::connection($db_name)->insert($qry);
            }
            DB::commit();
            
            return response([
                'success' => true,
                'status' => 3,
                'message' => "Successfully inserted",
            ]);
        } catch (\Throwable|\Exception $e) {
            DB::rollBack();
            $this->commonFunction->logException($e);
            $message = $active ? 'Roster Otp multi push SMS  status Unsuccessful' : 'Roster Otp multi push SMS  status Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ],500);
        }
    } 
   
    public function otp_roster_sms_send($request): FoundationApplication|Response|ResponseFactory
    {
        try {
            $branch_id = Auth::user()->BRANCH_ID;
            $userid = Auth::user()->id;
            $db_name = Auth::user()->dbname;
            $RS_ACTIVE=MyHelper::$RS_ACTIVE;
            $curdate=date('Y-m-d');
            $active=true;
            $date = Carbon::now();
            $currentTime = $date->format("Y-m-d H:i:s");
           $authUser = Auth::user();
           $branch=DB::connection($db_name)->table("branch")->select("BRANCH_NAME")->where("BRANCH_ID","=",$branch_id)->get();
           $sitename=$branch[0]->BRANCH_NAME;
           $selected_passenger_id=$request->selected_passenger_id;

            $result = DB::connection($db_name)->table('roster_passengers as RP')
           ->select(
               'RP.ROSTER_PASSENGER_ID',
               'RP.ROSTER_ID',
               'V.VEHICLE_REG_NO',
               'O.OTP',
               'L.LOCATION_NAME',
               'VM.MODEL',
               'DR.DRIVER_MOBILE','R.TRIP_TYPE',
               'DR.DRIVERS_NAME','EM.MOBILE',
               DB::raw("IF(R.TRIP_TYPE = 'P', RP.ESTIMATE_END_TIME, RP.ESTIMATE_START_TIME) AS Login_date")
           )
           ->join('rosters as R', 'R.ROSTER_ID', '=', 'RP.ROSTER_ID')
           ->join('cab as C', 'C.CAB_ID', '=', 'R.CAB_ID')
           ->join('vehicles as V', 'V.VEHICLE_ID', '=', 'C.VEHICLE_ID')
           ->join('otp_verification as O', 'O.ROSTER_PASSENGER_ID', '=', 'RP.ROSTER_PASSENGER_ID')
           ->join('locations as L', 'L.LOCATION_ID', '=', 'RP.LOCATION_ID')
           ->join('vehicle_models as VM', 'VM.VEHICLE_MODEL_ID', '=', 'V.VEHICLE_MODEL_ID')
           ->join('employees as EM', 'EM.EMPLOYEES_ID', '=', 'RP.EMPLOYEE_ID')
           ->leftJoin('drivers as DR', 'DR.DRIVERS_ID', '=', 'C.DRIVER_ID')
           ->where('RP.ROSTER_PASSENGER_ID', $selected_passenger_id)
           ->where('R.BRANCH_ID', $branch_id)
           ->get();
           $propertie = property::on($db_name)->where([['BRANCH_ID', '=', $branch_id], ['ACTIVE', '=', 1]])->get();
           $trip_type='';$otp='';
           for($i=0;$i<count($propertie);$i++)
           {
               $PROPERTIE_NAME = $propertie[$i]->PROPERTIE_NAME;
               $PROPERTIE_VALUE = $propertie[$i]->PROPERTIE_VALUE;

               switch ($PROPERTIE_NAME) {
                        case 'HELPLINE NO':
                       $HELP_LINE = $PROPERTIE_VALUE;
                       break;
                       case 'SMS_LAST_TAG':
                       $SMS_LAST_TAG = $PROPERTIE_VALUE;
                       break;
                       case 'SMS TAG':
                       $SMS_TAG = $PROPERTIE_VALUE;
                       break;
                   default:
                       break;
               }
           }
           //$this->sms_tag_value='NTLRAC';
           if(count($result))
           {
               $cab_det='cab details:'.$result[0]->VEHICLE_REG_NO.'-'.$result[0]->MODEL;
               $DRIVER_MOBILE=$result[0]->DRIVER_MOBILE;
              
              // $mobile=$this->CommonFunction->AES_DECRYPT($result[0]->MOBILE,env('AES_ENCRYPT_KEY'));
               $otp=$result[0]->OTP;
               $trip_type=$result[0]->TRIP_TYPE;
               $VEHICLE_REG_NO=str_replace(' ', '', $result[0]->VEHICLE_REG_NO);
               $Login_date=date("d/m/Y",strtotime($result[0]->Login_date));
               $Login_date_time=date("H:i",strtotime($result[0]->Login_date));
           }
           else
           {
               $cab_det='';
           }
           $mobile=7200389436;
           if($branch_id==32)
           {
               $SMS_LAST_TAG=' RRD';
               if($trip_type=='P')
               {
                   $MESSAGE = " Hi,Cab has arrived at your destination.Board in cab within 3Mts.  Enter OTP " . $otp . " in driver App.".$SMS_LAST_TAG;
                   
                   //$message="Dear Customer, Your booking for ".$Login_date." ".$Login_date_time." is confirmed. Reference no: $VEHICLE_REG_NO,$DRIVER_MOBILE otp-$otp. NTL";
               }
               else
               {
                   //$message="Dear Customer, Your booking for ".$Login_date." ".$Login_date_time." is confirmed. Reference no: $VEHICLE_REG_NO,$DRIVER_MOBILE otp-$otp. NTL";
                   
                   $MESSAGE = "Hi,Cab has started from " . $sitename . ".  Enter OTP " . $otp . " in driver App at your drop place.".$SMS_LAST_TAG;
               }
           }
           else
           {
               //$mobile=9543417214;
               if($trip_type=='P')
               {
                   $message="Hi,Cab has arrived at your destination.Board in cab within 3Mts.$VEHICLE_REG_NO Enter OTP $otp in driver App. Thanks.". $SMS_LAST_TAG;
                   
               }
               else
               {
                   $message = "Hi,Cab has started from " . $sitename . ".  Enter OTP " . $otp . " in driver App at your drop place.".$SMS_LAST_TAG;
                  
               }
               //$message="Dear Customer, Your booking for ".$Login_date." ".$Login_date_time." is confirmed. Reference no: $VEHICLE_REG_NO,$DRIVER_MOBILE otp-$otp. NTL";
               //$message="Dear Customer, Your booking for ".$Login_date." ".$Login_date_time." is confirmed. Reference no: $VEHICLE_REG_NO,$DRIVER_MOBILE otp-$otp. NTL";
           }
           
           $qry = "INSERT INTO sms (BRANCH_ID,ORIGINATOR,RECIPIENT,MESSAGE,SENT_DATE,CREATED_BY,CREATED_DATE)VALUES('" . $branch_id . "','".$SMS_TAG."','" . stripslashes(trim($mobile)) . "','" . $message . "','1900-01-01 00:00:00','$userid','$currentTime')";
           $res = DB::connection($db_name)->insert($qry);
       
           DB::commit();
            return response([
                'success' => true,
                'status' => 3,
                'message' => "successfully inserted",
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            DB::rollBack();
            $message = $active ? 'Otp send SMS  status Unsuccessful' : ' Otp send SMS  status Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ],500);
        }
    } 
   
    public function driver_otp_sms($request, bool $active = true): FoundationApplication|Response|ResponseFactory
    {
        try {
            $branch_id = Auth::user()->BRANCH_ID;
            $userid = Auth::user()->id;
            $db_name = Auth::user()->dbname;
            $RS_ACTIVE=MyHelper::$RS_ACTIVE;

           $authUser = Auth::user();
           $date = Carbon::now();
            
            $currentdate =$date->format("Y-m-d");

           $selected_cab_id=$request->selected_cab_id;
           $data = DB::connection($db_name)->table("drivers as D")
           ->select("D.DRIVERS_ID","D.DRIVERS_NAME","D.DRIVER_MOBILE","C.CAB_ID","O.OTP","O.CREATED_DATE")
           ->join('cab as C', 'C.DRIVER_ID', '=', 'D.DRIVERS_ID')
           ->join('otp_verification as O','O.ROSTER_PASSENGER_ID',"=","C.CAB_ID")
           ->WHERE('O.OTP_CATEGORY', '=','DriverLogin')
           ->WHERE('O.ROSTER_PASSENGER_ID', '=',$selected_cab_id )
           ->WHEREDATE('O.CREATED_DATE','=',$currentdate)
           ->WHERE('O.VERIFIED_STATUS','=',0)                            
           ->orderBy('O.CREATED_DATE', "DESC")->take(1);
          // ->get();
          $perPage = $request->input('per_page', MyHelper::$PAGINATION_PER_PAGE);
          $filterModel = $request->input('filterModel');
          if ($filterModel) {
              foreach ($filterModel as $field => $filter) {
                  if (isset($filter['filter']) && $filter['filter'] !== '') {
                      $value = $filter['filter'];
                      $type = $filter['type'];

                      switch ($field) {
                          case 'ROUTE_ID':
                              $data->where('RS.ROUTE_ID', 'like', "%{$value}%");
                              break;
                          case 'EMPLOYEE_ID':
                              $data->where('RP.EMPLOYEE_ID', 'like', "%{$value}%");
                              break;
                      }
                  }
              }
          }


          if ($request->has('orderBy') && !empty($request->input('orderBy'))) {
              $orderBy = $request->input('orderBy');
              $order = $request->input('order', 'asc');
              $data->orderBy($orderBy, $order);
          } else {
              $data->orderBy('O.CREATED_DATE');
          }


          if ($perPage === MyHelper::$PAGINATION_PER_PAGE_ALL) {
              $paginateddata = $data->paginate($data->count());
          } else {
              $perPage = is_numeric($perPage) ? (int)$perPage : MyHelper::$PAGINATION_PER_PAGE;
              $paginateddata = $data->paginate($perPage);
          }
          
          
            return response([
                'success' => true,
                'status' => 3,
                'driver_otp' => $paginateddata,
            ]);
        } catch (\Throwable|\Exception $e) {
            DB::rollBack();
            $this->commonFunction->logException($e);
            $message = $active ? 'Manual Driver otp status Unsuccessful' : 'Manual driver otp status Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ],500);
        }
    } 
    
    public function driver_otp_send_sms($request, bool $active = true): FoundationApplication|Response|ResponseFactory
    {
        try {
            $branch_id = Auth::user()->BRANCH_ID;
            $userid = Auth::user()->id;
            $db_name = Auth::user()->dbname;
            $RS_ACTIVE=MyHelper::$RS_ACTIVE;

            $authUser = Auth::user();
            $date = Carbon::now();
            
            $currentdate =$date->format("Y-m-d");
            $currentTime = date("Y-m-d H:i:s");

           $selected_cab_id=$request->selected_cab_id;

           $driver_otp = DB::connection($db_name)->table("drivers as D")
           ->select("D.DRIVERS_ID","D.DRIVERS_NAME","D.DRIVER_MOBILE","C.CAB_ID","O.OTP","O.CREATED_DATE")
           ->join('cab as C', 'C.DRIVER_ID', '=', 'D.DRIVERS_ID')
           ->join('otp_verification as O','O.ROSTER_PASSENGER_ID',"=","C.CAB_ID")
           ->WHERE('O.OTP_CATEGORY', '=','DriverLogin')
           ->WHERE('O.ROSTER_PASSENGER_ID', '=',$selected_cab_id )
           ->WHEREDATE('O.CREATED_DATE','=',$currentdate)
           ->WHERE('O.VERIFIED_STATUS','=',0)                            
           ->orderBy('O.CREATED_DATE', "DESC")->take(1)
           ->get();
           if(count($driver_otp)>0)
           {
           $otp=$driver_otp[0]->OTP;
           $driver_mobile=$driver_otp[0]->DRIVER_MOBILE;
           $driver_name=$driver_otp[0]->DRIVERS_NAME;

           $propertie = property::on($db_name)->where([['BRANCH_ID', '=', $branch_id], ['ACTIVE', '=', 1]])->get();
				for($i=0;$i<count($propertie);$i++)
				{
					$PROPERTIE_NAME = $propertie[$i]->PROPERTIE_NAME;
					$PROPERTIE_VALUE = $propertie[$i]->PROPERTIE_VALUE;

					switch ($PROPERTIE_NAME) {
							 case 'HELPLINE NO':
                            $HELP_LINE = $PROPERTIE_VALUE;
                            break;
							case 'SMS_LAST_TAG':
							$SMS_LAST_TAG = $PROPERTIE_VALUE;
							break;
                            case 'SMS TAG':
                                $SMS_TAG = $PROPERTIE_VALUE;
                                break;
						default:
							break;
					}
				}
				if($branch_id==32)
                {	
                        $message = "Dear ".$driver_name.",your login OTP is ".$otp."Please use this OTP in ".$otp." App.".$SMS_LAST_TAG;
                }
                else
                {
                        $message = "Dear ".$driver_name . " ,your login OTP is ".$otp."Please use this OTP in ".$otp." App.".$SMS_LAST_TAG;
                }
                
               // $message = "Dear ".stripslashes(trim($request->drivername)).",Your login OTP is ". stripslashes(trim($request->otp)) ." Please use this OTP in ZINGO Driver App.Thanks.".$this->sms_last_tag_value;
				
                $qry = "INSERT INTO sms (BRANCH_ID,ORIGINATOR,RECIPIENT,MESSAGE,SENT_DATE,CREATED_BY,CREATED_DATE)VALUES('" . $branch_id . "','".$SMS_TAG."','" .$driver_mobile. "','" . $message . "','1900-01-01 00:00:00','$userid','$currentTime')";
                $res = DB::connection($db_name)->insert($qry);
                DB::commit();
         
          
            return response([
                'success' => true,
                'status' => 3,
                'message' => "message sent successfully",
            ]);
        }
        else
        {
            return response([
                'success' => false,
                'status' => 3,
                'message' => "message not insert ",
            ]);
        }
        } catch (\Throwable|\Exception $e) {
            DB::rollBack();
            $this->commonFunction->logException($e);
            $message = $active ? 'Manual driver otp send status Unsuccessful' : 'Manual driver otp send status Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ],500);
        }
    } 
    
    
    public function escort_otp_sms($request, bool $active = true): FoundationApplication|Response|ResponseFactory
    {
        try {
            $branch_id = Auth::user()->BRANCH_ID;
            $userid = Auth::user()->id;
            $db_name = Auth::user()->dbname;
            $RS_ACTIVE=MyHelper::$RS_ACTIVE;

            $authUser = Auth::user();
            $date = Carbon::now();
            
            $currentdate =$date->format("Y-m-d");
            $currentTime = date("Y-m-d H:i:s");

           $selected_roster_id=$request->selected_roster_id;

           
           $data = DB::connection($db_name)->table('escorts as ES')
           ->select(
               'ES.ESCORT_ID',
               'ES.ESCORT_MOBILE',
               'ES.ESCORT_NAME',
               'O.OTP',
               'O.CREATED_DATE',
               'RP.ROSTER_PASSENGER_ID',
               'E.MOBILE',
               'E.NAME',
               'RE.STATUS',
               DB::Raw('if(RE.STATUS=3,true,false) as escort_btn_is_enabled'),
              DB::raw(" CASE 
               WHEN RE.STATUS = 1 THEN 'Escort Created'
               WHEN RE.STATUS = 2 THEN 'Escort Assigned'
               WHEN RE.STATUS = 3 THEN 'send_ms_button_enable'
               WHEN RE.STATUS = 4 THEN 'OTP Verified'
               WHEN RE.STATUS = 5 THEN 'Escort Interchanged'
               WHEN RE.STATUS = 6 THEN 'Escort Removed'
               
           END AS sts"))
           ->join('route_escorts as RE', 'RE.ESCORT_ID', '=', 'ES.ESCORT_ID')
           ->join('roster_passengers as RP', 'RP.ROSTER_ID', '=', 'RE.ROSTER_ID')
           ->join('otp_verification as O', 'O.ROSTER_PASSENGER_ID', '=', 'RP.ROSTER_PASSENGER_ID')
           ->join('employees as E', function($join) use ($selected_roster_id, $branch_id) {
               $join->on('E.EMPLOYEES_ID', '=', 'RE.EMPLOYEE_ID')
                    ->where('RE.ROSTER_ID', '=', $selected_roster_id)
                    ->where('E.BRANCH_ID', '=', $branch_id);
           })
           ->where('O.OTP_CATEGORY', 'Escort')
           ->where('O.ROSTER_PASSENGER_ID', '=', DB::raw('RP.ROSTER_PASSENGER_ID'))
          // ->orderByDesc('O.CREATED_DATE')
           ->limit(1);
           //->get();
           $perPage = $request->input('per_page', MyHelper::$PAGINATION_PER_PAGE);
          $filterModel = $request->input('filterModel');
          if ($filterModel) {
              foreach ($filterModel as $field => $filter) {
                  if (isset($filter['filter']) && $filter['filter'] !== '') {
                      $value = $filter['filter'];
                      $type = $filter['type'];

                      switch ($field) {
                          case 'ESCORT_MOBILE':
                              $data->where('ES.ESCORT_MOBILE', 'like', "%{$value}%");
                              break;
                          case 'ESCORT_NAME':
                              $data->where('ES.ESCORT_NAME', 'like', "%{$value}%");
                              break;
                      }
                  }
              }
          }


          if ($request->has('orderBy') && !empty($request->input('orderBy'))) {
              $orderBy = $request->input('orderBy');
              $order = $request->input('order', 'asc');
              $data->orderBy($orderBy, $order);
          } else {
              $data->orderBy('O.CREATED_DATE');
          }


          if ($perPage === MyHelper::$PAGINATION_PER_PAGE_ALL) {
              $paginateddata = $data->paginate($data->count());
          } else {
              $perPage = is_numeric($perPage) ? (int)$perPage : MyHelper::$PAGINATION_PER_PAGE;
              $paginateddata = $data->paginate($perPage);
          }
          
       
          
            return response([
                'success' => true,
                'status' => 3,
                'esort_roster' => $paginateddata,
            ]);
        
        } catch (\Throwable|\Exception $e) {
            DB::rollBack();
            $this->commonFunction->logException($e);
            $message = $active ? 'Manual esort otp send status Unsuccessful' : 'Manual escort otp send status Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ],500);
        }
    } 
    

    public function escort_otp_sms_send($request, bool $active = true): FoundationApplication|Response|ResponseFactory
    {
        try {
            $date = Carbon::now();
                $currentTime = $date->format("Y-m-d H:i:s");
                $id = Auth::user()->id;
                $db_name = Auth::user()->dbname;
				$branch_id=Auth::user()->BRANCH_ID;
				$propertie = property::on($db_name)->where([['BRANCH_ID', '=', $branch_id], ['ACTIVE', '=', 1]])->get();
				for($i=0;$i<count($propertie);$i++)
				{
					$PROPERTIE_NAME = $propertie[$i]->PROPERTIE_NAME;
					$PROPERTIE_VALUE = $propertie[$i]->PROPERTIE_VALUE;

					switch ($PROPERTIE_NAME) {
							 case 'HELPLINE NO':
                            $HELP_LINE = $PROPERTIE_VALUE;
                            break;
							case 'SMS_LAST_TAG':
							$SMS_LAST_TAG = $PROPERTIE_VALUE;
							break;
                            case 'SMS TAG':
                                $SMS_TAG = $PROPERTIE_VALUE;
                                break;
						default:
							break;
					}
				}
				 if($branch_id==32)
				 {
					 $message='Hi  , security escort as been alloted for your route. Escort OTP - '.$request->selected_otp.'. Need Support? Helpline -$HELP_LINE. '.$SMS_LAST_TAG;
				 }
				 else
				 {
					 $message = "Hi,Security escort is alloted for your route. Escort OTP ".$request->selected_otp.". Need Support? Helpline $HELP_LINE.".$SMS_LAST_TAG;
				 }
                $qry = "INSERT INTO sms (BRANCH_ID,ORIGINATOR,RECIPIENT,MESSAGE,SENT_DATE,CREATED_BY,CREATED_DATE)VALUES('" . $branch_id . "','".$SMS_TAG."','" . stripslashes(trim($request->selected_emp_mobile)) . "','" . $message . "','1900-01-01 00:00:00','$id','$currentTime')";
                $res = DB::connection($db_name)->insert($qry);
          
            return response([
                'success' => true,
                'status' => 3,
                'message' => "message sent successfully",
            ]);
        
    
        } catch (\Throwable|\Exception $e) {
            DB::rollBack();
            $this->commonFunction->logException($e);
            $message = $active ? 'Manual escort otp send status Unsuccessful' : 'Manual escort otp send status Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ],500);
        }
    } 
    public function employee_login_otp($request, bool $active = true): FoundationApplication|Response|ResponseFactory
    {
        try {
            $branch_id = Auth::user()->BRANCH_ID;
            $userid = Auth::user()->id;
            $db_name = Auth::user()->dbname;
            $RS_ACTIVE=MyHelper::$RS_ACTIVE;

            $authUser = Auth::user();
            $date = Carbon::now();
            
            $currentdate =$date->format("Y-m-d");
            $currentTime = date("Y-m-d H:i:s");

           $selected_emp_mobile=$request->selected_emp_mobile;
           
             $otpArr = [];

				$web_result = DB::connection($db_name)->table("users")
                ->select("created_password")				 
				->WHERE('name', '=',$selected_emp_mobile)
				->WHERE('BRANCH_ID', '=',$branch_id )
				->WHERE('employee_set_password','=',0)
				->orderBy('id', "DESC")->take(1)
				->get();
				if(count($web_result) > 0)
				{
					foreach($web_result as $web_otp)
					{
						$otpArr[] = ['application' => 'Web','otp' => $web_otp->created_password];
					}
				}

				$mobile_result = DB::connection($db_name)->table("otp_verification")->select("OTP")
				->WHERE('OTP_CATEGORY', '=','EmpLogin')
				->WHERE('MOBILE_NO', '=',$selected_emp_mobile )
				->WHEREDATE('CREATED_DATE','=',$currentdate)
				->WHERE('VERIFIED_STATUS','=',0)
				->orderBy('CREATED_DATE', "DESC")->take(1)
				->get();

				if(count($mobile_result) > 0)
				{
					foreach($mobile_result as $mobile_otp)
					{
						$otpArr[] = ['application' => 'Mobile','otp' => $mobile_otp->OTP];
					}
				}
       
          
            return response([
                'success' => true,
                'status' => 3,
                'web_result' => $web_result,
                'mobile_result' => $mobile_result,
                'employee_login_otp' => $otpArr
            ]);
        
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'Manual Employee otp status Unsuccessful' : 'Manual employee otp status Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ],500);
        }
    } 
    
    
    
    /**
     * @throws ConnectionException
     */
    public function dataForCreateManualOTP(): FoundationApplication|Response|ResponseFactory
    {

        try {
                $user = Auth::user();
                $RS_ACTIVE = MyHelper::$RS_ACTIVE;
                $db_name = Auth::user()->dbname;
                $branch_id = Auth::user()->BRANCH_ID;
                $vendor_id = Auth::user()->vendor_id;
                $reason = DB::connection($db_name)->table("reason_master")->select('REASON', 'REASON_ID')
                                ->where([["active", "=", $RS_ACTIVE], ["CATEGORY", "=", 'ManualOTP'], ["BRANCH_ID", "=", $branch_id]])->get();
                $reset_reason = DB::connection($db_name)->table("reason_master")->select('REASON', 'REASON_ID')
                                ->where([["active", "=", $RS_ACTIVE], ["CATEGORY", "=", 'NoshowReset'], ["BRANCH_ID", "=", $branch_id]])->get();
                if (Auth::user()->user_type == MyHelper::$ADMIN) {
                    $vendor_list = Vendor::on($db_name)->where('ACTIVE', '=', $RS_ACTIVE)->where("BRANCH_ID", "=", $branch_id)->get();
                } else {
                    $vendor_list = Vendor::on($db_name)->where('ACTIVE', '=', $RS_ACTIVE)->where("VENDOR_ID", "=", $vendor_id)->get();
                }
                
                return response([
                'success' => true,
                'status' => 3,
                'vendor_list' => $vendor_list,
                'reason' => $reason,
                'reset_reason' => $reset_reason,
                
            ]);
        } catch (\Throwable|\Exception $e) {
            DB::rollBack();
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Data For Create for Manual OTP  Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ],500);
        }
    }
   
    
	

   
}
