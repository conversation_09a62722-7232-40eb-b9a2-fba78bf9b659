<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;

use App\Services\Admin\EscortCancelService;
use App\Http\Requests\Admin\EscortCancelRequest;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class EscortCancelController extends Controller
{
    protected EscortCancelService $escortCancelService;

    public function __construct(EscortCancelService $escortCancelService)
    {
        $this->escortCancelService = $escortCancelService;
    }

    public function fetch_EscortCancel_RouteData(): FoundationApplication|Response|ResponseFactory
    {
        return $this->escortCancelService->fetch_EscortCancel_RouteData();
    }

    public function fetch_EscortCancel_RouteDetails(EscortCancelRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->escortCancelService->fetch_EscortCancel_RouteDetails($request);
    }


    public function cancel_EscortRoute(EscortCancelRequest $request): ResponseFactory|Application|Response
    {
        return $this->escortCancelService->cancel_EscortRoute($request);
    }
}
