<?php

namespace App\Http\Controllers\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Services\SuperAdmin\BranchService;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Requests\SuperAdmin\BranchDeleteRequest;
use App\Http\Requests\SuperAdmin\BranchRequest;

class BranchController extends Controller
{

    protected BranchService $branchService;

    public function __construct(BranchService $branchService)
    {
        $this->branchService = $branchService;
    }


    public function index(): FoundationApplication|Response|ResponseFactory
    {
        return $this->branchService->indexBranch();
    }

    public function store(BranchRequest $request): ResponseFactory|Application|Response
    {
        return $this->branchService->storeBranch($request);
    }


    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    public function edit($branchAutoIdCrypt): Application|Response|ResponseFactory
    {
        return $this->branchService->editBranch($branchAutoIdCrypt);
    }

    public function update(BranchRequest $request, $branchAutoIdCrypt): Application|Response|ResponseFactory
    {
        return $this->branchService->updateBranch($request, $branchAutoIdCrypt);
    }
    public function delete(BranchDeleteRequest $request, $branchAutoIdCrypt): FoundationApplication|Response|ResponseFactory
    {
        return $this->branchService->deleteBranch($request, $branchAutoIdCrypt);
    }

    public function branchlistpagination(Request $request): ResponseFactory|Application|Response
    {
        return $this->branchService->paginationBranch($request);
    }

    public function dataForCreate(): FoundationApplication|Response|ResponseFactory
    {
        return $this->branchService->dataForCreateBranch();
    }
}
