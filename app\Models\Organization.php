<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Crypt;

class Organization extends Model
{
    use HasFactory;

    protected $appends = ['id_crypt'];

    protected $table = 'organization';
    protected $primaryKey = 'ORGANIZATIONID';

    protected $fillable = [
        'NAME',
        'LOCATION',
        'LAT',
        'LONG',
        'CREATED_BY',
        'ENTITY_PROPERTIE',
        'ACTIVE',
        'UPDATED_BY',
        'REMARKS',
    ];


    public function createdBy()
    {
        return $this->hasOne(User::class,'id','CREATED_BY');
    }
    public function updatedBy()
    {
        return $this->hasOne(User::class,'id','UPDATED_BY');
    }

    public function getIdCryptAttribute($value)
    {
        return Crypt::encryptString($this->ORGANIZATIONID);
    }
}
