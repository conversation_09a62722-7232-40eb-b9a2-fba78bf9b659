<?php

namespace App\Http\Requests\Admin;

use App\Helpers\CommonFunction;
use Closure;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;

class EmployeeRequest extends FormRequest
{
    protected CommonFunction $commonFunction;

    public function __construct(CommonFunction $commonFunction)
    {
        parent::__construct();
        $this->commonFunction = $commonFunction;
    }

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        $mobileEncrypt = $this->commonFunction->AES_ENCRYPT($this->MOBILE, config('app.aes_encrypt_key'));
        $branchId = $this->BRANCH_ID;

        return [
            'EMPLOYEES_ID' => [
                'required',
                'string',
                Rule::unique('employees', 'EMPLOYEES_ID')->where('<PERSON><PERSON>CH_ID', $branchId),
                $this->checkDeactivatedEmployee($branchId),
            ],
            'BRANCH_ID' => 'required|numeric',
            'NAME' => 'required|string',
            'LNAME' => 'required|string',
            'MOBILE' => [
                'required',
                'numeric',
                $this->checkUniqueMobile($mobileEncrypt, $branchId),
                Rule::unique('users', 'name')
            ],
            'GENDER' => 'required|string',
            'EMP_EMAIL' => 'email',
            'PROJECT_NAME' => 'required|string',
            'LOCATION' => 'required|numeric',
            'ADDRESS' => 'required|string',
            'LATITUDE' => 'required',
            'LONGITUDE' => 'required',
            'addr_type' => 'required',
        ];
    }

    public function messages(): array
    {
        return [
            'EMPLOYEES_ID.required' => 'Employee ID field is required',
            'EMPLOYEES_ID.unique' => 'This Employee ID already exists',
            'NAME.required' => 'Name field is required',
            'MOBILE.numeric' => 'Mobile no is only numeric',
            'MOBILE.unique' => 'This mobile number is already registered',
            'BRANCH_ID.numeric' => 'Branch id no is only numeric',
            'LOCATION.numeric' => 'Location is only numeric',
            'EMP_EMAIL.email' => 'Email format was invalid',
            'PROJECT_NAME.required' => 'Project name field is required',
            'ADDRESS.required' => 'Address field is required',
        ];
    }

    public function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json([
            'success' => false,
            'validation_error' => true,
            'message' => 'Validation errors',
            'data' => $validator->errors()
        ]));
    }

    private function checkDeactivatedEmployee($branchId): Closure
    {
        return function (string $attribute, mixed $value, Closure $fail) use ($branchId) {
            $deactivatedEmployee = DB::table("employees")
                ->where("BRANCH_ID", $branchId)
                ->where("EMPLOYEES_ID", $value)
                ->where("ACTIVE", 2)
                ->exists();

            if ($deactivatedEmployee) {
                $fail("User already exists and is deactivated.");
            }
        };
    }

    private function checkUniqueMobile($mobileEncrypt, $branchId): Closure
    {
        return function (string $attribute, mixed $value, Closure $fail) use ($mobileEncrypt, $branchId) {
            $exists = DB::table('employees')
                ->where('BRANCH_ID', $branchId)
                ->where("MOBILE", $mobileEncrypt)
                ->exists();

            if ($exists) {
                $fail('The mobile number has already been taken.');
            }
        };
    }
}
