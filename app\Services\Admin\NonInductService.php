<?php

namespace App\Services\Admin;

use App\Helpers\CommonFunction;
use App\Helpers\MyHelper;
use Carbon\Carbon;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use App\Models\Vehicle;
use App\Models\Cab;

class NonInductService
{
    protected CommonFunction $commonFunction;
    protected ?Branch $currentBranch = null;

    public function __construct(CommonFunction $commonFunction)
    {
        $this->commonFunction = $commonFunction;
    }
	
	 public function indexNonInduct(): FoundationApplication|Response|ResponseFactory
    {
        $authUser = Auth::user();
       

        try 
		{

            if($authUser->user_type == MyHelper::$ADMIN)
			{
				$vendorid = "";
			} else {
				//$vendorid = "AND R.VENDOR_ID='$vendor_id'";
				$vendorid = $vendor_id;
			}
            $dbname = Auth::user()->dbname;
           
            $vehicle_rep = DB::connection("$dbname")->table("vehicles as V")->select("V.VEHICLE_ID", "V.VEHICLE_MODEL_ID", "V.VEHICLE_REG_NO", "V.VEHICLE_JOIN_DATE", "V.REG_DATE", "V.REG_STATUS", "V.PERMIT_EXPIRY", "V.INSURANCE_EXPIRY", "V.FC_EXPIRY", "V.TAX_EXPIRY", "V.created_at", "V.REMARKS", "V.COMPLIANT_STATUS", "U.name", "VM.MODEL", "V.MILEAGE_KM", "VM.CAPACITY", "V.VEHICLE_JOIN_DATE", "V.REG_DATE")
            ->join("vehicle_models as VM", "VM.VEHICLE_MODEL_ID", "=", "V.VEHICLE_MODEL_ID")
            ->join("users as U", "U.id", "=", "V.CREATED_BY")
            ->join("cab as C", "C.VEHICLE_ID", "=", "V.VEHICLE_ID")
            ->join("devices as D", "D.DEVICE_ID", "=", 'C.DEVICE_ID')
            ->Join("sim as S", "S.SIM_ID", "=", "C.SIM_ID")
            ->Join("vendors as VE", "VE.VENDOR_ID", "=", "C.VENDOR_ID")
            ->Join("reason_master as rm", "rm.REASON_ID", "=","C.TARIFF_TYPE")
            ->where("C.ACTIVE", "=",  MyHelper::$RS_ACTIVE)
            ->when($vendorid, function ($query, $vendorid) {
                return $query->where('C.VENDOR_ID', $vendorid);
            })
            ->where("C.BRANCH_ID", "=", $authUser->BRANCH_ID)
            ->where("C.CAB_STATUS", "=", 1)->orderBy('V.updated_at', 'DESC')->get();

            return response([
                'success' => true,
                'status' => 3,
                'vehicles' => $vehicle_rep,
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Vehicle Index Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ]);
        }
    }

	

    public function storeNonInduct($request): FoundationApplication|Response|ResponseFactory
    {

        try {

            //print_r($request->all());exit;
            DB::beginTransaction();
            $auth_user = Auth::user();
            $dbname = Auth::user()->dbname;
            $branch_id = Auth::user()->BRANCH_ID;
            $id = Auth::user()->id;
            $RS_ACTIVE=MyHelper::$RS_ACTIVE;
            $org_id=$this->commonFunction->getOrgId($auth_user->BRANCH_ID);
            $VEHICLE_NO = $request->VEHICLE_NO;
            $VEHICLE_MODEL_ID = $request->VEHICLE_MODEL_ID;
            $VENDOR_ID = $request->VENDOR_ID;
            $TARIFF_TYPE = $request->TARIFF_TYPE;
            $DRIVER_NAME = $request->DRIVER_NAME;
            $DRIVER_MOBILE = $request->DRIVER_MOBILE;
           
            $current_date = date('Y-m-d H:i:s');
            $stop_date = date('Y-m-d H:i:s', strtotime($current_date . ' +365 day'));
    
            $permit_expiry = $stop_date;
            $insurance_expiry = $stop_date;
            $fc_expiry = $stop_date;
            $tax_expiry = $stop_date;
            $mileage = 0;
            $reg_date = $current_date;
            $join_date = $current_date;
            $LICENCE_EXPIRY = $stop_date;
            $BADGE_EXPIRY = $stop_date;
            
            $query = DB::connection("$dbname")->table("vehicles")->select("VEHICLE_ID")->where("VEHICLE_REG_NO", "=", $VEHICLE_NO)->where("ACTIVE", "=", $RS_ACTIVE)->where("ORG_ID", "=", $org_id)->get();
            $cnt = count($query);

            $driverquery = DB::connection("$dbname")->table("drivers")->select("DRIVER_MOBILE")->where("DRIVER_MOBILE", "=", $DRIVER_MOBILE)->where("ACTIVE", "=", $RS_ACTIVE)->where("ORG_ID", "=", $org_id)->get();
			$device_cnt = count($driverquery);

            if ($cnt > 0 || $device_cnt > 0) {
                return response([
                    'success' => false,
                    'validation_controller' => true,
                    'status' => 4,
                    'message' => 'Vehicle or Driver Already Exist',
                    'error' => $e->getMessage(),
                ],500);
            }
            
            
            
            else {
               
                $insert_vehicle = array("VEHICLE_MODEL_ID" => $VEHICLE_MODEL_ID, "VEHICLE_REG_NO" => $VEHICLE_NO, "VEHICLE_JOIN_DATE" => $join_date, "REG_DATE" => $reg_date, "REG_STATUS" => 1, "PERMIT_EXPIRY" => $permit_expiry, "INSURANCE_EXPIRY" => $insurance_expiry, "FC_EXPIRY" => $fc_expiry, "TAX_EXPIRY" => $tax_expiry, "COMPLIANT_STATUS" => 2, "ACTIVE" => $RS_ACTIVE, "CREATED_BY" => $id, "MILEAGE_KM" => 0, "ORG_ID" => $org_id, "created_at" => date("Y-m-d H:i:s"));
    
                $last_vehicle_id = DB::connection("$dbname")->table("vehicles")->insertGetId($insert_vehicle);
    
                $insert_drivers = array("DRIVERS_NAME" => $DRIVER_NAME, "DRIVERS_ADRESS" => '--', "DRIVER_LOC_LAT" => '0.00', "DRIVER_LOC_LONG" => '0.00', "DRIVER_MOBILE" => $DRIVER_MOBILE, "DRIVER_LICENSE" => '--', "LICENCE_EXPIRY" => $LICENCE_EXPIRY, "BADGE_EXPIRY" => $BADGE_EXPIRY, "COMPLIANT_STATUS" => 2, "DRIVER_IMAGE" => '--', "ACTIVE" => 1, "SHIFT_IN_TIME" => '00:00:00', "SHIFT_OUT_TIME" => '23:59:00', "CREATED_BY" => $id, 'LOCATION_NAME' => '--', "ORG_ID" => $org_id, "created_at" => date("Y-m-d H:i:s"));
                $last_driver_id = DB::connection("$dbname")->table("drivers")->insertGetId($insert_drivers);
                
                $random ="01234567890".mt_rand(100000,999999);
                $insert_device = array("DEVICE_MODEL" => 'Noninducted', "IMEI_NO_1" => $random, "IMEI_NO_2" => '11111111111111111', "CREATED_BY" => $id, "CREATED_DATE" => date("Y-m-d H:i:s"), "updated_at" => date("Y-m-d H:i:s"), "ACTIVE" => $RS_ACTIVE, "ORG_ID" => $org_id);
                $last_device_id = DB::connection("$dbname")->table("devices")->insertGetId($insert_device);

                $random_sim =mt_rand(100000,999999)."012345678900";
                $insert_sim = array("SIM_MOBILE_NO" => $DRIVER_MOBILE, "SIM_SERIAL_NO" => $random_sim, "SIM_PROVIDER" => 'Noninducted', "ACTIVE" => $RS_ACTIVE, "SIM_MAP_STATUS" => "2", "CREATED_BY" => $id, "CREATED_DATE" => date("Y-m-d H:i:s"), "UPDATED_AT" => date("Y-m-d H:i:s"), "ORG_ID" => $org_id);
                $last_sim_id = DB::connection("$dbname")->table("sim")->insertGetId($insert_sim);
    
                $arr = array("VEHICLE_ID" => $last_vehicle_id, "DRIVER_ID" => $last_driver_id, "DEVICE_ID" => $last_device_id, "BRANCH_ID" => $branch_id, "VENDOR_ID" => $VENDOR_ID, "SIM_ID" => $last_sim_id, "CREATED_BY" => $id, "CREATED_DATE" => date("Y-m-d H:i:s"), "updated_at" => date("Y-m-d H:i:s"), "ATTACHMENT_DATE" => date("Y-m-d H:i:s"), "ACTIVE" => $RS_ACTIVE, "TARIFF_TYPE" => $TARIFF_TYPE, "CAB_STATUS" => 1);
    
                $result = Cab::on("$dbname")->insert($arr);
            }
            DB::commit();

           // $vehicleData = $this->prepareVehicleData($request, $auth_user, MyHelper::$RS_ACTIVE, $auth_user->id);
          

            return response([
                'success' => true,
                'status' => 3,
                'message' => 'Non Induct Vehicle Created Successfully',

            ]);

        } catch (\Throwable|\Exception $e) 
		{
            DB::rollBack();
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'validation_controller' => true,
                'status' => 4,
                'message' => 'Non Induct Vehicle Created UnSuccessfully',
                'error' => $e->getMessage(),
            ],500);
        } finally {
            DB::commit();
        }


    }
	
  
    public function dataForCreateNonInduct(): FoundationApplication|Response|ResponseFactory
    {

        try {
            $user = Auth::user();
            $rsActive = MyHelper::$RS_ACTIVE;
            $branchId = $user->BRANCH_ID;

            $models = DB::table('vehicle_models')
                ->select('VEHICLE_MODEL_ID', 'BRAND','MODEL','CAPACITY')
              //  ->where('ACTIVE', $rsActive)
                ->get();
            $vendors = DB::table('vendors')
                ->select('VENDOR_ID', 'NAME','MOBILE','EMAILID')
                ->where('BRANCH_ID', $branchId)
                ->get();

            $tariffType = DB::table('reason_master')
                ->select('REASON_ID', 'REASON')
                ->where('ACTIVE', $rsActive)
                ->where('CATEGORY', 'TariffType')
                ->where('BRANCH_ID', $branchId)
                ->get()
                ->map(function ($item) {
                    return [
                        'tariff_id' => $item->REASON_ID,
                        'tariff_value' => $item->REASON
                    ];
                });

          
            return response([
                'success' => true,
                'status' => 3,
                'cab_models' => $models,
                'vendors' => $vendors,
                'tariff_type' => $tariffType,
                
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Data For Create for Vehicle Models Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ]);
        }
    }
	
 
    public function paginationNonInduct($request, bool $active = true): FoundationApplication|Response|ResponseFactory
    {
        try {
            $authUser = Auth::user();
            if($authUser->user_type == MyHelper::$ADMIN)
			{
				$vendorid = "";
			} else {
				//$vendorid = "AND R.VENDOR_ID='$vendor_id'";
				$vendorid = $vendor_id;
			}
            $dbname = Auth::user()->dbname;
           
            $perPage = $request->input('per_page', MyHelper::$PAGINATION_PER_PAGE);
            $branch_id = Auth::user()->BRANCH_ID;
            $activeStatus = $active ? MyHelper::$RS_ACTIVE : MyHelper::$RS_INACTIVE;
            $authUser = Auth::user();
           // echo $authUser->BRANCH_ID;exit;
            $cab = Cab::select('cab.CAB_ID',
            'cab.DRIVER_ID','cab.VENDOR_ID','cab.VEHICLE_ID','cab.SIM_ID','cab.CAB_STATUS','cab.BRANCH_ID',
            'cab.TARIFF_TYPE','cab.ATTACHMENT_DATE',
            'DR.DRIVERS_NAME',
            'DR.DRIVERS_ADRESS',
            'DR.DRIVERS_ADDR_LAT',
            'DR.DRIVERS_ADDR_LONG',
            'DR.DRIVER_LOC_LAT',
            'DR.DRIVER_LOC_LONG',
            'DR.DRIVER_MOBILE',
            'DR.DRIVER_LICENSE',
            'DR.LICENCE_EXPIRY',
            'DR.BADGE_EXPIRY',
            'DR.LOCATION_NAME',
            'DR.SHIFT_IN_TIME',
            'DR.SHIFT_OUT_TIME', "VH.VEHICLE_JOIN_DATE", "VH.REG_DATE", "VH.REG_STATUS", "VH.PERMIT_EXPIRY", "VH.INSURANCE_EXPIRY", "VH.FC_EXPIRY", "VH.TAX_EXPIRY", 
            'rm.REASON','rm.REASON_ID','S.SIM_ID','S.SIM_MOBILE_NO','S.SIM_SERIAL_NO','S.SIM_PROVIDER','D.DEVICE_ID',
			'D.IMEI_NO_1','D.IMEI_NO_2','D.DEVICE_MODEL','VH.VEHICLE_REG_NO','VM.BRAND','VM.MODEL','VM.CAPACITY','V.NAME as vendor_name','U.name as created_by','VH.created_at'        )
          
            ->join("drivers as DR", "DR.DRIVERS_ID", "=", 'cab.DRIVER_ID')
            ->join("vehicles as VH", "VH.VEHICLE_ID", "=", 'cab.VEHICLE_ID')
            ->join("vehicle_models as VM", "VM.VEHICLE_MODEL_ID", "=", 'VH.VEHICLE_MODEL_ID')
            ->join("devices as D", "D.DEVICE_ID", "=", 'cab.DEVICE_ID')
            ->Join("sim as S", "S.SIM_ID", "=", "cab.SIM_ID")
            ->Join("vendors as V", "V.VENDOR_ID", "=", "cab.VENDOR_ID")
            ->join("users as U", "U.id", "=", "VH.CREATED_BY")
            ->Join("reason_master as rm", "rm.REASON_ID", "=","cab.TARIFF_TYPE")
            ->where('cab.ACTIVE', MyHelper::$RS_ACTIVE)
            ->where("cab.CAB_STATUS", "=", 1)
            ->when($vendorid, function ($query, $vendorid) {
                return $query->where('C.VENDOR_ID', $vendorid);
            })
           ->where("cab.BRANCH_ID",DB::raw( $authUser->BRANCH_ID));
           

            $filterModel = $request->input('filterModel');
            if ($filterModel) {
                foreach ($filterModel as $field => $filter) {
                    if (isset($filter['filter']) && $filter['filter'] !== '') {
                        $value = $filter['filter'];
                        $type = $filter['type'];

                        switch ($field) {
                            case 'CAB_ID':
                                $cab->where('cab.CAB_ID', 'like', "%{$value}%");
                                break;
                            case 'VEHICLE_REG_NO':
                                $cab->where('VH.VEHICLE_REG_NO', 'like', "%{$value}%");
                                break;
                            
                            case 'REG_DATE':
                                $cab->where('VH.REG_DATE', 'like', "%{$value}%");
                                break;
                            case 'REG_DATE':
                                $cab->where('VH.REG_DATE', 'like', "%{$value}%");
                                break;
                            case 'VEHICLE_JOIN_DATE':
                                $cab->where('VH.VEHICLE_JOIN_DATE', 'like', "%{$value}%");
                                break;
                            case 'PERMIT_EXPIRY':
                                $cab->where('VH.PERMIT_EXPIRY', 'like', "%{$value}%");
                                break;
                            case 'INSURANCE_EXPIRY':
                                $cab->where('VH.INSURANCE_EXPIRY', 'like', "%{$value}%");
                                break;
                            case 'TAX_EXPIRY':
                                $cab->where('VH.TAX_EXPIRY', 'like', "%{$value}%");
                                break;
                            
                            case 'FC_EXPIRY':
                                $cab->where('VH.FC_EXPIRY', 'like', "%{$value}%");
                                break;
                            
                        }
                    }
                }
            }


            if ($request->has('orderBy') && !empty($request->input('orderBy'))) 
            {
                $orderBy = $request->input('orderBy');
                $order = $request->input('order', 'asc');
                $cab->orderBy($orderBy, $order);
            } else {
                $cab->orderBy('cab.created_at', 'desc');
            }
// echo $driver->count();exit;
            if ($perPage === MyHelper::$PAGINATION_PER_PAGE_ALL) {
                $paginatedCab = $cab->paginate($cab->count());
            } else {
                $perPage = is_numeric($perPage) ? (int)$perPage : MyHelper::$PAGINATION_PER_PAGE;
                $paginatedCab = $cab->paginate($perPage);
            }
           // print_r($paginatedDriver);exit;
            return response([
                'success' => true,
                'status' => 3,
                'non_induct_vehicles' => $paginatedCab,
                
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'Non induct Pagination Unsuccessful' : 'Deactivate Non induct Pagination Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ],500);
        }
    } 

}
