<?php

use App\Helpers\MyHelper;
use App\Http\Controllers\Admin\EmployeeController;
use App\Http\Controllers\Admin\TrackingDashboardController;
use App\Http\Controllers\Auth\AuthController;
use App\Http\Controllers\EmployeeApp\EmployeeAppController;
use App\Http\Controllers\SampleCode\SampleCodeController;
use App\Http\Controllers\SuperAdmin\OrganizationController;
use App\Http\Controllers\SuperAdmin\CompanyController;
use App\Http\Controllers\SuperAdmin\DivisionController;
use App\Http\Controllers\SuperAdmin\BranchController;
use App\Http\Controllers\SuperAdmin\VendorController;
use App\Http\Controllers\Admin\ReportController;
use App\Http\Controllers\Elastic\ElasticsearchDriverGpsLoggingController;


use App\Http\Controllers\Admin\DriverController;
use App\Http\Controllers\Admin\VehicleController;
use App\Http\Controllers\Admin\SIMController;
use App\Http\Controllers\Admin\LocationController;
use App\Http\Controllers\Admin\DeviceController;
use App\Http\Controllers\Admin\EscortController;
use App\Http\Controllers\Admin\CabMappingController;
use App\Http\Controllers\Admin\NonInductController;
use App\Http\Controllers\Admin\TripcloseController;
use App\Http\Controllers\Admin\TollPenaltyController;
use App\Http\Controllers\Admin\RosterUploadController;
use App\Http\Controllers\Admin\ManualOTPController;
use App\Http\Controllers\Admin\OTPSMSController;
use App\Http\Controllers\Admin\ReclubController;
use App\Http\Controllers\Admin\EscortCancelController;
use App\Http\Controllers\Admin\DeactiveRouteController;
use App\Http\Controllers\Admin\RouteOrderController;
use App\Http\Controllers\Admin\NewRosterController;
use App\Http\Controllers\Admin\VehicleTravelPathController;
use App\Http\Controllers\Admin\TripTravelPathController;
use App\Http\Controllers\Admin\LiveTrackingController;
use App\Http\Controllers\Admin\VehicleTrackingController;
use App\Http\Controllers\Admin\VehicleTrackingGPSController;
use App\Http\Controllers\Admin\FileDownloadController;
use App\Http\Controllers\Admin\EmployeeUploadController;
use App\Http\Controllers\Admin\UploadFormatController;
use App\Http\Controllers\ElasticController;
use App\Http\Controllers\Admin\TripHistoryController;
use App\Http\Controllers\Admin\EmployeeRequestController;
use App\Http\Controllers\Admin\EmpLocationUpdateController;
use App\Http\Controllers\Admin\AdhocController;
use App\Http\Controllers\Admin\AdhocWeeklyRosterController;
use App\Http\Controllers\Admin\AutoRouteController;
use App\Http\Controllers\Admin\UploadDataController;
use App\Http\Controllers\Admin\TransportDataController;
use App\Http\Controllers\Admin\RealTimeTrackingController;
use App\Http\Controllers\Admin\AutoRouteUploadController;
use App\Http\Controllers\Admin\RRDMISController;
use App\Http\Controllers\Admin\BillingController;
use App\Http\Controllers\Admin\CronjobController;

use App\Http\Controllers\Elastic\ElasticsearchProductController;

use Illuminate\Support\Facades\Route;

Route::post('/login', [AuthController::class, 'login']);


Route::post('/employeemobileverification', [AuthController::class, 'employeemobileverification']);
Route::post('/employeeotpVerification', [AuthController::class, 'employeeotpVerification']);

Route::post('/getotpverify_forget',[AuthController::class, 'getotpverify_forget']);
Route::post('/verify/token', [AuthController::class, 'verifyToken'])
    ->middleware([
        'auth:api',
        'role:' . implode(',', [MyHelper::$ADMIN, MyHelper::$SUPER_ADMIN,MyHelper::$VENDOR,MyHelper::$EMPLOYEE])
    ]);
Route::get('/logout', [AuthController::class, 'logoutAllDevices'])->middleware('auth:api');
Route::get('/current/logout', [AuthController::class, 'logoutCurrentDevice'])->middleware('auth:api');

Route::prefix('organization')->middleware(['auth:api', 'role:' . MyHelper::$SUPER_ADMIN])->group(function () {
    Route::controller(OrganizationController::class)->group(function () {
        Route::get('/', 'index');
        Route::post('/', 'store');
        Route::post('/pagination', 'pagination');
        Route::get('/edit/{organizationIdCrypt}', 'edit');
        Route::put('/update/{organizationIdCrypt}', 'update');
        Route::put('/delete/{organizationIdCrypt}', 'delete');
        Route::get('/data/for/create', 'dataForCreate');
	    Route::get('/register', 'Register');
        Route::get('/getregister', 'GetRegister');
        Route::post('/branch_wise_vendor', 'BranchWiseVendor');
        Route::post('/postregister', 'PostRegister');
        Route::post('/updateregister', 'UpdateRegister');
	    Route::get('/getproperty', 'GetProperty');
        Route::post('/addproperty', 'AddProperty');
    });
});


Route::prefix('company')->middleware(['auth:api', 'role:' . MyHelper::$SUPER_ADMIN])->group(function () {
    Route::controller(CompanyController::class)->group(function () {
        Route::get('/', 'index');
        Route::post('/', 'store');
        Route::get('/edit/{companyIdCrypt}', 'edit');
        Route::put('/update/{companyIdCrypt}', 'update');
        Route::put('/delete/{companyIdCrypt}', 'delete');
        Route::post('/pagination', 'companylistpagination');
       
    });
});

Route::prefix('division')->middleware(['auth:api', 'role:' . MyHelper::$SUPER_ADMIN])->group(function () {
    Route::controller(DivisionController::class)->group(function () {
        Route::get('/', 'index');
        Route::post('/', 'store');
        Route::get('/edit/{divisionIdCrypt}', 'edit');
        Route::put('/update/{divisionIdCrypt}', 'update');
        Route::put('/delete/{divisionIdCrypt}', 'delete');
        Route::post('/pagination', 'divisionlistpagination');
       
    });
});


Route::prefix('branch')->middleware(['auth:api', 'role:' . MyHelper::$SUPER_ADMIN])->group(function () {
    Route::controller(BranchController::class)->group(function () {
        Route::get('/', 'index');
        Route::post('/', 'store');
        Route::get('/edit/{branchIdCrypt}', 'edit');
        Route::put('/update/{branchIdCrypt}', 'update');
        Route::put('/delete/{branchIdCrypt}', 'delete');
        Route::get('/data/for/create', 'dataForCreate');
        Route::post('/pagination', 'branchlistpagination');
       
    });
});


Route::prefix('vendor')->middleware(['auth:api', 'role:' . MyHelper::$SUPER_ADMIN])->group(function () {
    Route::controller(VendorController::class)->group(function () {
        Route::get('/', 'index');
        Route::post('/', 'store');
        Route::get('/edit/{branchIdCrypt}', 'edit');
        Route::put('/update/{branchIdCrypt}', 'update');
        Route::put('/delete/{branchIdCrypt}', 'delete');
        Route::get('/data/for/create', 'dataForCreate');
        Route::post('/pagination', 'vendorlistpagination');
       
    });
});

Route::prefix('employee')->middleware(['auth:api', 'role:' . MyHelper::$ADMIN])->group(function () {
    Route::controller(EmployeeController::class)->group(function () {
        Route::get('/', 'index');
        Route::post('/', 'store');
        Route::post('/active/pagination', 'activeEmployeePagination');
        Route::get('/edit/{employeeAutoIdCrypt}', 'edit');
        Route::put('/update/{employeeAutoIdCrypt}', 'update');
        Route::put('/delete/{employeeAutoIdCrypt}', 'delete');
        Route::post('de/active/pagination', 'deActiveEmployeePagination');
        Route::post('/activate', 'activate');
        Route::post('/needspecializedtransport', 'needspecializedtransport');
        Route::post('/enableEditAddress', 'enableEditAddress');
        Route::get('/data/for/create', 'dataForCreate');


    });
});



Route::prefix('sim')->middleware(['auth:api', 'role:' . implode(',', [MyHelper::$ADMIN, MyHelper::$VENDOR])])->group(function () {
    Route::controller(SIMController::class)->group(function () {
        Route::get('/', 'index');
        Route::post('/', 'store');
		Route::get('/edit/{simAutoIdCrypt}', 'edit');
        Route::put('/update/{simAutoIdCrypt}', 'update');
        Route::put('/delete/{simAutoIdCrypt}', 'delete');
        Route::post('/active/pagination', 'activeSimPagination');
       
    });
});
Route::prefix('device')->middleware(['auth:api', 'role:' . implode(',', [MyHelper::$ADMIN, MyHelper::$VENDOR])])->group(function () {
    Route::controller(DeviceController::class)->group(function () {
        Route::get('/', 'index');
        Route::post('/', 'store');
		Route::get('/edit/{deviceAutoIdCrypt}', 'edit');
        Route::put('/update/{deviceAutoIdCrypt}', 'update');
        Route::put('/delete/{deviceAutoIdCrypt}', 'delete');
        Route::post('/active/pagination', 'activeDevicePagination');
       
    });
});

Route::prefix('driver')->middleware(['auth:api', 'role:' . implode(',', [MyHelper::$ADMIN, MyHelper::$VENDOR])])->group(function () {
    Route::controller(DriverController::class)->group(function () {
    Route::get('/', 'index');
    Route::post('/', 'store');
    Route::get('/edit/{driverAutoIdCrypt}', 'edit');
    Route::post('/update/{driverAutoIdCrypt}', 'update');
    Route::put('/delete/{driverAutoIdCrypt}', 'delete');
    Route::post('/active/pagination', 'activeDriverPagination');
    Route::get('/data/for/create', 'dataForCreate'); 
});
});

Route::prefix('vehicle')->middleware(['auth:api', 'role:' . implode(',', [MyHelper::$ADMIN, MyHelper::$VENDOR])])->group(function () {
    Route::controller(VehicleController::class)->group(function () {
    Route::get('/', 'index');
    Route::post('/', 'store');
    Route::get('/edit/{vehicleAutoIdCrypt}', 'edit');
    Route::put('/update/{vehicleAutoIdCrypt}', 'update');
    Route::put('/delete/{vehicleAutoIdCrypt}', 'delete');
    Route::post('/active/pagination', 'activeVehiclePagination');
    Route::get('/data/for/create', 'dataForCreate');
   
});
});


Route::prefix('location')->middleware(['auth:api', 'role:' . MyHelper::$ADMIN])->group(function () {
    Route::controller(LocationController::class)->group(function () {
        Route::get('/', 'index');
        Route::post('/', 'store');
		Route::get('/edit/{locationAutoIdCrypt}', 'edit');
        Route::put('/update/{locationAutoIdCrypt}', 'update');
        Route::put('/delete/{locationAutoIdCrypt}', 'delete');
        Route::post('/active/pagination', 'activeLocationPagination');
        Route::get('/data/for/create', 'dataForCreate'); 
     
    });
});

Route::prefix('escort')->middleware(['auth:api', 'role:' . MyHelper::$ADMIN])->group(function () {
    Route::controller(EscortController::class)->group(function () {
        Route::get('/', 'index');
        Route::post('/', 'store');
		Route::get('/edit/{escortAutoIdCrypt}', 'edit');
        Route::post('/update/{escortAutoIdCrypt}', 'update');
        Route::put('/delete/{escortAutoIdCrypt}', 'delete');
        Route::post('/active/pagination', 'activeEscortPagination');
        
     
    });
});

Route::prefix('cabmapping')->middleware(['auth:api', 'role:' . implode(',', [MyHelper::$ADMIN, MyHelper::$VENDOR])])->group(function () {
    Route::controller(CabMappingController::class)->group(function () {
        Route::get('/', 'index');
        Route::post('/', 'store');
		Route::get('/edit/{cabAutoIdCrypt}', 'edit');
        Route::put('/update/{cabAutoIdCrypt}', 'update');
        Route::put('/delete/{cabAutoIdCrypt}', 'delete');
        Route::post('/active/pagination', 'activeCabPagination');
        Route::put('/simchange/{cabAutoIdCrypt}', 'simchange');
		Route::put('/devicechange/{cabAutoIdCrypt}', 'devicechange');

        Route::get('/data/for/create', 'dataForCreate'); 
     
    });
});

Route::prefix('noninduct')->middleware(['auth:api', 'role:' . implode(',', [MyHelper::$ADMIN, MyHelper::$VENDOR])])->group(function () {
    Route::controller(NonInductController::class)->group(function () {
        Route::get('/', 'index');
        Route::post('/', 'store');
		//Route::get('/edit/{cabAutoIdCrypt}', 'edit');
        //Route::put('/update/{cabAutoIdCrypt}', 'update');
        //Route::put('/delete/{cabAutoIdCrypt}', 'delete');
        Route::post('/active/pagination', 'activeNonInductPagination');
       // Route::put('/simchange/{cabAutoIdCrypt}', 'simchange');
		//Route::put('/devicechange/{cabAutoIdCrypt}', 'devicechange');

        Route::get('/data/for/create', 'dataForCreate'); 
     
    });
});







Route::post('/otp/creation', [AuthController::class, 'otpCreation'])->middleware(['VersionCheck']);
Route::post('/otp/verification', [AuthController::class, 'otpVerification'])->middleware(['VersionCheck', 'auth:api', 'role:' . MyHelper::$EMPLOYEE]);
Route::post('/gcm/store', [EmployeeAppController::class, 'gcmStore'])->middleware(['VersionCheck', 'auth:api', 'role:' . MyHelper::$EMPLOYEE, 'EnsureOtpIsVerified']);

Route::post('/csv/upload', [SampleCodeController::class, 'upload']);

Route::post('/otp/create', [EmployeeAppController::class, 'otpCreate']);

Route::prefix('tracking_dashboard')->middleware(['auth:api', 'role:' . implode(',', [MyHelper::$ADMIN, MyHelper::$VENDOR])])->group(function () {
    Route::controller(TrackingDashboardController::class)->group(function () {
        Route::post('/pagination', 'trackingDashboard');
        Route::post('/dashboard_count', 'dashboard_count');
        Route::post('/roster_details', 'roster_details');
        Route::post('/get_reason_details', 'get_reason_details');
        Route::post('/roster_vendor_change', 'roster_vendor_change');
        Route::post('/roster_reset', 'roster_reset');
        Route::post('/roster_noshow', 'roster_noshow');
        Route::post('/empclubmob', 'empclubmob');
        Route::post('/club_emp_details', 'club_emp_details');
        Route::post('/cab_allot_list', 'cab_allot_list');
        Route::post('/rostercaballot', 'rostercaballot');
        Route::post('/breakdownreason', 'breakdownreason');
        Route::post('/insert_clubdata', 'insert_clubdata');

        //Actions API
        Route::get('/data/for/create', 'dataForCreate');
        Route::post('/speedcount_details', 'speedcount_details');
        
        Route::POST('/overspeedremarks', 'overspeedremarks');

        Route::POST('/panic_action_update', 'panic_action_update');
        Route::get('/escort_details', 'escort_details');
        Route::post('/escort_assign', 'escort_assign');
        Route::post('/escort_reset', 'escort_reset');
        Route::post('/escort_validate', 'escort_validate');
        Route::post('/update_safedropremarks', 'update_safedropremarks');
        Route::post('/update_asfremarks', 'update_asfremarks');
        Route::post('/deviation_remarks_update', 'deviation_remarks_update');
      //  Route::POST('/overspeedremarks', 'ElasticController@cabSpeedRemarkUpdate');
    });
});

Route::prefix('upload')->middleware(['auth:api', 'role:' . MyHelper::$ADMIN])->group(function () {
    Route::controller(RosterUploadController::class)->group(function () {
        Route::post('/post_roster_upload', 'post_roster_upload');
    });
});

Route::prefix('upload')->middleware(['auth:api', 'role:' . MyHelper::$ADMIN])->group(function () {
    Route::controller(AutoRouteUploadController::class)->group(function () {
        Route::post('/auto_route_upload', 'auto_route_upload');
    });
});

Route::prefix('upload')->middleware(['auth:api', 'role:' . MyHelper::$ADMIN])->group(function () {
    Route::controller(EmployeeUploadController::class)->group(function () {
        Route::post('/post_employee_upload', 'post_employee_upload');
    });
});
Route::prefix('download')->middleware(['auth:api', 'role:' . MyHelper::$ADMIN])->group(function () {
    Route::controller(UploadFormatController::class)->group(function () {
        Route::get('/dataforcreate', 'dataforcreate');
    });
});

Route::prefix('manual_otp')->middleware(['auth:api', 'role:' . MyHelper::$ADMIN])->group(function () {
    Route::controller(ManualOTPController::class)->group(function () {
        Route::get('/data/for/create', 'dataForCreateManualOTP');
        Route::post('/vendorwise_vehicle_list', 'vendorwise_vehicle_list');
        Route::post('/select_cab_wise_route', 'select_cab_wise_route');
        Route::post('/selected_route_details', 'selected_route_details');
        Route::post('/arriveboarded', 'arriveboarded');
        Route::post('/manual_otp', 'manual_otp');
        Route::post('/noshow_reset', 'noshow_reset');
    });
});

Route::prefix('otp_sms')->middleware(['auth:api', 'role:' . implode(',', [MyHelper::$ADMIN, MyHelper::$VENDOR])])->group(function () {
    Route::controller(OTPSMSController::class)->group(function () {
        Route::get('/data/for/create', 'dataForCreateManualOTP');
        Route::get('/otp_sms', 'otp_sms');
        Route::post('/otp_roster_sms_details', 'otp_roster_sms_details');
        Route::post('/otp_roster_sms_send', 'otp_roster_sms_send');
        Route::post('/multi_push_message', 'multi_push_message');
        Route::post('/driver_otp_sms', 'driver_otp_sms');
        Route::post('/driver_otp_send_sms', 'driver_otp_send_sms');
        Route::post('/escort_otp_sms', 'escort_otp_sms');
        Route::post('/escort_otp_sms_send', 'escort_otp_sms_send');
        Route::post('/employee_login_otp', 'employee_login_otp');
    });
});

Route::prefix('reclub')->middleware(['auth:api', 'role:' . implode(',', [MyHelper::$ADMIN, MyHelper::$VENDOR])])->group(function () {
    Route::controller(ReclubController::class)->group(function () {
        Route::get('/reclub_roster', 'reclub_roster');
        Route::post('/reclub_roster_details', 'reclub_roster_details');
        Route::post('/otp_roster_sms_details', 'otp_roster_sms_details');
        Route::post('/reclubing_add', 'reclubing_add');
        Route::post('/reclubed_new_roster', 'reclubed_new_roster');
    });
});

Route::prefix('routeorder')->middleware(['auth:api', 'role:' . implode(',', [MyHelper::$ADMIN, MyHelper::$VENDOR])])->group(function () {
    Route::controller(RouteOrderController::class)->group(function () {
        Route::get('/dataForCreate', 'dataForCreate');
        Route::post('/VendorBaseType', 'VendorBaseType');
        Route::post('/select_cab_route', 'select_cab_route');
        Route::post('/select_cab_roster', 'select_cab_roster');
        Route::post('/change_order', 'change_order');

       /* Route::post('/reclub_roster_details', 'reclub_roster_details');
        Route::post('/otp_roster_sms_details', 'otp_roster_sms_details');
        Route::post('/reclubing_add', 'reclubing_add');
        Route::post('/reclubed_new_roster', 'reclubed_new_roster');*/
    });
});

Route::prefix('newroster')->middleware(['auth:api', 'role:' . MyHelper::$ADMIN])->group(function () {
    Route::controller(NewRosterController::class)->group(function () {
        Route::get('/dataForCreate', 'dataForCreate');
        Route::post('/newrosteremployee', 'newrosteremployee');
        Route::post('/trip_type_wise_login_time', 'trip_type_wise_login_time');
        Route::post('/PostRosterRequest', 'PostRosterRequest');
    });
});

Route::prefix('vehicletravelpath')->middleware(['auth:api', 'role:' . implode(',', [MyHelper::$ADMIN, MyHelper::$VENDOR])])->group(function () {
    Route::controller(VehicleTravelPathController::class)->group(function () {
        Route::get('/dataForCreate', 'dataForCreate');
        Route::post('/vendorwise_vehicle_find', 'vendorwise_vehicle_find');
        Route::post('/vehicle_travel_path', 'vehicle_travel_path');
        Route::post('/vehicle_travel_path_excel', 'vehicle_travel_path_excel');
    });
});

Route::prefix('triptravelpath')->middleware(['auth:api', 'role:' . implode(',', [MyHelper::$ADMIN, MyHelper::$VENDOR])])->group(function () {
    Route::controller(TripTravelPathController::class)->group(function () {
        Route::get('/dataForCreate', 'dataForCreate');
        Route::post('/vendorwise_vehicle_find', 'vendorwise_vehicle_find');
        Route::post('/select_cab_wise_route', 'select_cab_wise_route');
        Route::post('/selected_route_details', 'selected_route_details');
        Route::post('/trip_travel_path', 'trip_travel_path');
	    Route::post('/gpsactionlist', 'gpsactionlist');       
    });
});

Route::prefix('livetracking')->middleware(['auth:api', 'role:' . implode(',', [MyHelper::$ADMIN, MyHelper::$VENDOR])])->group(function () {
    Route::controller(LiveTrackingController::class)->group(function () {
        Route::get('/dataForCreate', 'dataForCreate');
        Route::post('/live_travel_path', 'live_travel_path');
    });
});
Route::prefix('vehicletracking')->middleware(['auth:api', 'role:' . implode(',', [MyHelper::$ADMIN, MyHelper::$VENDOR])])->group(function () {
    Route::controller(VehicleTrackingController::class)->group(function () {
        Route::get('/dataForCreate', 'dataForCreate');
        Route::post('/cab_status_vehicle', 'cab_status_vehicle');
        Route::post('/vehicle_status', 'vehicle_status');
        Route::post('/live_travel_path', 'live_travel_path');
    });
});
Route::prefix('vehicletrackingGPS')->middleware(['auth:api', 'role:' . implode(',', [MyHelper::$ADMIN, MyHelper::$VENDOR])])->group(function () {
    Route::controller(VehicleTrackingGPSController::class)->group(function () {
        Route::get('/dataForCreate', 'dataForCreate');
        Route::post('/get_roster_shifttime', 'get_roster_shifttime');
        Route::post('/cab_status_vehicle', 'cab_status_vehicle');
        Route::post('/vehicle_status', 'vehicle_status');
        Route::post('/postvehicletracking', 'postvehicletracking');
        Route::post('/tracking_roster_details', 'tracking_roster_details');
    });
});
Route::prefix('triphistory')->middleware(['auth:api', 'role:' . implode(',', [MyHelper::$ADMIN, MyHelper::$VENDOR])])->group(function () {
    Route::controller(TripHistoryController::class)->group(function () {
        Route::get('/dataForCreate', 'dataForCreate');
        Route::post('/date_shifttime', 'date_shifttime');
        Route::post('/getfilter_trip_history', 'getfilter_trip_history');
    });
});

Route::prefix('emprequest')->middleware(['auth:api', 'role:' . implode(',', [MyHelper::$EMPLOYEE])])->group(function () {
    Route::controller(EmployeeRequestController::class)->group(function () {
        Route::get('/dataForCreate', 'dataForCreate');
        Route::post('/getdate_webemprequest', 'getdate_webemprequest');
	    Route::post('/remove_webemprequest', 'RemoveWebEmpRequest');
	    Route::post('/weekroster', 'weekroster');
        Route::post('/rosterempweekreq', 'RosterEmpWeekReq');
        Route::post('/updatepass', 'PostUpdatePassword'); 
        Route::get('/weekroster_admin', 'WeekrosterAdmin');
        Route::post('/week_roster_admin_data', 'WeekRosterAdminData');
    });
});

Route::prefix('adhoc')->middleware(['auth:api', 'role:' . implode(',', [MyHelper::$EMPLOYEE])])->group(function () {
    Route::controller(AdhocController::class)->group(function () {
        Route::get('/dataForCreate', 'dataForCreate');
        Route::post('/getemplocation', 'getemplocation');
        Route::post('/getadhocdatechange', 'getadhocdatechange');
        Route::post('/getrequestedAdhoc', 'getrequestedAdhoc');
    });
});
Route::prefix('emplocation')->middleware(['auth:api', 'role:' . implode(',', [MyHelper::$EMPLOYEE])])->group(function () {
    Route::controller(EmpLocationUpdateController::class)->group(function () {
        Route::get('/dataForCreate', 'dataForCreate');
        Route::post('/update_emplocation', 'update_emplocation');
    });
});




Route::prefix('overall_report')->middleware(['auth:api', 'role:' . implode(',', [MyHelper::$ADMIN, MyHelper::$VENDOR])])->group(function () {
    Route::controller(ReportController::class)->group(function () {
        Route::get('/data/for/create/Employees', 'dataForCreateEmployees');
        Route::get('/data/for/create/Vehicles', 'dataForCreateVehicles');      
        Route::post('/fetch_OverAllReport_Details', 'fetch_OverAllReport_Details'); 
        Route::post('/fetch_EmployeesWise_OverAllReport_Details', 'fetch_EmployeesWise_OverAllReport_Details'); 
        Route::post('/fetch_VehicleWise_OverAllReport_Details', 'fetch_VehicleWise_OverAllReport_Details');
        Route::post('/fetch_Associate_Noshow_Details', 'fetch_Associate_Noshow_Details');
        Route::post('/fetch_Ontime_Report_Details', 'fetch_Ontime_Report_Details');
        Route::post('/fetch_Cancel_Report_Details', 'fetch_Cancel_Report_Details');
        Route::post('/fetch_ASF_Report_Details', 'fetch_ASF_Report_Details');    
        Route::post('/fetch_AcceptReject_Report_Details', 'fetch_AcceptReject_Report_Details'); 
        Route::post('/fetch_Escort_Report_Details', 'fetch_Escort_Report_Details');
        Route::post('/fetch_Toll_Report_Details', 'fetch_Toll_Report_Details');
        Route::post('/fetch_Vehicle_Report_Details', 'fetch_Vehicle_Report_Details');
        Route::post('/fetch_Breakdown_Report_Details', 'fetch_Breakdown_Report_Details');
        Route::post('/fetch_Vendorchange_Report_Details', 'fetch_Vendorchange_Report_Details');
        Route::post('/fetch_Feedback_Report_Details', 'fetch_Feedback_Report_Details');
        Route::post('/fetch_OverTime_Report_Details', 'fetch_OverTime_Report_Details');
        Route::post('/fetch_Adhoc_Report_Details', 'fetch_Adhoc_Report_Details');
        Route::post('/fetch_SMS_Report_Details', 'fetch_SMS_Report_Details');
        Route::post('/fetch_WeeklyRoster_Report_Details', 'fetch_WeeklyRoster_Report_Details');
        Route::post('/fetch_OtpReport_Details', 'fetch_OtpReport_Details');
        Route::post('/fetch_PanicReport_Details', 'fetch_PanicReport_Details');
        Route::post('/fetch_Deactive_Route_Employee_Details', 'fetch_Deactive_Route_Employee_Details');
        Route::post('/getCabOverspeedReport', 'getCabOverspeedReport');
        Route::post('/getWrongLocationReport', 'getWrongLocationReport');
        Route::post('/fetch_Adhoc_WeeklyRoster_Report_Details', 'fetch_Adhoc_WeeklyRoster_Report_Details');
        Route::post('/fetch_FWMIS_Report_Details', 'fetch_FWMIS_Report_Details');
        Route::post('/misreport_date_wise_shiftlogin', 'misreport_date_wise_shiftlogin');
        Route::post('/fetch_FWMIS_AcceptApprovelReport_Details', 'fetch_FWMIS_AcceptApprovelReport_Details');
        Route::post('/bill_Approvel', 'bill_Approvel');
        Route::post('/fetch_EmptyKmApprovel_Report_Details', 'fetch_EmptyKmApprovel_Report_Details');
        Route::post('/fetch_Mask_CallLogs_Report_Details', 'fetch_Mask_CallLogs_Report_Details');
        Route::post('/fetch_Mask_Transaction_Report_Details', 'fetch_Mask_Transaction_Report_Details');
        Route::post('/getMisReport', 'getMisReport');
        Route::get('/empty_route', 'EmptyRoute');
	    Route::post('/empty_route_vendorchange', 'EmptyRouteVendorChange');
        Route::post('/trip_type_change', 'TripTypeChange');
        Route::post('/empty_vehicle', 'EmptyVehicle');
        Route::post('/empty_route_add', 'EmptyRouteAdd');
	    Route::post('/getfilter_empty', 'GetFilterEmpty');
        Route::post('/approve_submit_empty', 'ApproveSubmitEmpty');
    });
});

Route::prefix('tripclose')->middleware(['auth:api', 'role:' . implode(',', [MyHelper::$ADMIN, MyHelper::$VENDOR])])->group(function () {
    Route::controller(TripcloseController::class)->group(function () {
        Route::post('/pagination', 'tripclose');
        Route::get('/data/for/create', 'dataForCreate'); 
        Route::post('/get_tripclose_details', 'get_tripclose_details'); 
        Route::post('/tripclose_cab_allot', 'tripclose_cab_allot'); 
        Route::post('/tripclose_cab_allot_vehicle_list', 'tripclose_cab_allot_vehicle_list'); 
        Route::post('/trip_status_update', 'trip_status_update');
        Route::post('/tripclose_date_wise_shiftlogin', 'tripclose_date_wise_shiftlogin');
        Route::post('/tripclose_boarded_noshow', 'tripclose_boarded_noshow');
        Route::post('/total_passenger_list', 'total_passenger_list');
        Route::post('/manualtripclose', 'manualtripclose');
        Route::post('/billable_trip_Approve', 'billable_trip_Approve');
    });
});


Route::prefix('toll_penalty')->middleware(['auth:api', 'role:' . implode(',', [MyHelper::$ADMIN, MyHelper::$VENDOR])])->group(function () {
    Route::controller(TollPenaltyController::class)->group(function () { 
        Route::post('/fetch_Toll_Penalty_Details', 'fetch_Toll_Penalty_Details');
        Route::get('/data/for/create', 'dataForCreatePenalties');
        Route::post('/toll', 'store');
    });
});



Route::prefix('escort_cancel')->middleware(['auth:api', 'role:' . implode(',', [MyHelper::$ADMIN, MyHelper::$VENDOR])])->group(function () {
    Route::controller(EscortCancelController::class)->group(function () { 
        Route::get('/fetch_EscortCancel_RouteData', 'fetch_EscortCancel_RouteData');
        Route::post('/fetch_EscortCancel_RouteDetails', 'fetch_EscortCancel_RouteDetails');
        Route::post('/cancel_EscortRoute', 'cancel_EscortRoute');
    });
});

Route::prefix('route_deactive')->middleware(['auth:api', 'role:' . implode(',', [MyHelper::$ADMIN, MyHelper::$VENDOR])])->group(function () {
    Route::controller(DeactiveRouteController::class)->group(function () { 
        Route::post('/fetch_Deactive_Route_Details', 'fetch_Deactive_Route_Details');
        Route::post('/deactivate_Routes', 'deactivate_Routes');
    });
});


Route::prefix('file_download')->middleware(['auth:api', 'role:' . implode(',', [MyHelper::$ADMIN, MyHelper::$VENDOR])])->group(function () {
    Route::controller(FileDownloadController::class)->group(function () { 
        Route::post('/fetch_UploadFileData', 'fetch_UploadFileData');
        Route::post('/fetch_UploadFileDetails', 'fetch_UploadFileDetails');
    });
});


Route::prefix('adhoc_weekly')->middleware(['auth:api', 'role:' . implode(',', [MyHelper::$ADMIN, MyHelper::$VENDOR])])->group(function () {
    Route::controller(AdhocWeeklyRosterController::class)->group(function () { 
        Route::get('/dataForCreate', 'dataForCreate');       
        Route::post('/fetchAdhocWeeklyDateWiseRequestTime', 'fetchAdhocWeeklyDateWiseRequestTime');
        Route::post('/fetch_AdhocWeeklyRosterRequest_Report_Details', 'fetch_AdhocWeeklyRosterRequest_Report_Details');
        Route::post('/postAdhocWeeklyRosterRequest', 'postAdhocWeeklyRosterRequest');      
        Route::put('/deleteAdhocWeeklyRosterRequest/{roster_req_id}', 'deleteAdhocWeeklyRosterRequest');
    });
});


Route::post('/elas_count', [ElasticController::class, 'elas_count'])->middleware(['auth:api']);
Route::post('/elas_overspeed_test', [ElasticController::class, 'elas_overspeed_test'])->middleware(['auth:api']);

Route::post('/products/create-index', [ElasticsearchProductController::class, 'createIndex']);
Route::get('/elasticsearch/version', [ElasticsearchProductController::class, 'version']);

Route::prefix('products')->group(function () {
    Route::get('/', [ElasticsearchProductController::class, 'index']);
    Route::post('/', [ElasticsearchProductController::class, 'store']);
    Route::get('/{id}', [ElasticsearchProductController::class, 'show']);
    Route::put('/{id}', [ElasticsearchProductController::class, 'update']);
    Route::delete('/{id}', [ElasticsearchProductController::class, 'destroy']);
});

Route::prefix('elastic/driver/gps/logging')->middleware(['auth:api'])->group(function () {
    Route::post('create/index', [ElasticsearchDriverGpsLoggingController::class, 'createIndex']);
    Route::get('/', [ElasticsearchDriverGpsLoggingController::class, 'index']);
    Route::post('/', [ElasticsearchDriverGpsLoggingController::class, 'store']);
    Route::get('{id}', [ElasticsearchDriverGpsLoggingController::class, 'show']);
    Route::put('{id}', [ElasticsearchDriverGpsLoggingController::class, 'update']);
    Route::delete('{id}', [ElasticsearchDriverGpsLoggingController::class, 'destroy']);
});

Route::prefix('autoroute')->middleware(['auth:api', 'role:' . implode(',', [MyHelper::$ADMIN, MyHelper::$VENDOR])])->group(function () {
    Route::controller(AutoRouteController::class)->group(function () { 
        Route::get('/data/for/create', 'dataForCreate');
        Route::post('/autorouteajax', 'AutoRouteAjax');
	    Route::post('/autorouteclubdata', 'AutoRouteClubData');
        Route::post('/getroutedetailsbyrouteid', 'GetRouteDetailsByRouteID');
        Route::post('/newroutecreate', 'NewRouteCreate');
        Route::post('/mergeautoroute', 'MergeAutoRoute');
        Route::post('/setroutevendor', 'SetRouteVendor');
        Route::post('/autoroutetoroster', 'AutoRouteToRoster');
        Route::post('/autoroutedetails', 'AutoRouteDetials');
        Route::post('/gettimeroutes', 'GetTimeRoutes');
        Route::get('/vendorassign', 'VendorAssignRoute');
        Route::get('/vendorlistroute', 'VendorListRoute');
        Route::get('/autoroutelist', 'AutoRouteList');
        Route::post('/autoroute_confirmnoshow', 'EmpNoshowRoute');
        Route::post('/get_time_result', 'GetTimeResult');
        Route::post('/admin_change_roster_req_time', 'AdminChangeRosterRequestTime');
        Route::post('/checkvendor', 'CheckVendor');
        Route::post('/getvendorroutecnt', 'GetVendorRouteCnt');
    });
});

Route::prefix('uploaddata')->middleware(['auth:api', 'role:' . implode(',', [MyHelper::$ADMIN, MyHelper::$VENDOR])])->group(function () {
    Route::controller(UploadDataController::class)->group(function () { 
        Route::get('/getemplocationupdate', 'GetEmpLocationUpdate');
        Route::post('/postemplocationupdate', 'PostEmpLocationUpdate');
    });
});

Route::prefix('transportdata')->middleware(['auth:api', 'role:' . implode(',', [MyHelper::$ADMIN, MyHelper::$VENDOR])])->group(function () {
    Route::controller(TransportDataController::class)->group(function () { 
        Route::get('/support', 'Support');
        Route::post('/postsupport', 'PostSuportUpdate');
	    Route::post('/postreopensupport', 'PostReopenSuportUpdate');
        Route::post('/support_remarks', 'SupportRemarks');
	    Route::post('/supportapproval', 'SupportApproval');
    });
});

Route::prefix('realtimetracking')->middleware(['auth:api', 'role:' . implode(',', [MyHelper::$ADMIN, MyHelper::$VENDOR])])->group(function () {
    Route::controller(RealTimeTrackingController::class)->group(function () {
        Route::get('/index', 'Index'); 
	    Route::get('/allotrealtimeroute', 'AllotRealTimeRoute');      
    });
});

Route::prefix('RRDMIS')->middleware(['auth:api', 'role:' . implode(',', [MyHelper::$ADMIN, MyHelper::$VENDOR])])->group(function () {
    Route::controller(RRDMISController::class)->group(function () {
        Route::get('/dataForCreate', 'dataForCreate');
	    Route::post('/vendorwise_vehicle_find', 'vendorwise_vehicle_find');   
	    Route::post('/fetch_RRDMis_Details', 'fetch_RRDMis_Details');   
    });
});

Route::prefix('billing')->middleware(['auth:api', 'role:' . implode(',', [MyHelper::$ADMIN, MyHelper::$VENDOR])])->group(function () {
    Route::controller(BillingController::class)->group(function () {
        Route::get('/data/for/create', 'dataForCreate');
        Route::post('/zohomisreport', 'ZohoMisReport');
        Route::post('/zohomisapprovel','ZohoMisApprovel');
        Route::post('/billkm_approve_update','billkm_approve_update');
        Route::post('/tollpayments','tollpayments');
        Route::post('/edit_toll_amount','edit_toll_amount');
        Route::get('/drivermisreportgenerate','drivermisreportgenerate');
        Route::post('/generatemisreport','GenerateMisReport');
        Route::post('/exportmisreport','exportmisreport');
        Route::post('/misreportdetails','MisReportDetails');

        Route::get('/driver/mis/report/data/for/create', 'drivermisreportdataForCreate');
        Route::get('/drivermisreportgeneratenew','DriverMisReportGenerateNew');
        Route::post('drivermisreportgeneratedetails', 'DriverMisReportGenerateDetails');
    });
});

Route::group(['prefix' => 'cronjob'], function () {
    Route::controller(CronjobController::class)->group(function () {
        Route::get('mask_channel', 'mask_channel');
        Route::get('update_secondary_address', 'update_secondary_address');
        Route::get('masknumber_new', 'masknumber_new');
        Route::get('update_tollpayment', 'update_tollpayment');
        Route::get('AddCabRosterDetails', 'AddCabRosterDetails');
        Route::get('AddCabMISDetails', 'AddCabMISDetails');
    });
});

Route::prefix('hashing')->group(function () {
    Route::controller(AuthController::class)->group(function (){ 
        Route::post('encrypt', 'messageencrypt');
        Route::post('decrypt', 'messagedecrypt');
    });
});


