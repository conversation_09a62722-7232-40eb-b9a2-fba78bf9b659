<?php

namespace App\Services\Admin;

use App\Helpers\CommonFunction;
use App\Helpers\MyHelper;
use App\Models\Branch;
use App\Models\property;
use App\Models\Roster;
use App\Models\RosterPassenger;
use App\Models\Cab_allocation;
use App\Models\OtpVerify;
use App\Models\RouteEscorts;
use App\Models\Reason_Log;
use App\Models\Cab;
use App\Models\Sms;
use Carbon\Carbon;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Crypt;
use App\Http\Controllers\MaskNumberClearController;

class TrackingDashboardService
{
    protected CommonFunction $commonFunction;
    protected ?Branch $currentBranch = null;

    public function __construct(CommonFunction $commonFunction)
    {
        $this->commonFunction = $commonFunction;
    }

    private function getPropertyValue()
    {
        $branch_id= Auth::user()->BRANCH_ID;
        //    exit;
        $property = Property::where('BRANCH_ID',  $branch_id)
            ->where('ACTIVE', MyHelper::$RS_ACTIVE)
            //->where('PROPERTIE_NAME', $propertyName)
            ->get();
			
        return $property;
    }

    public function tracking_dashboard($request): FoundationApplication|Response|ResponseFactory
    {

        $authUser = Auth::user();
        $tracking_type = $request->tracking_type;
        $date = Carbon::now();
        $curdatetime = $date->format("Y-m-d H:i:s");
        try {
            // echo "try";
            $PR_ESTIMATESUBTIME = MyHelper::$PR_ESTIMATESUBTIME;
            $PR_ESTIMATEADDTIME = MyHelper::$PR_ESTIMATEADDTIME;
            $PR_ESTIMATECURTIME = MyHelper::$PR_ESTIMATECURTIME;
            $PR_LEVEL2 = MyHelper::$PR_LEVEL2;
            $PR_ESTIMATECABALLOT = MyHelper::$PR_ESTIMATECABALLOT;
            $PR_LEVEL1 = MyHelper::$PR_LEVEL1;
            $PR_TRIPNOTEXECUTED = MyHelper::$PR_TRIPNOTEXECUTED;
            $PR_EMPLOYEE_BUFFER = MyHelper::$PR_EMPLOYEE_BUFFER;
			$user_type=$authUser->user_type;

            $property_result = $this->getPropertyValue();
            // print_r($property_result);
            // exit;
            for ($i = 0; $i < count($property_result); $i++) {
                $PROPERTIE_NAME = $property_result[$i]->PROPERTIE_NAME;
                $PROPERTIE_VALUE = $property_result[$i]->PROPERTIE_VALUE;
                switch ($PROPERTIE_NAME) {
                    case $PR_ESTIMATESUBTIME:
                        $ESTIMATE_SUBTIME = $PROPERTIE_VALUE;
                        break;
                    case $PR_ESTIMATEADDTIME:
                        $ESTIMATE_ADDTIME = $PROPERTIE_VALUE;
                        break;
                    case $PR_ESTIMATECURTIME:
                        $ESTIMATE_CURTIME = $PROPERTIE_VALUE;
                        break;
                    case $PR_LEVEL2:
                        $ESTIMATE_INTERVAL60 = $PROPERTIE_VALUE;
                        break;
                    case $PR_ESTIMATECABALLOT:
                        $ESTIMATE_CABALLOT = $PROPERTIE_VALUE;
                        break;
                    case $PR_LEVEL1:
                        $ESTIMATE_INTERVAL30 = $PROPERTIE_VALUE;
                        break;
                    case $PR_TRIPNOTEXECUTED:
                        $TRIP_NOT_EXECUTE = $PROPERTIE_VALUE;
                        break;
                     case $PR_EMPLOYEE_BUFFER:
                         $wapptime = $PROPERTIE_VALUE;
                        break;
                    default:
                        break;
                }
            }
			 if ($user_type == MyHelper::$ADMIN) {
				$vendorid = "";
				} else {
				$vendorid = "AND R.VENDOR_ID='$vendor_id'";
				}
            $track_data = Roster::query()
					->select(
                    'rosters.ROSTER_ID',
                    'rosters.BRANCH_ID',
                    'rosters.ROUTE_ID',
                    'rosters.TRIP_TYPE',
                    DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN rosters.ESTIMATE_END_TIME ELSE rosters.ESTIMATE_START_TIME END AS TRIPINTIME'),
                    DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN DATE(rosters.ESTIMATE_END_TIME) ELSE DATE(rosters.ESTIMATE_START_TIME) END AS TRIPDATE'),
                    DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN TIME(rosters.ESTIMATE_END_TIME) ELSE TIME(rosters.ESTIMATE_START_TIME) END AS TRIPTIME'),
                    'VE.NAME AS VENDORNAME',
                    'rosters.START_LOCATION',
                    'rosters.END_LOCATION',
                    'rosters.CAB_ID',
                    'rosters.VENDOR_ID',
                    'rosters.PASSENGER_ALLOT_COUNT',
                    'rosters.PASSENGER_CLUBING_COUNT',
                    'rosters.CAB_CAPACITY_COUNT',
                    'rosters.TRIP_APPROVED_KM',
                    'RD.TOLL_ROUTE_STATUS','rosters.ROSTER_STATUS',
                    DB::raw('IF(RE.STATUS IS NULL, 0, 1) AS ESCORTSTATUS'),
                    DB::raw('IF(RE.ESCORT_ID IS NULL, 0, 1) AS ESCORTALLOTSTATUS'),
                    'RE.ROUTE_ESCORT_ID',
                    DB::raw("IF(rosters.TRIP_TYPE = 'P', '$ESTIMATE_INTERVAL30', 'Interval 180 minute') AS EXECUTETIME"),
                    DB::raw('GROUP_CONCAT(RP.EMPLOYEE_ID) as empids0')
                )
                ->where('rosters.ACTIVE', MyHelper::$RS_ACTIVE)
                ->where('rosters.BRANCH_ID', $branch_id)
				 
				->whereIn('rosters.ROSTER_STATUSS',[$RS_NEWROSTER])
							
                ->whereIn('rosters.TRIP_TYPE', ['P', 'D'])
                ->leftjoin('roster_passengers as RP', 'RP.ROSTER_ID', '=', 'rosters.ROSTER_ID')
                ->leftJoin('vendors as VE', 'VE.VENDOR_ID', '=', 'rosters.VENDOR_ID')
                ->leftJoin('route_distance as RD', 'RD.ROSTER_ID', '=', 'rosters.ROSTER_ID')
                ->leftJoin('route_escorts as RE', 'RE.ROSTER_ID', '=', 'rosters.ROSTER_ID')
                ->groupBy('rosters.ROSTER_ID')
                ->havingRaw("TRIPINTIME BETWEEN SUBTIME('2019-07-01 01:30:00',$ESTIMATE_SUBTIME) AND ADDTIME('2019-07-30 01:30:00',$ESTIMATE_ADDTIME) AND '$curdatetime'>=SUBTIME(TRIPINTIME,$ESTIMATE_CURTIME)")
                ->orderBy("TRIPTIME")
                ->get();
            return response([
                'success' => true,
                'status' => 3,
                'employees' => $track_data,
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'No data',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function pagination_tracking_dashboard($request, bool $active = true): FoundationApplication|Response|ResponseFactory
    {
        try {


            $authUser = Auth::user();
			$branchid=$authUser->BRANCH_ID;
            $tracking_type = $request->tracking_type;
            $date = Carbon::now();
            $curdatetime = $date->format("Y-m-d H:i:s");
			$curdate= date('Y-m-d');


            $PR_ESTIMATESUBTIME = MyHelper::$PR_ESTIMATESUBTIME;
            $PR_ESTIMATEADDTIME = MyHelper::$PR_ESTIMATEADDTIME;
            $PR_ESTIMATECURTIME = MyHelper::$PR_ESTIMATECURTIME;
            $PR_LEVEL2 = MyHelper::$PR_LEVEL2;
            $PR_ESTIMATECABALLOT = MyHelper::$PR_ESTIMATECABALLOT;
            $PR_LEVEL1 = MyHelper::$PR_LEVEL1;
            $PR_TRIPNOTEXECUTED = MyHelper::$PR_TRIPNOTEXECUTED;
            $PR_EMPLOYEE_BUFFER = MyHelper::$PR_EMPLOYEE_BUFFER;
			
			$RS_ROSTER=MyHelper::$RS_ROSTER;
			
			$ESCORT_INTER=MyHelper::$ESCORT_INTER;
			$ESCORT_REMOVE=MyHelper::$ESCORT_REMOVE;
			
			$TD_ALLNORMAL=MyHelper::$TD_ALLNORMAL;
			$TD_NOTALLOTED=MyHelper::$TD_NOTALLOTED;
			$TD_ALLOTED=MyHelper::$TD_ALLOTED;
			$TD_TRIPACCEPTED=MyHelper::$TD_TRIPACCEPTED;
			$TD_TRIPREJECTED=MyHelper::$TD_TRIPREJECTED;
			$TD_TRIPEXECUTED=MyHelper::$TD_TRIPEXECUTED;
			$TD_NORESPONSE=MyHelper::$TD_NORESPONSE;
			$TD_TRIPNOTEXECUTED=MyHelper::$TD_TRIPNOTEXECUTED;
			$TD_BREAKDOWN=MyHelper::$TD_BREAKDOWN;
			$TD_WAITINGATPICKUPPOINT=MyHelper::$TD_WAITINGATPICKUPPOINT;
			$TD_SAFEDROP=MyHelper::$TD_SAFEDROP;
			
			
			$RS_NEWROSTER=MyHelper::$RS_NEWROSTER;
			$RS_TOTALALLOT=MyHelper::$RS_TOTALALLOT;
			$RS_TOTALACCEPT=MyHelper::$RS_TOTALACCEPT;
			$RS_TOTALREJECT=MyHelper::$RS_TOTALREJECT;
			$RS_TOTALEXECUTE=MyHelper::$RS_TOTALEXECUTE;
			$RS_TOTALBREAKDOWN=MyHelper::$RS_TOTALBREAKDOWN;
			
			$RPS_TTLARRIVAL=MyHelper::$RPS_TTLARRIVAL;
			$RPS_TTLCABDELAY=MyHelper::$RPS_TTLCABDELAY;
			
			
			

            $property_result = $this->getPropertyValue();
			$user_type=$authUser->user_type;
			$vendor_id= Auth::user()->vendor_id;
            // print_r($property_result);
            // exit;
            for ($i = 0; $i < count($property_result); $i++) {
                $PROPERTIE_NAME = $property_result[$i]->PROPERTIE_NAME;
                $PROPERTIE_VALUE = $property_result[$i]->PROPERTIE_VALUE;
                switch ($PROPERTIE_NAME) {
                    case $PR_ESTIMATESUBTIME:
                        $ESTIMATE_SUBTIME = $PROPERTIE_VALUE;
                        break;
                    case $PR_ESTIMATEADDTIME:
                        $ESTIMATE_ADDTIME = $PROPERTIE_VALUE;
                        break;
                    case $PR_ESTIMATECURTIME:
                        $ESTIMATE_CURTIME = $PROPERTIE_VALUE;
                        break;
                    case $PR_LEVEL2:
                        $ESTIMATE_INTERVAL60 = $PROPERTIE_VALUE;
                        break;
                    case $PR_ESTIMATECABALLOT:
                        $ESTIMATE_CABALLOT = $PROPERTIE_VALUE;
                        break;
                    case $PR_LEVEL1:
                        $ESTIMATE_INTERVAL30 = $PROPERTIE_VALUE;
                        break;
                    case $PR_TRIPNOTEXECUTED:
                        $TRIP_NOT_EXECUTE = $PROPERTIE_VALUE;
                        break;
                     case $PR_EMPLOYEE_BUFFER:
                         $wapptime = $PROPERTIE_VALUE;
						
                        break;
                    default:
                        break;
                }
            }

            $perPage = $request->input('per_page', MyHelper::$PAGINATION_PER_PAGE);
            $branch_id = Auth::user()->BRANCH_ID;
            $activeStatus = $active ? MyHelper::$RS_ACTIVE : MyHelper::$RS_INACTIVE;
			
			if($user_type == MyHelper::$ADMIN)
			{
				$vendorid = "";
			} else {
				//$vendorid = "AND R.VENDOR_ID='$vendor_id'";
				$vendorid = $vendor_id;
			}
			
//$curdatetime='2019-07-01 01:30:00';
//$curdate='2019-07-01';
       /*  $select_filed = "R.ROSTER_ID,R.BRANCH_ID,R.ROUTE_ID,R.TRIP_TYPE,
        IF(R.TRIP_TYPE = 'P',R.ESTIMATE_END_TIME,R.ESTIMATE_START_TIME) as TRIPINTIME,
        IF(R.TRIP_TYPE = 'P',DATE(R.ESTIMATE_END_TIME),DATE(R.ESTIMATE_START_TIME)) as TRIPDATE,
        IF(R.TRIP_TYPE = 'P',TIME(R.ESTIMATE_END_TIME),TIME(R.ESTIMATE_START_TIME)) as TRIPTIMES,
        VE.`NAME` AS VENDORNAME,R.START_LOCATION,R.END_LOCATION,R.CAB_ID,
        R.VENDOR_ID,R.PASSENGER_ALLOT_COUNT,R.PASSENGER_CLUBING_COUNT,R.CAB_CAPACITY_COUNT,R.TRIP_APPROVED_KM,RD.TOLL_ROUTE_STATUS,
		IF(RE.`STATUS` IS NULL,0,1) AS ESCORTSTATUS,IF(RE.ESCORT_ID IS NULL,0,1) AS ESCORTALLOTSTATUS,RE.ROUTE_ESCORT_ID,
        if(R.TRIP_TYPE = 'P','$ESTIMATE_INTERVAL30','Interval 180 minute') AS EXECUTETIME";

        $cabselect = "V.VEHICLE_REG_NO,VM.MODEL,RM.REASON,D.DRIVER_MOBILE";
        $vendor_join = "INNER JOIN vendors VE ON VE.VENDOR_ID=R.VENDOR_ID
		LEFT JOIN route_distance RD ON RD.ROSTER_ID = R.ROSTER_ID
      
	  LEFT JOIN route_escorts RE ON RE.ROSTER_ID = R.ROSTER_ID AND (DATE(R.ESTIMATE_END_TIME) = '$curdate' or date(R.ESTIMATE_START_TIME)='$curdate')
        AND RE.`STATUS` NOT IN ($ESCORT_INTER,$ESCORT_REMOVE)";
		

        $cabdetail_join = "INNER JOIN cab C ON C.CAB_ID=R.CAB_ID
        INNER JOIN vehicles V ON V.VEHICLE_ID=C.VEHICLE_ID
        INNER JOIN vehicle_models VM ON VM.VEHICLE_MODEL_ID=V.VEHICLE_MODEL_ID
        INNER JOIN drivers D ON D.DRIVERS_ID=C.DRIVER_ID
        INNER JOIN reason_master RM ON RM.REASON_ID=C.TARIFF_TYPE";
		$passenger_join="inner join roster_passengers RP on RP.ROSTER_ID=R.ROSTER_ID and R.ACTIVE=1";

        $whercond = "R.BRANCH_ID='$branchid' $vendorid and R.ACTIVE=".MyHelper::$RS_ACTIVE ." and R.TRIP_TYPE IN ('P','D')";
        $betweentime = "T.TRIPINTIME BETWEEN SUBTIME('$curdatetime',$ESTIMATE_SUBTIME) AND ADDTIME('$curdatetime',$ESTIMATE_ADDTIME)";
        $timeinterval = "AND (('$curdatetime'-$ESTIMATE_INTERVAL60)< T.TRIPINTIME)";
        
 */
			
			 switch ($tracking_type) 
			 {
				case $TD_ALLNORMAL:
					$data = Roster::query()
					->select(
                    'rosters.ROSTER_ID',
                    'rosters.BRANCH_ID',
                    'rosters.ROUTE_ID',
                    'rosters.TRIP_TYPE',
                    DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN rosters.ESTIMATE_END_TIME ELSE rosters.ESTIMATE_START_TIME END AS TRIPINTIME'),
                    DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN DATE(rosters.ESTIMATE_END_TIME) ELSE DATE(rosters.ESTIMATE_START_TIME) END AS TRIPDATE'),
                    DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN TIME(rosters.ESTIMATE_END_TIME) ELSE TIME(rosters.ESTIMATE_START_TIME) END AS TRIPTIME'),
                    'VE.NAME AS VENDORNAME',
                    'rosters.START_LOCATION',
                    'rosters.END_LOCATION',
                    'rosters.CAB_ID',
                    'rosters.VENDOR_ID',
                    'rosters.PASSENGER_ALLOT_COUNT',
                    'rosters.PASSENGER_CLUBING_COUNT',
                    'rosters.CAB_CAPACITY_COUNT',
                    'rosters.TRIP_APPROVED_KM',
                    'RD.TOLL_ROUTE_STATUS',
                    DB::raw('IF(RE.STATUS IS NULL, 0, 1) AS ESCORTSTATUS'),
                    DB::raw('IF(RE.ESCORT_ID IS NULL, 0, 1) AS ESCORTALLOTSTATUS'),
                    'RE.ROUTE_ESCORT_ID',
                    DB::raw("IF(rosters.TRIP_TYPE = 'P', '$ESTIMATE_INTERVAL30', 'Interval 180 minute') AS EXECUTETIME"),
                    DB::raw('GROUP_CONCAT(RP.EMPLOYEE_ID) as empids0')
                )
                ->where('rosters.ACTIVE', MyHelper::$RS_ACTIVE)
                ->where('rosters.ROSTER_STATUS', MyHelper::$RS_ROSTER)
				->where('rosters.BRANCH_ID', $branch_id)
				->when($vendorid, function ($query, $vendorid) {
						return $query->where('rosters.VENDOR_ID', $vendorid);
					})
                ->whereIn('rosters.TRIP_TYPE', ['P', 'D'])
                ->leftjoin('roster_passengers as RP', 'RP.ROSTER_ID', '=', 'rosters.ROSTER_ID')
                ->leftJoin('vendors as VE', 'VE.VENDOR_ID', '=', 'rosters.VENDOR_ID')
                ->leftJoin('route_distance as RD', 'RD.ROSTER_ID', '=', 'rosters.ROSTER_ID')
                ->leftJoin('route_escorts as RE', 'RE.ROSTER_ID', '=', 'rosters.ROSTER_ID')
                ->groupBy('rosters.ROSTER_ID')
                ->havingRaw("TRIPINTIME BETWEEN SUBTIME('$curdatetime',$ESTIMATE_SUBTIME) AND ADDTIME('$curdatetime',$ESTIMATE_ADDTIME) and  '$curdatetime' < SUBTIME(TRIPINTIME,$ESTIMATE_CURTIME)  "); 
				
				break;
				
				case $TD_NOTALLOTED:
					$status= explode(",",$RS_NEWROSTER); 
					
					$data = Roster::query()
					->select(
                    'rosters.ROSTER_ID',
                    'rosters.BRANCH_ID',
                    'rosters.ROUTE_ID',
                    'rosters.TRIP_TYPE',
                    DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN rosters.ESTIMATE_END_TIME ELSE rosters.ESTIMATE_START_TIME END AS TRIPINTIME'),
                    DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN DATE(rosters.ESTIMATE_END_TIME) ELSE DATE(rosters.ESTIMATE_START_TIME) END AS TRIPDATE'),
                    DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN TIME(rosters.ESTIMATE_END_TIME) ELSE TIME(rosters.ESTIMATE_START_TIME) END AS TRIPTIME'),
                    'VE.NAME AS VENDORNAME',
                    'rosters.START_LOCATION',
                    'rosters.END_LOCATION',
                    'rosters.CAB_ID',
                    'rosters.VENDOR_ID',
                    'rosters.PASSENGER_ALLOT_COUNT',
                    'rosters.PASSENGER_CLUBING_COUNT',
                    'rosters.CAB_CAPACITY_COUNT',
                    'rosters.TRIP_APPROVED_KM',
                    'RD.TOLL_ROUTE_STATUS','rosters.ROSTER_STATUS',
                    DB::raw('IF(RE.STATUS IS NULL, 0, 1) AS ESCORTSTATUS'),
                    DB::raw('IF(RE.ESCORT_ID IS NULL, 0, 1) AS ESCORTALLOTSTATUS'),
                    'RE.ROUTE_ESCORT_ID',
                    DB::raw("IF(rosters.TRIP_TYPE = 'P', '$ESTIMATE_INTERVAL30', 'Interval 180 minute') AS EXECUTETIME"),
                    DB::raw('GROUP_CONCAT(RP.EMPLOYEE_ID) as empids0')
                )
                ->where('rosters.ACTIVE', MyHelper::$RS_ACTIVE)
                ->where('rosters.BRANCH_ID', $branch_id)
				->when($vendorid, function ($query, $vendorid) {
						return $query->where('rosters.VENDOR_ID', $vendorid);
					})
				 
				->whereIn('rosters.ROSTER_STATUS',$status)
							
                ->whereIn('rosters.TRIP_TYPE', ['P', 'D'])
                ->leftjoin('roster_passengers as RP', 'RP.ROSTER_ID', '=', 'rosters.ROSTER_ID')
                ->leftJoin('vendors as VE', 'VE.VENDOR_ID', '=', 'rosters.VENDOR_ID')
                ->leftJoin('route_distance as RD', 'RD.ROSTER_ID', '=', 'rosters.ROSTER_ID')
                ->leftJoin('route_escorts as RE', 'RE.ROSTER_ID', '=', 'rosters.ROSTER_ID')
                ->groupBy('rosters.ROSTER_ID')
                ->havingRaw("TRIPINTIME BETWEEN SUBTIME('$curdatetime',$ESTIMATE_SUBTIME) AND ADDTIME('$curdatetime',$ESTIMATE_ADDTIME) AND '$curdatetime'>=SUBTIME(TRIPINTIME,$ESTIMATE_CURTIME)");
				
				break;
				
				//Trip Alloted
				case $TD_ALLOTED:   
				
				$status= explode(",",$RS_TOTALALLOT); 
				
				$data= DB::table('rosters')
				->leftJoin('cab as C', 'C.CAB_ID', '=', 'rosters.CAB_ID')
				->leftJoin('vehicles as VH', 'VH.VEHICLE_ID', '=', 'C.VEHICLE_ID')
				->leftJoin('vehicle_models as VM', 'VM.VEHICLE_MODEL_ID', '=', 'VH.VEHICLE_MODEL_ID')
				->leftJoin('drivers as D', 'D.DRIVERS_ID', '=', 'C.DRIVER_ID')
				->leftJoin('reason_master as RM', 'RM.REASON_ID', '=', 'C.TARIFF_TYPE')
				->leftJoin('roster_passengers as RP', 'RP.ROSTER_ID', '=', 'rosters.ROSTER_ID')
				->leftJoin('vendors as VE', 'VE.VENDOR_ID', '=', 'rosters.VENDOR_ID')
				->leftJoin('route_distance as RD', 'RD.ROSTER_ID', '=', 'rosters.ROSTER_ID')
				->leftJoin('route_escorts as RE', function ($join) use ($curdate,$ESCORT_INTER,$ESCORT_REMOVE) {
				$join->on('RE.ROSTER_ID', '=', 'rosters.ROSTER_ID')
             ->where(function ($query) use ($curdate) {
                 $query->whereDate('rosters.ESTIMATE_END_TIME', '=', $curdate)
                       ->orWhereDate('rosters.ESTIMATE_START_TIME', '=', $curdate);
             })
             ->whereNotIn('RE.STATUS',[$ESCORT_INTER,$ESCORT_REMOVE]);
           
    })
    ->select([
        'rosters.ROSTER_ID', 
        'rosters.BRANCH_ID', 
        'rosters.ROUTE_ID', 
        'rosters.TRIP_TYPE',
        DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN rosters.ESTIMATE_END_TIME ELSE rosters.ESTIMATE_START_TIME END AS TRIPINTIME'),
        DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN DATE(rosters.ESTIMATE_END_TIME) ELSE DATE(rosters.ESTIMATE_START_TIME) END AS TRIPDATE'),
        DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN TIME(rosters.ESTIMATE_END_TIME) ELSE TIME(rosters.ESTIMATE_START_TIME) END AS TRIPTIME'),
        'VE.NAME as VENDORNAME',
        'rosters.START_LOCATION', 
        'rosters.END_LOCATION', 
        'rosters.CAB_ID', 
        'rosters.VENDOR_ID', 
        'rosters.PASSENGER_ALLOT_COUNT',
        'rosters.PASSENGER_CLUBING_COUNT',
        'rosters.CAB_CAPACITY_COUNT',
        'rosters.CAB_ALLOT_TIME',
        'rosters.TRIP_APPROVED_KM',
        'VH.VEHICLE_REG_NO', 
        'VM.MODEL', 
        'RM.REASON', 
        'D.DRIVER_MOBILE', 
        'RD.TOLL_ROUTE_STATUS',
        'rosters.ROSTER_STATUS',
        DB::raw('IF(RE.STATUS IS NULL, 0, 1) AS ESCORTSTATUS'),
        DB::raw('IF(RE.ESCORT_ID IS NULL, 0, 1) AS ESCORTALLOTSTATUS'),
        'RE.ROUTE_ESCORT_ID',
        DB::raw('IF(rosters.TRIP_TYPE = "P", "Interval 30 minute", "Interval 180 minute") AS EXECUTETIME'),
        DB::raw('GROUP_CONCAT(RP.EMPLOYEE_ID) as empids0')
    ])
    ->where('rosters.ACTIVE', 1)
    ->where('rosters.BRANCH_ID', $branch_id)
    ->whereIn('rosters.ROSTER_STATUS', $status)
	->when($vendorid, function ($query, $vendorid) {
						return $query->where('rosters.VENDOR_ID', $vendorid);
					})
    ->whereIn('rosters.TRIP_TYPE', ['P', 'D'])
    ->groupBy('rosters.ROSTER_ID')
                ->havingRaw("TIMEDIFF('$curdatetime',`rosters`.`CAB_ALLOT_TIME`) < '$ESTIMATE_CABALLOT' and TRIPINTIME BETWEEN SUBTIME('$curdatetime',$ESTIMATE_SUBTIME) AND ADDTIME('$curdatetime',$ESTIMATE_ADDTIME) AND (('$curdatetime'-$ESTIMATE_INTERVAL60)< TRIPINTIME)");
				
				break;
				// Trip Accepted
				case $TD_TRIPACCEPTED:
				
				$status= explode(",",$RS_TOTALACCEPT); 
				
					$data = Roster::query()
					->select(
                    'rosters.ROSTER_ID',
                    'rosters.BRANCH_ID',
                    'rosters.ROUTE_ID',
                    'rosters.TRIP_TYPE',
                    DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN rosters.ESTIMATE_END_TIME ELSE rosters.ESTIMATE_START_TIME END AS TRIPINTIME'),
                    DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN DATE(rosters.ESTIMATE_END_TIME) ELSE DATE(rosters.ESTIMATE_START_TIME) END AS TRIPDATE'),
                    DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN TIME(rosters.ESTIMATE_END_TIME) ELSE TIME(rosters.ESTIMATE_START_TIME) END AS TRIPTIME'),
                    'VE.NAME AS VENDORNAME',
                    'rosters.START_LOCATION',
                    'rosters.END_LOCATION',
                    'rosters.CAB_ID',
                    'rosters.VENDOR_ID',
                    'rosters.PASSENGER_ALLOT_COUNT',
                    'rosters.PASSENGER_CLUBING_COUNT',
                    'rosters.CAB_CAPACITY_COUNT','rosters.CAB_ALLOT_TIME',
                    'rosters.TRIP_APPROVED_KM','VH.VEHICLE_REG_NO','VM.MODEL','RM.REASON','D.DRIVER_MOBILE',
                    'RD.TOLL_ROUTE_STATUS','rosters.ROSTER_STATUS',
                    DB::raw('IF(RE.STATUS IS NULL, 0, 1) AS ESCORTSTATUS'),
                    DB::raw('IF(RE.ESCORT_ID IS NULL, 0, 1) AS ESCORTALLOTSTATUS'),
                    'RE.ROUTE_ESCORT_ID',
                    DB::raw("IF(rosters.TRIP_TYPE = 'P', '$ESTIMATE_INTERVAL30', 'Interval 180 minute') AS EXECUTETIME"),
                    DB::raw('GROUP_CONCAT(RP.EMPLOYEE_ID) as empids0')
                )
                ->where('rosters.ACTIVE', MyHelper::$RS_ACTIVE)
                ->where('rosters.BRANCH_ID', $branch_id)
				->when($vendorid, function ($query, $vendorid) {
						return $query->where('rosters.VENDOR_ID', $vendorid);
					})
				 
				->whereIn('rosters.ROSTER_STATUS',$status)
							
                ->whereIn('rosters.TRIP_TYPE', ['P', 'D'])
				->leftjoin('cab as C','C.CAB_ID',"=","rosters.CAB_ID")
				->leftjoin('vehicles as VH','VH.VEHICLE_ID',"=","C.VEHICLE_ID")
				->leftjoin('vehicle_models as VM','VM.VEHICLE_MODEL_ID',"=","VH.VEHICLE_MODEL_ID")
				->leftjoin('drivers as D','D.DRIVERS_ID',"=","C.DRIVER_ID")
				->leftjoin('reason_master as RM','RM.REASON_ID',"=","C.TARIFF_TYPE")
                ->leftjoin('roster_passengers as RP', 'RP.ROSTER_ID', '=', 'rosters.ROSTER_ID')
                ->leftJoin('vendors as VE', 'VE.VENDOR_ID', '=', 'rosters.VENDOR_ID')
                ->leftJoin('route_distance as RD', 'RD.ROSTER_ID', '=', 'rosters.ROSTER_ID')
                //->leftJoin('route_escorts as RE', 'RE.ROSTER_ID', '=', 'rosters.ROSTER_ID',)
				/*  ->leftjoin('route_escorts as RE', function($join) use ($curdate, $ESCORT_INTER, $ESCORT_REMOVE) {
					$join->on('RE.ROSTER_ID', '=', 'rosters.ROSTER_ID')
						 ->whereDate('rosters.ESTIMATE_END_TIME', $curdate)
						 ->orWhereDate('rosters.ESTIMATE_START_TIME', $curdate)
						 ->whereNotIn('RE.STATUS', [$ESCORT_INTER, $ESCORT_REMOVE]); 
						})
				 */
				 ->leftJoin('route_escorts as RE', function ($join) use ($curdate,$ESCORT_INTER, $ESCORT_REMOVE) {
				$join->on('RE.ROSTER_ID', '=', 'rosters.ROSTER_ID')
				->where(function ($query) use ($curdate) {
                 $query->whereDate('rosters.ESTIMATE_END_TIME', '=', $curdate)
                       ->orWhereDate('rosters.ESTIMATE_START_TIME', '=', $curdate);
             })
             ->whereNotIn('RE.STATUS', [$ESCORT_INTER,$ESCORT_REMOVE]);
    })
				
                ->groupBy('rosters.ROSTER_ID')
                ->havingRaw("TRIPINTIME BETWEEN SUBTIME('$curdatetime',$ESTIMATE_SUBTIME) AND ADDTIME('$curdatetime',$ESTIMATE_ADDTIME) AND (('$curdatetime'-$ESTIMATE_INTERVAL60)< TRIPINTIME)");
				
				break;
				// Trip Rejected
				case $TD_TRIPREJECTED:
				
				$status= explode(",",$RS_TOTALREJECT); 
				
					$data = Roster::query()
					->select(
                    'rosters.ROSTER_ID',
                    'rosters.BRANCH_ID',
                    'rosters.ROUTE_ID',
                    'rosters.TRIP_TYPE',
                    DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN rosters.ESTIMATE_END_TIME ELSE rosters.ESTIMATE_START_TIME END AS TRIPINTIME'),
                    DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN DATE(rosters.ESTIMATE_END_TIME) ELSE DATE(rosters.ESTIMATE_START_TIME) END AS TRIPDATE'),
                    DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN TIME(rosters.ESTIMATE_END_TIME) ELSE TIME(rosters.ESTIMATE_START_TIME) END AS TRIPTIME'),
                    'VE.NAME AS VENDORNAME',
                    'rosters.START_LOCATION',
                    'rosters.END_LOCATION',
                    'rosters.CAB_ID',
                    'rosters.VENDOR_ID',
                    'rosters.PASSENGER_ALLOT_COUNT',
                    'rosters.PASSENGER_CLUBING_COUNT',
                    'rosters.CAB_CAPACITY_COUNT','rosters.CAB_ALLOT_TIME',
                    'rosters.TRIP_APPROVED_KM','VH.VEHICLE_REG_NO','VM.MODEL','RM.REASON','D.DRIVER_MOBILE',
                    'RD.TOLL_ROUTE_STATUS','rosters.ROSTER_STATUS',
                    DB::raw('IF(RE.STATUS IS NULL, 0, 1) AS ESCORTSTATUS'),
                    DB::raw('IF(RE.ESCORT_ID IS NULL, 0, 1) AS ESCORTALLOTSTATUS'),
                    'RE.ROUTE_ESCORT_ID',
                    DB::raw("IF(rosters.TRIP_TYPE = 'P', '$ESTIMATE_INTERVAL30', 'Interval 180 minute') AS EXECUTETIME"),
                    DB::raw('GROUP_CONCAT(RP.EMPLOYEE_ID) as empids0')
                )
                ->where('rosters.ACTIVE', MyHelper::$RS_ACTIVE)
                ->where('rosters.BRANCH_ID', $branch_id)
				->when($vendorid, function ($query, $vendorid) {
						return $query->where('rosters.VENDOR_ID', $vendorid);
					})
				 
				->whereIn('rosters.ROSTER_STATUS',$status)
							
                ->whereIn('rosters.TRIP_TYPE', ['P', 'D'])
				->leftjoin('cab as C','C.CAB_ID',"=","rosters.CAB_ID")
				->leftjoin('vehicles as VH','VH.VEHICLE_ID',"=","C.VEHICLE_ID")
				->leftjoin('vehicle_models as VM','VM.VEHICLE_MODEL_ID',"=","VH.VEHICLE_MODEL_ID")
				->leftjoin('drivers as D','D.DRIVERS_ID',"=","C.DRIVER_ID")
				->leftjoin('reason_master as RM','RM.REASON_ID',"=","C.TARIFF_TYPE")
                ->leftjoin('roster_passengers as RP', 'RP.ROSTER_ID', '=', 'rosters.ROSTER_ID')
                ->leftJoin('vendors as VE', 'VE.VENDOR_ID', '=', 'rosters.VENDOR_ID')
                ->leftJoin('route_distance as RD', 'RD.ROSTER_ID', '=', 'rosters.ROSTER_ID')
                ->leftJoin('route_escorts as RE', function ($join) use ($curdate,$ESCORT_INTER, $ESCORT_REMOVE) 
				{
					$join->on('RE.ROSTER_ID', '=', 'rosters.ROSTER_ID')
					->where(function ($query) use ($curdate) {
					 $query->whereDate('rosters.ESTIMATE_END_TIME', '=', $curdate)
						   ->orWhereDate('rosters.ESTIMATE_START_TIME', '=', $curdate);
					})
					->whereNotIn('RE.STATUS', [$ESCORT_INTER, $ESCORT_REMOVE]);
				})
				
                ->groupBy('rosters.ROSTER_ID')
                ->havingRaw("TIMEDIFF('$curdatetime',`rosters`.`CAB_ALLOT_TIME`) < '$ESTIMATE_CABALLOT' and TRIPINTIME BETWEEN SUBTIME('$curdatetime',$ESTIMATE_SUBTIME) AND ADDTIME('$curdatetime',$ESTIMATE_ADDTIME) AND (('$curdatetime'-$ESTIMATE_INTERVAL60)< TRIPINTIME)");
				
				break;
				
				//Trip Executed
				
				case $TD_TRIPEXECUTED: 
				
				$status= explode(",",$RS_TOTALEXECUTE); 
				
					$data = Roster::query()
					->select(
                    'rosters.ROSTER_ID',
                    'rosters.BRANCH_ID',
                    'rosters.ROUTE_ID',
                    'rosters.TRIP_TYPE',
                    DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN rosters.ESTIMATE_END_TIME ELSE rosters.ESTIMATE_START_TIME END AS TRIPINTIME'),
                    DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN DATE(rosters.ESTIMATE_END_TIME) ELSE DATE(rosters.ESTIMATE_START_TIME) END AS TRIPDATE'),
                    DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN TIME(rosters.ESTIMATE_END_TIME) ELSE TIME(rosters.ESTIMATE_START_TIME) END AS TRIPTIME'),
                    'VE.NAME AS VENDORNAME',
                    'rosters.START_LOCATION',
                    'rosters.END_LOCATION',
                    'rosters.CAB_ID',
                    'rosters.VENDOR_ID',
                    'rosters.PASSENGER_ALLOT_COUNT',
                    'rosters.PASSENGER_CLUBING_COUNT',
                    'rosters.CAB_CAPACITY_COUNT','rosters.CAB_ALLOT_TIME',
                    'rosters.TRIP_APPROVED_KM','VH.VEHICLE_REG_NO','VM.MODEL','RM.REASON','D.DRIVER_MOBILE',
                    'RD.TOLL_ROUTE_STATUS','rosters.ROSTER_STATUS',
                    DB::raw('IF(RE.STATUS IS NULL, 0, 1) AS ESCORTSTATUS'),
                    DB::raw('IF(RE.ESCORT_ID IS NULL, 0, 1) AS ESCORTALLOTSTATUS'),
                    'RE.ROUTE_ESCORT_ID',
                    //DB::raw("IF(rosters.TRIP_TYPE = 'P', '$ESTIMATE_INTERVAL30', 'Interval 180 minute') AS EXECUTETIME"),
					DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN "Interval 30 minute" ELSE "Interval 180 minute" END AS EXECUTETIME'),
                    DB::raw('GROUP_CONCAT(RP.EMPLOYEE_ID) as empids0')
                )
                ->where('rosters.ACTIVE', MyHelper::$RS_ACTIVE)
                ->where('rosters.BRANCH_ID', $branch_id)
				->when($vendorid, function ($query, $vendorid) {
						return $query->where('rosters.VENDOR_ID', $vendorid);
					})
				 
				->whereIn('rosters.ROSTER_STATUS',$status)
							
                ->whereIn('rosters.TRIP_TYPE', ['P', 'D'])
				->leftjoin('cab as C','C.CAB_ID',"=","rosters.CAB_ID")
				->leftjoin('vehicles as VH','VH.VEHICLE_ID',"=","C.VEHICLE_ID")
				->leftjoin('vehicle_models as VM','VM.VEHICLE_MODEL_ID',"=","VH.VEHICLE_MODEL_ID")
				->leftjoin('drivers as D','D.DRIVERS_ID',"=","C.DRIVER_ID")
				->leftjoin('reason_master as RM','RM.REASON_ID',"=","C.TARIFF_TYPE")
                ->leftjoin('roster_passengers as RP', 'RP.ROSTER_ID', '=', 'rosters.ROSTER_ID')
                ->leftJoin('vendors as VE', 'VE.VENDOR_ID', '=', 'rosters.VENDOR_ID')
                ->leftJoin('route_distance as RD', 'RD.ROSTER_ID', '=', 'rosters.ROSTER_ID')
                ->leftJoin('route_escorts as RE', function ($join) use ($curdate,$ESCORT_INTER, $ESCORT_REMOVE) 
				{
					$join->on('RE.ROSTER_ID', '=', 'rosters.ROSTER_ID')
					->where(function ($query) use ($curdate) {
					 $query->whereDate('rosters.ESTIMATE_END_TIME', '=', $curdate)
						   ->orWhereDate('rosters.ESTIMATE_START_TIME', '=', $curdate);
					})
					->whereNotIn('RE.STATUS', [$ESCORT_INTER, $ESCORT_REMOVE]);
				})
				
                ->groupBy('rosters.ROSTER_ID')
               // ->havingRaw("TRIPINTIME BETWEEN SUBTIME('$curdatetime',$ESTIMATE_SUBTIME) AND ADDTIME('$curdatetime',$ESTIMATE_ADDTIME) AND (('$curdatetime'-$ESTIMATE_INTERVAL60)< TRIPINTIME) and  (('$curdatetime'- IF(TRIP_TYPE = 'P', '$ESTIMATE_INTERVAL30', 'Interval 180 minute') < TRIPINTIME)");
                ->havingRaw("TRIPINTIME BETWEEN SUBTIME('$curdatetime',$ESTIMATE_SUBTIME) AND ADDTIME('$curdatetime',$ESTIMATE_ADDTIME) AND (('$curdatetime'-$ESTIMATE_INTERVAL60)< TRIPINTIME) ");
				//and  (DATE_SUB('$curdatetime',INTERVAL IF(TRIP_TYPE = 'P',  '$ESTIMATE_INTERVAL30', 180) MINUTE) < TRIPINTIME)
				break;
				
				case $TD_NORESPONSE:   
				
				$status= explode(",",$RS_TOTALALLOT); 
				
				$data= Roster::query()
				->leftJoin('cab as C', 'C.CAB_ID', '=', 'rosters.CAB_ID')
				->leftJoin('vehicles as VH', 'VH.VEHICLE_ID', '=', 'C.VEHICLE_ID')
				->leftJoin('vehicle_models as VM', 'VM.VEHICLE_MODEL_ID', '=', 'VH.VEHICLE_MODEL_ID')
				->leftJoin('drivers as D', 'D.DRIVERS_ID', '=', 'C.DRIVER_ID')
				->leftJoin('reason_master as RM', 'RM.REASON_ID', '=', 'C.TARIFF_TYPE')
				->leftJoin('roster_passengers as RP', 'RP.ROSTER_ID', '=', 'rosters.ROSTER_ID')
				
				//->leftJoin('roster_passengers AS RP', 'RP.ROSTER_ID', '=', 'rosters.ROSTER_ID')
				->leftJoin('employees AS E', function ($join) use ($branch_id) {
				 $join->on('E.EMPLOYEES_ID', '=', 'RP.EMPLOYEE_ID')
				->where('E.BRANCH_ID',$branch_id);
					})
				
				->leftJoin('vendors as VE', 'VE.VENDOR_ID', '=', 'rosters.VENDOR_ID')
				->leftJoin('route_distance as RD', 'RD.ROSTER_ID', '=', 'rosters.ROSTER_ID')
				->leftJoin('route_escorts as RE', function ($join) use ($curdate,$ESCORT_INTER,$ESCORT_REMOVE) {
				$join->on('RE.ROSTER_ID', '=', 'rosters.ROSTER_ID')
             ->where(function ($query) use ($curdate) {
                 $query->whereDate('rosters.ESTIMATE_END_TIME', '=', $curdate)
                       ->orWhereDate('rosters.ESTIMATE_START_TIME', '=', $curdate);
             })
             ->whereNotIn('RE.STATUS',[$ESCORT_INTER,$ESCORT_REMOVE]);
           
    })
    ->select([
        'rosters.ROSTER_ID', 
        'rosters.BRANCH_ID', 
        'rosters.ROUTE_ID', 
        'rosters.TRIP_TYPE',
        DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN rosters.ESTIMATE_END_TIME ELSE rosters.ESTIMATE_START_TIME END AS TRIPINTIME'),
        DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN DATE(rosters.ESTIMATE_END_TIME) ELSE DATE(rosters.ESTIMATE_START_TIME) END AS TRIPDATE'),
        DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN TIME(rosters.ESTIMATE_END_TIME) ELSE TIME(rosters.ESTIMATE_START_TIME) END AS TRIPTIME'),
        'VE.NAME as VENDORNAME',
        'rosters.START_LOCATION', 
        'rosters.END_LOCATION', 
        'rosters.CAB_ID', 
        'rosters.VENDOR_ID', 
        'rosters.PASSENGER_ALLOT_COUNT',
        'rosters.PASSENGER_CLUBING_COUNT',
        'rosters.CAB_CAPACITY_COUNT',
        'rosters.CAB_ALLOT_TIME',
        'rosters.TRIP_APPROVED_KM',
        'VH.VEHICLE_REG_NO', 
        'VM.MODEL', 
        'RM.REASON', 
        'D.DRIVER_MOBILE', 
        'RD.TOLL_ROUTE_STATUS',
        'rosters.ROSTER_STATUS','E.NAME as empname','E.GENDER',
        DB::raw('IF(RE.STATUS IS NULL, 0, 1) AS ESCORTSTATUS'),
        DB::raw('IF(RE.ESCORT_ID IS NULL, 0, 1) AS ESCORTALLOTSTATUS'),
        'RE.ROUTE_ESCORT_ID',
        DB::raw('IF(rosters.TRIP_TYPE = "P", "Interval 30 minute", "Interval 180 minute") AS EXECUTETIME'),
        DB::raw('GROUP_CONCAT(RP.EMPLOYEE_ID) as empids0')
    ])
    ->where('rosters.ACTIVE', 1)
    ->where('rosters.BRANCH_ID', $branch_id)
    ->whereIn('rosters.ROSTER_STATUS', $status)
	->when($vendorid, function ($query, $vendorid) {
						return $query->where('rosters.VENDOR_ID', $vendorid);
					})
    ->whereIn('rosters.TRIP_TYPE', ['P', 'D'])
    ->groupBy('rosters.ROSTER_ID')
                ->havingRaw("TIMEDIFF('$curdatetime',`rosters`.`CAB_ALLOT_TIME`) > '$ESTIMATE_CABALLOT' and TRIPINTIME BETWEEN SUBTIME('$curdatetime',$ESTIMATE_SUBTIME) AND ADDTIME('$curdatetime',$ESTIMATE_ADDTIME) AND (('$curdatetime'-$ESTIMATE_INTERVAL60)< TRIPINTIME)");
				
				break;
				
				//Trip Not Executed
				case $TD_TRIPNOTEXECUTED:
				
				$status= explode(",",$RS_TOTALACCEPT); 
				
					$data = Roster::query()
					->select(
                    'rosters.ROSTER_ID',
                    'rosters.BRANCH_ID',
                    'rosters.ROUTE_ID',
                    'rosters.TRIP_TYPE','rosters.ESTIMATE_START_TIME',
                    DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN rosters.ESTIMATE_END_TIME ELSE rosters.ESTIMATE_START_TIME END AS TRIPINTIME'),
                    DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN DATE(rosters.ESTIMATE_END_TIME) ELSE DATE(rosters.ESTIMATE_START_TIME) END AS TRIPDATE'),
                    DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN TIME(rosters.ESTIMATE_END_TIME) ELSE TIME(rosters.ESTIMATE_START_TIME) END AS TRIPTIME'),
                    'VE.NAME AS VENDORNAME',
                    'rosters.START_LOCATION',
                    'rosters.END_LOCATION',
                    'rosters.CAB_ID',
                    'rosters.VENDOR_ID',
                    'rosters.PASSENGER_ALLOT_COUNT',
                    'rosters.PASSENGER_CLUBING_COUNT',
                    'rosters.CAB_CAPACITY_COUNT','rosters.CAB_ALLOT_TIME',
                    'rosters.TRIP_APPROVED_KM','VH.VEHICLE_REG_NO','VM.MODEL','RM.REASON','D.DRIVER_MOBILE',
                    'RD.TOLL_ROUTE_STATUS','rosters.ROSTER_STATUS',
                    DB::raw('IF(RE.STATUS IS NULL, 0, 1) AS ESCORTSTATUS'),
                    DB::raw('IF(RE.ESCORT_ID IS NULL, 0, 1) AS ESCORTALLOTSTATUS'),
                    'RE.ROUTE_ESCORT_ID',
                    DB::raw("IF(rosters.TRIP_TYPE = 'P', '$ESTIMATE_INTERVAL30', 'Interval 180 minute') AS EXECUTETIME"),
                    DB::raw('GROUP_CONCAT(RP.EMPLOYEE_ID) as empids0')
                )
                ->where('rosters.ACTIVE', MyHelper::$RS_ACTIVE)
                ->where('rosters.BRANCH_ID', $branch_id)
				->when($vendorid, function ($query, $vendorid) {
						return $query->where('rosters.VENDOR_ID', $vendorid);
					})
				 
				->whereIn('rosters.ROSTER_STATUS',$status)
							
                ->whereIn('rosters.TRIP_TYPE', ['P', 'D'])
				->leftjoin('cab as C','C.CAB_ID',"=","rosters.CAB_ID")
				->leftjoin('vehicles as VH','VH.VEHICLE_ID',"=","C.VEHICLE_ID")
				->leftjoin('vehicle_models as VM','VM.VEHICLE_MODEL_ID',"=","VH.VEHICLE_MODEL_ID")
				->leftjoin('drivers as D','D.DRIVERS_ID',"=","C.DRIVER_ID")
				->leftjoin('reason_master as RM','RM.REASON_ID',"=","C.TARIFF_TYPE")
                ->leftjoin('roster_passengers as RP', 'RP.ROSTER_ID', '=', 'rosters.ROSTER_ID')
                ->leftJoin('vendors as VE', 'VE.VENDOR_ID', '=', 'rosters.VENDOR_ID')
                ->leftJoin('route_distance as RD', 'RD.ROSTER_ID', '=', 'rosters.ROSTER_ID')
                //->leftJoin('route_escorts as RE', 'RE.ROSTER_ID', '=', 'rosters.ROSTER_ID',)
				/*  ->leftjoin('route_escorts as RE', function($join) use ($curdate, $ESCORT_INTER, $ESCORT_REMOVE) {
					$join->on('RE.ROSTER_ID', '=', 'rosters.ROSTER_ID')
						 ->whereDate('rosters.ESTIMATE_END_TIME', $curdate)
						 ->orWhereDate('rosters.ESTIMATE_START_TIME', $curdate)
						 ->whereNotIn('RE.STATUS', [$ESCORT_INTER, $ESCORT_REMOVE]); 
						})
				 */
				 ->leftJoin('route_escorts as RE', function ($join) use ($curdate,$ESCORT_INTER, $ESCORT_REMOVE) {
				$join->on('RE.ROSTER_ID', '=', 'rosters.ROSTER_ID')
				->where(function ($query) use ($curdate) {
                 $query->whereDate('rosters.ESTIMATE_END_TIME', '=', $curdate)
                       ->orWhereDate('rosters.ESTIMATE_START_TIME', '=', $curdate);
             })
             ->whereNotIn('RE.STATUS', [$ESCORT_INTER,$ESCORT_REMOVE]);
    })
				
                ->groupBy('rosters.ROSTER_ID')
                ->havingRaw("TIMEDIFF('$curdatetime',ESTIMATE_START_TIME) < '$TRIP_NOT_EXECUTE' and TRIPINTIME BETWEEN SUBTIME('$curdatetime',$ESTIMATE_SUBTIME) AND ADDTIME('$curdatetime',$ESTIMATE_ADDTIME) AND (('$curdatetime'-$ESTIMATE_INTERVAL60)< TRIPINTIME)");
				
				break;
				
				// Break Down
			case $TD_BREAKDOWN:
				
					$status= explode(",",$RS_TOTALBREAKDOWN); 
					$data = Roster::query()
							->select(
								'rosters.ROSTER_ID',
								'rosters.BRANCH_ID',
								'rosters.ROUTE_ID',
								'rosters.TRIP_TYPE',
								'rosters.ESTIMATE_START_TIME',
								DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN rosters.ESTIMATE_END_TIME ELSE rosters.ESTIMATE_START_TIME END AS TRIPINTIME'),
								DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN DATE(rosters.ESTIMATE_END_TIME) ELSE DATE(rosters.ESTIMATE_START_TIME) END AS TRIPDATE'),
								DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN TIME(rosters.ESTIMATE_END_TIME) ELSE TIME(rosters.ESTIMATE_START_TIME) END AS TRIPTIME'),
								'VE.NAME AS VENDORNAME',
								'rosters.START_LOCATION',
								'rosters.END_LOCATION',
								'rosters.CAB_ID',
								'rosters.VENDOR_ID',
								'rosters.PASSENGER_ALLOT_IN_ROUT_COUNT',
								'rosters.TRIP_APPROVED_KM',
								'rosters.UPDATED_BY',
								'rosters.updated_at',
								'VH.VEHICLE_REG_NO',
								'D.DRIVERS_NAME',
								'D.DRIVER_MOBILE',
								'CA.ACTION',
								'RM.REASON',
								'U.name AS updateby'
							)
							->join('cab AS C', 'C.CAB_ID', '=', 'rosters.CAB_ID')
							->join('vendors AS VE', 'VE.VENDOR_ID', '=', 'rosters.VENDOR_ID')
							->join('vehicles AS VH', 'VH.VEHICLE_ID', '=', 'C.VEHICLE_ID')
							->join('vehicle_models AS VM', 'VM.VEHICLE_MODEL_ID', '=', 'VH.VEHICLE_MODEL_ID')
							->join('drivers AS D', 'D.DRIVERS_ID', '=', 'C.DRIVER_ID')
							
							
							 ->Join('cab_allocation AS CA', function ($join) use ($status) {
								 $join->on('CA.ROSTER_ID', '=', 'rosters.ROSTER_ID')
								->whereIn('CA.ACCEPTANCE_REJECT_STATE', $status);
				
								})
							
							->leftJoin('reason_master AS RM', 'RM.REASON_ID', '=', 'CA.REJECT_REASON_ID')
							->leftJoin('users AS U', 'U.id', '=', 'rosters.UPDATED_BY')
							->where('rosters.ACTIVE', MyHelper::$RS_INACTIVE)
							->where('rosters.BRANCH_ID', $branch_id)
							->whereIn('rosters.ROSTER_STATUS', $status)
							
							->whereDate('rosters.ESTIMATE_END_TIME', $curdate)
							//->where('CA.ACCEPTANCE_REJECT_STATE', $status)
							->when($vendorid, function ($query) use ($vendorid) {
								return $query->where('rosters.VENDOR_ID', $vendorid);
							})
							->groupBy('rosters.ROSTER_ID');   


				
				break;
				
				//Waitting At Pickup Point  Waiting At PickUp Point
					case $TD_WAITINGATPICKUPPOINT:
				
					$rps_status= explode(",",$RPS_TTLARRIVAL);
					$rps_delay_status= explode(",",$RPS_TTLCABDELAY);
					$status=array_merge($rps_status,$rps_delay_status);
					$data = Roster::query()
							->select(
								'rosters.ROSTER_ID',
								'rosters.BRANCH_ID',
								'rosters.TRIP_TYPE','rosters.ROUTE_ID',
								'RP.EMPLOYEE_ID','RP.DRIVER_ARRIVAL_TIME','RP.ESTIMATE_END_TIME','E.NAME as empname','E.GENDER',
								DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN rosters.ESTIMATE_END_TIME ELSE rosters.ESTIMATE_START_TIME END AS TRIPINTIME'),
								DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN DATE(rosters.ESTIMATE_END_TIME) ELSE DATE(rosters.ESTIMATE_START_TIME) END AS TRIPDATE'),
								DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN TIME(rosters.ESTIMATE_END_TIME) ELSE TIME(rosters.ESTIMATE_START_TIME) END AS TRIPTIME'),
								'VE.NAME AS VENDORNAME',
								'rosters.START_LOCATION',
								'rosters.END_LOCATION',
								'rosters.CAB_ID',
								'rosters.VENDOR_ID',
								'rosters.PASSENGER_ALLOT_IN_ROUT_COUNT',
								'rosters.TRIP_APPROVED_KM',
								'rosters.UPDATED_BY',
								'rosters.updated_at',
								'VH.VEHICLE_REG_NO',
								'D.DRIVERS_NAME',
								'D.DRIVER_MOBILE','B.BRANCH_NAME','VM.MODEL','L.LOCATION_NAME'
							)
							->join('cab AS C', 'C.CAB_ID', '=', 'rosters.CAB_ID')
							->join('vendors AS VE', 'VE.VENDOR_ID', '=', 'rosters.VENDOR_ID')
							->join('vehicles AS VH', 'VH.VEHICLE_ID', '=', 'C.VEHICLE_ID')
							->join('vehicle_models AS VM', 'VM.VEHICLE_MODEL_ID', '=', 'VH.VEHICLE_MODEL_ID')
							->join('drivers AS D', 'D.DRIVERS_ID', '=', 'C.DRIVER_ID')
							->Join('branch AS B', 'B.BRANCH_ID', '=', 'rosters.BRANCH_ID')
							->Join('reason_master AS RM', 'RM.REASON_ID', '=', 'C.TARIFF_TYPE')
							
							->Join('roster_passengers AS RP', 'RP.ROSTER_ID', '=', 'rosters.ROSTER_ID')
							->Join('locations AS L', 'L.LOCATION_ID', '=', 'RP.LOCATION_ID')
							
							
							->Join('employees AS E', function ($join) use ($branch_id) {
								 $join->on('E.EMPLOYEES_ID', '=', 'RP.EMPLOYEE_ID')
								->where('E.BRANCH_ID',$branch_id);
				
								})
							
							->where('rosters.TRIP_TYPE', 'P')
							
							->where('rosters.BRANCH_ID', $branch_id)
							
							->whereIn('RP.ROSTER_PASSENGER_STATUS', $status)
							
							->when($vendorid, function ($query) use ($vendorid) {
								return $query->where('rosters.VENDOR_ID', $vendorid);
							})
							//->groupBy('rosters.ROSTER_ID')
							->havingRaw("TIMEDIFF('$curdatetime',RP.DRIVER_ARRIVAL_TIME) > '$wapptime' AND RP.ESTIMATE_END_TIME BETWEEN SUBTIME('$curdatetime',10000) AND ADDTIME('$curdatetime',480000) AND '$curdatetime'>= SUBTIME(RP.ESTIMATE_END_TIME,040000)");

							
				break;
				
			//Safe Drop
					case $TD_SAFEDROP:
					
					$curdatetime='2024-09-20 16:29:32';
				
					$data = Roster::query()
							->select(
								'rosters.ROSTER_ID',
								'rosters.BRANCH_ID',
								'rosters.ROUTE_ID',
								'rosters.TRIP_TYPE','RP.ROSTER_PASSENGER_ID','RP.ROUTE_ORDER','E.ADDRESS','RP.ACTUAL_END_TIME',
								'RP.EMPLOYEE_ID','RP.DRIVER_ARRIVAL_TIME','RP.ESTIMATE_END_TIME','E.NAME as empname','E.GENDER',
							DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN rosters.ESTIMATE_END_TIME ELSE rosters.ESTIMATE_START_TIME END AS TRIPINTIME'),
							DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN DATE(rosters.ESTIMATE_END_TIME) ELSE DATE(rosters.ESTIMATE_START_TIME) END AS TRIPDATE'),
							DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN TIME(rosters.ESTIMATE_END_TIME) ELSE TIME(rosters.ESTIMATE_START_TIME) END AS TRIPTIME'),
								DB::raw('CASE WHEN RP.ROSTER_PASSENGER_STATUS & 256 THEN 1 ELSE 0 END AS SAFEDROP'),
								'rosters.ESTIMATE_START_TIME','RP.ROSTER_PASSENGER_STATUS',
								'VE.NAME AS VENDORNAME',
								'rosters.START_LOCATION',
								'rosters.END_LOCATION',
								'rosters.CAB_ID',
								'rosters.VENDOR_ID',
								'rosters.PASSENGER_ALLOT_IN_ROUT_COUNT',
								'rosters.TRIP_APPROVED_KM',
								'rosters.UPDATED_BY',
								'rosters.updated_at',
								'VH.VEHICLE_REG_NO',
								'D.DRIVERS_NAME',
								'D.DRIVER_MOBILE','B.BRANCH_NAME',DB::raw('if(E.GENDER = "F",1,0) AS FEMALE')
							)
							->join('cab AS C', 'C.CAB_ID', '=', 'rosters.CAB_ID')
							->join('vendors AS VE', 'VE.VENDOR_ID', '=', 'rosters.VENDOR_ID')
							->join('vehicles AS VH', 'VH.VEHICLE_ID', '=', 'C.VEHICLE_ID')
							->join('vehicle_models AS VM', 'VM.VEHICLE_MODEL_ID', '=', 'VH.VEHICLE_MODEL_ID')
							->join('drivers AS D', 'D.DRIVERS_ID', '=', 'C.DRIVER_ID')
							->Join('branch AS B', 'B.BRANCH_ID', '=', 'rosters.BRANCH_ID')
							->Join('reason_master AS RM', 'RM.BRANCH_ID', '=', DB::raw($branch_id))
							
							//->Join('reason_log AS RL', 'RL.ROSTER_PASSENGER_ID', '=', 'RP.ROSTER_PASSENGER_ID')
							->Join('roster_passengers AS RP', 'RP.ROSTER_ID', '=', 'rosters.ROSTER_ID')
							->leftJoin('reason_log AS RL', function ($join) {
								 $join->on('RL.ROSTER_PASSENGER_ID', '=', 'RP.ROSTER_PASSENGER_ID')
								->where('RL.REASON_ID','RM.REASON_ID');
                            })
							
							->Join('locations AS L', 'L.LOCATION_ID', '=', 'RP.LOCATION_ID')
							->Join('employees AS E', function ($join) use ($branch_id) {
								$join->on('E.EMPLOYEES_ID', '=', 'RP.EMPLOYEE_ID')
								->where('E.BRANCH_ID',$branch_id);
								})
                                ->where("RM.REASON", "=", 'Safe Drop' )
                                ->where("RM.BRANCH_ID", "=", $branch_id)
								
							->havingRaw("rosters.BRANCH_ID = $branch_id AND DATE(rosters.ESTIMATE_START_TIME) = '$curdate' AND rosters.ESTIMATE_START_TIME BETWEEN SUBTIME('$curdatetime',10000) AND ADDTIME('$curdatetime',480000) AND '$curdatetime'>=SUBTIME(rosters.ESTIMATE_START_TIME,040000) AND rosters.TRIP_TYPE = 'D' AND (RP.ROSTER_PASSENGER_STATUS & 32 OR RP.ROSTER_PASSENGER_STATUS & 128) ");  
							
				break;
				
				
			 }

            $filterModel = $request->input('filterModel');
            if ($filterModel) {
                foreach ($filterModel as $field => $filter) {
                    if (isset($filter['filter']) && $filter['filter'] !== '') {
                        $value = $filter['filter'];
                        $type = $filter['type'];

                        switch ($field) {
                            case 'EMPLOYEES_ID':
                                $data->where('E.EMPLOYEES_ID', 'like', "%{$value}%");
                                break;
                            case 'ROSTER_ID':
                                $data->where('rosters.ROSTER_ID', 'like', "%{$value}%");
                                break;
                            case 'ROUTE_ID':
                                $data->where('rosters.ROUTE_ID', 'like', "%{$value}%");
                                break;
                            case 'VENDORNAME':
                                $data->where('VE.NAME', 'like', "%{$value}%");
                                break;
                            case 'PASSENGER_ALLOT_COUNT':
                                $data->where('rosters.PASSENGER_ALLOT_COUNT', 'like', "%{$value}%");
                                break;
                            case 'START_LOCATION':
                                $data->where('rosters.START_LOCATION', 'like', "%{$value}%");
                                break;
                            case 'END_LOCATION':
                                $data->where('rosters.END_LOCATION', 'like', "%{$value}%");
                                break;
                            case 'VEHICLE_REG_NO':
                                $data->where('VH.VEHICLE_REG_NO', 'like', "%{$value}%");
                                break;
                            case 'MODEL':
                                $data->where('VM.MODEL', 'like', "%{$value}%");
                                break;
                            case 'TRIPTIME':
                                $data->where('rosters.ESTIMATE_END_TIME', 'like', "%{$value}%")
                                ->orwhere('rosters.ESTIMATE_START_TIME', 'like', "%{$value}%");
                                break;
                            case 'TRIP_APPROVED_KM':
                                $data->where('rosters.TRIP_APPROVED_KM', 'like', "%{$value}%");
                                break;
                            case 'DRIVERS_NAME':
                                $data->where('D.DRIVERS_NAME', 'like', "%{$value}%");
                                break;
                            case 'DRIVER_MOBILE':
                                $data->where('D.DRIVER_MOBILE', 'like', "%{$value}%");
                                break;
                            case 'TARIFF_TYPE':
                                $data->where('RM.REASON', 'like', "%{$value}%");
                                break;
                            case 'BRANCH_NAME':
                                $data->where('B.BRANCH_NAME', 'like', "%{$value}%");
                                break;
                            case 'LOCATION_NAME':
                                $data->where('L.LOCATION_NAME', 'like', "%{$value}%");
                                break;

                        }
                    }
                }
            }


            if ($request->has('orderBy') && !empty($request->input('orderBy'))) {
                $orderBy = $request->input('orderBy');
                $order = $request->input('order', 'asc');
                $data->orderBy($orderBy, $order);
            } else {
				
                $data->orderBy("TRIPTIME");
            }


            if ($perPage === MyHelper::$PAGINATION_PER_PAGE_ALL) {
                $paginatedData = $data->paginate($data->count());
            } else {
                $perPage = is_numeric($perPage) ? (int)$perPage : MyHelper::$PAGINATION_PER_PAGE;
				
                $paginatedData = $data->paginate($perPage);
            }
			/* $paginatedData = $paginatedData->map(function ($item) {
				$item->decrypted_name = Crypt::decrypt($item->empname);
				return $item;
			});
 */
            return response([
                'success' => true,
                'status' => 3,
                "normal_dashboard_data" => $paginatedData,
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'Data Pagination Unsuccessful first' : 'Data Pagination asasda Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }
	
	public function dashboard_count(): FoundationApplication|Response|ResponseFactory
    {

        $authUser = Auth::user();
        $branch_id = $authUser->BRANCH_ID;
		$RPTTLTYPE = explode(',',MyHelper::$RP_TTLTYPE);
		$RP_PICKROUTE = MyHelper::$RP_PICKROUTE;
		$RS_NEWROSTER=MyHelper::$RS_NEWROSTER;
		$RS_TOTALALLOT=MyHelper::$RS_TOTALALLOT;
		$RS_TOTALACCEPT=MyHelper::$RS_TOTALACCEPT;
		$RS_TOTALREJECT=MyHelper::$RS_TOTALREJECT;
		$RS_TOTALEXECUTE=MyHelper::$RS_TOTALEXECUTE;
		$RS_TOTALBREAKDOWN=MyHelper::$RS_TOTALBREAKDOWN;
		$RPS_TTLARRIVAL=MyHelper::$RPS_TTLARRIVAL;
		$RPS_TTLCABDELAY=MyHelper::$RPS_TTLCABDELAY;
		$RS_TOTALTRIPCLOSE=MyHelper::$RS_TOTALTRIPCLOSE;
		$RS_TOTALMANUALTRIPCLOSE=MyHelper::$RS_TOTALMANUALTRIPCLOSE;
		$RS_TOTALTRIPSHEETACCEPT=MyHelper::$RS_TOTALTRIPSHEETACCEPT;
		$RS_TOTALDELAYROUTES=MyHelper::$RS_TOTALDELAYROUTES;
		
			
		
        $date = Carbon::now();
        $currentDateTime = $date->format("Y-m-d H:i:s");
		$normal_cnt = 0;
        $notalloted_cnt = 0;
        $alloted_cnt = 0;
        $tripaccept_cnt = 0;
        $triprejected_cnt = 0;
        $tripnotexecute_cnt = 0;
        $tripexecuted_cnt = 0;
        $noresponse_cnt = 0;
        $tripclose_cnt = 0;
		$all_cnt = 0;
		
        try {
            
            $PR_ESTIMATESUBTIME = MyHelper::$PR_ESTIMATESUBTIME;
            $PR_ESTIMATEADDTIME = MyHelper::$PR_ESTIMATEADDTIME;
            $PR_ESTIMATECURTIME = MyHelper::$PR_ESTIMATECURTIME;
            $PR_LEVEL2 = MyHelper::$PR_LEVEL2;
            $PR_ESTIMATECABALLOT = MyHelper::$PR_ESTIMATECABALLOT;
            $PR_LEVEL1 = MyHelper::$PR_LEVEL1;
            $PR_TRIPNOTEXECUTED = MyHelper::$PR_TRIPNOTEXECUTED;
            $PR_EMPLOYEE_BUFFER = MyHelper::$PR_EMPLOYEE_BUFFER;
			$user_type=$authUser->user_type;
			$vendorid=$authUser->vendor_id;

            $property_result = $this->getPropertyValue();
           
            for ($i = 0; $i < count($property_result); $i++) {
                $PROPERTIE_NAME = $property_result[$i]->PROPERTIE_NAME;
                $PROPERTIE_VALUE = $property_result[$i]->PROPERTIE_VALUE;
                switch ($PROPERTIE_NAME) {
                    case $PR_ESTIMATESUBTIME:
                        $ESTIMATE_SUBTIME = $PROPERTIE_VALUE;
                        break;
                    case $PR_ESTIMATEADDTIME:
                        $ESTIMATE_ADDTIME = $PROPERTIE_VALUE;
                        break;
                    case $PR_ESTIMATECURTIME:
                        $ESTIMATE_CURTIME = $PROPERTIE_VALUE;
                        break;
                    case $PR_LEVEL2:
                        $ESTIMATE_INTERVAL60 = $PROPERTIE_VALUE;
                        break;
                    case $PR_ESTIMATECABALLOT:
                        $ESTIMATE_CABALLOT = $PROPERTIE_VALUE;
                        break;
                    case $PR_LEVEL1:
                        $ESTIMATE_INTERVAL30 = $PROPERTIE_VALUE;
                        break;
                    case $PR_TRIPNOTEXECUTED:
                        $TRIP_NOT_EXECUTE = $PROPERTIE_VALUE;
                        break;
                     case $PR_EMPLOYEE_BUFFER:
                         $wapptime = $PROPERTIE_VALUE;
                        break;
                    default:
                        break;
                }
            }
			if($user_type == MyHelper::$ADMIN) 
			{
				$vendorid = "";
				} else {
				$vendorid = $vendorid;
				}
        for($x=0;$x<count($RPTTLTYPE);$x++)
        {
            if($RPTTLTYPE[$x] == $RP_PICKROUTE)
            {
                 $TRIPTIME = 'ESTIMATE_END_TIME';
                $TREXECUTE = "DATE_SUB('$currentDateTime',$ESTIMATE_INTERVAL30)"; 
				
				
            }
            else
            {
                $TRIPTIME = 'ESTIMATE_START_TIME';
                $TREXECUTE = "SUBTIME('$currentDateTime',30000)";
            }

$results = DB::table('rosters')
    ->select(
        DB::raw("SUM(IF(ROSTER_STATUS IN (1, 3) AND $TRIPTIME BETWEEN SUBTIME('$currentDateTime', $ESTIMATE_SUBTIME) AND ADDTIME('$currentDateTime', $ESTIMATE_ADDTIME) AND PASSENGER_ALLOT_COUNT > 0 AND '$currentDateTime' < SUBTIME($TRIPTIME, $ESTIMATE_CURTIME), 1, 0)) AS normal_cnt"),
        
        DB::raw("SUM(IF(ROSTER_STATUS IN ($RS_NEWROSTER) AND $TRIPTIME BETWEEN SUBTIME('$currentDateTime', $ESTIMATE_SUBTIME) AND ADDTIME('$currentDateTime', $ESTIMATE_ADDTIME) AND PASSENGER_ALLOT_COUNT > 0 AND '$currentDateTime' >= SUBTIME($TRIPTIME, $ESTIMATE_CURTIME), 1, 0)) AS notalloted_cnt"),
        
        DB::raw("SUM(IF(ROSTER_STATUS IN ($RS_TOTALALLOT) AND $TRIPTIME BETWEEN SUBTIME('$currentDateTime', $ESTIMATE_SUBTIME) AND ADDTIME('$currentDateTime', $ESTIMATE_ADDTIME) AND ('$currentDateTime' - $ESTIMATE_INTERVAL60) < $TRIPTIME AND TIMEDIFF('$currentDateTime',CAB_ALLOT_TIME) < '$ESTIMATE_CABALLOT', 1, 0)) AS alloted_cnt"),
        
        DB::raw("SUM(IF(ROSTER_STATUS IN ($RS_TOTALACCEPT) AND $TRIPTIME BETWEEN SUBTIME('$currentDateTime', $ESTIMATE_SUBTIME) AND ADDTIME('$currentDateTime', $ESTIMATE_ADDTIME) AND ('$currentDateTime' - $ESTIMATE_INTERVAL60) < $TRIPTIME, 1, 0)) AS tripaccept_cnt"),
        
        DB::raw("SUM(IF(ROSTER_STATUS IN ($RS_TOTALREJECT) AND $TRIPTIME BETWEEN SUBTIME('$currentDateTime', $ESTIMATE_SUBTIME) AND ADDTIME('$currentDateTime', $ESTIMATE_ADDTIME) AND ('$currentDateTime' - $ESTIMATE_INTERVAL60) < $TRIPTIME, 1, 0)) AS triprejected_cnt"),
        
        DB::raw("SUM(IF(ROSTER_STATUS IN ($RS_TOTALACCEPT) AND $TRIPTIME BETWEEN SUBTIME('$currentDateTime', $ESTIMATE_SUBTIME) AND ADDTIME('$currentDateTime', $ESTIMATE_ADDTIME) AND TIMEDIFF('$currentDateTime', ESTIMATE_START_TIME) < '$TRIP_NOT_EXECUTE', 1, 0)) AS tripnotexecute_cnt"),
        
        DB::raw("SUM(IF(ROSTER_STATUS IN ($RS_TOTALEXECUTE) AND $TRIPTIME BETWEEN SUBTIME('$currentDateTime', $ESTIMATE_SUBTIME) AND ADDTIME('$currentDateTime',$ESTIMATE_ADDTIME) AND ($TREXECUTE < $TRIPTIME), 1, 0)) AS tripexecuted_cnt"),
        
        DB::raw("SUM(IF(ROSTER_STATUS IN ($RS_TOTALALLOT) AND $TRIPTIME BETWEEN SUBTIME('$currentDateTime', $ESTIMATE_SUBTIME) AND ADDTIME('$currentDateTime', $ESTIMATE_ADDTIME) AND ('$currentDateTime' - $ESTIMATE_INTERVAL60) < $TRIPTIME AND TIMEDIFF('$currentDateTime', CAB_ALLOT_TIME) > '$ESTIMATE_CABALLOT', 1, 0)) AS noresponse_cnt"),
        
        DB::raw("SUM(IF(ROSTER_STATUS IN ($RS_TOTALTRIPCLOSE, $RS_TOTALMANUALTRIPCLOSE, $RS_TOTALTRIPSHEETACCEPT, $RS_TOTALDELAYROUTES) AND             $TRIPTIME BETWEEN SUBTIME('$currentDateTime', $ESTIMATE_SUBTIME) AND ADDTIME('$currentDateTime', $ESTIMATE_ADDTIME) AND ('$currentDateTime' < $TRIPTIME), 1, 0)) AS tripclose_cnt")
    )
    ->where('BRANCH_ID', $branch_id)
    ->where('ACTIVE', '1')
    ->when($vendorid, function ($query) use ($vendorid) {
        return $query->where('rosters.VENDOR_ID', $vendorid);
    })
    ->whereBetween($TRIPTIME, [
        DB::raw("SUBTIME('$currentDateTime', $ESTIMATE_SUBTIME)"),
        DB::raw("ADDTIME('$currentDateTime', $ESTIMATE_ADDTIME)")
    ])
   // ->where('TRIP_TYPE', 'D')
    ->where('TRIP_TYPE', $RPTTLTYPE[$x])
    ->get();	
   
			$normal_cnt = $normal_cnt + $results[0]->normal_cnt;
            $notalloted_cnt = $notalloted_cnt + $results[0]->notalloted_cnt;
            $alloted_cnt = $alloted_cnt + $results[0]->alloted_cnt;
            $tripaccept_cnt = $tripaccept_cnt + $results[0]->tripaccept_cnt;
            $triprejected_cnt = $triprejected_cnt + $results[0]->triprejected_cnt;
            $tripnotexecute_cnt = $tripnotexecute_cnt + $results[0]->tripnotexecute_cnt;
            $tripexecuted_cnt = $tripexecuted_cnt + $results[0]->tripexecuted_cnt;
            $noresponse_cnt = $noresponse_cnt + $results[0]->noresponse_cnt;
            $tripclose_cnt = $tripclose_cnt + $results[0]->tripclose_cnt;
            
        }
     
		$breakdown_count=0;$waiting_cnt=0;
		$breakdown_count=$this->breakdown_count();
		
		$wapp_count=$this->wapp_count($wapptime);
		

  $returnarray = array("normal_cnt"=>$normal_cnt,"notalloted_cnt"=>$notalloted_cnt,"alloted_cnt"=>$alloted_cnt,"tripaccept_cnt"=>$tripaccept_cnt,
        "triprejected_cnt"=>$triprejected_cnt,"tripnotexecute_cnt"=>$tripnotexecute_cnt,"tripexecuted_cnt"=>$tripexecuted_cnt,
        "noresponse_cnt"=>$noresponse_cnt,"tripclose_cnt"=>$tripclose_cnt,"all_cnt"=>$all_cnt,"breakdown_count"=>$breakdown_count,"wapp_count"=>$wapp_count);
		//wapp_count() ,breakdown_count,//overtime_count //ttlasf_count //
                
            return response([
                'success' => true,
                'status' => 3,
                'dashboard_count' => $returnarray,
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'No data',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }
	
	public function breakdown_count():int
    {
		try
		{
		$currtDate = date("Y-m-d");
		$vendor = Auth::user()->user_type;
        $BRANCH_ID = Auth::user()->BRANCH_ID;
        $vendor_id = Auth::user()->vendor_id;
        $dbname = Auth::user()->dbname;
        $vendorid = "";
        $RS_INACTIVE = MyHelper::$RS_INACTIVE;
        $RS_TOTALBREAKDOWN = MyHelper::$RS_TOTALBREAKDOWN;
		

        if ($vendor == MyHelper::$ADMIN) {
            $vendorid = "";
        } else {
           // $vendorid = "AND R.VENDOR_ID='$vendor_id'";
		   $vendorid=$vendor_id;
        }
       
	   $breakdown_cnt=0;
	  $status= explode(',', $RS_TOTALBREAKDOWN);
       
		$result=DB::table('rosters as R')
		->select(
					DB::raw('COUNT(R.ROUTE_ID) as readcnt'),
					DB::raw('SUM(CASE WHEN CA.ACTION IS NULL THEN 1 ELSE 0 END) AS unreadcnt')
				)
			 ->Join('cab_allocation AS CA', function ($join) use ($status) {
								 $join->on('CA.ROSTER_ID', '=', 'R.ROSTER_ID')
								 ->on('CA.CAB_ID', '=', 'R.CAB_ID')
								->whereIn('CA.ACCEPTANCE_REJECT_STATE',$status);
				
								}) 
			->whereIn('R.ROSTER_STATUS', explode(',', $RS_TOTALBREAKDOWN))
			->where('R.BRANCH_ID', '=', $BRANCH_ID)
			->whereDate('R.ESTIMATE_END_TIME', '=', $currtDate)
			->where('R.ACTIVE', '=', $RS_INACTIVE)
			->when($vendorid, function ($query) use ($vendorid) {
					return $query->where('R.VENDOR_ID', $vendorid);
				})
			->get();
		
		$retcnt = count($result);
		
        if ($retcnt > 0) {
            $breakdown_cnt = $result[0]->readcnt;
            $unreadbreak = $result[0]->unreadcnt;
        }
		
		return $breakdown_cnt;
	}catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'No data',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
	}
	
public function wapp_count($wapptime):string
    {
		try
		{
		$date = Carbon::now();
		$curdatetime = $date->format("Y-m-d H:i:s");
		$currtDate = date('Y-m-d');
		$vendor = Auth::user()->user_type;
        $BRANCH_ID = Auth::user()->BRANCH_ID;
        $vendor_id = Auth::user()->vendor_id;
        $dbname = Auth::user()->dbname;
        $vendorid = "";
        $RS_ACTIVE = MyHelper::$RS_ACTIVE;
        $RPS_TTLARRIVAL = MyHelper::$RPS_TTLARRIVAL;
        $RPS_TTLCABDELAY = MyHelper::$RPS_TTLCABDELAY;
        $RS_ACTIVE = MyHelper::$RS_ACTIVE;
		$rps_status= explode(",",$RPS_TTLARRIVAL);
		$rps_delay_status= explode(",",$RPS_TTLCABDELAY);
		$status=array_merge($rps_status,$rps_delay_status);
		
		

        if ($vendor == MyHelper::$ADMIN) {
            $vendorid = "";
        } else {
            $vendorid = "AND R.VENDOR_ID='$vendor_id'";
		   //$vendorid=$vendor_id;
        }
       /*
		$result=DB::table('roster_passengersS as RP')
		->select(
					DB::raw('COUNT(RP.EMPLOYEE_ID) as ttlwapp'),'RP.DRIVER_ARRIVAL_TIME','RP.ESTIMATE_END_TIME','R.CAB_ID','R.TRIP_TYPE'
				)
			 ->join("rosters as R","R.ROSTER_ID","=","RP.ROSTER_ID")
			 
			->whereIn('RP.ROSTER_PASSENGER_STATUS', $status)
			->where('R.BRANCH_ID', '=', $BRANCH_ID)
			
			->where('R.ACTIVE', '=', $RS_ACTIVE)
			->when($vendorid, function ($query) use ($vendorid) {
					return $query->where('R.VENDOR_ID', $vendorid);
				})
				
			 	->whereBetween('RS.ESTIMATE_END_TIME', [
						DB::raw("SUBTIME('$currtDatetime', 10000)"),
						DB::raw("ADDTIME('$currtDatetime', 480000)")
					]) 
				
				->havingRaw("TIMEDIFF('$currtDatetime',RS.DRIVER_ARRIVAL_TIME)> '$wapptime' and R.CAB_ID!='' AND R.TRIP_TYPE='P'  AND '$currtDatetime'>=SUBTIME(RS.ESTIMATE_END_TIME,040000)")
				->get();*/
				 $wapp_data = "SELECT COUNT(RS.EMPLOYEE_ID) as ttlwapp
					FROM roster_passengers RS
					INNER JOIN rosters R ON R.ROSTER_ID = RS.ROSTER_ID and R.ACTIVE=$RS_ACTIVE
					WHERE TIMEDIFF('$curdatetime',RS.DRIVER_ARRIVAL_TIME)> '$wapptime' AND 
					RS.ROSTER_PASSENGER_STATUS IN ($RPS_TTLARRIVAL,$RPS_TTLCABDELAY) and R.CAB_ID!=''
					and R.BRANCH_ID='$BRANCH_ID' $vendorid AND R.TRIP_TYPE='P' AND RS.ESTIMATE_END_TIME BETWEEN 
					SUBTIME('$curdatetime',10000) AND ADDTIME('$curdatetime',480000) AND '$curdatetime'>=SUBTIME(RS.ESTIMATE_END_TIME,040000) ";

					$retqry = DB::select($wapp_data);
			
		$retcnt = count($retqry);
		
        if ($retcnt > 0) {
            $waiting_cnt = $retqry[0]->ttlwapp;
        }
		
		return $waiting_cnt;
	}catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'No data',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
	}

    public function roster_details($request): FoundationApplication|Response|ResponseFactory
    {
        $roster_id = $request->roster_id;
        $date = Carbon::now();
        $curdatetime = $date->format("Y-m-d H:i:s");
        try 
		{
			$RS_ACTIVE = MyHelper::$RS_ACTIVE;
			$branchid = Auth::user()->BRANCH_ID;
			$trip_type = DB::table("rosters as R")->select("R.TRIP_TYPE")
						->where("R.ROSTER_ID", "=", $roster_id)->get();
		   // $order = $trip_type[0]->TRIP_TYPE == 'P' ? 'DESC' : 'ASC';

			$order = 'ASC'; 
			if(isset($trip_type[0]) && $trip_type[0]->TRIP_TYPE == 'P'){
				$order = 'DESC';
			}  

            $RS_ACTIVE = MyHelper::$RS_ACTIVE; // Assuming this is defined somewhere

            $data = RosterPassenger::query()
                ->select(
                    'E.NAME as empname',
                    'E.ADDRESS',
                    'B.BRANCH_NAME',
                    'E.DISTANCE',
                    'E.GENDER',
                    'R.ROUTE_ID',
                    'R.ROSTER_STATUS',
                    'R.TRIP_TYPE',
                    DB::raw("IF(R.TRIP_TYPE = 'P', R.ESTIMATE_END_TIME, R.ESTIMATE_START_TIME) AS ESTIMATE_END_TIME"),
                    DB::raw("IF(R.TRIP_TYPE = 'P', 'roster_passengers.ACTUAL_START_TIME', 'roster_passengers.ACTUAL_END_TIME') AS ACTUAL_START_TIME"),
                    DB::raw("IF(R.TRIP_TYPE = 'P', 'roster_passengers.DRIVER_ARRIVAL_TIME', 'roster_passengers.ACTUAL_START_TIME') AS DRIVER_ARRIVAL_TIME"),
                    'roster_passengers.ESTIMATE_START_TIME',
                    'L.LOCATION_NAME',
                    'roster_passengers.EMPLOYEE_ID',
                    'roster_passengers.ROSTER_PASSENGER_STATUS',
                    'roster_passengers.ROSTER_PASSENGER_ID',
                    'roster_passengers.ROUTE_ORDER'
                )
                ->join('rosters AS R', 'R.ROSTER_ID', '=', 'roster_passengers.ROSTER_ID')
                ->join('employees AS E', function ($join) use ($branchid) {
                    $join->on('E.EMPLOYEES_ID', '=', 'roster_passengers.EMPLOYEE_ID')
                         ->where('E.BRANCH_ID', '=', $branchid);
                })
                ->join('locations AS L', 'L.LOCATION_ID', '=', 'roster_passengers.LOCATION_ID')
                ->join('branch AS B', 'B.BRANCH_ID', '=', DB::raw("'$branchid'"))
                ->where('R.BRANCH_ID', '=', $branchid)
                ->where('roster_passengers.ROSTER_ID', '=', $roster_id)
                ->where('roster_passengers.ACTIVE', '=', $RS_ACTIVE);
                //->orderBy('RP.ROUTE_ORDER', $order)
                //->get();

                $filterModel = $request->input('filterModel');
                if ($filterModel) {
                    foreach ($filterModel as $field => $filter) {
                        if (isset($filter['filter']) && $filter['filter'] !== '') {
                            $value = $filter['filter'];
                            $type = $filter['type'];
    
                            switch ($field) {
                                case 'EMPLOYEES_ID':
                                    $data->where('E.EMPLOYEES_ID', 'like', "%{$value}%");
                                    break;
                                case 'ROSTER_ID':
                                    $data->where('R.ROSTER_ID', 'like', "%{$value}%");
                                    break;
                                case 'ROUTE_ID':
                                    $data->where('R.ROUTE_ID', 'like', "%{$value}%");
                                    break;
                                 case 'START_LOCATION':
                                    $data->where('R.START_LOCATION', 'like', "%{$value}%");
                                    break;
                                case 'END_LOCATION':
                                    $data->where('R.END_LOCATION', 'like', "%{$value}%");
                                    break;
                                case 'VEHICLE_REG_NO':
                                    $data->where('VH.VEHICLE_REG_NO', 'like', "%{$value}%");
                                    break;
                                case 'MODEL':
                                    $data->where('VM.MODEL', 'like', "%{$value}%");
                                    break;
                                case 'TRIPTIME':
                                    $data->where('R.ESTIMATE_END_TIME', 'like', "%{$value}%")
                                    ->orwhere('R.ESTIMATE_START_TIME', 'like', "%{$value}%");
                                    break;
                                case 'TRIP_APPROVED_KM':
                                    $data->where('R.TRIP_APPROVED_KM', 'like', "%{$value}%");
                                    break;
                                case 'DRIVERS_NAME':
                                    $data->where('D.DRIVERS_NAME', 'like', "%{$value}%");
                                    break;
                                case 'DRIVER_MOBILE':
                                    $data->where('D.DRIVER_MOBILE', 'like', "%{$value}%");
                                    break;
                                case 'LOCATION_NAME':
                                    $data->where('L.LOCATION_NAME', 'like', "%{$value}%");
                                    break;
    
                            }
                        }
                    }
                }
    
    
                if ($request->has('orderBy') && !empty($request->input('orderBy'))) {
                    $orderBy = $request->input('orderBy');
                    $order = $request->input('order', 'asc');
                    $data->orderBy($orderBy, $order);
                } else {
                    
                    $data->orderBy("roster_passengers.ROUTE_ORDER", $order);
                }
                $perPage = $request->input('per_page', MyHelper::$PAGINATION_PER_PAGE);
    
                if ($perPage === MyHelper::$PAGINATION_PER_PAGE_ALL) {
                    $paginatedData = $data->paginate($data->count());
                } else {
                    $perPage = is_numeric($perPage) ? (int)$perPage : MyHelper::$PAGINATION_PER_PAGE;
                    
                    $paginatedData = $data->paginate($perPage);
                }
               
                return response([
                    'success' => true,
                    'status' => 3,
                    'roster_details' =>$paginatedData,
                ]);
			
		}
		catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'No data',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }
	
    public function get_reason_details($request): FoundationApplication|Response|ResponseFactory
    {
        
        try 
		{
            $branchid = Auth::user()->BRANCH_ID;
            $categoryName = $request->categoryName;
           $reason= $this->commonFunction->getReasonList($categoryName,$branchid);

                return response([
                    'success' => true,
                    'status' => 3,
                    'reason' => $reason,
                ]);
			
		}
		catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'No data',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }
	
    //Vendor Change
    public function roster_vendor_change($request): FoundationApplication|Response|ResponseFactory
    {
        
        try 
		{
            
            DB::beginTransaction();
            $date = Carbon::now();
            $branchid = Auth::user()->BRANCH_ID;
            $currtDateTime = date('Y-m-d H:i:s');
            $USERID = Auth::user()->id;
            $dbname = Auth::user()->dbname;
            
            $update_vendor_id = $request->vendor_id;
            $roster_id = $request->roster_id;
            $vendor_remarks = $request->remarks;
            $RS_VENDORCHANGE=MyHelper::$RS_VENDORCHANGE;
            $RS_ACTIVE = MyHelper::$RS_ACTIVE;
            $RS_INACTIVE =MyHelper::$RS_INACTIVE;

            $NEWESCORT = 0;
            $PASSENGER_ROSTERUPDATE = 0;

         $sql = "SELECT R.ROSTER_STATUS,if(R.ROSTER_STATUS & $RS_VENDORCHANGE,1,0) as vendorcheck,
        conv(bin(R.ROSTER_STATUS)+bin($RS_VENDORCHANGE),2,10) as upstatus,R.VENDOR_ID 
        FROM (SELECT ROSTER_STATUS,VENDOR_ID FROM rosters WHERE ACTIVE=$RS_ACTIVE and  ROSTER_ID=$roster_id) R";
       
        $rosterstatus = DB::connection("$dbname")->select($sql);
        $ROSTER_STATUS = $rosterstatus[0]->ROSTER_STATUS;
        $vendorcheck = $rosterstatus[0]->vendorcheck;
        $updatestatus = $rosterstatus[0]->upstatus;
        $OLDVENDORID = $rosterstatus[0]->VENDOR_ID;
        if($vendorcheck == $RS_ACTIVE)
        {
            $upstatus = $ROSTER_STATUS;
        }
        else
        {
            if($updatestatus != 0){
                $upstatus = $updatestatus;
            }else{
               $upstatus = $ROSTER_STATUS; 
            }
        }
        $result1 = Roster::on("$dbname")->where([["ROSTER_ID", "=", $roster_id], ["ACTIVE", "=", "$RS_ACTIVE"]])
                ->update(array("ACTIVE" => $RS_INACTIVE, "ROSTER_STATUS" => $upstatus, "REMARKS" => $vendor_remarks, "UPDATED_BY" => $USERID));

        $dateset = Roster::on("$dbname")->where([['ROSTER_ID', "=", $roster_id], ["ACTIVE", "=", $RS_INACTIVE]])->get();
        $BRANCH_ID = $dateset[0]->BRANCH_ID;
        $ROUTE_ID = $dateset[0]->ROUTE_ID;
        $FILE_ID = $dateset[0]->FILE_ID;
        $ESTIMATE_START_TIME = $dateset[0]->ESTIMATE_START_TIME;
        $ACTUAL_START_TIME = $dateset[0]->ACTUAL_START_TIME;
        $ACTUAL_END_TIME = $dateset[0]->ACTUAL_END_TIME;
        $ESTIMATE_END_TIME = $dateset[0]->ESTIMATE_END_TIME;
        $TRIP_TYPE = $dateset[0]->TRIP_TYPE;
        $START_LOCATION = $dateset[0]->START_LOCATION;
        $END_LOCATION = $dateset[0]->END_LOCATION;
        $CAB_ID = $dateset[0]->CAB_ID;
        $ROSTER_ALLOT_TIME = $dateset[0]->ROSTER_ALLOT_TIME;
        $CAB_ALLOT_TIME = $dateset[0]->CAB_ALLOT_TIME;
        $PASSENGER_ALLOT_COUNT = $dateset[0]->PASSENGER_ALLOT_COUNT;
        $PASSENGER_ALLOT_IN_ROUT_COUNT = $dateset[0]->PASSENGER_ALLOT_IN_ROUT_COUNT;
        $PASSENGER_CLUBING_COUNT = $dateset[0]->PASSENGER_CLUBING_COUNT;
        $CAB_CAPACITY_COUNT = $dateset[0]->CAB_CAPACITY_COUNT;
        $TOTAL_KM = $dateset[0]->TOTAL_KM;
        $TRIP_APPROVED_KM = $dateset[0]->TRIP_APPROVED_KM;
        $ROSTER_STATUS = $dateset[0]->ROSTER_STATUS;

        $insert_roster = array("BRANCH_ID" => $BRANCH_ID, "ROUTE_ID" => $ROUTE_ID, "FILE_ID" => $FILE_ID, "ESTIMATE_START_TIME" => $ESTIMATE_START_TIME, "ACTUAL_START_TIME" => $ACTUAL_START_TIME, "ACTUAL_END_TIME" => $ACTUAL_END_TIME, "ESTIMATE_END_TIME" => $ESTIMATE_END_TIME, "TRIP_TYPE" => $TRIP_TYPE, "START_LOCATION" => $START_LOCATION, "END_LOCATION" => $END_LOCATION, "VENDOR_ID" => $update_vendor_id, "CAB_ID" => $CAB_ID, "ROSTER_ALLOT_TIME" => $ROSTER_ALLOT_TIME, "CAB_ALLOT_TIME" => $CAB_ALLOT_TIME, "PASSENGER_ALLOT_COUNT" => $PASSENGER_ALLOT_COUNT, "PASSENGER_ALLOT_IN_ROUT_COUNT" => $PASSENGER_ALLOT_IN_ROUT_COUNT, "PASSENGER_CLUBING_COUNT" => $PASSENGER_CLUBING_COUNT, "CAB_CAPACITY_COUNT" => $CAB_CAPACITY_COUNT, "TOTAL_KM" => $TOTAL_KM, "TRIP_APPROVED_KM" => $TRIP_APPROVED_KM, "ROSTER_STATUS" => $ROSTER_STATUS, "ACTIVE" => $RS_ACTIVE, "CREATED_BY" => $USERID, "created_at" => $currtDateTime);

        $result = Roster::on("$dbname")->insert($insert_roster);
        $lastInsertId = DB::connection("$dbname")->getPdo()->lastInsertId();
         $PASSENGER_ROSTERUPDATE = RosterPassenger::on("$dbname")->where([["ROSTER_ID", "=", $roster_id],['ACTIVE', '=', $RS_ACTIVE]])
                ->update(array("ROSTER_ID" => $lastInsertId ));
         $escortcheck = DB::connection("$dbname")->select("select count(*) as ttlcnt from route_escorts WHERE ROSTER_ID ='$roster_id'");
         $ttlcnt = $escortcheck[0]->ttlcnt;
         if($ttlcnt != 0)
         {
             $NEWESCORT = RouteEscorts::on("$dbname")->where([["ROSTER_ID", "=", $roster_id]])->update(array("ROSTER_ID" => $lastInsertId ));
         }
         
        $date_f = $this->commonFunction->date_format_add();
        $log_arr = array("BRANCH_ID"=>$BRANCH_ID,"VENDOR_ID"=>$update_vendor_id,"ACTION"=>'WEB VENDOR CHANGE',"CATEGORY"=>'TRACKING DASHBOARD',"UPDATE_ROSTER"=>$result1,"UPDATE_ROSTER_STATUS"=>$upstatus,"NEW VENDOR ID" => $update_vendor_id,"OLD ROSTER ID"=>$roster_id,"OLD VENDOR ID"=> $OLDVENDORID,"VENDOR CHANGE REMARKS"=>$vendor_remarks,"NEW ROSTER ID"=>$lastInsertId,"ESCORT INSERT"=>$NEWESCORT,"PASSENGER ROSTER UPDATE"=>$PASSENGER_ROSTERUPDATE,"USER_ID"=> $USERID,"PROCESS_DATE"=>$date_f);

         // $ret=$this->commonFunction->weblogs($log_arr);


           
            // $roster = Roster::where('ACTIVE', MyHelper::$RS_ACTIVE)
            //    // ->where('BRANCH_ID', $authUser->BRANCH_ID)
            //     ->findOrFail($roster_id);

            // $roster->update([
            //     'REMARKS' => $request->input('remark'),
            //     'VENDOR_ID' => $vendor_id,
            //     'UPDATED_BY' => Auth::user()->id,
            //     'UPDATED_AT' => $date->format("Y-m-d H:i:s"),
            // ]);

            DB::commit();

                return response([
                    'success' => true,
                    'status' => 3,
                    'Vendor' =>'Vendor Update Successfully',
                ]);
			
		}
		catch (\Throwable|\Exception $e) {
            DB::rollBack();
            $this->commonFunction->logException($e);
           
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'No data',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }
    public function getempdetails($empid) {
        try
        {
        $dbname = Auth::user()->dbname;
        $branchid = Auth::user()->BRANCH_ID;
		$RS_ACTIVE = MyHelper::$RS_ACTIVE;
		 $employeeData=DB::connection("$dbname")->table('employees AS E')
        ->select(
            'E.NAME AS EMPNAME',
            'E.MOBILE',
            'E.EMAIL',
            'E.GENDER',
            'E.EMPLOYEES_ID',
            'E.PROJECT_NAME',
            'E.ADDRESS',
            'E.MOBILE_GCM',
            'L.LOCATION_ID',
            'L.LOCATION_NAME',
            'L.LATITUDE',
            'L.LONGITUDE',
            'MOBILE_CATEGORY'
        )
        ->join('locations AS L', 'L.LOCATION_ID', '=', 'E.LOCATION_ID')
        ->where('E.BRANCH_ID', $branchid)
        ->where('E.EMPLOYEES_ID', $empid)
        ->where('E.ACTIVE', $RS_ACTIVE)
        ->get();
        return $employeeData;

    }
    catch (\Throwable|\Exception $e) {
        DB::rollBack();
        $this->commonFunction->logException($e);
       
        return response([
            'success' => false,
            'status' => 4,
            'message' => 'No data',
            'validation_controller' => true,
            'error' => $e->getMessage(),
        ], 500);
    }
       
    }
	
	
    public function roster_reset($request): FoundationApplication|Response|ResponseFactory 
    {
        try
        {
           // print_r($request->all());exit;
             $reset_type=$request->reset_type;
          
            $resetrouteid=$request->reset_roster_id;
            $reset_reason=$request->reset_reason;
            $currtDateTime = date('Y-m-d H:i:s');
            $USERID = Auth::user()->id;
            $branchid = Auth::user()->BRANCH_ID;
            $vendorid = Auth::user()->vendor_id;
            $dbname = Auth::user()->dbname;
            $RS_VENDORCHANGE = Myhelper::$RS_VENDORCHANGE;
            $RS_BILLABLE = Myhelper::$RS_BILLABLE;
            $RS_CABALLOT = Myhelper::$RS_CABALLOT;
            $RS_ACCEPT = Myhelper::$RS_ACCEPT;
            $RS_REJECT = Myhelper::$RS_REJECT;
            $RS_ACTIVE = Myhelper::$RS_ACTIVE;
           // $UT_OPTION = Myhelper::$UT_OPTION;
            $TD_ALLOTED = Myhelper::$TD_ALLOTED;
            $TD_NORESPONSE = Myhelper::$TD_NORESPONSE;
            $TD_TRIPNOTEXECUTED = Myhelper::$TD_TRIPNOTEXECUTED;
            $TD_TRIPACCEPTED = Myhelper::$TD_TRIPACCEPTED;
            $TD_TRIPREJECTED = Myhelper::$TD_TRIPREJECTED;
            
            $RS_TOTALALLOT = Myhelper::$RS_TOTALALLOT;
            $RS_TOTALACCEPT = Myhelper::$RS_TOTALACCEPT;
            $RS_TOTALREJECT = Myhelper::$RS_TOTALREJECT;
            $ttlresetstatus = $RS_TOTALALLOT.','.$RS_TOTALACCEPT.','.$RS_TOTALREJECT;
            $checkdata = explode(",",$ttlresetstatus);
            $VERIFIEDSTATUS = 2;
            $resetmasknum = 0;
            $ROSTER_PASSENGER_ID = 0;
            $otpverify = 0;
            $otpverifycnt = 0;
            $caballotsts = 0;
            $cabid = 0;
            $update_passenger_mask = 0;
            $update_roster_mask = 0;
           // $CommonController = new CommonController;
            $NOTIFYINSERT = array();
            DB::beginTransaction();
            if ($reset_type == $TD_ALLOTED || $reset_type == $TD_NORESPONSE) {
                
                
               // $resetmasknum = $CommonController->ResetMasking($resetrouteid);
                
                 $sql1 = "SELECT if(T.vendorchange=1,3,1) as rosterstatus,T.CAB_ID,T.ROSTER_STATUS
                FROM (SELECT if(ROSTER_STATUS & 2,1,0) as vendorchange,ROSTER_STATUS,CAB_ID FROM rosters WHERE ROSTER_ID='$resetrouteid'
                and BRANCH_ID='$branchid' and ACTIVE=$RS_ACTIVE) T ";
                $rs1 = DB::connection("$dbname")->select($sql1);
                $roster_status = $rs1[0]->rosterstatus;
                $existstatus = $rs1[0]->ROSTER_STATUS;
                
                if($roster_status != 0){
                    $rosterstatus = $roster_status;
                }else{
                    $rosterstatus = $existstatus;
                }
                $result3 = Roster::on("$dbname")->where([["ROSTER_ID", "=", $resetrouteid], ["BRANCH_ID", "=", $branchid], ["ACTIVE", "=", $RS_ACTIVE]])
                       ->update(array("ROSTER_STATUS" => $rosterstatus, "CAB_ID" => NULL, "ROSTER_ALLOT_TIME" => NULL, "CAB_ALLOT_TIME" => NULL,
                        "CAB_CAPACITY_COUNT" => 0, "UPDATED_BY" => $USERID));
                $rosterpassanger = RosterPassenger::on("$dbname")->where([["ROSTER_ID", "=", $resetrouteid],["ACTIVE", "=", $RS_ACTIVE]])->get();
                
                foreach($rosterpassanger as $value)
                {
                   $ROSTER_PASSENGER_ID = $value->ROSTER_PASSENGER_ID;
                   $EMPLOYEE_ID = $value->EMPLOYEE_ID;
                   $empdetails = $this->getempdetails($EMPLOYEE_ID);
                  
                   $MOBILEGCM = $empdetails[0]->MOBILE_GCM;
                   $MOBILE_CATEGORY = $empdetails[0]->MOBILE_CATEGORY;
                   $otpverify = OtpVerify::on("$dbname")->where([["ROSTER_PASSENGER_ID", '=', $ROSTER_PASSENGER_ID]])->update(array("VERIFIED_STATUS"=>$VERIFIEDSTATUS));
                   $otpverifycnt = $otpverifycnt + $otpverify;
                    if($MOBILEGCM != ''){
                        $NOTIFYINSERT[] = "('$branchid', '$EMPLOYEE_ID', '$MOBILEGCM', 'Trip Reset', 'Your cab has been changed.Soon you will get new cab details for your trip', '5','$MOBILE_CATEGORY','$currtDateTime')";
                    }
                }
                 $cabid = $rs1[0]->CAB_ID;
                 $sql = "SELECT D.MOBILE_GCM FROM cab C INNER JOIN devices D ON D.DEVICE_ID = C.DEVICE_ID WHERE C.CAB_ID = '$cabid'";
                $fcmid = DB::connection("$dbname")->select($sql);
                $MOBILEGCMCABID = $fcmid[0]->MOBILE_GCM?$fcmid[0]->MOBILE_GCM:'NULL';
                 $NOTIFYINSERT[] = "('$branchid', '$cabid', '$MOBILEGCMCABID', 'Trip Reset', 'Your trip was reset.Soon you get new trip', '5','ANDROID','$currtDateTime')";
                $caballotsts = Cab_allocation::on("$dbname")->where([["CAB_ID", "=", $cabid], ["ROSTER_ID", "=", $resetrouteid]])
                    ->update(array("ACCEPTANCE_REJECT_STATE" => 41, "ACCEPTANCE_REJECT_DATE_TIME" => $currtDateTime,
                "REJECT_REASON_ID" => $reset_reason, "ACTION" => 'Reset', "UPDATED_BY" => $USERID));
                
                
                
                //$sql1 = "UPDATE roster_passengers SET PASSENGER_MASK_NUMBER='--' WHERE ROSTER_ID=$resetrouteid";
                //$update_passenger_mask = DB::connection("$dbname")->update($sql1);
                $sql2 = "UPDATE rosters SET DRIVER_MASK_NUMBER = '--',MASK_ASSIGN_STATUS=0,UPDATED_BY = '$USERID' WHERE BRANCH_ID = $branchid AND ROSTER_ID=$resetrouteid";
                $update_roster_mask = DB::connection("$dbname")->update($sql2);
                
                $update_driver="update driver_billing_summary set ACTIVE=3,UPDATED_AT='".date('Y-m-d H:i:s')."',UPDATED_BY='".$USERID."' where ROSTER_ID='".$resetrouteid."' ";
                $update_driver_bill=DB::update($update_driver);
    //            exit;
            } else if ($reset_type == $TD_TRIPACCEPTED || $reset_type == $TD_TRIPNOTEXECUTED) {
                $sql1 = "SELECT ROSTER_STATUS,conv(bin(ROSTER_STATUS)-bin($RS_ACCEPT),2,10) as upstatus,CAB_ID FROM rosters WHERE BRANCH_ID='$branchid'
                and ROSTER_ID='$resetrouteid' and ACTIVE=$RS_ACTIVE";
                $rs1 = DB::connection("$dbname")->select($sql1);
                $roster_status = $rs1[0]->upstatus;
                $existstatus = $rs1[0]->ROSTER_STATUS;
                if(in_array($roster_status, $checkdata)){
                    $rosterstatus = $roster_status;
                }else{
                    $rosterstatus = $existstatus;
                }
    //            if($roster_status != 0){
    //                $rosterstatus = $roster_status;
    //            }else{
    //                $rosterstatus = $existstatus;
    //            }
                $result3 = Roster::on("$dbname")->where([["ROSTER_ID", "=", $resetrouteid], ["BRANCH_ID", "=", $branchid], ["ACTIVE", "=", $RS_ACTIVE]])->update(array("ROSTER_STATUS" => $rosterstatus, "UPDATED_BY" => $USERID));
                
                $update_driver="update driver_billing_summary set ACTIVE=1,UPDATED_AT='".date('Y-m-d H:i:s')."',UPDATED_BY='".$USERID."' where ROSTER_ID='".$resetrouteid."' ";
                    $update_driver_bill=DB::update($update_driver);
            } else if ($reset_type == $TD_TRIPREJECTED) {
                $sql1 = "SELECT ROSTER_STATUS,conv(bin(ROSTER_STATUS)-bin($RS_REJECT),2,10) as upstatus,CAB_ID FROM rosters WHERE BRANCH_ID='$branchid' and ROSTER_ID='$resetrouteid' and ACTIVE=$RS_ACTIVE";
                $rs1 = DB::connection("$dbname")->select($sql1);
                $roster_status = $rs1[0]->upstatus;
                $existstatus = $rs1[0]->ROSTER_STATUS;
                
                if(in_array($roster_status, $checkdata)){
                    $rosterstatus = $roster_status;
                }else{
                    $rosterstatus = $existstatus;
                }
    //            if($roster_status != 0){
    //                $rosterstatus = $roster_status;
    //            }else{
    //                $rosterstatus = $existstatus;
    //            }
                $result3 = Roster::on("$dbname")->where([["ROSTER_ID", "=", $resetrouteid], ["BRANCH_ID", "=", $branchid], ["ACTIVE", "=", $RS_ACTIVE]])
                        ->update(array("ROSTER_STATUS" => $rosterstatus, "UPDATED_BY" => $USERID));
                $update_driver="update driver_billing_summary set ACTIVE=1,UPDATED_AT='".date('Y-m-d H:i:s')."',UPDATED_BY='".$USERID."' where ROSTER_ID='".$resetrouteid."' ";
                    $update_driver_bill=DB::update($update_driver);
            }
            if(count($NOTIFYINSERT)>0)
            {
                 $cabnotification = "INSERT INTO `notification` (`branch_id`, `empid_cabid`, `fcm_id`, `heading`, `message`, `category_value`,`app_category`,`created_at`) VALUES ".implode(',', $NOTIFYINSERT);
                $insertnotifycnt = DB::connection("$dbname")->insert($cabnotification);
            }
            $date_f =  $this->commonFunction->date_format_add();
            
            $mask_enable =  $this->commonFunction->GetPropertyValue('CALL MASKING OPTION');
            //echo "test ".$mask_enable; exit;
            if($mask_enable=='Y')
            {
               // $mask= new MaskNumberClearController();
               // $mask->Clear_MaskNumber(0,$branchid,$resetrouteid,'Roster');
            }
            
            
            
            $log_arr = array("BRANCH_ID" => $branchid, "VENDOR_ID" => $vendorid, "ACTION" => "WEB RESET", "CATEGORY" => 'TRACKING DASHBOARD', "RESET_MASKNUMBER" => '--',"ROSTER_EXIST_STATUS"=>$existstatus, "ROSTER_UPDATE_STATUS" => $rosterstatus, "ROSTER_ID" => $resetrouteid, "OTP_PASSENGERID" => $ROSTER_PASSENGER_ID, "VERIFY_STATUS" => $VERIFIEDSTATUS, "OTP_VERIFIED" => $otpverifycnt, "CAB_ALLOT_RESET" => $caballotsts, "CABID" => $cabid,"PASSENGER_MASK_RESET" => $update_passenger_mask,"ROSTER_MASK_RESET"=>$update_roster_mask,"USER_ID" => $USERID, "PROCESS_DATE" => $date_f);
    
    
            //$ret = $CommonController->weblogs($log_arr);
            DB::commit();

            return response([
                'success' => true,
                'status' => 5,
                'reset' =>'Reset Update Successfully',
            ]);
       
    }
    catch (\Throwable|\Exception $e) {
        DB::rollBack();
        $this->commonFunction->logException($e);
       
        return response([
            'success' => false,
            'status' => 4,
            'message' => 'No data',
            'validation_controller' => true,
            'error' => $e->getMessage(),
        ], 500);
    }
       

    }

    public function roster_noshow($request): FoundationApplication|Response|ResponseFactory 
    {
        try
        {
            //($emp_reason_id, $roster_passenger_id,$sts)
          //  print_r($request->all());exit;
         $emp_reason_id=$request->emp_reason_id;
         $roster_passenger_id=$request->roster_passenger_id;
         $sts=$request->sts;
           $result1 = 0;
           $Empcnt_Decrement = 0;
           $EscortRemove = 0;
           $RemoveMaskno = 0;
           $NewEscort = 0;
           $NOSHOW_TIME_UPDATE = 0;
           $sql2 = 0;
           $Cab_Allocation = 0;
           $SENDNOSHOWSMS = 0;
           $curdatetime = date('Y-m-d H:i:s');
           $userid = Auth::user()->id;
           $BRANCH_ID = Auth::user()->BRANCH_ID;
           $vendorid = Auth::user()->vendor_id;
           $dbname = Auth::user()->dbname;
           $RPS_NOSHOW = MyHelper::$RPS_NOSHOW;
           $RPS_TTLNOSHOW = MyHelper::$RPS_TTLNOSHOW;
           $ESCORT_NEW = MyHelper::$ESCORT_NEW;
           $AES_KEY = env("AES_ENCRYPT_KEY");
           $ESCORT_REMOVE = MyHelper::$ESCORT_REMOVE;
           $RS_ACTIVE = MyHelper::$RS_ACTIVE;
           $RS_AUTOCANCEL = MyHelper::$RS_AUTOCANCEL;
           $RP_DROPROUTE = MyHelper::$RP_DROPROUTE;
           $RS_MANUALTRIPCLOSE = MyHelper::$RS_MANUALTRIPCLOSE;
           $RS_MGENDER = MyHelper::$RS_MGENDER;
           $RS_FGENDER = MyHelper::$RS_FGENDER;
           $RS_MANUALTRIPCLOSE = MyHelper::$RS_MANUALTRIPCLOSE;
           $PR_ESCORT_ENABLE = MyHelper::$PR_ESCORT_ENABLE;
           $PR_ESCORT_START_TIME = MyHelper::$PR_ESCORT_START_TIME;
           $PR_ESCORT_END_TIME = MyHelper::$PR_ESCORT_END_TIME;
           $PR_HELPLINENO = MyHelper::$PR_HELPLINENO;
           
           $propertie = property::on("$dbname")->where([['BRANCH_ID', '=', $BRANCH_ID], ['ACTIVE', '=', $RS_ACTIVE]])->get();
   
           for ($ii = 0; $ii < count($propertie); $ii++)
           {
   
               $PROPERTIE_NAME = $propertie[$ii]->PROPERTIE_NAME;
               $PROPERTIE_VALUE = $propertie[$ii]->PROPERTIE_VALUE;
   
               switch ($PROPERTIE_NAME) {
                   case $PR_ESCORT_ENABLE:
                       $PR_ESCORTENABLE = $PROPERTIE_VALUE;
                       break;
                   case $PR_ESCORT_START_TIME:
                       $PR_ESCORTSTART_TIME = $PROPERTIE_VALUE;
                       break;
                   case $PR_ESCORT_END_TIME:
                       $PR_ESCORTEND_TIME = $PROPERTIE_VALUE;
                       break;
                   case 'SMS_ALERT':
                       $PR_SMS_ALERT = $PROPERTIE_VALUE;
                       break;
                   case 'SMS_LAST_TAG':
                       $SMS_LAST_TAG = $PROPERTIE_VALUE;
                       break;
                   case $PR_HELPLINENO:
                       $HELP_LINE = $PROPERTIE_VALUE;
                       break;
                   default:
                       break;
               }
           }
           
           $sql = "SELECT conv(bin(RP.ROSTER_PASSENGER_STATUS)+bin($RPS_NOSHOW),2,10) as upstatus,RP.ROSTER_ID,RP.ROSTER_PASSENGER_ID,R.ROUTE_ID,RP.EMPLOYEE_ID,
           if(R.TRIP_TYPE = 'P',R.ESTIMATE_END_TIME,R.ESTIMATE_START_TIME) AS LOGINDATETIME,R.TRIP_TYPE,
           if(R.PASSENGER_CLUBING_COUNT > 0,'PASSENGER_CLUBING_COUNT','PASSENGER_ALLOT_COUNT') AS decrementfield,E.MOBILE,B.BRANCH_NAME,
           VE.VEHICLE_REG_NO,if(R.ROSTER_STATUS & 64,1,0) as executestatus,E.MOBILE_GCM,E.MOBILE_CATEGORY,E.SMS_ALERT,R.CAB_ID,L.LOCATION_NAME,
           E.NAME as empname,D.MOBILE_GCM AS DRIVERGCMID
           FROM roster_passengers RP 
           INNER JOIN rosters R ON R.ROSTER_ID = RP.ROSTER_ID
           INNER JOIN employees E on E.EMPLOYEES_ID = RP.EMPLOYEE_ID AND E.BRANCH_ID = $BRANCH_ID
           INNER JOIN branch B on B.BRANCH_ID = R.BRANCH_ID
           LEFT JOIN cab C ON C.CAB_ID = R.CAB_ID
           LEFT JOIN devices D ON D.DEVICE_ID = C.DEVICE_ID
           LEFT JOIN vehicles VE ON VE.VEHICLE_ID = C.VEHICLE_ID
           LEFT JOIN locations L ON L.LOCATION_ID = RP.LOCATION_ID 
           WHERE RP.ACTIVE = $RS_ACTIVE AND RP.ROSTER_PASSENGER_ID='$roster_passenger_id'";
           
           $rosterstatus = DB::connection("$dbname")->select($sql);
           //print_r($rosterstatus);exit;

           $upstatus = $rosterstatus[0]->upstatus;
           $ROSTER_ID = $rosterstatus[0]->ROSTER_ID;
           $TRIP_TYPE = $rosterstatus[0]->TRIP_TYPE;
           $executestatus = $rosterstatus[0]->executestatus;
           $EMP_LOCATION_NAME = $rosterstatus[0]->LOCATION_NAME;
           $MOBILE_GCM = $rosterstatus[0]->MOBILE_GCM;
           $MOBILE_CATEGORY = $rosterstatus[0]->MOBILE_CATEGORY;
           $TRIPID = $rosterstatus[0]->ROUTE_ID;
           $EMPLOYEE_ID = $rosterstatus[0]->EMPLOYEE_ID;
           $DRIVER_CAB_ID = $rosterstatus[0]->CAB_ID;
           $DRIVERGCMID = $rosterstatus[0]->DRIVERGCMID;
           $empname = $rosterstatus[0]->empname;
           $empmobile = $rosterstatus[0]->MOBILE;
           $VEHICLEREGNO = $rosterstatus[0]->VEHICLE_REG_NO;
           $LOGINDATETIME = $rosterstatus[0]->LOGINDATETIME;
           $BRANCH_NAME = $rosterstatus[0]->BRANCH_NAME;
           $decrementfield = $rosterstatus[0]->decrementfield;
           $SMS_ALERT = $rosterstatus[0]->SMS_ALERT;
           $ROSTER_PASSENGER_ID = $rosterstatus[0]->ROSTER_PASSENGER_ID;
          
           //$EMP_NAME = $this->commonFunction->AES_DECRYPT($empname, $AES_KEY);
           $EMP_NAME ="AKS";
           
          if($sts=='NOSHOW')
           {
               $result1 = RosterPassenger::where([["ROSTER_PASSENGER_ID", "=", $roster_passenger_id],['ACTIVE', '=', $RS_ACTIVE]])
                       ->update(array("ROSTER_PASSENGER_STATUS" => $upstatus));
               $Empcnt_Decrement = Roster::where([["ROSTER_ID", "=", $ROSTER_ID],["ACTIVE", "=", $RS_ACTIVE]])->decrement("$decrementfield");
               Roster::where([["ROSTER_ID", "=", $ROSTER_ID], ["ACTIVE", "=", $RS_ACTIVE]])->increment("PASSENGER_NOSHOW_COUNT");
               DB::update("update driver_billing_summary set UPDATED_AT='".date('Y-m-d H:i:s')."',FIRST_POINT_DATETIME='".date("Y-m-d H:i:s")."',FIRST_POINT_LAT='0',FIRST_POINT_LONG='0' where FIRST_POINT_LAT is NULL and FIRST_POINT_LONG is NULL and  FIRST_POINT_DATETIME is NULL and ROSTER_ID='".$ROSTER_ID."'");
           }
           else if($sts=='DEACTIVE')
           {
               $result1 = RosterPassenger::where([["ROSTER_PASSENGER_ID", "=", $roster_passenger_id],['ACTIVE', '=', $RS_ACTIVE]])
                       ->update(array("ACTIVE" => 3));
               $Empcnt_Decrement = Roster::where([["ROSTER_ID", "=", $ROSTER_ID],["ACTIVE", "=", $RS_ACTIVE]])->decrement("$decrementfield");
               Roster::where([["ROSTER_ID", "=", $ROSTER_ID], ["ACTIVE", "=", $RS_ACTIVE]])->increment("PASSENGER_DEACTIVE_COUNT");
               
               $mask_enable = $this->commonFunction->GetPropertyValue('CALL MASKING OPTION');
               
                   if($mask_enable=='Y')
                   {
                       
                      // $mask= new MaskNumberClearController();
                       //$mask->Clear_MaskNumber($ROSTER_PASSENGER_ID,$BRANCH_ID,$ROSTER_ID,'Passenger');
                   }
               
           }
           else
           {
               $result1 = RosterPassenger::where([["ROSTER_PASSENGER_ID", "=", $roster_passenger_id],['ACTIVE', '=', $RS_ACTIVE]])
                       ->update(array("ROSTER_PASSENGER_STATUS" => $upstatus));
               $Empcnt_Decrement = Roster::where([["ROSTER_ID", "=", $ROSTER_ID],["ACTIVE", "=", $RS_ACTIVE]])->decrement("$decrementfield");
               Roster::where([["ROSTER_ID", "=", $ROSTER_ID], ["ACTIVE", "=", $RS_ACTIVE]])->increment("PASSENGER_NOSHOW_COUNT");
           }
           if($executestatus ==1 && $sts=='NOSHOW'){
               $SENDSMSTIME = date('Y-m-d H:i:s');
               $notifymsg = "You are noshow for this route($TRIPID-$LOGINDATETIME)";
               $notifymsg1 = "$EMP_NAME - $EMP_LOCATION_NAME was noshow in your trip";
               $cabnotification = "INSERT INTO `notification` (`branch_id`, `empid_cabid`, `fcm_id`, `heading`, `message`, `category_value`,`app_category`,`created_at`) VALUES ('$BRANCH_ID', '$EMPLOYEE_ID', '$MOBILE_GCM', 'Noshow', '$notifymsg', '3','$MOBILE_CATEGORY','$SENDSMSTIME')";
           $insert_notify = DB::connection("$dbname")->insert($cabnotification);
               $cabnotification1 = "INSERT INTO `notification` (`branch_id`, `empid_cabid`, `fcm_id`, `heading`, `message`, `category_value`,`app_category`,`created_at`) VALUES ('$BRANCH_ID', '$DRIVER_CAB_ID', '$DRIVERGCMID', 'Emp Noshow', '$notifymsg1', '1','ANDROID','$SENDSMSTIME')";
               $insert_notify1 = DB::connection("$dbname")->insert($cabnotification1);
           }
           
           $insert_reason = array("ROSTER_PASSENGER_ID" => $roster_passenger_id, "REASON_ID" => $emp_reason_id, "CREATE_BY" => $userid, "CREATED_DATE" => $curdatetime);
           $result = Reason_Log::on("$dbname")->insert($insert_reason);
           
           $check_escort = "SELECT COUNT(*) as ttlcnt FROM route_escorts WHERE ROSTER_ID='$ROSTER_ID' AND EMPLOYEE_ID='$EMPLOYEE_ID'";
           $checkescort = DB::connection("$dbname")->select($check_escort);
           $ttlcnt = $checkescort[0]->ttlcnt;
           if($ttlcnt != 0)
           {
              $EscortRemove = RouteEscorts::on("$dbname")->where([["ROSTER_ID", "=", $ROSTER_ID],["EMPLOYEE_ID", "=", $EMPLOYEE_ID]])->update(array("ESCORT_ID" => NULL, "STATUS" => $ESCORT_REMOVE)); 
           }
           $escort_check = "SELECT R.TRIP_TYPE,if(R.CAB_ID is NULL,0,R.CAB_ID) AS CABID,rp.ROSTER_ID,rp.EMPLOYEE_ID,e.BRANCH_ID,e.GENDER,
           rp.EMPLOYEE_ID,rp.ROSTER_PASSENGER_STATUS,R.PASSENGER_CLUBING_COUNT,R.PASSENGER_ALLOT_COUNT,R.ESTIMATE_END_TIME,R.ESTIMATE_START_TIME
           ,R.PASSENGER_ALLOT_IN_ROUT_COUNT
           FROM roster_passengers as rp
           INNER JOIN rosters R ON R.ROSTER_ID = '$ROSTER_ID'
           INNER JOIN employees as e on e.EMPLOYEES_ID=rp.EMPLOYEE_ID AND e.BRANCH_ID = $BRANCH_ID
           WHERE R.BRANCH_ID = $BRANCH_ID and rp.ROSTER_ID = '$ROSTER_ID' and R.ACTIVE = $RS_ACTIVE and rp.ROSTER_PASSENGER_STATUS NOT IN ($RPS_TTLNOSHOW) and rp.ACTIVE = $RS_ACTIVE
            ORDER BY rp.ROUTE_ORDER desc LIMIT 1";
           $escortcheck = DB::connection("$dbname")->select($escort_check);
           $count = count($escortcheck);
           $TTLCNTEMP = 0;
           if($count != 0)
           {
               $GENDER = $escortcheck[0]->GENDER;
               $EEMPLOYEE_ID = $escortcheck[0]->EMPLOYEE_ID;
               $TRIP_TYPE = $escortcheck[0]->TRIP_TYPE;
               $CABID = $escortcheck[0]->CABID;
               $ESTIMATE_END_TIME = $escortcheck[0]->ESTIMATE_END_TIME;
               $ESTIMATE_START_TIME = $escortcheck[0]->ESTIMATE_START_TIME;
               $PASSENGER_CLUBING_COUNT = $escortcheck[0]->PASSENGER_CLUBING_COUNT;
               $PASSENGER_ALLOT_COUNT = $escortcheck[0]->PASSENGER_ALLOT_COUNT;
               $PASSENGER_ALLOT_IN_ROUT_COUNT = $escortcheck[0]->PASSENGER_ALLOT_IN_ROUT_COUNT;
               $TTLCNTEMP = $PASSENGER_CLUBING_COUNT + $PASSENGER_ALLOT_COUNT;
               if($CABID != 0){
                   /* Call masking disable */
                   
                   //$RemoveMaskno = $CommonController->property_check($roster_passenger_id);
                   
                   $mask_enable = $this->commonFunction->GetPropertyValue('CALL MASKING OPTION');
                       if($mask_enable=='Y')
                       {
                           $mask= new MaskNumberClearController();
                           $mask->Clear_MaskNumber($roster_passenger_id,$BRANCH_ID,$ROSTER_ID,'Passenger');
                       }
                   /* Call masking disable */
               }
               if($GENDER == $RS_FGENDER){
                   $CHECKESCORT_TIME = '';
                   if($TRIP_TYPE == $RP_DROPROUTE){
                       $CHECKESCORT_TIME = $ESTIMATE_START_TIME;
                   }else{
                       $CHECKESCORT_TIME = $ESTIMATE_END_TIME;
                   }
                   $isEscort = '';
                   $is_Escort_Time = date('H:i:s', strtotime($CHECKESCORT_TIME));
                   
                   if ($PR_ESCORTENABLE == 'Y') {
                       $ret1 = $this->check_time($PR_ESCORTSTART_TIME, $PR_ESCORTEND_TIME, $is_Escort_Time) ? "yes" : "no";
                       if ($ret1 == "yes") {
                           $isEscort = 'true';
                       }
                   }
                   if($isEscort == 'true'){
                       $checkescort = "SELECT COUNT(*) as ttlcnt FROM route_escorts WHERE ROSTER_ID='$ROSTER_ID' AND EMPLOYEE_ID='$EEMPLOYEE_ID' and STATUS='".$ESCORT_NEW."'";
                       $checkescorts = DB::connection("$dbname")->select($checkescort);
                       $ttlcnts = $checkescorts[0]->ttlcnt;
                       if($ttlcnts == 0 && $PASSENGER_ALLOT_IN_ROUT_COUNT==0 && $TRIP_TYPE == env('RP_PICKROUTE')){
                       $insert_escort = array("BRANCH_ID" => $BRANCH_ID, "ROSTER_ID" => $ROSTER_ID, "EMPLOYEE_ID" => $EEMPLOYEE_ID, "STATUS" => $ESCORT_NEW,
                           "CREATED_BY" => $userid, "created_at" => date('Y-m-d H:i:s'));
                       RouteEscorts::on("$dbname")->insert($insert_escort);
                       $NewEscort = DB::connection("$dbname")->getPdo()->lastInsertId();
                       }
                       else if($ttlcnts == 0  && $TRIP_TYPE == $RP_DROPROUTE){
                       $insert_escort = array("BRANCH_ID" => $BRANCH_ID, "ROSTER_ID" => $ROSTER_ID, "EMPLOYEE_ID" => $EEMPLOYEE_ID, "STATUS" => $ESCORT_NEW,
                           "CREATED_BY" => $userid, "created_at" => date('Y-m-d H:i:s'));
                       RouteEscorts::on("$dbname")->insert($insert_escort);
                       $NewEscort = DB::connection("$dbname")->getPdo()->lastInsertId();
                       }
                   }
               }
               
           }
           else
           {
               $roster_sts = "SELECT TRIP_TYPE,if(CAB_ID is NULL,0,CAB_ID) AS CABID,PASSENGER_CLUBING_COUNT,PASSENGER_ALLOT_COUNT FROM rosters WHERE ROSTER_ID = '$ROSTER_ID' AND ACTIVE = $RS_ACTIVE";
               $rostersts = DB::connection("$dbname")->select($roster_sts);
               $TRIP_TYPE = $rostersts[0]->TRIP_TYPE;
               $CABID = $rostersts[0]->CABID;
           }
           
           if($TRIP_TYPE == $RP_DROPROUTE)
           {
               $PICKDROPNOSHOW = "ACTUAL_END_TIME";
           }
           else{
               $PICKDROPNOSHOW = "ACTUAL_START_TIME";  
           }
           $NOSHOW_TIME_UPDATE = RosterPassenger::on("$dbname")->where([["ROSTER_PASSENGER_ID", "=", $roster_passenger_id],['ACTIVE', '=', $RS_ACTIVE]])
                   ->update(array($PICKDROPNOSHOW => date('Y-m-d H:i:s')));
           
           
           
           $sql1 = "SELECT count(*) as empcount,SUM(if(ROSTER_PASSENGER_STATUS & $RPS_NOSHOW,1,0)) as pendingcnt FROM roster_passengers WHERE
           ROSTER_ID = '$ROSTER_ID' AND ACTIVE ='$RS_ACTIVE'";
           $RPstatus = DB::connection("$dbname")->select($sql1);
           $RPCNTSTS = count($RPstatus);
           if($RPCNTSTS != 0){
               $empcount = $RPstatus[0]->empcount;
               $pendingcnt = $RPstatus[0]->pendingcnt!=''?$RPstatus[0]->pendingcnt:0;
           }
            $status = $ROSTER_ID;
           if($empcount == $pendingcnt && $CABID == 0)
           {
               $sql2 =  DB::connection("$dbname")->update("UPDATE rosters SET ROSTER_STATUS = ROSTER_STATUS + $RS_AUTOCANCEL WHERE ROSTER_ID = $ROSTER_ID");
               $status = 0;
           }
           else
           { 
               if($empcount == $pendingcnt && $TRIP_TYPE == $RP_DROPROUTE)
               {
                   $sql2 =  DB::connection("$dbname")->update("UPDATE rosters SET ROSTER_STATUS = ROSTER_STATUS + $RS_AUTOCANCEL WHERE ROSTER_ID = $ROSTER_ID");
                   $status = 0;
                   if($CABID != 0)
                   {
                       $Cab_Allocation = DB::connection("$dbname")->update("UPDATE `cab_allocation`  SET ACCEPTANCE_REJECT_STATE = ACCEPTANCE_REJECT_STATE + $RS_AUTOCANCEL
                        WHERE ROSTER_ID = $ROSTER_ID AND CAB_ID = $CABID");
                   }
               }else if($empcount == $pendingcnt){
                   $sql2 =  DB::connection("$dbname")->update("UPDATE rosters SET ROSTER_STATUS = ROSTER_STATUS + $RS_MANUALTRIPCLOSE WHERE ROSTER_ID = $ROSTER_ID");
                   $status = 0;
                   if($CABID != 0)
                   {
                      $Cab_Allocation = DB::connection("$dbname")->update("UPDATE `cab_allocation`  SET ACCEPTANCE_REJECT_STATE = ACCEPTANCE_REJECT_STATE + $RS_MANUALTRIPCLOSE
                        WHERE ROSTER_ID = $ROSTER_ID AND CAB_ID = $CABID");
                   }
               }
           }
           
           $SENDSMSTIME = date('Y-m-d H:i:s');
           $SMSTAG = $this->commonFunction->GetPropertyValue('SMS TAG');
           $AES_KEY = env("AES_ENCRYPT_KEY");
           //$emp_mobile = $this->commonFunction->AES_DECRYPT($empmobile,$AES_KEY);
           $emp_mobile=9543417214;
           if($VEHICLEREGNO != ''){
               $TRIPDET = $TRIPID.', Cab No '.$VEHICLEREGNO;
           }else{
               $TRIPDET = $TRIPID;
           }
           if($BRANCH_ID==32 )
           {
               //$message="Hi $emp_name, you have been marked as NO SHOW for your trip from office/Home at ".date("H:i",strtotime($LOGINDATETIME))." for ".date("d/m/Y",strtotime($LOGINDATETIME)).". Need Support? Helpline - $helpline_no.".$SMS_LAST_TAG;
               if($sts=='NOSHOW')
               {
                   $message = "Hi $EMP_NAME, you have been marked as NO SHOW for your trip from office/Home at ".date("H:i",strtotime($LOGINDATETIME))." for ".date("d/m/Y",strtotime($LOGINDATETIME)).". Need Support? Helpline - $HELP_LINE. ".$SMS_LAST_TAG;
               }
               elseif($sts=='DEACTIVE')
               {
                   
                   $message ="Hi $EMP_NAME, your cab booking for pickup/drop has been cancelled for ".date("d/m/Y",strtotime($LOGINDATETIME)).".  Need support? - Helpline - $HELP_LINE. ".$SMS_LAST_TAG;
                   
               }
               else
               {
                   
                   $message = "Hi $EMP_NAME, you have been marked as 'NO SHOW' for your trip from office/Home at ".date("H:i",strtotime($LOGINDATETIME))." for ".date("d/m/Y",strtotime($LOGINDATETIME)).". Need Support? Helpline - $HELP_LINE. ".$SMS_LAST_TAG;
               }
           }
           else{
              
              if($sts=='NOSHOW')
               {
                   $message="Greetings from $BRANCH_NAME, you are noshow for the trip id $TRIPID on $LOGINDATETIME.$SMS_LAST_TAG";
                   //$message = 'Greetings from '.$BRANCH_NAME.', you are "No show" for the Trip '.$TRIPDET.', on '.$LOGINDATETIME;
               }
               elseif($sts=='DEACTIVE')
               {
                   $message="Greetings from $BRANCH_NAME, you are cancel for the trip id $TRIPID on $LOGINDATETIME.$SMS_LAST_TAG";
                   //$message = 'Greetings from '.$BRANCH_NAME.', you are "Cancel" for the Trip '.$TRIPDET.', on '.$LOGINDATETIME;
               }
               else
               {
                   $message = 'Greetings from '.$BRANCH_NAME.', you are "No show" for the Trip '.$TRIPDET.', on '.$LOGINDATETIME;
               }
           }
           $noshow_sms = array("BRANCH_ID" => $BRANCH_ID, "ORIGINATOR" => $SMSTAG, "RECIPIENT" => $emp_mobile, "MESSAGE" => $message,
                           "SENT_DATE" => '1900-01-01 00:00:00', "REF_NO" => '--', "CREATED_BY" => $userid, "CREATED_DATE" => $SENDSMSTIME);
           if($SMS_ALERT==1 && $PR_SMS_ALERT==1)
           {	
               $SENDNOSHOWSMS = Sms::insert($noshow_sms);
           }
           
           $this->commonFunction->noshow_change_roster_approve_km($ROSTER_ID,$TRIP_TYPE);
           // Web Noshow log 
           $date_f =  $this->commonFunction->date_format_add();
           $log_arr = array("BRANCH_ID"=>$BRANCH_ID,"VENDOR_ID"=>$vendorid,"ACTION"=>'WEB NOSHOW',"CATEGORY"=>'TRACKING DASHBOARD',"NOSHOW_PASSENGERID"=>$roster_passenger_id,"REASON_ID"=>$emp_reason_id,"ROSTER_PASSENGERSTATUS" => $upstatus,"ROSTER_PASSANGERSTATUS_UPDATE"=>$result1,"ROSTER_DECREMENT"=>$Empcnt_Decrement,"REASON_LOG"=>$result,"ESCORT_REMOVE"=>$EscortRemove,"REMOVE_MASKNUMBER"=>$RemoveMaskno,"NEW_ESCORT_INSERT"=>$NewEscort,"NOSHOW_TIME_UPDATE"=>$NOSHOW_TIME_UPDATE,"ROSTER_AUTOCANCEL"=>$sql2,"CAB_ALLOCATION_UPDATE"=>$Cab_Allocation,"SEND_NOSHOWSMS"=>$SENDNOSHOWSMS,"USER_ID"=>$userid,"PROCESS_DATE"=>$date_f);
           //$ret=$CommonController->weblogs($log_arr);
           
           // Web Noshow log End 
           
            DB::commit();

            return response([
                'success' => true,
                'status' => 5,
                'noshow' =>$sts.'Update Successfully',
            ]);
       
    }
    catch (\Throwable|\Exception $e) {
        DB::rollBack();
        $this->commonFunction->logException($e);
       
        return response([
            'success' => false,
            'status' => 4,
            'message' => 'No data',
            'validation_controller' => true,
            'error' => $e->getMessage(),
        ], 500);
    }
       

    }
	
	
	


}
