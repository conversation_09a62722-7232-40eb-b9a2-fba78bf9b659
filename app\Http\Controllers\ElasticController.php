<?php

namespace App\Http\Controllers;

use App\Http\Controllers\CommonController;
use App\Http\Controllers\Controller;
use App\Services\Elastic\CommonFn;
use App\Services\Elastic\ElasticAccess;
use App\Services\Elastic\ElasticGpsSearch;
use App\Services\Elastic\ElasticSearchCount;
use Elasticsearch\ClientBuilder;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Support\Facades\Input;
// use Auth;
use Illuminate\Support\Facades\Auth;
use DB;

class ElasticController extends Controller
{
    protected $elasticSearchCount;
    protected $elasticSearch;
    protected $elasticAcc;

    /**
     * Create a new ElasticSearchCount & ElasticGpsSearch & ElasticAccess controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        // echo "tet";exit;
        $this->elasticSearchCount = new ElasticSearchCount;
        $this->elasticSearch = new ElasticGpsSearch;
        $this->elasticAcc = new ElasticAccess;
    }

    // respond gps logging data to create path
    public function cabPathSearch($branch_id, $cab_id, $route_id, $from_date, $to_date)
    {
        $params = $this->elasticAcc->gpsCabPath($branch_id, $cab_id, $route_id, $from_date, $to_date);
        $response = $this->elasticSearch->search($params);
        return $response;
    }

    // respond gps logging data to trip create path
    public function cabTripPathSearch($branch_id, $cab_id, $route_id, $from_date, $to_date)
    {
        $params = $this->elasticAcc->gpsCabTripPath($branch_id, $cab_id, $route_id, $from_date, $to_date);
        $response = $this->elasticSearch->search($params);
        return $response;
    }

    // gps logging for perticular cab between time interval
    public function cabPathSearchFull($cab_no, $vendor_id, $from_date, $to_date)
    {
        $params = $this->elasticAcc->gpsCabPathFull($cab_no, $vendor_id, $from_date, $to_date);
        $response = $this->elasticSearch->search($params);
        return $response;
    }

    // respond gps logging data to create path
    public function gpsPathSearch($cab_id, $from_date, $to_date)
    {
        $params = $this->elasticAcc->cabPath($cab_id, $from_date, $to_date);
        $response = $this->elasticSearch->search($params);
        return $response;
    }

    // Insert route path data of roster
    public function routePathInsert(array $pathArr)
    {
        $response = $this->elasticSearchCount->pathInsert($pathArr);
        return $response;
    }

    // Get speed count of cab
    public function getCabSpeedCount()
    {
        $branchId = Auth::user()->BRANCH_ID;
        $vendorId = Auth::user()->vendor_id;
        $curDate = CommonFn::cur_date();

        $params = $this->elasticAcc->queryCount(env('ES_TYPE_SPEED_ALERT'), $branchId, $vendorId, $curDate);
        $response[] = $this->elasticSearchCount->getCount($params);
        $paramsremark = $this->elasticAcc->queryRemarkCount(env('ES_TYPE_SPEED_ALERT'), $branchId, $vendorId, $curDate);
        $responseremark[] = $this->elasticSearchCount->getCount($paramsremark);
        $response = array_merge($response, $responseremark);
        return $response;
    }

    // individual cab speed count
    public function getIndividualCabSpeedCount($cab_id, $from_date, $to_date)
    {
        $branchId = Auth::user()->BRANCH_ID;
        $vendorId = Auth::user()->vendor_id;
        $params = $this->elasticAcc->queryIndividualSpeedCount(env('ES_TYPE_SPEED_ALERT'), $branchId, $vendorId, $cab_id, $from_date, $to_date);
        $response[] = $this->elasticSearchCount->getCount($params);
        return $response;
    }

    // Get deviation count and count without remark of cab
    public function getCabDeviationCount()
    {
        $branchId = Auth::user()->BRANCH_ID;
        $vendorId = Auth::user()->vendor_id;
        $curDate = CommonFn::cur_date();
        $params = $this->elasticAcc->queryCount(env('ES_TYPE_DEVIATION_ALERT'), $branchId, $vendorId, $curDate);
        $response[] = $this->elasticSearchCount->getCount($params);
        $paramsremark = $this->elasticAcc->queryRemarkCount(env('ES_TYPE_DEVIATION_ALERT'), $branchId, $vendorId, $curDate);
        $responseremark[] = $this->elasticSearchCount->getCount($paramsremark);
        $response = array_merge($response, $responseremark);
        return $response;
    }

    // individual cab speed count
    public function getCabSpeedCountGroup($from_date, $to_date)
    {
        $branchId = Auth::user()->BRANCH_ID;
        $vendorId = Auth::user()->vendor_id;
        $params = $this->elasticAcc->queryCountGroup(env('ES_TYPE_SPEED_ALERT'), $branchId, $vendorId, $from_date, $to_date);
        $response = $this->elasticSearchCount->getCountGroup($params);
        return $response;
    }

    // individual cab deviation count
    public function elas_count()
    {
        $from_date = date('Y-m-d');
        $to_date = date('Y-m-d');
        $user = Auth::user();
        $branchId = $user->BRANCH_ID;
        $vendorId = Auth::user()->vendor_id;
        $params = $this->elasticAcc->queryCountGroup(env('ES_TYPE_SPEED_ALERT'), $branchId, $vendorId, $from_date, $to_date);

        $response = $this->elasticSearchCount->getCountGroup($params);
        // print_r($response);exit;
        return $response;
    }

    public function elas_overspeed_test()
    {
        $from_date = date('Y-m-d');
        $to_date = date('Y-m-d');
        $user = Auth::user();
        $branchId = 48;
        // $branchId =$user->BRANCH_ID;
        $vendorId = Auth::user()->vendor_id;
        $params = $this->elasticAcc->queryGroup_OverSpeed_Report(env('ES_TYPE_SPEED_ALERT'), $branchId, $from_date, $to_date);

        $response = $this->elasticSearchCount->getData($params);
        print_r($response);
        exit;
        return $response;
    }

    // individual cab deviation count
    public function getCabDevCountGroup($from_date, $to_date)
    {
        $branchId = Auth::user()->BRANCH_ID;
        $vendorId = Auth::user()->vendor_id;
        $params = $this->elasticAcc->queryCountGroup(env('ES_TYPE_DEVIATION_ALERT'), $branchId, $vendorId, $from_date, $to_date);
        $response = $this->elasticSearchCount->getCountGroup($params);
        return $response;
    }

    // individual cab speed data
    public function getCabSpeedGroup($cabId)
    {
        $branchId = Auth::user()->BRANCH_ID;
        $curDate = CommonFn::cur_date();
        $params = $this->elasticAcc->queryGroup(env('ES_TYPE_SPEED_ALERT'), $branchId, $cabId, $curDate);
        $response = $this->elasticSearchCount->getData($params);
        return $response;
    }

    // individual OVERSPEED REPORT DATA
    public function getCabOverspeedReport($from_date, $to_date)
    {
        $branchId = Auth::user()->BRANCH_ID;

        $curDate = CommonFn::cur_date();
        $params = $this->elasticAcc->queryGroup_OverSpeed_Report(env('ES_TYPE_SPEED_ALERT'), $branchId, $from_date, $to_date);
        $response = $this->elasticSearchCount->getData($params);

        return $response;
    }

    // individual max speed data
    public function getMaxSpeed($cab_id, $from_date, $to_date)
    {
        $branchId = Auth::user()->BRANCH_ID;
        $curDate = CommonFn::cur_date();
        if ($to_date != '1900-01-01 00:00:00') {
            $to_date = date('Y-m-d H:i:s', strtotime('+ 5 minute', strtotime($from_date)));
            $from_date = date('Y-m-d H:i:s', strtotime('- 5 minute', strtotime($from_date)));
        }
        $params = $this->elasticAcc->queryMaxSpeed($branchId, $cab_id, $from_date, $to_date);
        $response = $this->elasticSearchCount->getMaxSpeed($params);
        return $response;
    }

    // individual cab deviation data
    public function getCabDevGroup($cabId)
    {
        $branchId = Auth::user()->BRANCH_ID;
        $curDate = CommonFn::cur_date();
        $params = $this->elasticAcc->queryGroup(env('ES_TYPE_DEVIATION_ALERT'), $branchId, $cabId, $curDate);
        $response = $this->elasticSearchCount->getData($params);
        return $response;
    }

    // count of cab that doesn't having gps log for 5min
    public function getCabNotLiveCount()
    {
        $curDate = CommonFn::cur_date();
        $sDate = $curDate . ' 00:00:00';
        $before5mins = strtotime('-5 minutes');
        $eDate = date('Y-m-d H:i:s', $before5mins);
        $USERID = Auth::user()->id;
        $branchId = Auth::user()->BRANCH_ID;
        $vendorId = Auth::user()->vendor_id;
        $params = $this->elasticAcc->gpsCabNotLiveCount($branchId, $vendorId, $sDate, $eDate);
        $response = $this->elasticSearchCount->getCount($params);
        return $response;
    }

    // get data of cab which not having gps log more than five min
    public function getCabNotLiveData(Request $request)
    {
        $USERID = Auth::user()->id;
        $branchId = Auth::user()->BRANCH_ID;
        $vendorId = Auth::user()->vendor_id;
        $params = $this->elasticAcc->cabNotLiveData($branchId, $vendorId, $sDate, $eDate);
        $response = $this->elasticSearchCount->getData($params);
        return $response;
    }

    // live tracking data of all cab
    public function getCabLiveData($branchId, $vendorId)
    {
        $params = $this->elasticAcc->cabLiveData($branchId, $vendorId);
        $response = $this->elasticSearchCount->getData($params);
        return $response;
    }

    // onduty data of all cab
    public function getCabOnDutyData($branchId, $vendorId)
    {
        $curDate = CommonFn::cur_date();
        $sDate = $curDate . ' 00:00:00';
        $params = $this->elasticAcc->cabOnDuty($branchId, $vendorId, $sDate);
        $response = $this->elasticSearchCount->getData($params);
        return $response;
    }

    // live tracking data of single cab
    public function getSingleCabLiveData($branchId, $vendorId, $cabId)
    {
        $params = $this->elasticAcc->singleCabLiveData($branchId, $vendorId, $cabId);
        $response = $this->elasticSearchCount->getData($params);
        return $response;
    }

    // Employee distance from driver when make click action
    public function getEmpDistance($rosterId, $branchId, $passengerId)
    {
        $params = $this->elasticAcc->actionRadius($rosterId, $branchId, $passengerId);
        $response = $this->elasticSearchCount->getData($params);
        return $response;
    }

    // Employee otp wrong from driver when make click action
    public function getDistanceAlert($branchId, $vendorId, $startdate, $end_date)
    {
        $params = $this->elasticAcc->distanceAlert($branchId, $vendorId, $startdate, $end_date);
        $response = $this->elasticSearchCount->getData($params);
        return $response;
    }

    // Cab wise live tracking

    public function getLiveTrack($cabId, $gpsDate)
    {
        $params = $this->elasticAcc->gpsCabNavigation($cabId, $gpsDate);
        $response = $this->elasticSearchCount->getData($params);

        return $response;
    }

    public function getCabLiveOverspeedStatus($branchId, $vendorId, $status, $cabid, $speed)
    {
        $before15mins = strtotime(env('ES_IDLE_TIME_15MIN_MINUS'));
        $sDate = date('Y-m-d H:i:s', $before15mins);
        $params = $this->elasticAcc->cabOverSpeedData($branchId, $vendorId, $cabid, env('ES_FIELD_ROUTE_STATUS'), env('ES_ONTRIP'), $speed, $sDate);
        $response = $this->elasticSearchCount->getData($params);

        return $response;
    }

    // live tracking of perticular status
    public function getCabLiveStatus($branchId, $vendorId, $status, $cabid)
    {
        $params = array();

        switch ($status) {
            case env('ES_STATUS_ALL'):
                $params = $this->elasticAcc->cabLiveData($branchId, $vendorId);
                break;
            case env('ES_CAB_STATUS'):
                $params = $this->elasticAcc->cabStatusData($branchId, $vendorId, $cabid);
                break;
            case env('ES_CAB_FILTER'):
                $params = $this->elasticAcc->multipleCabLiveData($branchId, $vendorId, $cabid);
                break;
            case env('ES_STATUS_LOGOUT'):
                $params = $this->elasticAcc->cabLiveStatusData($branchId, $vendorId, $cabid, env('ES_FIELD_LOGIN_STATUS'), env('ES_LOGOUT'));
                break;
            case env('ES_STATUS_LOGIN'):
                $params = $this->elasticAcc->cabLiveStatusData($branchId, $vendorId, $cabid, env('ES_FIELD_LOGIN_STATUS'), env('ES_LOGIN'));
                break;
            case env('ES_STATUS_ONEHOUR_BELOW'):
                $before5mins = strtotime(env('ES_IDLE_TIME_5MIN_MINUS'));
                $before1hour = strtotime(env('ES_IDLE_TIME_1HOUR_MINUS'));
                $eDate = date('Y-m-d H:i:s', $before5mins);
                $sDate = date('Y-m-d H:i:s', $before1hour);
                $params = $this->elasticAcc->cabIdleStatusData($branchId, $vendorId, $cabid, env('ES_FIELD_LOGIN_STATUS'), env('ES_LOGIN'), $sDate, $eDate);
                break;
            case env('ES_STATUS_SIXHOURS_BELOW'):
                $before1hour = strtotime(env('ES_IDLE_TIME_1HOUR_MINUS'));
                $before6hour = strtotime(env('ES_IDLE_TIME_6HOUR_MINUS'));
                $eDate = date('Y-m-d H:i:s', $before1hour);
                $sDate = date('Y-m-d H:i:s', $before6hour);
                $params = $this->elasticAcc->cabIdleStatusData($branchId, $vendorId, $cabid, env('ES_FIELD_LOGIN_STATUS'), env('ES_LOGIN'), $sDate, $eDate);
                break;
            case env('ES_STATUS_SIXHOURS_ABOVE'):
                $before0mins = strtotime(env('ES_DEFAULT_ZERO'));
                $before6hour = strtotime(env('ES_IDLE_TIME_6HOUR_MINUS'));
                $eDate = date('Y-m-d H:i:s', $before6hour);
                $sDate = date('Y-m-d H:i:s', $before0mins);
                $params = $this->elasticAcc->cabIdleStatusData($branchId, $vendorId, $cabid, env('ES_FIELD_LOGIN_STATUS'), env('ES_LOGIN'), $sDate, $eDate);
                break;
            case env('ES_STATUS_GPS_OFF'):
                $params = $this->elasticAcc->cabLiveStatusData($branchId, $vendorId, $cabid, env('ES_FIELD_GPS_STATUS'), env('ES_GPS_OFF'));
                break;
            case env('ES_STATUS_GPS_NOTFIX'):
                $before5mins = strtotime(env('ES_IDLE_TIME_5MIN_MINUS'));
                $before0mins = strtotime(env('ES_DEFAULT_ZERO'));
                $eDate = date('Y-m-d H:i:s', $before5mins);
                $sDate = date('Y-m-d H:i:s', $before0mins);
                $params = $this->elasticAcc->cabIdleStatusData($branchId, $vendorId, $cabid, env('ES_FIELD_LOGIN_STATUS'), env('ES_LOGIN'), $sDate, $eDate);
                break;
            case env('ES_STATUS_GPS_FIX'):
                $params = $this->elasticAcc->cabLiveStatusData($branchId, $vendorId, $cabid, env('ES_FIELD_GPS_STATUS'), env('ES_GPS_FIX'));
                break;
            case env('ES_STATUS_BATTERY_LOW'):
                $params = $this->elasticAcc->cabLiveStatusData($branchId, $vendorId, $cabid, env('ES_FIELD_BATTERY_STATUS'), env('ES_BATTERY'));
                break;
            case env('ES_STATUS_VACANT'):
                $params = $this->elasticAcc->cabLiveStatusData($branchId, $vendorId, $cabid, env('ES_FIELD_ROUTE_STATUS'), env('ES_VACANT'));
                break;
            case env('ES_STATUS_ONTRIP'):
                $before3hour = strtotime(env('ES_IDLE_TIME_3HOUR_MINUS'));
                $sDate = date('Y-m-d H:i:s', $before3hour);
                $params = $this->elasticAcc->cabOnDutyData($branchId, $vendorId, $cabid, env('ES_FIELD_ROUTE_STATUS'), env('ES_ONTRIP'), $sDate);
                break;
        }
        $response = $this->elasticSearchCount->getData($params);
        return $response;
    }

    // service to get idle cab list to sql insert
    public function cabLiveService()
    {
        $before5mins = strtotime(env('ES_IDLE_TIME_5MIN_MINUS'));
        $before1hour = strtotime(env('ES_IDLE_TIME_1HOUR_MINUS'));
        $sDate = date('Y-m-d H:i:s', $before1hour);
        $eDate = date('Y-m-d H:i:s', $before5mins);
        $params = $this->elasticAcc->cabNotLiveData($sDate, $eDate);
        $response = $this->elasticSearchCount->getCabIdleData($params);
        return $response;;
    }

    // update based on response
    public function cabLiveUpdateService($returnid)
    {
        //        $xmltoarray = json_decode($returnid, TRUE);
        $response = $this->elasticSearchCount->cabNotLiveUpdate($returnid);
        return $response;
    }

    //
    public function getCabSpeedUpdate(Request $request)
    {
        $params = $this->elasticAcc->gpsSpeedUpdate($index, $type, $id, $field, $value);
        $response = $this->elasticSearchCount->setSpeedUpdate($params);
        return $response;
    }

    // update remark of cab that have speed data
    public function overspeedremarks($request)
    {
        $cab_id = $request->cab_id;
        $reason_id = $request->reason_id;
        $remark = $request->remark;
        $branchId = Auth::user()->BRANCH_ID;
        // print_r($request->all());exit;
        $curDate = CommonFn::cur_date();
        $params = $this->elasticAcc->queryUpdateGroup(env('ES_TYPE_SPEED_ALERT'), $branchId, $cab_id, $curDate);

        $response = $this->elasticSearchCount->updateGroup(env('ES_TYPE_SPEED_ALERT'), $params, $reason_id, $remark);
        // return $response;
    }

    // public function cabSpeedRemarkUpdate() {
    //     $cabId = Input::get('op_vehicleid');
    //     $reason_id = Input::get('osvehicle_reason');
    //     $remark = Input::get('overspeed_remarks');
    //     $branchId = Auth::user()->BRANCH_ID;
    //     $curDate = CommonFn::cur_date();
    //     $params = $this->elasticAcc->queryUpdateGroup(env('ES_TYPE_SPEED_ALERT'), $branchId, $cabId, $curDate);
    //     $response = $this->elasticSearchCount->updateGroup(env('ES_TYPE_SPEED_ALERT'), $params, $reason_id, $remark);
    //     return $response;
    // }

    // update remark of cab that have speed data admin app
    public function cabSpeedRemarkUpdateApp($branchId, $cabId, $reason_id, $remark)
    {
        $curDate = CommonFn::cur_date();
        $params = $this->elasticAcc->queryUpdateGroup(env('ES_TYPE_SPEED_ALERT'), $branchId, $cabId, $curDate);
        $response = $this->elasticSearchCount->updateGroup(env('ES_TYPE_SPEED_ALERT'), $params, $reason_id, $remark);
        return $response;
    }

    // update remark of cab that have deviation data
    /* public function cabDevRemarkUpdate(Request $request) {
        $cabId = Input::get('cabId');
        $reason_id = Input::get('reason_id');
        $remark = Input::get('remark');
        $branchId = Auth::user()->BRANCH_ID;
        $curDate = CommonFn::cur_date();
        $params = $this->elasticAcc->queryUpdateGroup(env('ES_TYPE_DEVIATION_ALERT'), $branchId, $cabId, $curDate);
        $response = $this->elasticSearchCount->updateGroup(env('ES_TYPE_DEVIATION_ALERT'), $params, $reason_id, $remark);
        return $response;
    } */

    public function cabDevRemarkUpdate($cabId, $reason_id, $remark)
    {
        /* $cabId = Input::get('cabId');
        $reason_id = Input::get('reason_id');
        $remark = Input::get('remark'); */
        $branchId = Auth::user()->BRANCH_ID;
        $curDate = CommonFn::cur_date();
        $params = $this->elasticAcc->queryUpdateGroup(env('ES_TYPE_DEVIATION_ALERT'), $branchId, $cabId, $curDate);
        $response = $this->elasticSearchCount->updateGroup(env('ES_TYPE_DEVIATION_ALERT'), $params, $reason_id, $remark);
        return $response;
    }

    // insert google new address
    public function gpsAddress(Request $request)
    {
        $gps_address = $request->input('gps_address');
        $xmltoarray = json_decode($gps_address, TRUE);
        if (array_key_exists('GpsLocation', $xmltoarray)) {
            $response = $this->elasticSearchCount->gpsLocationInsert($xmltoarray['GpsLocation']);
        }
    }
    // get google address

    public function getGpsAddress($lat, $lng)
    {
        if ($lat != 0 && $lng != 0) {
            $params = $this->elasticAcc->getGoogleAddress($lat, $lng);
            return $this->elasticSearchCount->getData($params);
        } else {
            return 'No Record';
        }
    }

    // Insert Driver Message
    public function insertDriverMsg($roster_id, $branch_id, $vendor_id, $cab_id, $action, $msg, $status, $speed_interval,
        $request_time, $acknowledge_time)
    {
        $response = $this->elasticSearchCount->driverMsgInsert($roster_id, $branch_id, $vendor_id, $cab_id, $action, $msg, $status, $speed_interval,
            $request_time, $acknowledge_time);
        return $response;
    }

    // Insert Web Log
    public function insertWebLogs($log_array)
    {
        $response = $this->elasticSearchCount->webLogInsert($log_array);
        return $response;
    }

    // Get Travel Km
    public function getTravelDistance($cab_id, $from_date_time, $to_date_time)
    {
        $params = $this->elasticAcc->queryGpsDistance($cab_id, $from_date_time, $to_date_time);
        $response = $this->elasticSearchCount->getDistance($params);
        return $response;
    }

    public function StopId()
    {
        //        $id = 5;
        //        $origin = '12.924535,80.115924';
        //        $origin = '12.922703,80.133270';
        //        $origin = '12.997064,80.255685';
        //        $origin = '13.128468,80.290559';
        //        $origin = '13.159795,80.224825';
        //        $origin = '13.122721,80.148406';
        //        $origin = '13.124677,80.031056';
        //
        //        $origin = '13.0232648,80.1770327';
        //
        for ($j = 5; $j < 15; $j++) {
            $id = $j;
            $common_control = new CommonController;
            $params = $this->elasticAcc->queryStopId($id);
            $response = $this->elasticSearchCount->getEmpDistanceGroup($params);

            $count = count($response);
            for ($i = 0; $i < $count; $i++) {
                $STOP_ID = $response[$i]['STOP_ID'];
                $POSITION = $response[$i]['POSITION'];
                $APROX_DURATION = $response[$i]['APROX_DURATION'];
                //            $minsval = $common_control->getDistance($origin, $POSITION);
                $qry = "SELECT  SEC_TO_TIME('$APROX_DURATION') as pickuptime ";
                $pickup_time = DB::select($qry);
                $pickuptime = $pickup_time[0]->pickuptime;
                //            echo '<br/>'."INSERT INTO `shuttle_route_stops` (`Route_ID`, `Stop_ID`, `Stop_Time`, `status`) VALUES ('$id', '$STOP_ID', '$pickuptime', '1');";
                echo '<br/>' . "UPDATE shuttle_route_stops SET Stop_Time='$pickuptime' WHERE Route_ID='$id' AND Stop_ID='$STOP_ID';";
            }
        }

        //        $count = count($response);
        //        for($i=0;$i<$count;$i++)
        //       {
        //             $latlong = explode(',', $response[$i]["POSITION"]);
        //           $latlong[0];
        //           $latlong[1];
        //           $STOP_ID = $response[$i]["STOP_ID"];
        //           $LOCATION = $response[$i]["LOCATION"];
        //
        //         echo $STOP_ID;
        //
        //           echo $update = "UPDATE shuttle_stops SET Stops_Name='$LOCATION',Stops_Lat='$latlong[0]',Stops_Long='$latlong[1]' WHERE Stops_ID='$STOP_ID';";
        //           echo '<br/>';
        //
        //        }
    }

    // update Login Status

    public function cabUpdateLoginStatus($cabid)
    {
        $queryId = $this->elasticAcc->getCabLiveId($cabid);

        $id = $this->elasticSearchCount->getCabLiveStatusId($queryId);
        if (env('ES_DEFAULT_ZERO') != $id) {
            $response = $this->elasticSearchCount->cabLoginStatusUpdate($id);
        }
        return $response;
    }

    public function cabMappingDeactiveStatus($cabid)
    {
        $queryId = $this->elasticAcc->getCabLiveId($cabid);

        $id = $this->elasticSearchCount->getCabLiveStatusId($queryId);
        $response = 0;
        if (env('ES_DEFAULT_ZERO') != $id) {
            $response = $this->elasticSearchCount->cabMappingDeactiveStatus($id);
        }
        return $response;
    }

    public function close_onduty($cabid)
    {
        $queryId = $this->elasticAcc->getCabLiveId($cabid);
        $response = array();
        $id = $this->elasticSearchCount->getCabLiveStatusId($queryId);
        if (env('ES_DEFAULT_ZERO') != $id) {
            $response = $this->elasticSearchCount->close_onduty($id);
        }
        return $response;
    }

    public function gpsCabNavigationPath($cabId, $gpsDate)
    {
        $params = $this->elasticAcc->gpsCabNavigationPath($cabId, $gpsDate);
        $params1 = $this->elasticAcc->gpsCabNavigationPath($cabId, env('ES_DEFAULT_DATE'));
        $response = $this->elasticSearchCount->getData($params);
        if ($response == 'No Record') {
            $response = $this->elasticSearchCount->getData($params1);
        }
        return $response;
    }

    // Employee gps and network actions
    /*public function getGpsActionAlert($branchId, $vendorId, $cabId, $roster_id, $startdate, $end_date)
    {
        $params = $this->elasticAcc->cabGpsAlert($branchId, $vendorId, $cabId, $roster_id, $startdate, $end_date);
        $response = $this->elasticSearchCount->getData($params);
        return $response;
    }*/
	 // Employee gps and network actions
	 public function getGpsActionAlert($branchId, $vendorId, $cabId, $roster_id, $startdate, $end_date,$page,$per_page,$orderBy,$order,$filterModel)
    {
        $params = $this->elasticAcc->cabGpsAlertWithPagination($branchId, $vendorId, $cabId, $roster_id, $startdate, $end_date,$page,$per_page,$orderBy,$order,$filterModel);
		
        $response = $this->elasticSearchCount->getDataPagination($params,$page,$per_page,$orderBy,$order);
		
		return $response;
    }

    // Employee gps processdate single record
    public function cabGpsRecordSinglePast($branchId, $vendorId, $cabId, $date)
    {
        $params = $this->elasticAcc->cabGpsProcessdatePast($branchId, $vendorId, $cabId, $date);
        $response = $this->elasticSearchCount->getData($params);
        return $response;
    }

    public function cabGpsRecordSingleFuture($branchId, $vendorId, $cabId, $date)
    {
        $params = $this->elasticAcc->cabGpsProcessdateFuture($branchId, $vendorId, $cabId, $date);
        $response = $this->elasticSearchCount->getData($params);
        return $response;
    }

    public function tollCrossedPatSearch($branch_id, $cab_id, $route_id, $from_date, $to_date, $latitude, $longitude)
    {
        $params = $this->elasticAcc->getTollCrossedPath($branch_id, $cab_id, $route_id, $from_date, $to_date, $latitude, $longitude);
        $response = $this->elasticSearch->search($params);
        return $response;
    }
}
