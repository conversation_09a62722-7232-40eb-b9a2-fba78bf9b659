<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;

use App\Services\Admin\AdhocWeeklyRosterService;

use App\Http\Requests\Admin\PostAdhocWeeklyRosterRequest;
use App\Http\Requests\Admin\DateShiftTimeRequest;
use App\Http\Requests\Admin\MaskTransactionRequest;
use App\Http\Requests\Admin\DeleteRosterRequest;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class AdhocWeeklyRosterController extends Controller
{
    protected AdhocWeeklyRosterService $adhoc_weeklyRosterService;

    public function __construct(AdhocWeeklyRosterService $adhoc_weeklyRosterService)
    {
        $this->adhoc_weeklyRosterService = $adhoc_weeklyRosterService;
    }
    
    public function dataForCreate(): FoundationApplication|Response|ResponseFactory
    {
        return $this->adhoc_weeklyRosterService->dataForCreate();
    }
   

    public function fetchAdhocWeeklyDateWiseRequestTime(DateShiftTimeRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->adhoc_weeklyRosterService->fetchAdhocWeeklyDateWiseRequestTime($request);
    }


  public function fetch_AdhocWeeklyRosterRequest_Report_Details(MaskTransactionRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->adhoc_weeklyRosterService->fetch_AdhocWeeklyRosterRequest_Report_Details($request);
    }
    
    
   
    public function postAdhocWeeklyRosterRequest(PostAdhocWeeklyRosterRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->adhoc_weeklyRosterService->postAdhocWeeklyRosterRequest($request);
        
    }
   
        public function deleteAdhocWeeklyRosterRequest(DeleteRosterRequest $request, $roster_req_id): FoundationApplication|Response|ResponseFactory
    {
        return $this->adhoc_weeklyRosterService->DeleteAdhocWeeklyRosterRequest($request, $roster_req_id);
        
    }   
 
}
