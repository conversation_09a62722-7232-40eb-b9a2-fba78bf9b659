<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;

use App\Services\Admin\ReportService;
use App\Http\Requests\Admin\ReportRequest;
use App\Http\Requests\Admin\ASFRequest;
use App\Http\Requests\Admin\VehicleRequest;
use App\Http\Requests\Admin\MisReportRequest;
use App\Http\Requests\Admin\MisShiftTimeRequest;
use App\Http\Requests\Admin\MisApprovelRequest;
use App\Http\Requests\Admin\MisApprovelUpdateRequest;
use App\Http\Requests\Admin\WrongLocationRequest;
use App\Http\Requests\Admin\MaskTransactionRequest;
use App\Http\Requests\Admin\TripTypeChangeRequest;
use App\Http\Requests\Admin\EmptyVehicleRequest;
use App\Http\Requests\Admin\EmptyRouteAddRequest;
use App\Http\Requests\Admin\EmptyRouteVendorChangeRequest;
use App\Http\Requests\Admin\GetFilterEmptyRequest;
use App\Http\Requests\Admin\ApproveSubmitEmptyRequest;
use App\Http\Requests\Admin\DateShiftTimeRequest;
use App\Http\Requests\Admin\getMisReportRequest;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class ReportController extends Controller
{
    protected ReportService $reportService;

    public function __construct(ReportService $reportService)
    {
        $this->reportService = $reportService;
    }


    public function dataForCreateEmployees(): FoundationApplication|Response|ResponseFactory
    {
        return $this->reportService->dataForCreateEmployees();
    }

    public function dataForCreateVehicles(): FoundationApplication|Response|ResponseFactory
    {
        return $this->reportService->dataForCreateVehicles();
    }

    public function fetch_OverAllReport_Details(ReportRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->reportService->fetch_OverAllReport_Details($request);
    }

    public function fetch_EmployeesWise_OverAllReport_Details(ReportRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->reportService->fetch_EmployeesWise_OverAllReport_Details($request);
    }

    public function fetch_VehicleWise_OverAllReport_Details(ReportRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->reportService->fetch_VehicleWise_OverAllReport_Details($request);
    }

    public function fetch_Associate_Noshow_Details(ReportRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->reportService->fetch_Associate_Noshow_Details($request);
    }

    public function fetch_Cancel_Report_Details(ReportRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->reportService->fetch_Cancel_Report_Details($request);
    }

    public function fetch_Ontime_Report_Details(ReportRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->reportService->fetch_Ontime_Report_Details($request);
    }
    
    public function fetch_Escort_Report_Details(ReportRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->reportService->fetch_Escort_Report_Details($request);
    }

      public function fetch_ASF_Report_Details(ASFRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->reportService->fetch_ASF_Report_Details($request);
    }

    public function fetch_AcceptReject_Report_Details(ReportRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->reportService->fetch_AcceptReject_Report_Details($request);
    }

   public function fetch_Toll_Report_Details(ReportRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->reportService->fetch_Toll_Report_Details($request);
    }
    
    public function fetch_Vehicle_Report_Details(VehicleRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->reportService->fetch_Vehicle_Report_Details($request);
    }

    public function fetch_Breakdown_Report_Details(ReportRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->reportService->fetch_Breakdown_Report_Details($request);
    }
    
  public function fetch_Vendorchange_Report_Details(ReportRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->reportService->fetch_Vendorchange_Report_Details($request);
    }

public function fetch_Feedback_Report_Details(ReportRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->reportService->fetch_Feedback_Report_Details($request);
    }


 public function fetch_OverTime_Report_Details(ReportRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->reportService->fetch_OverTime_Report_Details($request);
    }

public function fetch_Adhoc_Report_Details(ReportRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->reportService->fetch_Adhoc_Report_Details($request);
    }
    
public function fetch_SMS_Report_Details(ASFRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->reportService->fetch_SMS_Report_Details($request);
    }

 public function fetch_WeeklyRoster_Report_Details(ReportRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->reportService->fetch_WeeklyRoster_Report_Details($request);
    }

  public function fetch_OtpReport_Details(ReportRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->reportService->fetch_OtpReport_Details($request);
    }

   public function fetch_PanicReport_Details(ReportRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->reportService->fetch_PanicReport_Details($request);
    }

   public function fetch_Deactive_Route_Employee_Details(VehicleRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->reportService->fetch_Deactive_Route_Employee_Details($request);
    }
  
   public function getCabOverspeedReport(ASFRequest $request): Application|Response|ResponseFactory
    {
         return $this->reportService->getCabOverspeedReport($request);

    }
   public function getWrongLocationReport(WrongLocationRequest $request): Application|Response|ResponseFactory
    {
         return $this->reportService->getWrongLocationReport($request);

    }

     public function fetch_Adhoc_WeeklyRoster_Report_Details(ReportRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->reportService->fetch_Adhoc_WeeklyRoster_Report_Details($request);
    }
  
    public function fetch_FWMIS_Report_Details(MisReportRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->reportService->fetch_FWMIS_Report_Details($request);
    }

     public function misreport_date_wise_shiftlogin(MisShiftTimeRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->reportService->misreport_date_wise_shiftlogin($request);
    }

    public function fetch_FWMIS_AcceptApprovelReport_Details(MisApprovelRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->reportService->fetch_FWMIS_AcceptApprovelReport_Details($request);
    }

   public function bill_Approvel(MisApprovelUpdateRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->reportService->bill_Approvel($request);
    }

    public function fetch_EmptyKmApprovel_Report_Details(ASFRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->reportService->fetch_EmptyKmApprovel_Report_Details($request);
    }
    
   public function fetch_Mask_CallLogs_Report_Details(ASFRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->reportService->fetch_Mask_CallLogs_Report_Details($request);
    }
 
    public function fetch_Mask_Transaction_Report_Details(MaskTransactionRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->reportService->fetch_Mask_Transaction_Report_Details($request);
    } 
     
    public function EmptyRoute(): FoundationApplication|Response|ResponseFactory
    {
        return $this->reportService->EmptyRoute();
    }

    public function EmptyRouteVendorChange(EmptyRouteVendorChangeRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->reportService->EmptyRouteVendorChange($request);
    }

    public function TripTypeChange(TripTypeChangeRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->reportService->TripTypeChange($request);
    }

    public function EmptyVehicle(EmptyVehicleRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->reportService->EmptyVehicle($request);
    }

    public function EmptyRouteAdd(EmptyRouteAddRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->reportService->EmptyRouteAdd($request);
    }

    public function GetFilterEmpty(GetFilterEmptyRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->reportService->GetFilterEmpty($request);
    }

    public function ApproveSubmitEmpty(ApproveSubmitEmptyRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->reportService->ApproveSubmitEmpty($request);
    }
    public function getMisReport(getMisReportRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->reportService->getMisReport($request);
    }

}
