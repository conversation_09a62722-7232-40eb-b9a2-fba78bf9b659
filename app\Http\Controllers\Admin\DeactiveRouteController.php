<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;

use App\Services\Admin\DeactiveRouteService;
use App\Http\Requests\Admin\ASFRequest;
use App\Http\Requests\Admin\DeactivateRoutes;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class DeactiveRouteController extends Controller
{
    protected DeactiveRouteService $deactiveRouteService;

    public function __construct(DeactiveRouteService $deactiveRouteService)
    {
        $this->deactiveRouteService = $deactiveRouteService;
    }

    public function fetch_Deactive_Route_Details(ASFRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->deactiveRouteService->fetch_Deactive_Route_Details($request);
    }

     public function deactivate_Routes(DeactivateRoutes $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->deactiveRouteService->deactivate_Routes($request);
    }
}
