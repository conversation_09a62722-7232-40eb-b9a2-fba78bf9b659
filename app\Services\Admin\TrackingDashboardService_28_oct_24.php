<?php

namespace App\Services\Admin;

use App\Helpers\CommonFunction;
use App\Helpers\MyHelper;
use App\Models\Branch;
use App\Models\property;
use App\Models\Roster;
use App\Models\RosterPassenger;
use App\Models\Cab_allocation;
use App\Models\OtpVerify;
use App\Models\RouteEscorts;
use App\Models\Reason_Log;
use App\Models\Cab;
use App\Models\Sms;
use App\Models\Location;
use App\Models\CabAttendance;
use App\Models\Vehicle;
use App\Models\VehicleModel;
use App\Models\Driver;
use App\Models\Employee;
use Carbon\Carbon;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Crypt;
use App\Http\Controllers\MaskNumberClearController;

use App\Http\Controllers\ElasticController;

class TrackingDashboardService
{
    protected CommonFunction $commonFunction;
    protected ?Branch $currentBranch = null;

    public function __construct(CommonFunction $commonFunction)
    {
        $this->commonFunction = $commonFunction;
    }

    private function getPropertyValue()
    {
        $branch_id= Auth::user()->BRANCH_ID;
        //    exit;
        $property = Property::where('BRANCH_ID',  $branch_id)
            ->where('ACTIVE', MyHelper::$RS_ACTIVE)
            //->where('PROPERTIE_NAME', $propertyName)
            ->get();
			
        return $property;
    }

    public function tracking_dashboard($request): FoundationApplication|Response|ResponseFactory
    {

        $authUser = Auth::user();
        $tracking_type = $request->tracking_type;
        $date = Carbon::now();
        $curdatetime = $date->format("Y-m-d H:i:s");
        try {
            // echo "try";
            $PR_ESTIMATESUBTIME = MyHelper::$PR_ESTIMATESUBTIME;
            $PR_ESTIMATEADDTIME = MyHelper::$PR_ESTIMATEADDTIME;
            $PR_ESTIMATECURTIME = MyHelper::$PR_ESTIMATECURTIME;
            $PR_LEVEL2 = MyHelper::$PR_LEVEL2;
            $PR_ESTIMATECABALLOT = MyHelper::$PR_ESTIMATECABALLOT;
            $PR_LEVEL1 = MyHelper::$PR_LEVEL1;
            $PR_TRIPNOTEXECUTED = MyHelper::$PR_TRIPNOTEXECUTED;
            $PR_EMPLOYEE_BUFFER = MyHelper::$PR_EMPLOYEE_BUFFER;
			$user_type=$authUser->user_type;

            $property_result = $this->getPropertyValue();
            // print_r($property_result);
            // exit;
            for ($i = 0; $i < count($property_result); $i++) {
                $PROPERTIE_NAME = $property_result[$i]->PROPERTIE_NAME;
                $PROPERTIE_VALUE = $property_result[$i]->PROPERTIE_VALUE;
                switch ($PROPERTIE_NAME) {
                    case $PR_ESTIMATESUBTIME:
                        $ESTIMATE_SUBTIME = $PROPERTIE_VALUE;
                        break;
                    case $PR_ESTIMATEADDTIME:
                        $ESTIMATE_ADDTIME = $PROPERTIE_VALUE;
                        break;
                    case $PR_ESTIMATECURTIME:
                        $ESTIMATE_CURTIME = $PROPERTIE_VALUE;
                        break;
                    case $PR_LEVEL2:
                        $ESTIMATE_INTERVAL60 = $PROPERTIE_VALUE;
                        break;
                    case $PR_ESTIMATECABALLOT:
                        $ESTIMATE_CABALLOT = $PROPERTIE_VALUE;
                        break;
                    case $PR_LEVEL1:
                        $ESTIMATE_INTERVAL30 = $PROPERTIE_VALUE;
                        break;
                    case $PR_TRIPNOTEXECUTED:
                        $TRIP_NOT_EXECUTE = $PROPERTIE_VALUE;
                        break;
                     case $PR_EMPLOYEE_BUFFER:
                         $wapptime = $PROPERTIE_VALUE;
                        break;
                    default:
                        break;
                }
            }
			 if ($user_type == MyHelper::$ADMIN) {
				$vendorid = "";
				} else {
				$vendorid = "AND R.VENDOR_ID='$vendor_id'";
				}
            $track_data = Roster::query()
					->select(
                    'rosters.ROSTER_ID',
                    'rosters.BRANCH_ID',
                    'rosters.ROUTE_ID',
                    'rosters.TRIP_TYPE',
                    DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN rosters.ESTIMATE_END_TIME ELSE rosters.ESTIMATE_START_TIME END AS TRIPINTIME'),
                    DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN DATE(rosters.ESTIMATE_END_TIME) ELSE DATE(rosters.ESTIMATE_START_TIME) END AS TRIPDATE'),
                    DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN TIME(rosters.ESTIMATE_END_TIME) ELSE TIME(rosters.ESTIMATE_START_TIME) END AS TRIPTIME'),
                    'VE.NAME AS VENDORNAME',
                    'rosters.START_LOCATION',
                    'rosters.END_LOCATION',
                    'rosters.CAB_ID',
                    'rosters.VENDOR_ID',
                    'rosters.PASSENGER_ALLOT_COUNT',
                    'rosters.PASSENGER_CLUBING_COUNT',
                    'rosters.CAB_CAPACITY_COUNT',
                    'rosters.TRIP_APPROVED_KM',
                    'RD.TOLL_ROUTE_STATUS','rosters.ROSTER_STATUS',
                    DB::raw('IF(RE.STATUS IS NULL, 0, 1) AS ESCORTSTATUS'),
                    DB::raw('IF(RE.ESCORT_ID IS NULL, 0, 1) AS ESCORTALLOTSTATUS'),
                    'RE.ROUTE_ESCORT_ID',
                    DB::raw("IF(rosters.TRIP_TYPE = 'P', '$ESTIMATE_INTERVAL30', 'Interval 180 minute') AS EXECUTETIME"),
                    DB::raw('GROUP_CONCAT(RP.EMPLOYEE_ID) as empids0')
                )
                ->where('rosters.ACTIVE', MyHelper::$RS_ACTIVE)
                ->where('rosters.BRANCH_ID', $branch_id)
				 
				->whereIn('rosters.ROSTER_STATUSS',[$RS_NEWROSTER])
							
                ->whereIn('rosters.TRIP_TYPE', ['P', 'D'])
                ->leftjoin('roster_passengers as RP', 'RP.ROSTER_ID', '=', 'rosters.ROSTER_ID')
                ->leftJoin('vendors as VE', 'VE.VENDOR_ID', '=', 'rosters.VENDOR_ID')
                ->leftJoin('route_distance as RD', 'RD.ROSTER_ID', '=', 'rosters.ROSTER_ID')
                ->leftJoin('route_escorts as RE', 'RE.ROSTER_ID', '=', 'rosters.ROSTER_ID')
                ->groupBy('rosters.ROSTER_ID')
                ->havingRaw("TRIPINTIME BETWEEN SUBTIME('2019-07-01 01:30:00',$ESTIMATE_SUBTIME) AND ADDTIME('2019-07-30 01:30:00',$ESTIMATE_ADDTIME) AND '$curdatetime'>=SUBTIME(TRIPINTIME,$ESTIMATE_CURTIME)")
                ->orderBy("TRIPTIME")
                ->get();
            return response([
                'success' => true,
                'status' => 3,
                'employees' => $track_data,
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'No data',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function pagination_tracking_dashboard($request, bool $active = true): FoundationApplication|Response|ResponseFactory
    {
        try {


            $authUser = Auth::user();
			$branchid=$authUser->BRANCH_ID;
            $tracking_type = $request->tracking_type;
            $date = Carbon::now();
            $curdatetime = $date->format("Y-m-d H:i:s");
			$curdate= date('Y-m-d');
            $ElasticController=new ElasticController();

            $PR_ESTIMATESUBTIME = MyHelper::$PR_ESTIMATESUBTIME;
            $PR_ESTIMATEADDTIME = MyHelper::$PR_ESTIMATEADDTIME;
            $PR_ESTIMATECURTIME = MyHelper::$PR_ESTIMATECURTIME;
            $PR_LEVEL2 = MyHelper::$PR_LEVEL2;
            $PR_ESTIMATECABALLOT = MyHelper::$PR_ESTIMATECABALLOT;
            $PR_LEVEL1 = MyHelper::$PR_LEVEL1;
            $PR_TRIPNOTEXECUTED = MyHelper::$PR_TRIPNOTEXECUTED;
            $PR_EMPLOYEE_BUFFER = MyHelper::$PR_EMPLOYEE_BUFFER;
			
			$RS_ROSTER=MyHelper::$RS_ROSTER;
			
			$ESCORT_INTER=MyHelper::$ESCORT_INTER;
			$ESCORT_REMOVE=MyHelper::$ESCORT_REMOVE;
			
			$TD_ALLNORMAL=MyHelper::$TD_ALLNORMAL;
			$TD_NOTALLOTED=MyHelper::$TD_NOTALLOTED;
			$TD_ALLOTED=MyHelper::$TD_ALLOTED;
			$TD_TRIPACCEPTED=MyHelper::$TD_TRIPACCEPTED;
			$TD_TRIPREJECTED=MyHelper::$TD_TRIPREJECTED;
			$TD_TRIPEXECUTED=MyHelper::$TD_TRIPEXECUTED;
			$TD_NORESPONSE=MyHelper::$TD_NORESPONSE;
			$TD_TRIPNOTEXECUTED=MyHelper::$TD_TRIPNOTEXECUTED;
			$TD_BREAKDOWN=MyHelper::$TD_BREAKDOWN;
			$TD_WAITINGATPICKUPPOINT=MyHelper::$TD_WAITINGATPICKUPPOINT;
			$TD_SAFEDROP=MyHelper::$TD_SAFEDROP;
			$TD_ESCORT=MyHelper::$TD_ESCORT;
			$TD_OVERTIMEALERT=MyHelper::$TD_OVERTIMEALERT;
			$TD_OVERSPEED=MyHelper::$TD_OVERSPEED;
			$TD_ALERTSIGNALFAILURE=MyHelper::$TD_ALERTSIGNALFAILURE;
			$TD_DEVIATIONALERT=MyHelper::$TD_DEVIATIONALERT;
			$TD_SEARCHALL=MyHelper::$TD_SEARCHALL;
			$TD_NOSHOW=MyHelper::$TD_NOSHOW;


			$ES_REMOVEINTER=MyHelper::$ES_REMOVEINTER;
			
			
			$RS_NEWROSTER=MyHelper::$RS_NEWROSTER;
			$RS_TOTALALLOT=MyHelper::$RS_TOTALALLOT;
			$RS_TOTALACCEPT=MyHelper::$RS_TOTALACCEPT;
			$RS_TOTALREJECT=MyHelper::$RS_TOTALREJECT;
			$RS_TOTALEXECUTE=MyHelper::$RS_TOTALEXECUTE;
			$RS_TOTALBREAKDOWN=MyHelper::$RS_TOTALBREAKDOWN;
            $RS_CABALLOT = Myhelper::$RS_CABALLOT;
            $RPS_NOSHOW = Myhelper::$RPS_NOSHOW;
			
			$RPS_TTLARRIVAL=MyHelper::$RPS_TTLARRIVAL;
			$RPS_TTLCABDELAY=MyHelper::$RPS_TTLCABDELAY;
			
			
			

            $property_result = $this->getPropertyValue();
			$user_type=$authUser->user_type;
			$vendor_id= Auth::user()->vendor_id;
            // print_r($property_result);
            // exit;
            for ($i = 0; $i < count($property_result); $i++) {
                $PROPERTIE_NAME = $property_result[$i]->PROPERTIE_NAME;
                $PROPERTIE_VALUE = $property_result[$i]->PROPERTIE_VALUE;
                switch ($PROPERTIE_NAME) {
                    case $PR_ESTIMATESUBTIME:
                        $ESTIMATE_SUBTIME = $PROPERTIE_VALUE;
                        break;
                    case $PR_ESTIMATEADDTIME:
                        $ESTIMATE_ADDTIME = $PROPERTIE_VALUE;
                        break;
                    case $PR_ESTIMATECURTIME:
                        $ESTIMATE_CURTIME = $PROPERTIE_VALUE;
                        break;
                    case $PR_LEVEL2:
                        $ESTIMATE_INTERVAL60 = $PROPERTIE_VALUE;
                        break;
                    case $PR_ESTIMATECABALLOT:
                        $ESTIMATE_CABALLOT = $PROPERTIE_VALUE;
                        break;
                    case $PR_LEVEL1:
                        $ESTIMATE_INTERVAL30 = $PROPERTIE_VALUE;
                        break;
                    case $PR_TRIPNOTEXECUTED:
                        $TRIP_NOT_EXECUTE = $PROPERTIE_VALUE;
                        break;
                     case $PR_EMPLOYEE_BUFFER:
                         $wapptime = $PROPERTIE_VALUE;
						
                        break;
                    default:
                        break;
                }
            }

            $perPage = $request->input('per_page', MyHelper::$PAGINATION_PER_PAGE);
            $branch_id = Auth::user()->BRANCH_ID;
            $activeStatus = $active ? MyHelper::$RS_ACTIVE : MyHelper::$RS_INACTIVE;
			
			if($user_type == MyHelper::$ADMIN)
			{
				$vendorid = "";
			} else {
				//$vendorid = "AND R.VENDOR_ID='$vendor_id'";
				$vendorid = $vendor_id;
			}
			
//$curdatetime='2019-07-01 01:30:00';
//$curdate='2019-07-01';
       /*  $select_filed = "R.ROSTER_ID,R.BRANCH_ID,R.ROUTE_ID,R.TRIP_TYPE,
        IF(R.TRIP_TYPE = 'P',R.ESTIMATE_END_TIME,R.ESTIMATE_START_TIME) as TRIPINTIME,
        IF(R.TRIP_TYPE = 'P',DATE(R.ESTIMATE_END_TIME),DATE(R.ESTIMATE_START_TIME)) as TRIPDATE,
        IF(R.TRIP_TYPE = 'P',TIME(R.ESTIMATE_END_TIME),TIME(R.ESTIMATE_START_TIME)) as TRIPTIMES,
        VE.`NAME` AS VENDORNAME,R.START_LOCATION,R.END_LOCATION,R.CAB_ID,
        R.VENDOR_ID,R.PASSENGER_ALLOT_COUNT,R.PASSENGER_CLUBING_COUNT,R.CAB_CAPACITY_COUNT,R.TRIP_APPROVED_KM,RD.TOLL_ROUTE_STATUS,
		IF(RE.`STATUS` IS NULL,0,1) AS ESCORTSTATUS,IF(RE.ESCORT_ID IS NULL,0,1) AS ESCORTALLOTSTATUS,RE.ROUTE_ESCORT_ID,
        if(R.TRIP_TYPE = 'P','$ESTIMATE_INTERVAL30','Interval 180 minute') AS EXECUTETIME";

        $cabselect = "V.VEHICLE_REG_NO,VM.MODEL,RM.REASON,D.DRIVER_MOBILE";
        $vendor_join = "INNER JOIN vendors VE ON VE.VENDOR_ID=R.VENDOR_ID
		LEFT JOIN route_distance RD ON RD.ROSTER_ID = R.ROSTER_ID
      
	  LEFT JOIN route_escorts RE ON RE.ROSTER_ID = R.ROSTER_ID AND (DATE(R.ESTIMATE_END_TIME) = '$curdate' or date(R.ESTIMATE_START_TIME)='$curdate')
        AND RE.`STATUS` NOT IN ($ESCORT_INTER,$ESCORT_REMOVE)";
		

        $cabdetail_join = "INNER JOIN cab C ON C.CAB_ID=R.CAB_ID
        INNER JOIN vehicles V ON V.VEHICLE_ID=C.VEHICLE_ID
        INNER JOIN vehicle_models VM ON VM.VEHICLE_MODEL_ID=V.VEHICLE_MODEL_ID
        INNER JOIN drivers D ON D.DRIVERS_ID=C.DRIVER_ID
        INNER JOIN reason_master RM ON RM.REASON_ID=C.TARIFF_TYPE";
		$passenger_join="inner join roster_passengers RP on RP.ROSTER_ID=R.ROSTER_ID and R.ACTIVE=1";

        $whercond = "R.BRANCH_ID='$branchid' $vendorid and R.ACTIVE=".MyHelper::$RS_ACTIVE ." and R.TRIP_TYPE IN ('P','D')";
        $betweentime = "T.TRIPINTIME BETWEEN SUBTIME('$curdatetime',$ESTIMATE_SUBTIME) AND ADDTIME('$curdatetime',$ESTIMATE_ADDTIME)";
        $timeinterval = "AND (('$curdatetime'-$ESTIMATE_INTERVAL60)< T.TRIPINTIME)";
        
 */
			
			 switch ($tracking_type) 
			 {
				case $TD_ALLNORMAL:
					$data = Roster::query()
					->select(
                    'rosters.ROSTER_ID',
                    'rosters.BRANCH_ID',
                    'rosters.ROUTE_ID',
                    'rosters.TRIP_TYPE',
                    DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN rosters.ESTIMATE_END_TIME ELSE rosters.ESTIMATE_START_TIME END AS TRIPINTIME'),
                    DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN DATE(rosters.ESTIMATE_END_TIME) ELSE DATE(rosters.ESTIMATE_START_TIME) END AS TRIPDATE'),
                    DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN TIME(rosters.ESTIMATE_END_TIME) ELSE TIME(rosters.ESTIMATE_START_TIME) END AS TRIPTIME'),
                    'VE.NAME AS VENDORNAME',
                    'rosters.START_LOCATION',
                    'rosters.END_LOCATION',
                    'rosters.CAB_ID',
                    'rosters.VENDOR_ID',
                    'rosters.PASSENGER_ALLOT_COUNT',
                    'rosters.PASSENGER_CLUBING_COUNT',
                    'rosters.CAB_CAPACITY_COUNT',
                    'rosters.TRIP_APPROVED_KM',
                    'RD.TOLL_ROUTE_STATUS','rosters.ROSTER_STATUS',
                    DB::raw('IF(RE.STATUS IS NULL, 0, 1) AS ESCORTSTATUS'),
                    DB::raw('IF(RE.ESCORT_ID IS NULL, 0, 1) AS ESCORTALLOTSTATUS'),
                    'RE.ROUTE_ESCORT_ID',
                    DB::raw("IF(rosters.TRIP_TYPE = 'P', '$ESTIMATE_INTERVAL30', 'Interval 180 minute') AS EXECUTETIME"),
                    DB::raw('GROUP_CONCAT(RP.EMPLOYEE_ID) as empids0')
                )
                ->where('rosters.ACTIVE', MyHelper::$RS_ACTIVE)
                ->where('rosters.ROSTER_STATUS', MyHelper::$RS_ROSTER)
				->where('rosters.BRANCH_ID', $branch_id)
				->when($vendorid, function ($query, $vendorid) {
						return $query->where('rosters.VENDOR_ID', $vendorid);
					})
                ->whereIn('rosters.TRIP_TYPE', ['P', 'D'])
                ->leftjoin('roster_passengers as RP', 'RP.ROSTER_ID', '=', 'rosters.ROSTER_ID')
                ->leftJoin('vendors as VE', 'VE.VENDOR_ID', '=', 'rosters.VENDOR_ID')
                ->leftJoin('route_distance as RD', 'RD.ROSTER_ID', '=', 'rosters.ROSTER_ID')
                ->leftJoin('route_escorts as RE', 'RE.ROSTER_ID', '=', 'rosters.ROSTER_ID')
                ->groupBy('rosters.ROSTER_ID')
                ->havingRaw("TRIPINTIME BETWEEN SUBTIME('$curdatetime',$ESTIMATE_SUBTIME) AND ADDTIME('$curdatetime',$ESTIMATE_ADDTIME) and  '$curdatetime' < SUBTIME(TRIPINTIME,$ESTIMATE_CURTIME)  "); 
				
				break;
				
				case $TD_NOTALLOTED:
					$status= explode(",",$RS_NEWROSTER); 
					
					$data = Roster::query()
					->select(
                    'rosters.ROSTER_ID',
                    'rosters.BRANCH_ID',
                    'rosters.ROUTE_ID',
                    'rosters.TRIP_TYPE',
                    DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN rosters.ESTIMATE_END_TIME ELSE rosters.ESTIMATE_START_TIME END AS TRIPINTIME'),
                    DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN DATE(rosters.ESTIMATE_END_TIME) ELSE DATE(rosters.ESTIMATE_START_TIME) END AS TRIPDATE'),
                    DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN TIME(rosters.ESTIMATE_END_TIME) ELSE TIME(rosters.ESTIMATE_START_TIME) END AS TRIPTIME'),
                    'VE.NAME AS VENDORNAME',
                    'rosters.START_LOCATION',
                    'rosters.END_LOCATION',
                    'rosters.CAB_ID',
                    'rosters.VENDOR_ID',
                    'rosters.PASSENGER_ALLOT_COUNT',
                    'rosters.PASSENGER_CLUBING_COUNT',
                    'rosters.CAB_CAPACITY_COUNT',
                    'rosters.TRIP_APPROVED_KM',
                    'RD.TOLL_ROUTE_STATUS','rosters.ROSTER_STATUS',
                    DB::raw('IF(RE.STATUS IS NULL, 0, 1) AS ESCORTSTATUS'),
                    DB::raw('IF(RE.ESCORT_ID IS NULL, 0, 1) AS ESCORTALLOTSTATUS'),
                    'RE.ROUTE_ESCORT_ID',
                    DB::raw("IF(rosters.TRIP_TYPE = 'P', '$ESTIMATE_INTERVAL30', 'Interval 180 minute') AS EXECUTETIME"),
                    DB::raw('GROUP_CONCAT(RP.EMPLOYEE_ID) as empids0')
                )
                ->where('rosters.ACTIVE', MyHelper::$RS_ACTIVE)
                ->where('rosters.BRANCH_ID', $branch_id)
				->when($vendorid, function ($query, $vendorid) {
						return $query->where('rosters.VENDOR_ID', $vendorid);
					})
				 
				->whereIn('rosters.ROSTER_STATUS',$status)
							
                ->whereIn('rosters.TRIP_TYPE', ['P', 'D'])
                ->leftjoin('roster_passengers as RP', 'RP.ROSTER_ID', '=', 'rosters.ROSTER_ID')
                ->leftJoin('vendors as VE', 'VE.VENDOR_ID', '=', 'rosters.VENDOR_ID')
                ->leftJoin('route_distance as RD', 'RD.ROSTER_ID', '=', 'rosters.ROSTER_ID')
                ->leftJoin('route_escorts as RE', 'RE.ROSTER_ID', '=', 'rosters.ROSTER_ID')
                ->groupBy('rosters.ROSTER_ID')
                ->havingRaw("TRIPINTIME BETWEEN SUBTIME('$curdatetime',$ESTIMATE_SUBTIME) AND ADDTIME('$curdatetime',$ESTIMATE_ADDTIME) AND '$curdatetime'>=SUBTIME(TRIPINTIME,$ESTIMATE_CURTIME)");
				
				break;
				
				//Trip Alloted
				case $TD_ALLOTED:   
				
				$status= explode(",",$RS_TOTALALLOT); 
				
				$data=Roster::query()
				->leftJoin('cab as C', 'C.CAB_ID', '=', 'rosters.CAB_ID')
				->leftJoin('vehicles as VH', 'VH.VEHICLE_ID', '=', 'C.VEHICLE_ID')
				->leftJoin('vehicle_models as VM', 'VM.VEHICLE_MODEL_ID', '=', 'VH.VEHICLE_MODEL_ID')
				->leftJoin('drivers as D', 'D.DRIVERS_ID', '=', 'C.DRIVER_ID')
				->leftJoin('reason_master as RM', 'RM.REASON_ID', '=', 'C.TARIFF_TYPE')
				->leftJoin('roster_passengers as RP', 'RP.ROSTER_ID', '=', 'rosters.ROSTER_ID')
				->leftJoin('vendors as VE', 'VE.VENDOR_ID', '=', 'rosters.VENDOR_ID')
				->leftJoin('route_distance as RD', 'RD.ROSTER_ID', '=', 'rosters.ROSTER_ID')
				->leftJoin('route_escorts as RE', function ($join) use ($curdate,$ESCORT_INTER,$ESCORT_REMOVE) {
				$join->on('RE.ROSTER_ID', '=', 'rosters.ROSTER_ID')
             ->where(function ($query) use ($curdate) {
                 $query->whereDate('rosters.ESTIMATE_END_TIME', '=', $curdate)
                       ->orWhereDate('rosters.ESTIMATE_START_TIME', '=', $curdate);
             })
             ->whereNotIn('RE.STATUS',[$ESCORT_INTER,$ESCORT_REMOVE]);
           
    })
    ->select([
        'rosters.ROSTER_ID', 
        'rosters.BRANCH_ID', 
        'rosters.ROUTE_ID', 
        'rosters.TRIP_TYPE',
        DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN rosters.ESTIMATE_END_TIME ELSE rosters.ESTIMATE_START_TIME END AS TRIPINTIME'),
        DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN DATE(rosters.ESTIMATE_END_TIME) ELSE DATE(rosters.ESTIMATE_START_TIME) END AS TRIPDATE'),
        DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN TIME(rosters.ESTIMATE_END_TIME) ELSE TIME(rosters.ESTIMATE_START_TIME) END AS TRIPTIME'),
        'VE.NAME as VENDORNAME',
        'rosters.START_LOCATION', 
        'rosters.END_LOCATION', 
        'rosters.CAB_ID', 
        'rosters.VENDOR_ID', 
        'rosters.PASSENGER_ALLOT_COUNT',
        'rosters.PASSENGER_CLUBING_COUNT',
        'rosters.CAB_CAPACITY_COUNT',
        'rosters.CAB_ALLOT_TIME',
        'rosters.TRIP_APPROVED_KM',
        'VH.VEHICLE_REG_NO', 
        'VM.MODEL', 
        'RM.REASON', 
        'D.DRIVER_MOBILE', 
        'RD.TOLL_ROUTE_STATUS',
        'rosters.ROSTER_STATUS',
        DB::raw('IF(RE.STATUS IS NULL, 0, 1) AS ESCORTSTATUS'),
        DB::raw('IF(RE.ESCORT_ID IS NULL, 0, 1) AS ESCORTALLOTSTATUS'),
        'RE.ROUTE_ESCORT_ID',
        DB::raw('IF(rosters.TRIP_TYPE = "P", "Interval 30 minute", "Interval 180 minute") AS EXECUTETIME'),
        DB::raw('GROUP_CONCAT(RP.EMPLOYEE_ID) as empids0')
    ])
    ->where('rosters.ACTIVE', 1)
    ->where('rosters.BRANCH_ID', $branch_id)
    ->whereIn('rosters.ROSTER_STATUS', $status)
	->when($vendorid, function ($query, $vendorid) {
						return $query->where('rosters.VENDOR_ID', $vendorid);
					})
    ->whereIn('rosters.TRIP_TYPE', ['P', 'D'])
    ->groupBy('rosters.ROSTER_ID')
                ->havingRaw("TIMEDIFF('$curdatetime',`rosters`.`CAB_ALLOT_TIME`) < '$ESTIMATE_CABALLOT' and TRIPINTIME BETWEEN SUBTIME('$curdatetime',$ESTIMATE_SUBTIME) AND ADDTIME('$curdatetime',$ESTIMATE_ADDTIME) AND (('$curdatetime'-$ESTIMATE_INTERVAL60)< TRIPINTIME)");
				
				break;
				// Trip Accepted
				case $TD_TRIPACCEPTED:
				
				$status= explode(",",$RS_TOTALACCEPT); 
				
					$data = Roster::query()
					->select(
                    'rosters.ROSTER_ID',
                    'rosters.BRANCH_ID',
                    'rosters.ROUTE_ID',
                    'rosters.TRIP_TYPE',
                    DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN rosters.ESTIMATE_END_TIME ELSE rosters.ESTIMATE_START_TIME END AS TRIPINTIME'),
                    DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN DATE(rosters.ESTIMATE_END_TIME) ELSE DATE(rosters.ESTIMATE_START_TIME) END AS TRIPDATE'),
                    DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN TIME(rosters.ESTIMATE_END_TIME) ELSE TIME(rosters.ESTIMATE_START_TIME) END AS TRIPTIME'),
                    'VE.NAME AS VENDORNAME',
                    'rosters.START_LOCATION',
                    'rosters.END_LOCATION',
                    'rosters.CAB_ID',
                    'rosters.VENDOR_ID',
                    'rosters.PASSENGER_ALLOT_COUNT',
                    'rosters.PASSENGER_CLUBING_COUNT',
                    'rosters.CAB_CAPACITY_COUNT','rosters.CAB_ALLOT_TIME',
                    'rosters.TRIP_APPROVED_KM','VH.VEHICLE_REG_NO','VM.MODEL','RM.REASON','D.DRIVER_MOBILE',
                    'RD.TOLL_ROUTE_STATUS','rosters.ROSTER_STATUS',
                    DB::raw('IF(RE.STATUS IS NULL, 0, 1) AS ESCORTSTATUS'),
                    DB::raw('IF(RE.ESCORT_ID IS NULL, 0, 1) AS ESCORTALLOTSTATUS'),
                    'RE.ROUTE_ESCORT_ID',
                    DB::raw("IF(rosters.TRIP_TYPE = 'P', '$ESTIMATE_INTERVAL30', 'Interval 180 minute') AS EXECUTETIME"),
                    DB::raw('GROUP_CONCAT(RP.EMPLOYEE_ID) as empids0')
                )
                ->where('rosters.ACTIVE', MyHelper::$RS_ACTIVE)
                ->where('rosters.BRANCH_ID', $branch_id)
				->when($vendorid, function ($query, $vendorid) {
						return $query->where('rosters.VENDOR_ID', $vendorid);
					})
				 
				->whereIn('rosters.ROSTER_STATUS',$status)
							
                ->whereIn('rosters.TRIP_TYPE', ['P', 'D'])
				->leftjoin('cab as C','C.CAB_ID',"=","rosters.CAB_ID")
				->leftjoin('vehicles as VH','VH.VEHICLE_ID',"=","C.VEHICLE_ID")
				->leftjoin('vehicle_models as VM','VM.VEHICLE_MODEL_ID',"=","VH.VEHICLE_MODEL_ID")
				->leftjoin('drivers as D','D.DRIVERS_ID',"=","C.DRIVER_ID")
				->leftjoin('reason_master as RM','RM.REASON_ID',"=","C.TARIFF_TYPE")
                ->leftjoin('roster_passengers as RP', 'RP.ROSTER_ID', '=', 'rosters.ROSTER_ID')
                ->leftJoin('vendors as VE', 'VE.VENDOR_ID', '=', 'rosters.VENDOR_ID')
                ->leftJoin('route_distance as RD', 'RD.ROSTER_ID', '=', 'rosters.ROSTER_ID')
                //->leftJoin('route_escorts as RE', 'RE.ROSTER_ID', '=', 'rosters.ROSTER_ID',)
				/*  ->leftjoin('route_escorts as RE', function($join) use ($curdate, $ESCORT_INTER, $ESCORT_REMOVE) {
					$join->on('RE.ROSTER_ID', '=', 'rosters.ROSTER_ID')
						 ->whereDate('rosters.ESTIMATE_END_TIME', $curdate)
						 ->orWhereDate('rosters.ESTIMATE_START_TIME', $curdate)
						 ->whereNotIn('RE.STATUS', [$ESCORT_INTER, $ESCORT_REMOVE]); 
						})
				 */
				 ->leftJoin('route_escorts as RE', function ($join) use ($curdate,$ESCORT_INTER, $ESCORT_REMOVE) {
				$join->on('RE.ROSTER_ID', '=', 'rosters.ROSTER_ID')
				->where(function ($query) use ($curdate) {
                 $query->whereDate('rosters.ESTIMATE_END_TIME', '=', $curdate)
                       ->orWhereDate('rosters.ESTIMATE_START_TIME', '=', $curdate);
             })
             ->whereNotIn('RE.STATUS', [$ESCORT_INTER,$ESCORT_REMOVE]);
    })
				
                ->groupBy('rosters.ROSTER_ID')
                ->havingRaw("TRIPINTIME BETWEEN SUBTIME('$curdatetime',$ESTIMATE_SUBTIME) AND ADDTIME('$curdatetime',$ESTIMATE_ADDTIME) AND (('$curdatetime'-$ESTIMATE_INTERVAL60)< TRIPINTIME)");
				
				break;
				// Trip Rejected
				case $TD_TRIPREJECTED:
				
				$status= explode(",",$RS_TOTALREJECT); 
				
					$data = Roster::query()
					->select(
                    'rosters.ROSTER_ID',
                    'rosters.BRANCH_ID',
                    'rosters.ROUTE_ID',
                    'rosters.TRIP_TYPE',
                    DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN rosters.ESTIMATE_END_TIME ELSE rosters.ESTIMATE_START_TIME END AS TRIPINTIME'),
                    DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN DATE(rosters.ESTIMATE_END_TIME) ELSE DATE(rosters.ESTIMATE_START_TIME) END AS TRIPDATE'),
                    DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN TIME(rosters.ESTIMATE_END_TIME) ELSE TIME(rosters.ESTIMATE_START_TIME) END AS TRIPTIME'),
                    'VE.NAME AS VENDORNAME',
                    'rosters.START_LOCATION',
                    'rosters.END_LOCATION',
                    'rosters.CAB_ID',
                    'rosters.VENDOR_ID',
                    'rosters.PASSENGER_ALLOT_COUNT',
                    'rosters.PASSENGER_CLUBING_COUNT',
                    'rosters.CAB_CAPACITY_COUNT','rosters.CAB_ALLOT_TIME',
                    'rosters.TRIP_APPROVED_KM','VH.VEHICLE_REG_NO','VM.MODEL','RM.REASON','D.DRIVER_MOBILE',
                    'RD.TOLL_ROUTE_STATUS','rosters.ROSTER_STATUS',
                    DB::raw('IF(RE.STATUS IS NULL, 0, 1) AS ESCORTSTATUS'),
                    DB::raw('IF(RE.ESCORT_ID IS NULL, 0, 1) AS ESCORTALLOTSTATUS'),
                    'RE.ROUTE_ESCORT_ID',
                    DB::raw("IF(rosters.TRIP_TYPE = 'P', '$ESTIMATE_INTERVAL30', 'Interval 180 minute') AS EXECUTETIME"),
                    DB::raw('GROUP_CONCAT(RP.EMPLOYEE_ID) as empids0')
                )
                ->where('rosters.ACTIVE', MyHelper::$RS_ACTIVE)
                ->where('rosters.BRANCH_ID', $branch_id)
				->when($vendorid, function ($query, $vendorid) {
						return $query->where('rosters.VENDOR_ID', $vendorid);
					})
				 
				->whereIn('rosters.ROSTER_STATUS',$status)
							
                ->whereIn('rosters.TRIP_TYPE', ['P', 'D'])
				->leftjoin('cab as C','C.CAB_ID',"=","rosters.CAB_ID")
				->leftjoin('vehicles as VH','VH.VEHICLE_ID',"=","C.VEHICLE_ID")
				->leftjoin('vehicle_models as VM','VM.VEHICLE_MODEL_ID',"=","VH.VEHICLE_MODEL_ID")
				->leftjoin('drivers as D','D.DRIVERS_ID',"=","C.DRIVER_ID")
				->leftjoin('reason_master as RM','RM.REASON_ID',"=","C.TARIFF_TYPE")
                ->leftjoin('roster_passengers as RP', 'RP.ROSTER_ID', '=', 'rosters.ROSTER_ID')
                ->leftJoin('vendors as VE', 'VE.VENDOR_ID', '=', 'rosters.VENDOR_ID')
                ->leftJoin('route_distance as RD', 'RD.ROSTER_ID', '=', 'rosters.ROSTER_ID')
                ->leftJoin('route_escorts as RE', function ($join) use ($curdate,$ESCORT_INTER, $ESCORT_REMOVE) 
				{
					$join->on('RE.ROSTER_ID', '=', 'rosters.ROSTER_ID')
					->where(function ($query) use ($curdate) {
					 $query->whereDate('rosters.ESTIMATE_END_TIME', '=', $curdate)
						   ->orWhereDate('rosters.ESTIMATE_START_TIME', '=', $curdate);
					})
					->whereNotIn('RE.STATUS', [$ESCORT_INTER, $ESCORT_REMOVE]);
				})
				
                ->groupBy('rosters.ROSTER_ID')
                // ->havingRaw("TIMEDIFF('$curdatetime',`rosters`.`CAB_ALLOT_TIME`) < '$ESTIMATE_CABALLOT' and TRIPINTIME BETWEEN SUBTIME('$curdatetime',$ESTIMATE_SUBTIME) AND ADDTIME('$curdatetime',$ESTIMATE_ADDTIME) AND (('$curdatetime'-$ESTIMATE_INTERVAL60)< TRIPINTIME)");
                ->havingRaw("TRIPINTIME BETWEEN SUBTIME('$curdatetime',$ESTIMATE_SUBTIME) AND ADDTIME('$curdatetime',$ESTIMATE_ADDTIME) AND (('$curdatetime'-$ESTIMATE_INTERVAL60)< TRIPINTIME)");
				
				break;
				
				//Trip Executed
				
				case $TD_TRIPEXECUTED: 
				
				$status= explode(",",$RS_TOTALEXECUTE); 
				
					$data = Roster::query()
					->select(
                    'rosters.ROSTER_ID',
                    'rosters.BRANCH_ID',
                    'rosters.ROUTE_ID',
                    'rosters.TRIP_TYPE',
                    DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN rosters.ESTIMATE_END_TIME ELSE rosters.ESTIMATE_START_TIME END AS TRIPINTIME'),
                    DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN DATE(rosters.ESTIMATE_END_TIME) ELSE DATE(rosters.ESTIMATE_START_TIME) END AS TRIPDATE'),
                    DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN TIME(rosters.ESTIMATE_END_TIME) ELSE TIME(rosters.ESTIMATE_START_TIME) END AS TRIPTIME'),
                    'VE.NAME AS VENDORNAME',
                    'rosters.START_LOCATION',
                    'rosters.END_LOCATION',
                    'rosters.CAB_ID',
                    'rosters.VENDOR_ID',
                    'rosters.PASSENGER_ALLOT_COUNT',
                    'rosters.PASSENGER_CLUBING_COUNT',
                    'rosters.CAB_CAPACITY_COUNT','rosters.CAB_ALLOT_TIME',
                    'rosters.TRIP_APPROVED_KM','VH.VEHICLE_REG_NO','VM.MODEL','RM.REASON','D.DRIVER_MOBILE',
                    'RD.TOLL_ROUTE_STATUS','rosters.ROSTER_STATUS',
                    DB::raw('IF(RE.STATUS IS NULL, 0, 1) AS ESCORTSTATUS'),
                    DB::raw('IF(RE.ESCORT_ID IS NULL, 0, 1) AS ESCORTALLOTSTATUS'),
                    'RE.ROUTE_ESCORT_ID',
                    //DB::raw("IF(rosters.TRIP_TYPE = 'P', '$ESTIMATE_INTERVAL30', 'Interval 180 minute') AS EXECUTETIME"),
					DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN "Interval 30 minute" ELSE "Interval 180 minute" END AS EXECUTETIME'),
                    DB::raw('GROUP_CONCAT(RP.EMPLOYEE_ID) as empids0')
                )
                ->where('rosters.ACTIVE', MyHelper::$RS_ACTIVE)
                ->where('rosters.BRANCH_ID', $branch_id)
				->when($vendorid, function ($query, $vendorid) {
						return $query->where('rosters.VENDOR_ID', $vendorid);
					})
				 
				->whereIn('rosters.ROSTER_STATUS',$status)
							
                ->whereIn('rosters.TRIP_TYPE', ['P', 'D'])
				->leftjoin('cab as C','C.CAB_ID',"=","rosters.CAB_ID")
				->leftjoin('vehicles as VH','VH.VEHICLE_ID',"=","C.VEHICLE_ID")
				->leftjoin('vehicle_models as VM','VM.VEHICLE_MODEL_ID',"=","VH.VEHICLE_MODEL_ID")
				->leftjoin('drivers as D','D.DRIVERS_ID',"=","C.DRIVER_ID")
				->leftjoin('reason_master as RM','RM.REASON_ID',"=","C.TARIFF_TYPE")
                ->leftjoin('roster_passengers as RP', 'RP.ROSTER_ID', '=', 'rosters.ROSTER_ID')
                ->leftJoin('vendors as VE', 'VE.VENDOR_ID', '=', 'rosters.VENDOR_ID')
                ->leftJoin('route_distance as RD', 'RD.ROSTER_ID', '=', 'rosters.ROSTER_ID')
                ->leftJoin('route_escorts as RE', function ($join) use ($curdate,$ESCORT_INTER, $ESCORT_REMOVE) 
				{
					$join->on('RE.ROSTER_ID', '=', 'rosters.ROSTER_ID')
					->where(function ($query) use ($curdate) {
					 $query->whereDate('rosters.ESTIMATE_END_TIME', '=', $curdate)
						   ->orWhereDate('rosters.ESTIMATE_START_TIME', '=', $curdate);
					})
					->whereNotIn('RE.STATUS', [$ESCORT_INTER, $ESCORT_REMOVE]);
				})
				
                ->groupBy('rosters.ROSTER_ID')
               // ->havingRaw("TRIPINTIME BETWEEN SUBTIME('$curdatetime',$ESTIMATE_SUBTIME) AND ADDTIME('$curdatetime',$ESTIMATE_ADDTIME) AND (('$curdatetime'-$ESTIMATE_INTERVAL60)< TRIPINTIME) and  (('$curdatetime'- IF(TRIP_TYPE = 'P', '$ESTIMATE_INTERVAL30', 'Interval 180 minute') < TRIPINTIME)");
                ->havingRaw("TRIPINTIME BETWEEN SUBTIME('$curdatetime',$ESTIMATE_SUBTIME) AND ADDTIME('$curdatetime',$ESTIMATE_ADDTIME) AND (('$curdatetime'-$ESTIMATE_INTERVAL60)< TRIPINTIME) ");
				//and  (DATE_SUB('$curdatetime',INTERVAL IF(TRIP_TYPE = 'P',  '$ESTIMATE_INTERVAL30', 180) MINUTE) < TRIPINTIME)
				break;
				
				case $TD_NORESPONSE:   
				
				$status= explode(",",$RS_TOTALALLOT); 
				
				$data= Roster::query()
				->leftJoin('cab as C', 'C.CAB_ID', '=', 'rosters.CAB_ID')
				->leftJoin('vehicles as VH', 'VH.VEHICLE_ID', '=', 'C.VEHICLE_ID')
				->leftJoin('vehicle_models as VM', 'VM.VEHICLE_MODEL_ID', '=', 'VH.VEHICLE_MODEL_ID')
				->leftJoin('drivers as D', 'D.DRIVERS_ID', '=', 'C.DRIVER_ID')
				->leftJoin('reason_master as RM', 'RM.REASON_ID', '=', 'C.TARIFF_TYPE')
				->leftJoin('roster_passengers as RP', 'RP.ROSTER_ID', '=', 'rosters.ROSTER_ID')
				
				//->leftJoin('roster_passengers AS RP', 'RP.ROSTER_ID', '=', 'rosters.ROSTER_ID')
				->leftJoin('employees AS E', function ($join) use ($branch_id) {
				 $join->on('E.EMPLOYEES_ID', '=', 'RP.EMPLOYEE_ID')
				->where('E.BRANCH_ID',$branch_id);
					})
				
				->leftJoin('vendors as VE', 'VE.VENDOR_ID', '=', 'rosters.VENDOR_ID')
				->leftJoin('route_distance as RD', 'RD.ROSTER_ID', '=', 'rosters.ROSTER_ID')
				->leftJoin('route_escorts as RE', function ($join) use ($curdate,$ESCORT_INTER,$ESCORT_REMOVE) {
				$join->on('RE.ROSTER_ID', '=', 'rosters.ROSTER_ID')
             ->where(function ($query) use ($curdate) {
                 $query->whereDate('rosters.ESTIMATE_END_TIME', '=', $curdate)
                       ->orWhereDate('rosters.ESTIMATE_START_TIME', '=', $curdate);
             })
             ->whereNotIn('RE.STATUS',[$ESCORT_INTER,$ESCORT_REMOVE]);
           
    })
    ->select([
        'rosters.ROSTER_ID', 
        'rosters.BRANCH_ID', 
        'rosters.ROUTE_ID', 
        'rosters.TRIP_TYPE',
        DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN rosters.ESTIMATE_END_TIME ELSE rosters.ESTIMATE_START_TIME END AS TRIPINTIME'),
        DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN DATE(rosters.ESTIMATE_END_TIME) ELSE DATE(rosters.ESTIMATE_START_TIME) END AS TRIPDATE'),
        DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN TIME(rosters.ESTIMATE_END_TIME) ELSE TIME(rosters.ESTIMATE_START_TIME) END AS TRIPTIME'),
        'VE.NAME as VENDORNAME',
        'rosters.START_LOCATION', 
        'rosters.END_LOCATION', 
        'rosters.CAB_ID', 
        'rosters.VENDOR_ID', 
        'rosters.PASSENGER_ALLOT_COUNT',
        'rosters.PASSENGER_CLUBING_COUNT',
        'rosters.CAB_CAPACITY_COUNT',
        'rosters.CAB_ALLOT_TIME',
        'rosters.TRIP_APPROVED_KM',
        'VH.VEHICLE_REG_NO', 
        'VM.MODEL', 
        'RM.REASON', 
        'D.DRIVER_MOBILE', 
        'RD.TOLL_ROUTE_STATUS',
        'rosters.ROSTER_STATUS','E.NAME as empname','E.GENDER',
        DB::raw('IF(RE.STATUS IS NULL, 0, 1) AS ESCORTSTATUS'),
        DB::raw('IF(RE.ESCORT_ID IS NULL, 0, 1) AS ESCORTALLOTSTATUS'),
        'RE.ROUTE_ESCORT_ID',
        DB::raw('IF(rosters.TRIP_TYPE = "P", "Interval 30 minute", "Interval 180 minute") AS EXECUTETIME'),
        DB::raw('GROUP_CONCAT(RP.EMPLOYEE_ID) as empids0')
    ])
    ->where('rosters.ACTIVE', 1)
    ->where('rosters.BRANCH_ID', $branch_id)
    ->whereIn('rosters.ROSTER_STATUS', $status)
	->when($vendorid, function ($query, $vendorid) {
						return $query->where('rosters.VENDOR_ID', $vendorid);
					})
    ->whereIn('rosters.TRIP_TYPE', ['P', 'D'])
    ->groupBy('rosters.ROSTER_ID')
                ->havingRaw("TIMEDIFF('$curdatetime',`rosters`.`CAB_ALLOT_TIME`) > '$ESTIMATE_CABALLOT' and TRIPINTIME BETWEEN SUBTIME('$curdatetime',$ESTIMATE_SUBTIME) AND ADDTIME('$curdatetime',$ESTIMATE_ADDTIME) AND (('$curdatetime'-$ESTIMATE_INTERVAL60)< TRIPINTIME)");
				
				break;
				
				//Trip Not Executed
				case $TD_TRIPNOTEXECUTED:
				
				$status= explode(",",$RS_TOTALACCEPT); 
				
					$data = Roster::query()
					->select(
                    'rosters.ROSTER_ID',
                    'rosters.BRANCH_ID',
                    'rosters.ROUTE_ID',
                    'rosters.TRIP_TYPE','rosters.ESTIMATE_START_TIME',
                    DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN rosters.ESTIMATE_END_TIME ELSE rosters.ESTIMATE_START_TIME END AS TRIPINTIME'),
                    DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN DATE(rosters.ESTIMATE_END_TIME) ELSE DATE(rosters.ESTIMATE_START_TIME) END AS TRIPDATE'),
                    DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN TIME(rosters.ESTIMATE_END_TIME) ELSE TIME(rosters.ESTIMATE_START_TIME) END AS TRIPTIME'),
                    'VE.NAME AS VENDORNAME',
                    'rosters.START_LOCATION',
                    'rosters.END_LOCATION',
                    'rosters.CAB_ID',
                    'rosters.VENDOR_ID',
                    'rosters.PASSENGER_ALLOT_COUNT',
                    'rosters.PASSENGER_CLUBING_COUNT',
                    'rosters.CAB_CAPACITY_COUNT','rosters.CAB_ALLOT_TIME',
                    'rosters.TRIP_APPROVED_KM','VH.VEHICLE_REG_NO','VM.MODEL','RM.REASON','D.DRIVER_MOBILE',
                    'RD.TOLL_ROUTE_STATUS','rosters.ROSTER_STATUS',
                    DB::raw('IF(RE.STATUS IS NULL, 0, 1) AS ESCORTSTATUS'),
                    DB::raw('IF(RE.ESCORT_ID IS NULL, 0, 1) AS ESCORTALLOTSTATUS'),
                    'RE.ROUTE_ESCORT_ID',
                    DB::raw("IF(rosters.TRIP_TYPE = 'P', '$ESTIMATE_INTERVAL30', 'Interval 180 minute') AS EXECUTETIME"),
                    DB::raw('GROUP_CONCAT(RP.EMPLOYEE_ID) as empids0')
                )
                ->where('rosters.ACTIVE', MyHelper::$RS_ACTIVE)
                ->where('rosters.BRANCH_ID', $branch_id)
				->when($vendorid, function ($query, $vendorid) {
						return $query->where('rosters.VENDOR_ID', $vendorid);
					})
				 
				->whereIn('rosters.ROSTER_STATUS',$status)
							
                ->whereIn('rosters.TRIP_TYPE', ['P', 'D'])
				->leftjoin('cab as C','C.CAB_ID',"=","rosters.CAB_ID")
				->leftjoin('vehicles as VH','VH.VEHICLE_ID',"=","C.VEHICLE_ID")
				->leftjoin('vehicle_models as VM','VM.VEHICLE_MODEL_ID',"=","VH.VEHICLE_MODEL_ID")
				->leftjoin('drivers as D','D.DRIVERS_ID',"=","C.DRIVER_ID")
				->leftjoin('reason_master as RM','RM.REASON_ID',"=","C.TARIFF_TYPE")
                ->leftjoin('roster_passengers as RP', 'RP.ROSTER_ID', '=', 'rosters.ROSTER_ID')
                ->leftJoin('vendors as VE', 'VE.VENDOR_ID', '=', 'rosters.VENDOR_ID')
                ->leftJoin('route_distance as RD', 'RD.ROSTER_ID', '=', 'rosters.ROSTER_ID')
                //->leftJoin('route_escorts as RE', 'RE.ROSTER_ID', '=', 'rosters.ROSTER_ID',)
				/*  ->leftjoin('route_escorts as RE', function($join) use ($curdate, $ESCORT_INTER, $ESCORT_REMOVE) {
					$join->on('RE.ROSTER_ID', '=', 'rosters.ROSTER_ID')
						 ->whereDate('rosters.ESTIMATE_END_TIME', $curdate)
						 ->orWhereDate('rosters.ESTIMATE_START_TIME', $curdate)
						 ->whereNotIn('RE.STATUS', [$ESCORT_INTER, $ESCORT_REMOVE]); 
						})
				 */
				 ->leftJoin('route_escorts as RE', function ($join) use ($curdate,$ESCORT_INTER, $ESCORT_REMOVE) {
				$join->on('RE.ROSTER_ID', '=', 'rosters.ROSTER_ID')
				->where(function ($query) use ($curdate) {
                 $query->whereDate('rosters.ESTIMATE_END_TIME', '=', $curdate)
                       ->orWhereDate('rosters.ESTIMATE_START_TIME', '=', $curdate);
             })
             ->whereNotIn('RE.STATUS', [$ESCORT_INTER,$ESCORT_REMOVE]);
    })
				
                ->groupBy('rosters.ROSTER_ID')
                ->havingRaw("TIMEDIFF('$curdatetime',ESTIMATE_START_TIME) < '$TRIP_NOT_EXECUTE' and TRIPINTIME BETWEEN SUBTIME('$curdatetime',$ESTIMATE_SUBTIME) AND ADDTIME('$curdatetime',$ESTIMATE_ADDTIME) AND (('$curdatetime'-$ESTIMATE_INTERVAL60)< TRIPINTIME)");
				
				break;
				
				// Break Down
			case $TD_BREAKDOWN:
				
					$status= explode(",",$RS_TOTALBREAKDOWN); 
					$data = Roster::query()
							->select(
								'rosters.ROSTER_ID',
								'rosters.BRANCH_ID',
								'rosters.ROUTE_ID',
								'rosters.TRIP_TYPE',
								'rosters.ESTIMATE_START_TIME',
								DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN rosters.ESTIMATE_END_TIME ELSE rosters.ESTIMATE_START_TIME END AS TRIPINTIME'),
								DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN DATE(rosters.ESTIMATE_END_TIME) ELSE DATE(rosters.ESTIMATE_START_TIME) END AS TRIPDATE'),
								DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN TIME(rosters.ESTIMATE_END_TIME) ELSE TIME(rosters.ESTIMATE_START_TIME) END AS TRIPTIME'),
								'VE.NAME AS VENDORNAME',
								'rosters.START_LOCATION',
								'rosters.END_LOCATION',
								'rosters.CAB_ID',
								'rosters.VENDOR_ID',
								'rosters.PASSENGER_ALLOT_IN_ROUT_COUNT',
								'rosters.TRIP_APPROVED_KM',
								'rosters.UPDATED_BY',
								'rosters.updated_at',
								'VH.VEHICLE_REG_NO',
								'D.DRIVERS_NAME',
								'D.DRIVER_MOBILE',
								'CA.ACTION',
								'RM.REASON',
								'U.name AS updateby'
							)
							->join('cab AS C', 'C.CAB_ID', '=', 'rosters.CAB_ID')
							->join('vendors AS VE', 'VE.VENDOR_ID', '=', 'rosters.VENDOR_ID')
							->join('vehicles AS VH', 'VH.VEHICLE_ID', '=', 'C.VEHICLE_ID')
							->join('vehicle_models AS VM', 'VM.VEHICLE_MODEL_ID', '=', 'VH.VEHICLE_MODEL_ID')
							->join('drivers AS D', 'D.DRIVERS_ID', '=', 'C.DRIVER_ID')
							
							
							 ->Join('cab_allocation AS CA', function ($join) use ($status) {
								 $join->on('CA.ROSTER_ID', '=', 'rosters.ROSTER_ID')
								->whereIn('CA.ACCEPTANCE_REJECT_STATE', $status);
				
								})
							
							->leftJoin('reason_master AS RM', 'RM.REASON_ID', '=', 'CA.REJECT_REASON_ID')
							->leftJoin('users AS U', 'U.id', '=', 'rosters.UPDATED_BY')
							->where('rosters.ACTIVE', MyHelper::$RS_INACTIVE)
							->where('rosters.BRANCH_ID', $branch_id)
							->whereIn('rosters.ROSTER_STATUS', $status)
							
							->whereDate('rosters.ESTIMATE_END_TIME', $curdate)
							//->where('CA.ACCEPTANCE_REJECT_STATE', $status)
							->when($vendorid, function ($query) use ($vendorid) {
								return $query->where('rosters.VENDOR_ID', $vendorid);
							})
							->groupBy('rosters.ROSTER_ID');   


				
				break;
				
				//Waitting At Pickup Point  Waiting At PickUp Point
					case $TD_WAITINGATPICKUPPOINT:
				
					$rps_status= explode(",",$RPS_TTLARRIVAL);
					$rps_delay_status= explode(",",$RPS_TTLCABDELAY);
					$status=array_merge($rps_status,$rps_delay_status);
					$data = Roster::query()
							->select(
								'rosters.ROSTER_ID',
								'rosters.BRANCH_ID',
								'rosters.TRIP_TYPE','rosters.ROUTE_ID',
								'RP.EMPLOYEE_ID','RP.DRIVER_ARRIVAL_TIME','RP.ESTIMATE_END_TIME','E.NAME as empname','E.GENDER',
								DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN rosters.ESTIMATE_END_TIME ELSE rosters.ESTIMATE_START_TIME END AS TRIPINTIME'),
								DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN DATE(rosters.ESTIMATE_END_TIME) ELSE DATE(rosters.ESTIMATE_START_TIME) END AS TRIPDATE'),
								DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN TIME(rosters.ESTIMATE_END_TIME) ELSE TIME(rosters.ESTIMATE_START_TIME) END AS TRIPTIME'),
								'VE.NAME AS VENDORNAME',
								'rosters.START_LOCATION',
								'rosters.END_LOCATION',
								'rosters.CAB_ID',
								'rosters.VENDOR_ID',
								'rosters.PASSENGER_ALLOT_IN_ROUT_COUNT',
								'rosters.TRIP_APPROVED_KM',
								'rosters.UPDATED_BY',
								'rosters.updated_at',
								'VH.VEHICLE_REG_NO',
								'D.DRIVERS_NAME',
								'D.DRIVER_MOBILE','B.BRANCH_NAME','VM.MODEL','L.LOCATION_NAME'
							)
							->join('cab AS C', 'C.CAB_ID', '=', 'rosters.CAB_ID')
							->join('vendors AS VE', 'VE.VENDOR_ID', '=', 'rosters.VENDOR_ID')
							->join('vehicles AS VH', 'VH.VEHICLE_ID', '=', 'C.VEHICLE_ID')
							->join('vehicle_models AS VM', 'VM.VEHICLE_MODEL_ID', '=', 'VH.VEHICLE_MODEL_ID')
							->join('drivers AS D', 'D.DRIVERS_ID', '=', 'C.DRIVER_ID')
							->Join('branch AS B', 'B.BRANCH_ID', '=', 'rosters.BRANCH_ID')
							->Join('reason_master AS RM', 'RM.REASON_ID', '=', 'C.TARIFF_TYPE')
							
							->Join('roster_passengers AS RP', 'RP.ROSTER_ID', '=', 'rosters.ROSTER_ID')
							->Join('locations AS L', 'L.LOCATION_ID', '=', 'RP.LOCATION_ID')
							
							
							->Join('employees AS E', function ($join) use ($branch_id) {
								 $join->on('E.EMPLOYEES_ID', '=', 'RP.EMPLOYEE_ID')
								->where('E.BRANCH_ID',$branch_id);
				
								})
							
							->where('rosters.TRIP_TYPE', 'P')
							
							->where('rosters.BRANCH_ID', $branch_id)
							
							->whereIn('RP.ROSTER_PASSENGER_STATUS', $status)
							
							->when($vendorid, function ($query) use ($vendorid) {
								return $query->where('rosters.VENDOR_ID', $vendorid);
							})
							//->groupBy('rosters.ROSTER_ID')
							->havingRaw("TIMEDIFF('$curdatetime',RP.DRIVER_ARRIVAL_TIME) > '$wapptime' AND RP.ESTIMATE_END_TIME BETWEEN SUBTIME('$curdatetime',10000) AND ADDTIME('$curdatetime',480000) AND '$curdatetime'>= SUBTIME(RP.ESTIMATE_END_TIME,040000)");

							
				break;
				
			//Safe Drop
					case $TD_SAFEDROP:
					
					//$curdatetime='2024-09-20 16:29:32';
				
					$data = Roster::query()
							->select(
								'rosters.ROSTER_ID',
								'rosters.BRANCH_ID',
								'rosters.ROUTE_ID',
								'rosters.TRIP_TYPE','RP.ROSTER_PASSENGER_ID','RP.ROUTE_ORDER','E.ADDRESS','RP.ACTUAL_END_TIME',
								'RP.EMPLOYEE_ID','RP.DRIVER_ARRIVAL_TIME','RP.ESTIMATE_END_TIME','E.NAME as empname','E.GENDER',
							DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN rosters.ESTIMATE_END_TIME ELSE rosters.ESTIMATE_START_TIME END AS TRIPINTIME'),
							DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN DATE(rosters.ESTIMATE_END_TIME) ELSE DATE(rosters.ESTIMATE_START_TIME) END AS TRIPDATE'),
							DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN TIME(rosters.ESTIMATE_END_TIME) ELSE TIME(rosters.ESTIMATE_START_TIME) END AS TRIPTIME'),
								DB::raw('CASE WHEN RP.ROSTER_PASSENGER_STATUS & 256 THEN 1 ELSE 0 END AS SAFEDROP'),
								'rosters.ESTIMATE_START_TIME','RP.ROSTER_PASSENGER_STATUS',
								'VE.NAME AS VENDORNAME',
								'rosters.START_LOCATION',
								'rosters.END_LOCATION',
								'rosters.CAB_ID',
								'rosters.VENDOR_ID',
								'rosters.PASSENGER_ALLOT_IN_ROUT_COUNT',
								'rosters.TRIP_APPROVED_KM',
								'rosters.UPDATED_BY',
								'rosters.updated_at',
								'VH.VEHICLE_REG_NO',
								'D.DRIVERS_NAME',
								'D.DRIVER_MOBILE','B.BRANCH_NAME',DB::raw('if(E.GENDER = "F",1,0) AS FEMALE')
							)
							->join('cab AS C', 'C.CAB_ID', '=', 'rosters.CAB_ID')
							->join('vendors AS VE', 'VE.VENDOR_ID', '=', 'rosters.VENDOR_ID')
							->join('vehicles AS VH', 'VH.VEHICLE_ID', '=', 'C.VEHICLE_ID')
							->join('vehicle_models AS VM', 'VM.VEHICLE_MODEL_ID', '=', 'VH.VEHICLE_MODEL_ID')
							->join('drivers AS D', 'D.DRIVERS_ID', '=', 'C.DRIVER_ID')
							->Join('branch AS B', 'B.BRANCH_ID', '=', 'rosters.BRANCH_ID')
							->Join('reason_master AS RM', 'RM.BRANCH_ID', '=', DB::raw($branch_id))
							
							//->Join('reason_log AS RL', 'RL.ROSTER_PASSENGER_ID', '=', 'RP.ROSTER_PASSENGER_ID')
							->Join('roster_passengers AS RP', 'RP.ROSTER_ID', '=', 'rosters.ROSTER_ID')
							->leftJoin('reason_log AS RL', function ($join) {
								 $join->on('RL.ROSTER_PASSENGER_ID', '=', 'RP.ROSTER_PASSENGER_ID')
								->where('RL.REASON_ID','RM.REASON_ID');
                            })
							
							->Join('locations AS L', 'L.LOCATION_ID', '=', 'RP.LOCATION_ID')
							->Join('employees AS E', function ($join) use ($branch_id) {
								$join->on('E.EMPLOYEES_ID', '=', 'RP.EMPLOYEE_ID')
								->where('E.BRANCH_ID',$branch_id);
								})
                                ->where("RM.REASON", "=", 'Safe Drop' )
                                ->where("RM.BRANCH_ID", "=", $branch_id)
								
							->havingRaw("rosters.BRANCH_ID = $branch_id AND DATE(rosters.ESTIMATE_START_TIME) = '$curdate' AND rosters.ESTIMATE_START_TIME BETWEEN SUBTIME('$curdatetime',10000) AND ADDTIME('$curdatetime',480000) AND '$curdatetime'>=SUBTIME(rosters.ESTIMATE_START_TIME,040000) AND rosters.TRIP_TYPE = 'D' AND (RP.ROSTER_PASSENGER_STATUS & 32 OR RP.ROSTER_PASSENGER_STATUS & 128) ");  
							
				break;
				
					case $TD_ESCORT:
					
					$data = Roster::query()
                    ->select(
                        'RE.ROSTER_ID',
                        'rosters.TRIP_TYPE',
                        'RE.EMPLOYEE_ID',
                        'RE.REASON',
                        'RE.REMARKS',
                        'B.BRANCH_NAME',
                        'E.NAME as empname',
                        'E.MOBILE',
                        'L.LOCATION_NAME',
                        'V.NAME as vendor_name',
                        'ES.ESCORT_ID',
                        'ES.ESCORT_NAME',
                        'ES.ESCORT_MOBILE',
                        'D.DRIVERS_NAME',
                        'VE.VEHICLE_REG_NO',
                        'VM.MODEL',
                        'rosters.ESTIMATE_END_TIME',
                        'D.DRIVER_MOBILE',
                        'rosters.ROUTE_ID',
                        'RE.ROUTE_ESCORT_ID',
                        'RE.STATUS',
                        DB::raw("IF(rosters.ROSTER_STATUS & {$RS_CABALLOT}, 1, 0) AS ROSTERSTATUS"),
                        DB::raw("IF(rosters.TRIP_TYPE = 'P', rosters.ESTIMATE_END_TIME, rosters.ESTIMATE_START_TIME) as TRIPINTIME"),
                        DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN TIME(rosters.ESTIMATE_END_TIME) ELSE TIME(rosters.ESTIMATE_START_TIME) END AS TRIPTIME'),
                    )
                    ->join('route_escorts as RE', function($join) use ($ES_REMOVEINTER) {
                        $join->on('RE.ROSTER_ID', '=', 'rosters.ROSTER_ID')
                             ->whereNotIn('RE.STATUS', [$ES_REMOVEINTER]);
                    })
                    ->join('branch as B', 'B.BRANCH_ID', '=', 'rosters.BRANCH_ID')
                    ->join('employees as E', function($join) use ($branch_id) {
                        $join->on('E.EMPLOYEES_ID', '=', 'RE.EMPLOYEE_ID')
                             ->where('E.BRANCH_ID', '=', $branch_id);
                    })
                    ->join('roster_passengers as RS', function($join) {
                        $join->on('RS.EMPLOYEE_ID', '=', 'RE.EMPLOYEE_ID')
                             ->on('RS.ROSTER_ID', '=', 'rosters.ROSTER_ID');
                    })
                    ->join('locations as L', 'L.LOCATION_ID', '=', 'RS.LOCATION_ID')
                    ->join('vendors as V', 'V.VENDOR_ID', '=', 'rosters.VENDOR_ID')
                    ->leftJoin('escorts as ES', 'ES.ESCORT_ID', '=', 'RE.ESCORT_ID')
                    ->leftJoin('cab as C', 'C.CAB_ID', '=', 'rosters.CAB_ID')
                    ->leftJoin('drivers as D', 'D.DRIVERS_ID', '=', 'C.DRIVER_ID')
                    ->leftJoin('vehicles as VE', 'VE.VEHICLE_ID', '=', 'C.VEHICLE_ID')
                    ->leftJoin('vehicle_models as VM', 'VM.VEHICLE_MODEL_ID', '=', 'VE.VEHICLE_MODEL_ID')
                    ->where('rosters.BRANCH_ID', '=', $branch_id)
                    ->where('rosters.ACTIVE', '=', '1')
                    ->when($vendorid, function ($query) use ($vendorid) {
                        return $query->where('rosters.VENDOR_ID', $vendorid);
                    })
                    ->havingRaw("TRIPINTIME BETWEEN SUBTIME(?, 10000) AND ADDTIME(?, 480000)", [$curdatetime, $curdatetime])
                    ->havingRaw("? >= SUBTIME(TRIPINTIME, 40000)", [$curdatetime]);
                  //  ->get(); 
							
				break;
				
					case $TD_OVERTIMEALERT:

                    $overtime=$this->commonFunction->GetPropertyValue("OVER TIME");
					
					$data =  DB::table('cab_attandance as CA')
                    ->select(
                        'V.VEHICLE_REG_NO',
                        'VM.MODEL',
                        'B.BRANCH_NAME',
                        'D.DRIVERS_NAME',
                        'D.DRIVER_MOBILE',
                        'D.DRIVERS_ADRESS',
                        DB::raw('MIN(CA.LOGIN_TIME) as intime'),
                        DB::raw('MIN(CA.LOGIN_TIME) as TRIPTIME'),
                        DB::raw('MAX(CA.LOGOUT_TIME) as outtime'),
                        DB::raw("IF(TIMEDIFF(MAX(CA.LOGOUT_TIME), MIN(CA.LOGIN_TIME)) > '$overtime', 
                            TIMEDIFF(MAX(CA.LOGOUT_TIME), MIN(CA.LOGIN_TIME)), 0) as total_time"),
                        'VE.NAME as vendorname'
                    )
                    ->join('cab as C', 'C.CAB_ID', '=', 'CA.CAB_ID')
                    ->join('vehicles as V', 'V.VEHICLE_ID', '=', 'C.VEHICLE_ID')
                    ->join('vehicle_models as VM', 'VM.VEHICLE_MODEL_ID', '=', 'V.VEHICLE_MODEL_ID')
                    ->join('branch as B', 'B.BRANCH_ID', '=', 'CA.BRANCH_ID')
                    ->join('drivers as D', 'D.DRIVERS_ID', '=', 'C.DRIVER_ID')
                    ->leftJoin('vendors as VE', 'VE.VENDOR_ID', '=', 'C.VENDOR_ID')
                    ->where('CA.BRANCH_ID', $branch_id)
                    ->whereDate('CA.LOGIN_TIME', $curdate)
                    ->groupBy('CA.CAB_ID')
                    ->having('total_time', '>', 0);
                   // ->get();
							
				break;
				
					case $TD_OVERSPEED:
                        
                        $curdate = date('Y-m-d');
                        $CabSpeedCount = $ElasticController->getCabSpeedCountGroup($curdate,$curdate);
                        $results = array();
                       // print_r($CabSpeedCount);
				
                for ($k = 0; $k < count($CabSpeedCount); $k++)
                {
                    $CAB_ID = $CabSpeedCount[$k]['CAB_ID'];
                    $VENDOR_ID = $CabSpeedCount[$k]['VENDOR_ID'];
                    $SpeedCount = $CabSpeedCount[$k]['SpeedCount'];
                    $ACTION_REMARK = $CabSpeedCount[$k]['ACTION_REMARK'];
                    $GPS_DATE = $CabSpeedCount[$k]['START_GPS_DATE'];
                    $ADDRESS = $CabSpeedCount[$k]['START_LOCATION'];
                   
                    $cabdetails = $this->cabdetails($CAB_ID);
                    $j=0;
                    $VEHICLE_REG_NO = $cabdetails[$j]->VEHICLE_REG_NO;
                    $MODEL = $cabdetails[$j]->MODEL;
                    $DRIVERS_NAME = $cabdetails[$j]->DRIVERS_NAME;
                    $DRIVER_MOBILE = $cabdetails[$j]->DRIVER_MOBILE;
                    $vendorname = $cabdetails[$j]->vendorname;
                   
                    $results[] = array("VENDORNAME" => $vendorname, "DRIVERNAME" => $DRIVERS_NAME, "DRIVER_MOBILE" => $DRIVER_MOBILE,
                    "CABNO" => $VEHICLE_REG_NO ,"MODEL" => $MODEL, "ADDRESS" => $ADDRESS,
                    "GPS_DATE" => $GPS_DATE,"SPEED_COUNT"=>$SpeedCount,"ACTION_REMARK"=>$ACTION_REMARK,"CAB_ID"=>$CAB_ID);
                } 
                return response([
                    'success' => true,
                    'status' => 3,
                    'overspeed_data' => $results,
                ]);
							
				break;
				
				case $TD_ALERTSIGNALFAILURE:
                        
                    $currentDate = date('Y-m-d');
                    $branchId = Auth::user()->BRANCH_ID; // Ensure this is sanitized
                    $dbname = Auth::user()->dbname; // Ensure the db name is valid
                    
                    // Build the query using Laravel's Query Builder
                    $data = DB::connection($dbname)->table('alertsignalfailure as ASF')
                        ->select(
                            'ASF.ALERT_ID',
                            'R.ROUTE_ID',
                            'R.ROSTER_ID',
                            'R.TRIP_TYPE',
                            'B.BRANCH_NAME',
                            'V.NAME as vendor_name',
                            'D.DRIVERS_NAME',
                            'D.DRIVER_MOBILE',
                            'ASF.CAB_NO',
                            'VM.MODEL',
                            'ASF.LOCATION',
                            'ASF.GPS_DATE',
                            'ASF.REMARKS',
                            DB::raw('CASE WHEN R.TRIP_TYPE = "P" THEN TIME(R.ESTIMATE_END_TIME) ELSE TIME(R.ESTIMATE_START_TIME) END AS TRIPTIME')
                        )
                        ->join('rosters as R', 'R.ROSTER_ID', '=', 'ASF.ROSTER_ID')
                        ->join('branch as B', 'B.BRANCH_ID', '=', 'ASF.BRANCH_ID')
                        ->join('vendors as V', 'V.VENDOR_ID', '=', 'ASF.VENDOR_ID')
                        ->join('cab as C', 'C.CAB_ID', '=', 'ASF.CAB_ID')
                        ->join('drivers as D', 'D.DRIVERS_ID', '=', 'C.DRIVER_ID')
                        ->join('vehicles as VE', 'VE.VEHICLE_ID', '=', 'C.VEHICLE_ID')
                        ->join('vehicle_models as VM', 'VM.VEHICLE_MODEL_ID', '=', 'VE.VEHICLE_MODEL_ID')
                        ->where('ASF.BRANCH_ID', $branchId)
                        ->whereDate('ASF.created_at', $currentDate);
							
				break;
				
				case $TD_DEVIATIONALERT:
                        
                    $curdate = date('Y-m-d');$results = array();
                    $CabDevCount = $ElasticController->getCabDevCountGroup($curdate,$curdate);
                   
                   for ($k = 0; $k < count($CabDevCount); $k++){
                       $ROSTER_ID = $CabDevCount[$k]['ROSTER_ID'];
                       $routedetails = $this->route_details($ROSTER_ID);
                       $ROUTE_ID = $routedetails[0]->ROUTE_ID;
                       $TRIP_TYPE = $routedetails[0]->TRIP_TYPE;
                       $routedet = $TRIP_TYPE.'-'.$ROUTE_ID;
                       $BRANCH_ID = $CabDevCount[$k]['BRANCH_ID'];
                       $CAB_ID = $CabDevCount[$k]['CAB_ID'];
                       $cabdetails = $this->cabdetails($CAB_ID);
                       $vehicleno = $cabdetails[0]->VEHICLE_REG_NO;
                       $CAB_NO = $CabDevCount[$k]['CAB_NO'];
                       $VENDOR_ID = $CabDevCount[$k]['VENDOR_ID'];
                       $SpeedCount = $CabDevCount[$k]['SpeedCount'];
                       $ACTION_REMARK = $CabDevCount[$k]['ACTION_REMARK'];
                       $INITIATED_GPS_DATE = $CabDevCount[$k]['INITIATED_GPS_DATE'];
                       $ENDED_GPS_DATE = $CabDevCount[$k]['ENDED_GPS_DATE'];
                       $INITIATED_LOCATION = $CabDevCount[$k]['INITIATED_LOCATION'];
                       $ENDED_LOCATION = $CabDevCount[$k]['ENDED_LOCATION'];
                      
                       
                       $results[] = array("ROUTE_ID" => $routedet, "VEHICLE_ID" => $vehicleno, "INITIATEDLOCATION" => $INITIATED_LOCATION,
                       "ENDEDLOCATION" => $ENDED_LOCATION,"INITIATEDGPSDATE" => $INITIATED_GPS_DATE, "ENDEDGPSDATE" => $ENDED_GPS_DATE,
                       "speedcnt" => $SpeedCount,"ACTION" => $ACTION_REMARK);
                   } 
                   return response([
                    'success' => true,
                    'status' => 3,
                    'deviation_alert' => $results,
                ]);
					
                    
                   
							
				break;
				
				
			 
				case $TD_SEARCHALL:
                        
                    $curdate = date('Y-m-d');
                    $BRANCH_ID = Auth::user()->BRANCH_ID; // Ensure this is sanitized
                    $vendor_id = Auth::user()->vendor_id; // Ensure this is sanitized
                    $dbname = Auth::user()->dbname;
                    $RS_ACTIVE=MyHelper::$RS_ACTIVE;
                   
                    $data = RosterPassenger::on($dbname)
                    //$data = DB::table('roster_passengers as RS')
                        ->join('rosters as R', function($join) use ($RS_ACTIVE) {
                            $join->on('R.ROSTER_ID', '=', 'roster_passengers.ROSTER_ID')
                                ->where('R.ACTIVE', '=', $RS_ACTIVE);
                        })
                        ->join('employees as E', function($join) use ($BRANCH_ID) {
                            $join->on('E.EMPLOYEES_ID', '=', 'roster_passengers.EMPLOYEE_ID')
                                ->where('E.BRANCH_ID', '=', $BRANCH_ID);
                        })
                        ->join('branch as B', 'B.BRANCH_ID', '=', 'R.BRANCH_ID')
                        ->join('vendors as V', 'V.VENDOR_ID', '=', 'R.VENDOR_ID')
                        ->leftJoin('cab as C', 'C.CAB_ID', '=', 'R.CAB_ID')
                        ->leftJoin('vehicles as VE', 'VE.VEHICLE_ID', '=', 'C.VEHICLE_ID')
                        ->leftJoin('vehicle_models as VM', 'VM.VEHICLE_MODEL_ID', '=', 'VE.VEHICLE_MODEL_ID')
                        ->leftJoin('drivers as D', 'D.DRIVERS_ID', '=', 'C.DRIVER_ID')
                        ->join('locations as L', 'L.LOCATION_ID', '=', 'roster_passengers.LOCATION_ID')
                        ->select(
                            'R.ROSTER_ID',
                            'R.ROUTE_ID',
                            'R.TRIP_TYPE',
                            'L.LOCATION_NAME',
                            'R.START_LOCATION',
                            'roster_passengers.EMPLOYEE_ID',
                            'roster_passengers.DRIVER_ARRIVAL_TIME',
                            'E.NAME as empname',
                            'E.GENDER',
                            'B.BRANCH_NAME',
                            'V.NAME as vendorname',
                            'VE.VEHICLE_REG_NO',
                            'VM.MODEL',
                            'D.DRIVERS_NAME',
                            'D.DRIVER_MOBILE',
                            'roster_passengers.ROSTER_PASSENGER_ID',
                            'roster_passengers.REMARKS',
                            DB::raw("IF(R.TRIP_TYPE = 'P', R.ESTIMATE_END_TIME, R.ESTIMATE_START_TIME) AS TRIPINTIME"),
                            DB::raw("IF(R.TRIP_TYPE = 'P', TIME(R.ESTIMATE_END_TIME), TIME(R.ESTIMATE_START_TIME)) AS TRIPTIME"),
                            'roster_passengers.ROSTER_PASSENGER_STATUS',
                            'R.ROSTER_STATUS',
                            'roster_passengers.ESTIMATE_START_TIME as PICKUPTIME'
                        )
                        ->where('R.BRANCH_ID', $BRANCH_ID)
                        ->when($vendorid, function ($query) use ($vendorid) {
                            return $query->where('rosters.VENDOR_ID', $vendorid);
                        })
                        ->where(function($query) use ($curdate) {
                            $query->whereDate('R.ESTIMATE_START_TIME', $curdate)
                                ->orWhereDate('R.ESTIMATE_END_TIME', $curdate);
                        })
                        ->where('roster_passengers.ACTIVE', 1);
                       // ->groupBy('RP.ROSTER_ID');
                      //  ->get();
                                    
							
				break;
				
				
			 
				case $TD_NOSHOW:
                        
                    $curdate = date('Y-m-d');
                    $BRANCH_ID = Auth::user()->BRANCH_ID; // Ensure this is sanitized
                    $vendor_id = Auth::user()->vendor_id; // Ensure this is sanitized
                    $dbname = Auth::user()->dbname;
                    $RS_ACTIVE=MyHelper::$RS_ACTIVE;
                   
                    $data = RosterPassenger::query()
                    ->select(
                        'R.ROUTE_ID',
                        'R.TRIP_TYPE',
                        'V.VEHICLE_REG_NO',
                        'VM.MODEL',
                        'VM.CAPACITY',
                        'VE.NAME AS VENDORNAME',
                        'E.EMPLOYEES_ID',
                        'E.NAME AS EMPNAME',
                        'E.MOBILE',
                        'L.LOCATION_NAME',
                        'R.ESTIMATE_END_TIME',
                        'R.ESTIMATE_START_TIME','roster_passengers.ROSTER_PASSENGER_STATUS','R.ROSTER_STATUS',
                        DB::raw("if(R.TRIP_TYPE = 'P',DATE(R.ESTIMATE_END_TIME),DATE(R.ESTIMATE_START_TIME)) as INOUT_DATE"),
                        DB::raw("if(R.TRIP_TYPE = 'P',TIME(R.ESTIMATE_END_TIME),TIME(R.ESTIMATE_START_TIME)) AS TRIPTIME"),
                        DB::raw("if(roster_passengers.REMARKS is NULL,RM.REASON,roster_passengers.REMARKS) AS REASON_REMARKS"),
                        'roster_passengers.ROSTER_PASSENGER_ID'
                    )
                    ->Join('rosters as R', 'R.ROSTER_ID', '=', 'roster_passengers.ROSTER_ID')
                    ->Join('branch as  B', 'B.BRANCH_ID', '=', 'R.BRANCH_ID')
                    ->Join('vendors as  VE', 'VE.VENDOR_ID', '=', 'R.VENDOR_ID')
                    ->leftJoin('cab as  C', 'C.CAB_ID', '=', 'R.CAB_ID')
                    ->leftJoin('vehicles as V', 'V.VEHICLE_ID', '=', 'C.VEHICLE_ID')
                    ->leftJoin('vehicle_models as VM', 'VM.VEHICLE_MODEL_ID', '=', 'V.VEHICLE_MODEL_ID')
                    ->join('employees as E', function ($join) use ($BRANCH_ID) {
                        $join->on('E.EMPLOYEES_ID', '=', 'roster_passengers.EMPLOYEE_ID')
                            ->where('E.BRANCH_ID',  '=', $BRANCH_ID);
                    })
                    ->leftJoin('locations as  L', 'L.LOCATION_ID', '=', 'roster_passengers.LOCATION_ID')
                    ->leftJoin('reason_log as   RL', 'RL.ROSTER_PASSENGER_ID', '=', 'roster_passengers.ROSTER_PASSENGER_ID')
                    ->leftJoin('reason_master as RM', 'RM.REASON_ID', '=', 'RL.REASON_ID')
                    ->where('R.ACTIVE', $RS_ACTIVE)
                    //->whereIn('R.TRIP_TYPE', $triptype)
                    ->where('R.BRANCH_ID', $BRANCH_ID)
                    ->when($vendorid, function ($query, $vendorid) {
                        return $query->where('R.VENDOR_ID', $vendorid);
                    })
                    ->whereRaw("roster_passengers.ROSTER_PASSENGER_STATUS & $RPS_NOSHOW")
                   ->havingRaw("(date(R.ESTIMATE_END_TIME) BETWEEN  '$curdate' AND '$curdate' OR  date(R.ESTIMATE_START_TIME) BETWEEN  '$curdate' AND '$curdate')");
    
                                    
							
				break;
				
				
			 }

            $filterModel = $request->input('filterModel');
            if ($filterModel) {
                foreach ($filterModel as $field => $filter) {
                    if (isset($filter['filter']) && $filter['filter'] !== '') {
                        $value = $filter['filter'];
                        $type = $filter['type'];

                        switch ($field) {
                            case 'EMPLOYEES_ID':
                                $data->where('E.EMPLOYEES_ID', 'like', "%{$value}%");
                                break;
                            case 'ROSTER_ID':
                                $data->where('rosters.ROSTER_ID', 'like', "%{$value}%");
                                break;
                            case 'ROUTE_ID':
                                $data->where('rosters.ROUTE_ID', 'like', "%{$value}%");
                                break;
                            case 'VENDORNAME':
                                $data->where('VE.NAME', 'like', "%{$value}%");
                                break;
                            case 'PASSENGER_ALLOT_COUNT':
                                $data->where('rosters.PASSENGER_ALLOT_COUNT', 'like', "%{$value}%");
                                break;
                            case 'START_LOCATION':
                                $data->where('rosters.START_LOCATION', 'like', "%{$value}%");
                                break;
                            case 'END_LOCATION':
                                $data->where('rosters.END_LOCATION', 'like', "%{$value}%");
                                break;
                            case 'VEHICLE_REG_NO':
                                $data->where('VH.VEHICLE_REG_NO', 'like', "%{$value}%");
                                break;
                            case 'MODEL':
                                $data->where('VM.MODEL', 'like', "%{$value}%");
                                break;
                            case 'TRIPTIME':
                                $data->where('rosters.ESTIMATE_END_TIME', 'like', "%{$value}%")
                                ->orwhere('rosters.ESTIMATE_START_TIME', 'like', "%{$value}%");
                                break;
                            case 'TRIP_APPROVED_KM':
                                $data->where('rosters.TRIP_APPROVED_KM', 'like', "%{$value}%");
                                break;
                            case 'DRIVERS_NAME':
                                $data->where('D.DRIVERS_NAME', 'like', "%{$value}%");
                                break;
                            case 'DRIVER_MOBILE':
                                $data->where('D.DRIVER_MOBILE', 'like', "%{$value}%");
                                break;
                            case 'TARIFF_TYPE':
                                $data->where('RM.REASON', 'like', "%{$value}%");
                                break;
                            case 'BRANCH_NAME':
                                $data->where('B.BRANCH_NAME', 'like', "%{$value}%");
                                break;
                            case 'LOCATION_NAME':
                                $data->where('L.LOCATION_NAME', 'like', "%{$value}%");
                                break;

                        }
                    }
                }
            }


            if ($request->has('orderBy') && !empty($request->input('orderBy'))) {
                $orderBy = $request->input('orderBy');
                $order = $request->input('order', 'asc');
                $data->orderBy($orderBy, $order);
            } else {
				
                $data->orderBy("TRIPTIME");
            }


            if ($perPage === MyHelper::$PAGINATION_PER_PAGE_ALL) {
                $paginatedData = $data->paginate($data->count());
            } else {
                $perPage = is_numeric($perPage) ? (int)$perPage : MyHelper::$PAGINATION_PER_PAGE;
				
                $paginatedData = $data->paginate($perPage);
            }
			/* $paginatedData = $paginatedData->map(function ($item) {
				$item->decrypted_name = Crypt::decrypt($item->empname);
				return $item;
			});
 */
            return response([
                'success' => true,
                'status' => 3,
                "normal_dashboard_data" => $paginatedData,
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'Data Pagination Unsuccessful first' : 'Data Pagination asasda Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function route_details($roster_id) {
        $RS_ACTIVE = MyHelper::$RS_ACTIVE;
        $branchid = Auth::user()->BRANCH_ID;
        $dbname = Auth::user()->dbname;
		$trip_type = DB::table("rosters as R")->select("R.TRIP_TYPE")
                                ->where("R.ROSTER_ID", "=", $roster_id)->get();
       // $order = $trip_type[0]->TRIP_TYPE == 'P' ? 'DESC' : 'ASC';

        $order = 'ASC'; 
        if(isset($trip_type[0]) && $trip_type[0]->TRIP_TYPE == 'P'){
            $order = 'DESC';
        }  

		$retdata = DB::connection($dbname)->table('roster_passengers as RP')
    ->select(
        'E.NAME',
        'E.ADDRESS',
        'B.BRANCH_NAME',
        'E.DISTANCE',
        'E.GENDER',
        'R.ROUTE_ID',
        'R.ROSTER_STATUS',
        'R.TRIP_TYPE',
        DB::raw("IF(R.TRIP_TYPE = 'P', R.ESTIMATE_END_TIME, R.ESTIMATE_START_TIME) AS ESTIMATE_END_TIME"),
        DB::raw("IF(R.TRIP_TYPE = 'P', RP.ACTUAL_START_TIME, RP.ACTUAL_END_TIME) AS ACTUAL_START_TIME"),
        DB::raw("IF(R.TRIP_TYPE = 'P', RP.DRIVER_ARRIVAL_TIME, RP.ACTUAL_START_TIME) AS DRIVER_ARRIVAL_TIME"),
        'RP.ESTIMATE_START_TIME',
        'L.LOCATION_NAME',
        'RP.EMPLOYEE_ID',
        'RP.ROSTER_PASSENGER_STATUS',
        'RP.ROSTER_PASSENGER_ID',
        'RP.ROUTE_ORDER'
    )
    ->join('rosters as R', function ($join) use ($RS_ACTIVE) {
        $join->on('R.ROSTER_ID', '=', 'RP.ROSTER_ID')
             ->where('R.ACTIVE', $RS_ACTIVE);
    })
    ->join('employees as E', function ($join) use ($branchid) {
        $join->on('E.EMPLOYEES_ID', '=', 'RP.EMPLOYEE_ID')
             ->where('E.BRANCH_ID', $branchid);
    })
    ->join('locations as L', 'L.LOCATION_ID', '=', 'RP.LOCATION_ID')
    ->join('branch as B', function ($join) use ($branchid) {
        $join->on('B.BRANCH_ID', '=', $branchid);
    })
    ->where('R.BRANCH_ID', $branchid)
    ->where('RP.ROSTER_ID', $roster_id)
    ->where('RP.ACTIVE', $RS_ACTIVE)
    ->orderBy('RP.ROUTE_ORDER', $order) // Ensure $order is either 'ASC' or 'DESC'
    ->get();
        return $retdata;
    }
	
	public function cabdetails($cabid)
    {
        try 
        {
            $rsActive = MyHelper::$RS_ACTIVE;
            $branchId = Auth::user()->BRANCH_ID;
            $dbname = Auth::user()->dbname;

            // Build the query using Laravel's Query Builder
           $result = DB::connection($dbname)->table('cab as C')
                ->select(
                    'C.VENDOR_ID',
                    'C.CAB_ID',
                    'V.VEHICLE_REG_NO',
                    'VM.MODEL',
                    'D.DRIVERS_NAME',
                    'D.DRIVER_MOBILE',
                    'VD.NAME as vendorname'
                )
                ->join('vehicles as V', 'V.VEHICLE_ID', '=', 'C.VEHICLE_ID')
                ->join('vehicle_models as VM', 'VM.VEHICLE_MODEL_ID', '=', 'V.VEHICLE_MODEL_ID')
                ->join('drivers as D', 'D.DRIVERS_ID', '=', 'C.DRIVER_ID')
                ->join('vendors as VD', 'VD.VENDOR_ID', '=', 'C.VENDOR_ID')
                ->where('C.BRANCH_ID', $branchId)
                ->where('C.CAB_ID', $cabid)
                ->get();
           return $result;
            
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'No data',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }

    }
	public function dashboard_count(): FoundationApplication|Response|ResponseFactory
    {

        $authUser = Auth::user();
        $branch_id = $authUser->BRANCH_ID;
		$RPTTLTYPE = explode(',',MyHelper::$RP_TTLTYPE);
		$RP_PICKROUTE = MyHelper::$RP_PICKROUTE;
		$RS_NEWROSTER=MyHelper::$RS_NEWROSTER;
		$RS_TOTALALLOT=MyHelper::$RS_TOTALALLOT;
		$RS_TOTALACCEPT=MyHelper::$RS_TOTALACCEPT;
		$RS_TOTALREJECT=MyHelper::$RS_TOTALREJECT;
		$RS_TOTALEXECUTE=MyHelper::$RS_TOTALEXECUTE;
		$RS_TOTALBREAKDOWN=MyHelper::$RS_TOTALBREAKDOWN;
		$RPS_TTLARRIVAL=MyHelper::$RPS_TTLARRIVAL;
		$RPS_TTLCABDELAY=MyHelper::$RPS_TTLCABDELAY;
		$RS_TOTALTRIPCLOSE=MyHelper::$RS_TOTALTRIPCLOSE;
		$RS_TOTALMANUALTRIPCLOSE=MyHelper::$RS_TOTALMANUALTRIPCLOSE;
		$RS_TOTALTRIPSHEETACCEPT=MyHelper::$RS_TOTALTRIPSHEETACCEPT;
		$RS_TOTALDELAYROUTES=MyHelper::$RS_TOTALDELAYROUTES;
		
			
		
        $date = Carbon::now();
        $currentDateTime = $date->format("Y-m-d H:i:s");
		$normal_cnt = 0;
        $notalloted_cnt = 0;
        $alloted_cnt = 0;
        $tripaccept_cnt = 0;
        $triprejected_cnt = 0;
        $tripnotexecute_cnt = 0;
        $tripexecuted_cnt = 0;
        $noresponse_cnt = 0;
        $tripclose_cnt = 0;
		$all_cnt = 0;
		
        try {
            
            $PR_ESTIMATESUBTIME = MyHelper::$PR_ESTIMATESUBTIME;
            $PR_ESTIMATEADDTIME = MyHelper::$PR_ESTIMATEADDTIME;
            $PR_ESTIMATECURTIME = MyHelper::$PR_ESTIMATECURTIME;
            $PR_LEVEL2 = MyHelper::$PR_LEVEL2;
            $PR_ESTIMATECABALLOT = MyHelper::$PR_ESTIMATECABALLOT;
            $PR_LEVEL1 = MyHelper::$PR_LEVEL1;
            $PR_TRIPNOTEXECUTED = MyHelper::$PR_TRIPNOTEXECUTED;
            $PR_EMPLOYEE_BUFFER = MyHelper::$PR_EMPLOYEE_BUFFER;
			$user_type=$authUser->user_type;
			$vendorid=$authUser->vendor_id;

            $property_result = $this->getPropertyValue();
           
            for ($i = 0; $i < count($property_result); $i++) {
                $PROPERTIE_NAME = $property_result[$i]->PROPERTIE_NAME;
                $PROPERTIE_VALUE = $property_result[$i]->PROPERTIE_VALUE;
                switch ($PROPERTIE_NAME) {
                    case $PR_ESTIMATESUBTIME:
                        $ESTIMATE_SUBTIME = $PROPERTIE_VALUE;
                        break;
                    case $PR_ESTIMATEADDTIME:
                        $ESTIMATE_ADDTIME = $PROPERTIE_VALUE;
                        break;
                    case $PR_ESTIMATECURTIME:
                        $ESTIMATE_CURTIME = $PROPERTIE_VALUE;
                        break;
                    case $PR_LEVEL2:
                        $ESTIMATE_INTERVAL60 = $PROPERTIE_VALUE;
                        break;
                    case $PR_ESTIMATECABALLOT:
                        $ESTIMATE_CABALLOT = $PROPERTIE_VALUE;
                        break;
                    case $PR_LEVEL1:
                        $ESTIMATE_INTERVAL30 = $PROPERTIE_VALUE;
                        break;
                    case $PR_TRIPNOTEXECUTED:
                        $TRIP_NOT_EXECUTE = $PROPERTIE_VALUE;
                        break;
                     case $PR_EMPLOYEE_BUFFER:
                         $wapptime = $PROPERTIE_VALUE;
                        break;
                    default:
                        break;
                }
            }
			if($user_type == MyHelper::$ADMIN) 
			{
				$vendorid = "";
				} else {
				$vendorid = $vendorid;
				}
        for($x=0;$x<count($RPTTLTYPE);$x++)
        {
            if($RPTTLTYPE[$x] == $RP_PICKROUTE)
            {
                 $TRIPTIME = 'ESTIMATE_END_TIME';
                $TREXECUTE = "DATE_SUB('$currentDateTime',$ESTIMATE_INTERVAL30)"; 
				
				
            }
            else
            {
                $TRIPTIME = 'ESTIMATE_START_TIME';
                $TREXECUTE = "SUBTIME('$currentDateTime',30000)";
            }

$results = DB::table('rosters')
    ->select(
        DB::raw("SUM(IF(ROSTER_STATUS IN (1, 3) AND $TRIPTIME BETWEEN SUBTIME('$currentDateTime', $ESTIMATE_SUBTIME) AND ADDTIME('$currentDateTime', $ESTIMATE_ADDTIME) AND PASSENGER_ALLOT_COUNT > 0 AND '$currentDateTime' < SUBTIME($TRIPTIME, $ESTIMATE_CURTIME), 1, 0)) AS normal_cnt"),
        
        DB::raw("SUM(IF(ROSTER_STATUS IN ($RS_NEWROSTER) AND $TRIPTIME BETWEEN SUBTIME('$currentDateTime', $ESTIMATE_SUBTIME) AND ADDTIME('$currentDateTime', $ESTIMATE_ADDTIME) AND PASSENGER_ALLOT_COUNT > 0 AND '$currentDateTime' >= SUBTIME($TRIPTIME, $ESTIMATE_CURTIME), 1, 0)) AS notalloted_cnt"),
        
        DB::raw("SUM(IF(ROSTER_STATUS IN ($RS_TOTALALLOT) AND $TRIPTIME BETWEEN SUBTIME('$currentDateTime', $ESTIMATE_SUBTIME) AND ADDTIME('$currentDateTime', $ESTIMATE_ADDTIME) AND ('$currentDateTime' - $ESTIMATE_INTERVAL60) < $TRIPTIME AND TIMEDIFF('$currentDateTime',CAB_ALLOT_TIME) < '$ESTIMATE_CABALLOT', 1, 0)) AS alloted_cnt"),
        
        DB::raw("SUM(IF(ROSTER_STATUS IN ($RS_TOTALACCEPT) AND $TRIPTIME BETWEEN SUBTIME('$currentDateTime', $ESTIMATE_SUBTIME) AND ADDTIME('$currentDateTime', $ESTIMATE_ADDTIME) AND ('$currentDateTime' - $ESTIMATE_INTERVAL60) < $TRIPTIME, 1, 0)) AS tripaccept_cnt"),
        
        DB::raw("SUM(IF(ROSTER_STATUS IN ($RS_TOTALREJECT) AND $TRIPTIME BETWEEN SUBTIME('$currentDateTime', $ESTIMATE_SUBTIME) AND ADDTIME('$currentDateTime', $ESTIMATE_ADDTIME) AND ('$currentDateTime' - $ESTIMATE_INTERVAL60) < $TRIPTIME, 1, 0)) AS triprejected_cnt"),
        
        DB::raw("SUM(IF(ROSTER_STATUS IN ($RS_TOTALACCEPT) AND $TRIPTIME BETWEEN SUBTIME('$currentDateTime', $ESTIMATE_SUBTIME) AND ADDTIME('$currentDateTime', $ESTIMATE_ADDTIME) AND TIMEDIFF('$currentDateTime', ESTIMATE_START_TIME) < '$TRIP_NOT_EXECUTE', 1, 0)) AS tripnotexecute_cnt"),
        
        DB::raw("SUM(IF(ROSTER_STATUS IN ($RS_TOTALEXECUTE) AND $TRIPTIME BETWEEN SUBTIME('$currentDateTime', $ESTIMATE_SUBTIME) AND ADDTIME('$currentDateTime',$ESTIMATE_ADDTIME) AND ($TREXECUTE < $TRIPTIME), 1, 0)) AS tripexecuted_cnt"),
        
        DB::raw("SUM(IF(ROSTER_STATUS IN ($RS_TOTALALLOT) AND $TRIPTIME BETWEEN SUBTIME('$currentDateTime', $ESTIMATE_SUBTIME) AND ADDTIME('$currentDateTime', $ESTIMATE_ADDTIME) AND ('$currentDateTime' - $ESTIMATE_INTERVAL60) < $TRIPTIME AND TIMEDIFF('$currentDateTime', CAB_ALLOT_TIME) > '$ESTIMATE_CABALLOT', 1, 0)) AS noresponse_cnt"),
        
        DB::raw("SUM(IF(ROSTER_STATUS IN ($RS_TOTALTRIPCLOSE, $RS_TOTALMANUALTRIPCLOSE, $RS_TOTALTRIPSHEETACCEPT, $RS_TOTALDELAYROUTES) AND             $TRIPTIME BETWEEN SUBTIME('$currentDateTime', $ESTIMATE_SUBTIME) AND ADDTIME('$currentDateTime', $ESTIMATE_ADDTIME) AND ('$currentDateTime' < $TRIPTIME), 1, 0)) AS tripclose_cnt")
    )
    ->where('BRANCH_ID', $branch_id)
    ->where('ACTIVE', '1')
    ->when($vendorid, function ($query) use ($vendorid) {
        return $query->where('rosters.VENDOR_ID', $vendorid);
    })
    ->whereBetween($TRIPTIME, [
        DB::raw("SUBTIME('$currentDateTime', $ESTIMATE_SUBTIME)"),
        DB::raw("ADDTIME('$currentDateTime', $ESTIMATE_ADDTIME)")
    ])
   // ->where('TRIP_TYPE', 'D')
    ->where('TRIP_TYPE', $RPTTLTYPE[$x])
    ->get();	
   
			$normal_cnt = $normal_cnt + $results[0]->normal_cnt;
            $notalloted_cnt = $notalloted_cnt + $results[0]->notalloted_cnt;
            $alloted_cnt = $alloted_cnt + $results[0]->alloted_cnt;
            $tripaccept_cnt = $tripaccept_cnt + $results[0]->tripaccept_cnt;
            $triprejected_cnt = $triprejected_cnt + $results[0]->triprejected_cnt;
            $tripnotexecute_cnt = $tripnotexecute_cnt + $results[0]->tripnotexecute_cnt;
            $tripexecuted_cnt = $tripexecuted_cnt + $results[0]->tripexecuted_cnt;
            $noresponse_cnt = $noresponse_cnt + $results[0]->noresponse_cnt;
            $tripclose_cnt = $tripclose_cnt + $results[0]->tripclose_cnt;
            
        }
     
		$breakdown_count=0;$waiting_cnt=0;$panic_count=0;$escort_count=0;$overtime_count=0;$over_speed_count=0;
		$breakdown_count=$this->breakdown_count();
		
		$wapp_count=$this->wapp_count($wapptime);

        $panic_count=$this->panic_count();
        $escort_count=$this->escort_count();
        $overtime_count=$this->overtime_count();
        $over_speed_count=$this->over_speed_count();
		

  $returnarray = array("normal_cnt"=>$normal_cnt,"notalloted_cnt"=>$notalloted_cnt,"alloted_cnt"=>$alloted_cnt,"tripaccept_cnt"=>$tripaccept_cnt,
        "triprejected_cnt"=>$triprejected_cnt,"tripnotexecute_cnt"=>$tripnotexecute_cnt,"tripexecuted_cnt"=>$tripexecuted_cnt,
        "noresponse_cnt"=>$noresponse_cnt,"tripclose_cnt"=>$tripclose_cnt,"all_cnt"=>$all_cnt,"breakdown_count"=>$breakdown_count,"wapp_count"=>$wapp_count,"panic_count"=>$panic_count,"escort_count"=>$escort_count,"overtime_count"=>$overtime_count,"over_speed_count"=>$over_speed_count);
		//wapp_count() ,breakdown_count,//overtime_count //ttlasf_count //
                
            return response([
                'success' => true,
                'status' => 3,
                'dashboard_count' => $returnarray,
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'No data',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }
	
	public function breakdown_count():int
    {
		try
		{
		$currtDate = date("Y-m-d");
		$vendor = Auth::user()->user_type;
        $BRANCH_ID = Auth::user()->BRANCH_ID;
        $vendor_id = Auth::user()->vendor_id;
        $dbname = Auth::user()->dbname;
        $vendorid = "";
        $RS_INACTIVE = MyHelper::$RS_INACTIVE;
        $RS_TOTALBREAKDOWN = MyHelper::$RS_TOTALBREAKDOWN;
		

        if ($vendor == MyHelper::$ADMIN) {
            $vendorid = "";
        } else {
           // $vendorid = "AND R.VENDOR_ID='$vendor_id'";
		   $vendorid=$vendor_id;
        }
       
	   $breakdown_cnt=0;
	  $status= explode(',', $RS_TOTALBREAKDOWN);
       
		$result=DB::table('rosters as R')
		->select(
					DB::raw('COUNT(R.ROUTE_ID) as readcnt'),
					DB::raw('SUM(CASE WHEN CA.ACTION IS NULL THEN 1 ELSE 0 END) AS unreadcnt')
				)
			 ->Join('cab_allocation AS CA', function ($join) use ($status) {
								 $join->on('CA.ROSTER_ID', '=', 'R.ROSTER_ID')
								 ->on('CA.CAB_ID', '=', 'R.CAB_ID')
								->whereIn('CA.ACCEPTANCE_REJECT_STATE',$status);
				
								}) 
			->whereIn('R.ROSTER_STATUS', explode(',', $RS_TOTALBREAKDOWN))
			->where('R.BRANCH_ID', '=', $BRANCH_ID)
			->whereDate('R.ESTIMATE_END_TIME', '=', $currtDate)
			->where('R.ACTIVE', '=', $RS_INACTIVE)
			->when($vendorid, function ($query) use ($vendorid) {
					return $query->where('R.VENDOR_ID', $vendorid);
				})
			->get();
		
		$retcnt = count($result);
		
        if ($retcnt > 0) {
            $breakdown_cnt = $result[0]->readcnt;
            $unreadbreak = $result[0]->unreadcnt;
        }
		
		return $breakdown_cnt;
	}catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'No data',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
	}
    public function panic_count()
    {
        try
        {
            $dbname = Auth::user()->dbname;
            $BRANCH_ID=Auth::user()->BRANCH_ID;
            $vendor_id=Auth::user()->vendor_id;
            $alertCondition = ($vendor_id == 0) ? '' : 'EMPLOYEE_ID IS NULL AND ';
            // if ($vendor_id == 0) {
            //     $alert = '';
            // } else {
            //     $alert = 'EMPLOYEE_ID is null and ';
            // }
            $panic_cnt=0;
            $currntdate = date('Y-m-d');
           
            $panic_routecnt = DB::connection($dbname)
                ->table('panic_alert')
                ->selectRaw('
                    COUNT(*) AS ttlcnt,
                    SUM(IF(EMPLOYEE_ID != \'\', 1, 0)) AS emp_panic,
                    SUM(IF(ACTION_TAKEN != \'\', 1, 0)) AS read_panic
                ')
                ->whereRaw($alertCondition . 'BRANCH_ID = ?', [$BRANCH_ID])
                ->whereDate('created_at', '=', $currntdate)
                ->get();
                $panic_ttlcnt=count($panic_routecnt);
                if($panic_ttlcnt>0)
                {
                    $panic_cnt   = $panic_routecnt[0]->ttlcnt;
                   // $unreadpanic = $panic_routecnt[0]->unread_panic;
                }
                return $panic_cnt;
        }
        catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'No data',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }

    }
	
    public function escort_count()
    {
        try
        {
            $USERID = Auth::user()->id;
            $branch_id = Auth::user()->BRANCH_ID;
            $dbname = Auth::user()->dbname;
            $user_type=Auth::user()->user_type;
            $ES_REMOVEINTER=MyHelper::$ES_REMOVEINTER;
            $RS_CABALLOT=MyHelper::$RS_CABALLOT;
            $curdatetime=date("Y-m-d H:i:s");
           // $curdatetime='2024-10-19 17:32:22';
            if($user_type == MyHelper::$ADMIN) 
			{
				$vendorid = "";
				} else {
				$vendorid = $vendorid;
				}
               
            $ES_ROMOVEINTER = env('ES_ROMOVEINTER');
            $currntdate = date('Y-m-d');
            $data = Roster::on($dbname)
            ->select(
                'RE.ROSTER_ID',
                'rosters.TRIP_TYPE',
                'RE.EMPLOYEE_ID',
                'RE.REASON',
                'RE.REMARKS',
                'B.BRANCH_NAME',
                'E.NAME as empname',
                'E.MOBILE',
                'L.LOCATION_NAME',
                'V.NAME as vendor_name',
                'ES.ESCORT_ID',
                'ES.ESCORT_NAME',
                'ES.ESCORT_MOBILE',
                'D.DRIVERS_NAME',
                'VE.VEHICLE_REG_NO',
                'VM.MODEL',
                'rosters.ESTIMATE_END_TIME',
                'D.DRIVER_MOBILE',
                'rosters.ROUTE_ID',
                'RE.ROUTE_ESCORT_ID',
                'RE.STATUS',
                DB::raw("IF(rosters.ROSTER_STATUS & {$RS_CABALLOT}, 1, 0) AS ROSTERSTATUS"),
                DB::raw("IF(rosters.TRIP_TYPE = 'P', rosters.ESTIMATE_END_TIME, rosters.ESTIMATE_START_TIME) as TRIPINTIME"),
                DB::raw('CASE WHEN rosters.TRIP_TYPE = "P" THEN TIME(rosters.ESTIMATE_END_TIME) ELSE TIME(rosters.ESTIMATE_START_TIME) END AS TRIPTIME'),
            )
            ->join('route_escorts as RE', function($join) use ($ES_REMOVEINTER) {
                $join->on('RE.ROSTER_ID', '=', 'rosters.ROSTER_ID')
                     ->whereNotIn('RE.STATUS', [$ES_REMOVEINTER]);
            })
            ->join('branch as B', 'B.BRANCH_ID', '=', 'rosters.BRANCH_ID')
            ->join('employees as E', function($join) use ($branch_id) {
                $join->on('E.EMPLOYEES_ID', '=', 'RE.EMPLOYEE_ID')
                     ->where('E.BRANCH_ID', '=', $branch_id);
            })
            ->join('roster_passengers as RS', function($join) {
                $join->on('RS.EMPLOYEE_ID', '=', 'RE.EMPLOYEE_ID')
                     ->on('RS.ROSTER_ID', '=', 'rosters.ROSTER_ID');
            })
            ->join('locations as L', 'L.LOCATION_ID', '=', 'RS.LOCATION_ID')
            ->join('vendors as V', 'V.VENDOR_ID', '=', 'rosters.VENDOR_ID')
            ->leftJoin('escorts as ES', 'ES.ESCORT_ID', '=', 'RE.ESCORT_ID')
            ->leftJoin('cab as C', 'C.CAB_ID', '=', 'rosters.CAB_ID')
            ->leftJoin('drivers as D', 'D.DRIVERS_ID', '=', 'C.DRIVER_ID')
            ->leftJoin('vehicles as VE', 'VE.VEHICLE_ID', '=', 'C.VEHICLE_ID')
            ->leftJoin('vehicle_models as VM', 'VM.VEHICLE_MODEL_ID', '=', 'VE.VEHICLE_MODEL_ID')
            ->where('rosters.BRANCH_ID', '=', $branch_id)
            ->where('rosters.ACTIVE', '=', '1')
            ->when($vendorid, function ($query) use ($vendorid) {
                return $query->where('rosters.VENDOR_ID', $vendorid);
            })
            ->havingRaw("TRIPINTIME BETWEEN SUBTIME(?, 10000) AND ADDTIME(?, 480000)", [$curdatetime, $curdatetime])
            ->havingRaw("? >= SUBTIME(TRIPINTIME, 40000)", [$curdatetime])
            ->get(); 
                return count($data);
        }
        catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'No data',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }

    }
	
    public function overtime_count()
    {
        try
        {
            $USERID = Auth::user()->id;
            $branch_id = Auth::user()->BRANCH_ID;
            $dbname = Auth::user()->dbname;
            $user_type=Auth::user()->user_type;
            $ES_REMOVEINTER=MyHelper::$ES_REMOVEINTER;
            $RS_CABALLOT=MyHelper::$RS_CABALLOT;
            $curdatetime=date("Y-m-d H:i:s");
           // $curdatetime='2024-10-19 17:32:22';
            if($user_type == MyHelper::$ADMIN) 
			{
				$vendorid = "";
				} else {
				$vendorid = $vendorid;
				}
            $currntdate = date('Y-m-d');
            $overtime=$this->commonFunction->GetPropertyValue("OVER TIME");
           
            $data=DB::table('cab_attandance as CA')
            ->select(
                DB::raw("IF(TIMEDIFF(MAX(CA.LOGOUT_TIME), MIN(CA.LOGIN_TIME)) > '{$overtime}', 
                    TIMEDIFF(MAX(CA.LOGOUT_TIME), MIN(CA.LOGIN_TIME)), 
                    0) AS total_time") )
            ->where('CA.BRANCH_ID', '=', $branch_id)
            ->whereDate('CA.LOGIN_TIME', '=', $currntdate)
            ->groupBy('CA.CAB_ID')
            ->having('total_time', '>', 0)
            ->get();

            
                return count($data);
        }
        catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'No data',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }

    }
	
    public function over_speed_count()
    {
        try
        {
            $USERID = Auth::user()->id;
            $branch_id = Auth::user()->BRANCH_ID;
            $dbname = Auth::user()->dbname;
            $user_type=Auth::user()->user_type;
            $ElasticController = new ElasticController;
            $speed_data = $ElasticController->getCabSpeedCount();
            return $speed_data[0]['distinct_count']['value'];
               
        }
        catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'No data',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }

    }
	
public function wapp_count($wapptime):string
    {
		try
		{
		$date = Carbon::now();
		$curdatetime = $date->format("Y-m-d H:i:s");
		$currtDate = date('Y-m-d');
		$vendor = Auth::user()->user_type;
        $BRANCH_ID = Auth::user()->BRANCH_ID;
        $vendor_id = Auth::user()->vendor_id;
        $dbname = Auth::user()->dbname;
        $vendorid = "";
        $RS_ACTIVE = MyHelper::$RS_ACTIVE;
        $RPS_TTLARRIVAL = MyHelper::$RPS_TTLARRIVAL;
        $RPS_TTLCABDELAY = MyHelper::$RPS_TTLCABDELAY;
        $RS_ACTIVE = MyHelper::$RS_ACTIVE;
		$rps_status= explode(",",$RPS_TTLARRIVAL);
		$rps_delay_status= explode(",",$RPS_TTLCABDELAY);
		$status=array_merge($rps_status,$rps_delay_status);
		
		

        if ($vendor == MyHelper::$ADMIN) {
            $vendorid = "";
        } else {
            $vendorid = "AND R.VENDOR_ID='$vendor_id'";
		   //$vendorid=$vendor_id;
        }
       /*
		$result=DB::table('roster_passengersS as RP')
		->select(
					DB::raw('COUNT(RP.EMPLOYEE_ID) as ttlwapp'),'RP.DRIVER_ARRIVAL_TIME','RP.ESTIMATE_END_TIME','R.CAB_ID','R.TRIP_TYPE'
				)
			 ->join("rosters as R","R.ROSTER_ID","=","RP.ROSTER_ID")
			 
			->whereIn('RP.ROSTER_PASSENGER_STATUS', $status)
			->where('R.BRANCH_ID', '=', $BRANCH_ID)
			
			->where('R.ACTIVE', '=', $RS_ACTIVE)
			->when($vendorid, function ($query) use ($vendorid) {
					return $query->where('R.VENDOR_ID', $vendorid);
				})
				
			 	->whereBetween('RS.ESTIMATE_END_TIME', [
						DB::raw("SUBTIME('$currtDatetime', 10000)"),
						DB::raw("ADDTIME('$currtDatetime', 480000)")
					]) 
				
				->havingRaw("TIMEDIFF('$currtDatetime',RS.DRIVER_ARRIVAL_TIME)> '$wapptime' and R.CAB_ID!='' AND R.TRIP_TYPE='P'  AND '$currtDatetime'>=SUBTIME(RS.ESTIMATE_END_TIME,040000)")
				->get();*/
				 $wapp_data = "SELECT COUNT(RS.EMPLOYEE_ID) as ttlwapp
					FROM roster_passengers RS
					INNER JOIN rosters R ON R.ROSTER_ID = RS.ROSTER_ID and R.ACTIVE=$RS_ACTIVE
					WHERE TIMEDIFF('$curdatetime',RS.DRIVER_ARRIVAL_TIME)> '$wapptime' AND 
					RS.ROSTER_PASSENGER_STATUS IN ($RPS_TTLARRIVAL,$RPS_TTLCABDELAY) and R.CAB_ID!=''
					and R.BRANCH_ID='$BRANCH_ID' $vendorid AND R.TRIP_TYPE='P' AND RS.ESTIMATE_END_TIME BETWEEN 
					SUBTIME('$curdatetime',10000) AND ADDTIME('$curdatetime',480000) AND '$curdatetime'>=SUBTIME(RS.ESTIMATE_END_TIME,040000) ";

					$retqry = DB::select($wapp_data);
			
		$retcnt = count($retqry);
		
        if ($retcnt > 0) {
            $waiting_cnt = $retqry[0]->ttlwapp;
        }
		
		return $waiting_cnt;
	}catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'No data',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
	}

    public function roster_details($request): FoundationApplication|Response|ResponseFactory
    {
        $roster_id = $request->roster_id;
        $date = Carbon::now();
        $curdatetime = $date->format("Y-m-d H:i:s");
        try 
		{
			$RS_ACTIVE = MyHelper::$RS_ACTIVE;
			$branchid = Auth::user()->BRANCH_ID;
			$trip_type = DB::table("rosters as R")->select("R.TRIP_TYPE")
						->where("R.ROSTER_ID", "=", $roster_id)->get();
		   // $order = $trip_type[0]->TRIP_TYPE == 'P' ? 'DESC' : 'ASC';

			$order = 'ASC'; 
			if(isset($trip_type[0]) && $trip_type[0]->TRIP_TYPE == 'P'){
				$order = 'DESC';
			}  

            $RS_ACTIVE = MyHelper::$RS_ACTIVE; // Assuming this is defined somewhere

            $data = RosterPassenger::query()
                ->select(
                    'E.NAME as empname',
                    'E.ADDRESS',
                    'B.BRANCH_NAME',
                    'E.DISTANCE',
                    'E.GENDER',
                    'R.ROUTE_ID',
                    'R.ROSTER_STATUS',
                    'R.TRIP_TYPE',
                    DB::raw("IF(R.TRIP_TYPE = 'P', R.ESTIMATE_END_TIME, R.ESTIMATE_START_TIME) AS ESTIMATE_END_TIME"),
                    DB::raw("IF(R.TRIP_TYPE = 'P', roster_passengers.ACTUAL_START_TIME, roster_passengers.ACTUAL_END_TIME) AS ACTUAL_START_TIME"),
                    DB::raw("IF(R.TRIP_TYPE = 'P', roster_passengers.DRIVER_ARRIVAL_TIME, roster_passengers.ACTUAL_START_TIME) AS DRIVER_ARRIVAL_TIME"),
                    'roster_passengers.ESTIMATE_START_TIME',
                    'L.LOCATION_NAME',
                    'roster_passengers.EMPLOYEE_ID',
                    'roster_passengers.ROSTER_PASSENGER_STATUS',
                    'roster_passengers.ROSTER_PASSENGER_ID',
                    'roster_passengers.ROUTE_ORDER'
                )
                ->join('rosters AS R', 'R.ROSTER_ID', '=', 'roster_passengers.ROSTER_ID')
                ->join('employees AS E', function ($join) use ($branchid) {
                    $join->on('E.EMPLOYEES_ID', '=', 'roster_passengers.EMPLOYEE_ID')
                         ->where('E.BRANCH_ID', '=', $branchid);
                })
                ->join('locations AS L', 'L.LOCATION_ID', '=', 'roster_passengers.LOCATION_ID')
                ->join('branch AS B', 'B.BRANCH_ID', '=', DB::raw("'$branchid'"))
                ->where('R.BRANCH_ID', '=', $branchid)
                ->where('roster_passengers.ROSTER_ID', '=', $roster_id)
                ->where('roster_passengers.ACTIVE', '=', $RS_ACTIVE);
                //->orderBy('RP.ROUTE_ORDER', $order)
                //->get();

                $filterModel = $request->input('filterModel');
                if ($filterModel) {
                    foreach ($filterModel as $field => $filter) {
                        if (isset($filter['filter']) && $filter['filter'] !== '') {
                            $value = $filter['filter'];
                            $type = $filter['type'];
    
                            switch ($field) {
                                case 'EMPLOYEES_ID':
                                    $data->where('E.EMPLOYEES_ID', 'like', "%{$value}%");
                                    break;
                                case 'ROSTER_ID':
                                    $data->where('R.ROSTER_ID', 'like', "%{$value}%");
                                    break;
                                case 'ROUTE_ID':
                                    $data->where('R.ROUTE_ID', 'like', "%{$value}%");
                                    break;
                                 case 'START_LOCATION':
                                    $data->where('R.START_LOCATION', 'like', "%{$value}%");
                                    break;
                                case 'END_LOCATION':
                                    $data->where('R.END_LOCATION', 'like', "%{$value}%");
                                    break;
                                case 'VEHICLE_REG_NO':
                                    $data->where('VH.VEHICLE_REG_NO', 'like', "%{$value}%");
                                    break;
                                case 'MODEL':
                                    $data->where('VM.MODEL', 'like', "%{$value}%");
                                    break;
                                case 'TRIPTIME':
                                    $data->where('R.ESTIMATE_END_TIME', 'like', "%{$value}%")
                                    ->orwhere('R.ESTIMATE_START_TIME', 'like', "%{$value}%");
                                    break;
                                case 'TRIP_APPROVED_KM':
                                    $data->where('R.TRIP_APPROVED_KM', 'like', "%{$value}%");
                                    break;
                                case 'DRIVERS_NAME':
                                    $data->where('D.DRIVERS_NAME', 'like', "%{$value}%");
                                    break;
                                case 'DRIVER_MOBILE':
                                    $data->where('D.DRIVER_MOBILE', 'like', "%{$value}%");
                                    break;
                                case 'LOCATION_NAME':
                                    $data->where('L.LOCATION_NAME', 'like', "%{$value}%");
                                    break;
    
                            }
                        }
                    }
                }
    
    
                if ($request->has('orderBy') && !empty($request->input('orderBy'))) {
                    $orderBy = $request->input('orderBy');
                    $order = $request->input('order', 'asc');
                    $data->orderBy($orderBy, $order);
                } else {
                    
                    $data->orderBy("roster_passengers.ROUTE_ORDER", $order);
                }
                $perPage = $request->input('per_page', MyHelper::$PAGINATION_PER_PAGE);
    
                if ($perPage === MyHelper::$PAGINATION_PER_PAGE_ALL) {
                    $paginatedData = $data->paginate($data->count());
                } else {
                    $perPage = is_numeric($perPage) ? (int)$perPage : MyHelper::$PAGINATION_PER_PAGE;
                    
                    $paginatedData = $data->paginate($perPage);
                }
               
                return response([
                    'success' => true,
                    'status' => 3,
                    'roster_details' =>$paginatedData,
                ]);
			
		}
		catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'No data',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }
	
    public function get_reason_details($request): FoundationApplication|Response|ResponseFactory
    {
        
        try 
		{
            $branchid = Auth::user()->BRANCH_ID;
            $categoryName = $request->categoryName;
           $reason= $this->commonFunction->getReasonList($categoryName,$branchid);

                return response([
                    'success' => true,
                    'status' => 3,
                    'reason' => $reason,
                ]);
			
		}
		catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'No data',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }
	
    //Vendor Change
    public function roster_vendor_change($request): FoundationApplication|Response|ResponseFactory
    {
        
        try 
		{
            
            DB::beginTransaction();
            $date = Carbon::now();
            $branchid = Auth::user()->BRANCH_ID;
            $currtDateTime = date('Y-m-d H:i:s');
            $USERID = Auth::user()->id;
            $dbname = Auth::user()->dbname;
            
            $update_vendor_id = $request->vendor_id;
            $roster_id = $request->roster_id;
            $vendor_remarks = $request->remarks;
            $RS_VENDORCHANGE=MyHelper::$RS_VENDORCHANGE;
            $RS_ACTIVE = MyHelper::$RS_ACTIVE;
            $RS_INACTIVE =MyHelper::$RS_INACTIVE;

            $NEWESCORT = 0;
            $PASSENGER_ROSTERUPDATE = 0;

         $sql = "SELECT R.ROSTER_STATUS,if(R.ROSTER_STATUS & $RS_VENDORCHANGE,1,0) as vendorcheck,
        conv(bin(R.ROSTER_STATUS)+bin($RS_VENDORCHANGE),2,10) as upstatus,R.VENDOR_ID 
        FROM (SELECT ROSTER_STATUS,VENDOR_ID FROM rosters WHERE ACTIVE=$RS_ACTIVE and  ROSTER_ID=$roster_id) R";
       
        $rosterstatus = DB::connection("$dbname")->select($sql);
        $ROSTER_STATUS = $rosterstatus[0]->ROSTER_STATUS;
        $vendorcheck = $rosterstatus[0]->vendorcheck;
        $updatestatus = $rosterstatus[0]->upstatus;
        $OLDVENDORID = $rosterstatus[0]->VENDOR_ID;
        if($vendorcheck == $RS_ACTIVE)
        {
            $upstatus = $ROSTER_STATUS;
        }
        else
        {
            if($updatestatus != 0){
                $upstatus = $updatestatus;
            }else{
               $upstatus = $ROSTER_STATUS; 
            }
        }
        $result1 = Roster::on("$dbname")->where([["ROSTER_ID", "=", $roster_id], ["ACTIVE", "=", "$RS_ACTIVE"]])
                ->update(array("ACTIVE" => $RS_INACTIVE, "ROSTER_STATUS" => $upstatus, "REMARKS" => $vendor_remarks, "UPDATED_BY" => $USERID));

        $dateset = Roster::on("$dbname")->where([['ROSTER_ID', "=", $roster_id], ["ACTIVE", "=", $RS_INACTIVE]])->get();
        $BRANCH_ID = $dateset[0]->BRANCH_ID;
        $ROUTE_ID = $dateset[0]->ROUTE_ID;
        $FILE_ID = $dateset[0]->FILE_ID;
        $ESTIMATE_START_TIME = $dateset[0]->ESTIMATE_START_TIME;
        $ACTUAL_START_TIME = $dateset[0]->ACTUAL_START_TIME;
        $ACTUAL_END_TIME = $dateset[0]->ACTUAL_END_TIME;
        $ESTIMATE_END_TIME = $dateset[0]->ESTIMATE_END_TIME;
        $TRIP_TYPE = $dateset[0]->TRIP_TYPE;
        $START_LOCATION = $dateset[0]->START_LOCATION;
        $END_LOCATION = $dateset[0]->END_LOCATION;
        $CAB_ID = $dateset[0]->CAB_ID;
        $ROSTER_ALLOT_TIME = $dateset[0]->ROSTER_ALLOT_TIME;
        $CAB_ALLOT_TIME = $dateset[0]->CAB_ALLOT_TIME;
        $PASSENGER_ALLOT_COUNT = $dateset[0]->PASSENGER_ALLOT_COUNT;
        $PASSENGER_ALLOT_IN_ROUT_COUNT = $dateset[0]->PASSENGER_ALLOT_IN_ROUT_COUNT;
        $PASSENGER_CLUBING_COUNT = $dateset[0]->PASSENGER_CLUBING_COUNT;
        $CAB_CAPACITY_COUNT = $dateset[0]->CAB_CAPACITY_COUNT;
        $TOTAL_KM = $dateset[0]->TOTAL_KM;
        $TRIP_APPROVED_KM = $dateset[0]->TRIP_APPROVED_KM;
        $ROSTER_STATUS = $dateset[0]->ROSTER_STATUS;

        $insert_roster = array("BRANCH_ID" => $BRANCH_ID, "ROUTE_ID" => $ROUTE_ID, "FILE_ID" => $FILE_ID, "ESTIMATE_START_TIME" => $ESTIMATE_START_TIME, "ACTUAL_START_TIME" => $ACTUAL_START_TIME, "ACTUAL_END_TIME" => $ACTUAL_END_TIME, "ESTIMATE_END_TIME" => $ESTIMATE_END_TIME, "TRIP_TYPE" => $TRIP_TYPE, "START_LOCATION" => $START_LOCATION, "END_LOCATION" => $END_LOCATION, "VENDOR_ID" => $update_vendor_id, "CAB_ID" => $CAB_ID, "ROSTER_ALLOT_TIME" => $ROSTER_ALLOT_TIME, "CAB_ALLOT_TIME" => $CAB_ALLOT_TIME, "PASSENGER_ALLOT_COUNT" => $PASSENGER_ALLOT_COUNT, "PASSENGER_ALLOT_IN_ROUT_COUNT" => $PASSENGER_ALLOT_IN_ROUT_COUNT, "PASSENGER_CLUBING_COUNT" => $PASSENGER_CLUBING_COUNT, "CAB_CAPACITY_COUNT" => $CAB_CAPACITY_COUNT, "TOTAL_KM" => $TOTAL_KM, "TRIP_APPROVED_KM" => $TRIP_APPROVED_KM, "ROSTER_STATUS" => $ROSTER_STATUS, "ACTIVE" => $RS_ACTIVE, "CREATED_BY" => $USERID, "created_at" => $currtDateTime);

        $result = Roster::on("$dbname")->insert($insert_roster);
        $lastInsertId = DB::connection("$dbname")->getPdo()->lastInsertId();
         $PASSENGER_ROSTERUPDATE = RosterPassenger::on("$dbname")->where([["ROSTER_ID", "=", $roster_id],['ACTIVE', '=', $RS_ACTIVE]])
                ->update(array("ROSTER_ID" => $lastInsertId ));
         $escortcheck = DB::connection("$dbname")->select("select count(*) as ttlcnt from route_escorts WHERE ROSTER_ID ='$roster_id'");
         $ttlcnt = $escortcheck[0]->ttlcnt;
         if($ttlcnt != 0)
         {
             $NEWESCORT = RouteEscorts::on("$dbname")->where([["ROSTER_ID", "=", $roster_id]])->update(array("ROSTER_ID" => $lastInsertId ));
         }
         
        $date_f = $this->commonFunction->date_format_add();
        $log_arr = array("BRANCH_ID"=>$BRANCH_ID,"VENDOR_ID"=>$update_vendor_id,"ACTION"=>'WEB VENDOR CHANGE',"CATEGORY"=>'TRACKING DASHBOARD',"UPDATE_ROSTER"=>$result1,"UPDATE_ROSTER_STATUS"=>$upstatus,"NEW VENDOR ID" => $update_vendor_id,"OLD ROSTER ID"=>$roster_id,"OLD VENDOR ID"=> $OLDVENDORID,"VENDOR CHANGE REMARKS"=>$vendor_remarks,"NEW ROSTER ID"=>$lastInsertId,"ESCORT INSERT"=>$NEWESCORT,"PASSENGER ROSTER UPDATE"=>$PASSENGER_ROSTERUPDATE,"USER_ID"=> $USERID,"PROCESS_DATE"=>$date_f);

         // $ret=$this->commonFunction->weblogs($log_arr);


           
            // $roster = Roster::where('ACTIVE', MyHelper::$RS_ACTIVE)
            //    // ->where('BRANCH_ID', $authUser->BRANCH_ID)
            //     ->findOrFail($roster_id);

            // $roster->update([
            //     'REMARKS' => $request->input('remark'),
            //     'VENDOR_ID' => $vendor_id,
            //     'UPDATED_BY' => Auth::user()->id,
            //     'UPDATED_AT' => $date->format("Y-m-d H:i:s"),
            // ]);

            DB::commit();

                return response([
                    'success' => true,
                    'status' => 3,
                    'Vendor' =>'Vendor Update Successfully',
                ]);
			
		}
		catch (\Throwable|\Exception $e) {
            DB::rollBack();
            $this->commonFunction->logException($e);
           
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'No data',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }
   
	
    public function roster_reset($request): FoundationApplication|Response|ResponseFactory 
    {
        try
        {
           // print_r($request->all());exit;
             $reset_type=$request->reset_type;
          
            $resetrouteid=$request->reset_roster_id;
            $reset_reason=$request->reset_reason;
            $currtDateTime = date('Y-m-d H:i:s');
            $USERID = Auth::user()->id;
            $branchid = Auth::user()->BRANCH_ID;
            $vendorid = Auth::user()->vendor_id;
            $dbname = Auth::user()->dbname;
            $RS_VENDORCHANGE = Myhelper::$RS_VENDORCHANGE;
            $RS_BILLABLE = Myhelper::$RS_BILLABLE;
            $RS_CABALLOT = Myhelper::$RS_CABALLOT;
            $RS_ACCEPT = Myhelper::$RS_ACCEPT;
            $RS_REJECT = Myhelper::$RS_REJECT;
            $RS_ACTIVE = Myhelper::$RS_ACTIVE;
           // $UT_OPTION = Myhelper::$UT_OPTION;
            $TD_ALLOTED = Myhelper::$TD_ALLOTED;
            $TD_NORESPONSE = Myhelper::$TD_NORESPONSE;
            $TD_TRIPNOTEXECUTED = Myhelper::$TD_TRIPNOTEXECUTED;
            $TD_TRIPACCEPTED = Myhelper::$TD_TRIPACCEPTED;
            $TD_TRIPREJECTED = Myhelper::$TD_TRIPREJECTED;
            
            $RS_TOTALALLOT = Myhelper::$RS_TOTALALLOT;
            $RS_TOTALACCEPT = Myhelper::$RS_TOTALACCEPT;
            $RS_TOTALREJECT = Myhelper::$RS_TOTALREJECT;
            $ttlresetstatus = $RS_TOTALALLOT.','.$RS_TOTALACCEPT.','.$RS_TOTALREJECT;
            $checkdata = explode(",",$ttlresetstatus);
            $VERIFIEDSTATUS = 2;
            $resetmasknum = 0;
            $ROSTER_PASSENGER_ID = 0;
            $otpverify = 0;
            $otpverifycnt = 0;
            $caballotsts = 0;
            $cabid = 0;
            $update_passenger_mask = 0;
            $update_roster_mask = 0;
           // $CommonController = new CommonController;
            $NOTIFYINSERT = array();
            DB::beginTransaction();
            if ($reset_type == $TD_ALLOTED || $reset_type == $TD_NORESPONSE) {
                
                
               // $resetmasknum = $CommonController->ResetMasking($resetrouteid);
                
                 $sql1 = "SELECT if(T.vendorchange=1,3,1) as rosterstatus,T.CAB_ID,T.ROSTER_STATUS
                FROM (SELECT if(ROSTER_STATUS & 2,1,0) as vendorchange,ROSTER_STATUS,CAB_ID FROM rosters WHERE ROSTER_ID='$resetrouteid'
                and BRANCH_ID='$branchid' and ACTIVE=$RS_ACTIVE) T ";
                $rs1 = DB::connection("$dbname")->select($sql1);
                $roster_status = $rs1[0]->rosterstatus;
                $existstatus = $rs1[0]->ROSTER_STATUS;
                
                if($roster_status != 0){
                    $rosterstatus = $roster_status;
                }else{
                    $rosterstatus = $existstatus;
                }
                $result3 = Roster::on("$dbname")->where([["ROSTER_ID", "=", $resetrouteid], ["BRANCH_ID", "=", $branchid], ["ACTIVE", "=", $RS_ACTIVE]])
                       ->update(array("ROSTER_STATUS" => $rosterstatus, "CAB_ID" => NULL, "ROSTER_ALLOT_TIME" => NULL, "CAB_ALLOT_TIME" => NULL,
                        "CAB_CAPACITY_COUNT" => 0, "UPDATED_BY" => $USERID));
                $rosterpassanger = RosterPassenger::on("$dbname")->where([["ROSTER_ID", "=", $resetrouteid],["ACTIVE", "=", $RS_ACTIVE]])->get();
                
                foreach($rosterpassanger as $value)
                {
                   $ROSTER_PASSENGER_ID = $value->ROSTER_PASSENGER_ID;
                   $EMPLOYEE_ID = $value->EMPLOYEE_ID;
                   $empdetails = $this->getempdetails($EMPLOYEE_ID);
                  
                   $MOBILEGCM = $empdetails[0]->MOBILE_GCM;
                   $MOBILE_CATEGORY = $empdetails[0]->MOBILE_CATEGORY;
                   $otpverify = OtpVerify::on("$dbname")->where([["ROSTER_PASSENGER_ID", '=', $ROSTER_PASSENGER_ID]])->update(array("VERIFIED_STATUS"=>$VERIFIEDSTATUS));
                   $otpverifycnt = $otpverifycnt + $otpverify;
                    if($MOBILEGCM != ''){
                        $NOTIFYINSERT[] = "('$branchid', '$EMPLOYEE_ID', '$MOBILEGCM', 'Trip Reset', 'Your cab has been changed.Soon you will get new cab details for your trip', '5','$MOBILE_CATEGORY','$currtDateTime')";
                    }
                }
                 $cabid = $rs1[0]->CAB_ID;
                 $sql = "SELECT D.MOBILE_GCM FROM cab C INNER JOIN devices D ON D.DEVICE_ID = C.DEVICE_ID WHERE C.CAB_ID = '$cabid'";
                $fcmid = DB::connection("$dbname")->select($sql);
                $MOBILEGCMCABID = $fcmid[0]->MOBILE_GCM?$fcmid[0]->MOBILE_GCM:'NULL';
                 $NOTIFYINSERT[] = "('$branchid', '$cabid', '$MOBILEGCMCABID', 'Trip Reset', 'Your trip was reset.Soon you get new trip', '5','ANDROID','$currtDateTime')";
                $caballotsts = Cab_allocation::on("$dbname")->where([["CAB_ID", "=", $cabid], ["ROSTER_ID", "=", $resetrouteid]])
                    ->update(array("ACCEPTANCE_REJECT_STATE" => 41, "ACCEPTANCE_REJECT_DATE_TIME" => $currtDateTime,
                "REJECT_REASON_ID" => $reset_reason, "ACTION" => 'Reset', "UPDATED_BY" => $USERID));
                
                
                
                //$sql1 = "UPDATE roster_passengers SET PASSENGER_MASK_NUMBER='--' WHERE ROSTER_ID=$resetrouteid";
                //$update_passenger_mask = DB::connection("$dbname")->update($sql1);
                $sql2 = "UPDATE rosters SET DRIVER_MASK_NUMBER = '--',MASK_ASSIGN_STATUS=0,UPDATED_BY = '$USERID' WHERE BRANCH_ID = $branchid AND ROSTER_ID=$resetrouteid";
                $update_roster_mask = DB::connection("$dbname")->update($sql2);
                
                $update_driver="update driver_billing_summary set ACTIVE=3,UPDATED_AT='".date('Y-m-d H:i:s')."',UPDATED_BY='".$USERID."' where ROSTER_ID='".$resetrouteid."' ";
                $update_driver_bill=DB::update($update_driver);
    //            exit;
            } else if ($reset_type == $TD_TRIPACCEPTED || $reset_type == $TD_TRIPNOTEXECUTED) {
                $sql1 = "SELECT ROSTER_STATUS,conv(bin(ROSTER_STATUS)-bin($RS_ACCEPT),2,10) as upstatus,CAB_ID FROM rosters WHERE BRANCH_ID='$branchid'
                and ROSTER_ID='$resetrouteid' and ACTIVE=$RS_ACTIVE";
                $rs1 = DB::connection("$dbname")->select($sql1);
                $roster_status = $rs1[0]->upstatus;
                $existstatus = $rs1[0]->ROSTER_STATUS;
                if(in_array($roster_status, $checkdata)){
                    $rosterstatus = $roster_status;
                }else{
                    $rosterstatus = $existstatus;
                }
    //            if($roster_status != 0){
    //                $rosterstatus = $roster_status;
    //            }else{
    //                $rosterstatus = $existstatus;
    //            }
                $result3 = Roster::on("$dbname")->where([["ROSTER_ID", "=", $resetrouteid], ["BRANCH_ID", "=", $branchid], ["ACTIVE", "=", $RS_ACTIVE]])->update(array("ROSTER_STATUS" => $rosterstatus, "UPDATED_BY" => $USERID));
                
                $update_driver="update driver_billing_summary set ACTIVE=1,UPDATED_AT='".date('Y-m-d H:i:s')."',UPDATED_BY='".$USERID."' where ROSTER_ID='".$resetrouteid."' ";
                    $update_driver_bill=DB::update($update_driver);
            } else if ($reset_type == $TD_TRIPREJECTED) {
                $sql1 = "SELECT ROSTER_STATUS,conv(bin(ROSTER_STATUS)-bin($RS_REJECT),2,10) as upstatus,CAB_ID FROM rosters WHERE BRANCH_ID='$branchid' and ROSTER_ID='$resetrouteid' and ACTIVE=$RS_ACTIVE";
                $rs1 = DB::connection("$dbname")->select($sql1);
                $roster_status = $rs1[0]->upstatus;
                $existstatus = $rs1[0]->ROSTER_STATUS;
                
                if(in_array($roster_status, $checkdata)){
                    $rosterstatus = $roster_status;
                }else{
                    $rosterstatus = $existstatus;
                }
    //            if($roster_status != 0){
    //                $rosterstatus = $roster_status;
    //            }else{
    //                $rosterstatus = $existstatus;
    //            }
                $result3 = Roster::on("$dbname")->where([["ROSTER_ID", "=", $resetrouteid], ["BRANCH_ID", "=", $branchid], ["ACTIVE", "=", $RS_ACTIVE]])
                        ->update(array("ROSTER_STATUS" => $rosterstatus, "UPDATED_BY" => $USERID));
                $update_driver="update driver_billing_summary set ACTIVE=1,UPDATED_AT='".date('Y-m-d H:i:s')."',UPDATED_BY='".$USERID."' where ROSTER_ID='".$resetrouteid."' ";
                    $update_driver_bill=DB::update($update_driver);
            }
            if(count($NOTIFYINSERT)>0)
            {
                 $cabnotification = "INSERT INTO `notification` (`branch_id`, `empid_cabid`, `fcm_id`, `heading`, `message`, `category_value`,`app_category`,`created_at`) VALUES ".implode(',', $NOTIFYINSERT);
                $insertnotifycnt = DB::connection("$dbname")->insert($cabnotification);
            }
            $date_f =  $this->commonFunction->date_format_add();
            
            $mask_enable =  $this->commonFunction->GetPropertyValue('CALL MASKING OPTION');
            //echo "test ".$mask_enable; exit;
            if($mask_enable=='Y')
            {
               // $mask= new MaskNumberClearController();
               // $mask->Clear_MaskNumber(0,$branchid,$resetrouteid,'Roster');
            }
            
            
            
            $log_arr = array("BRANCH_ID" => $branchid, "VENDOR_ID" => $vendorid, "ACTION" => "WEB RESET", "CATEGORY" => 'TRACKING DASHBOARD', "RESET_MASKNUMBER" => '--',"ROSTER_EXIST_STATUS"=>$existstatus, "ROSTER_UPDATE_STATUS" => $rosterstatus, "ROSTER_ID" => $resetrouteid, "OTP_PASSENGERID" => $ROSTER_PASSENGER_ID, "VERIFY_STATUS" => $VERIFIEDSTATUS, "OTP_VERIFIED" => $otpverifycnt, "CAB_ALLOT_RESET" => $caballotsts, "CABID" => $cabid,"PASSENGER_MASK_RESET" => $update_passenger_mask,"ROSTER_MASK_RESET"=>$update_roster_mask,"USER_ID" => $USERID, "PROCESS_DATE" => $date_f);
    
    
            //$ret = $CommonController->weblogs($log_arr);
            DB::commit();

            return response([
                'success' => true,
                'status' => 5,
                'reset' =>'Reset Update Successfully',
            ]);
       
    }
    catch (\Throwable|\Exception $e) {
        DB::rollBack();
        $this->commonFunction->logException($e);
       
        return response([
            'success' => false,
            'status' => 4,
            'message' => 'No data',
            'validation_controller' => true,
            'error' => $e->getMessage(),
        ], 500);
    }
       

    }

    public function roster_noshow($request): FoundationApplication|Response|ResponseFactory 
    {
        try
        {
            //($emp_reason_id, $roster_passenger_id,$sts)
          //  print_r($request->all());exit;
         $emp_reason_id=$request->emp_reason_id;
         $roster_passenger_id=$request->roster_passenger_id;
         $sts=$request->sts;
           $result1 = 0;
           $Empcnt_Decrement = 0;
           $EscortRemove = 0;
           $RemoveMaskno = 0;
           $NewEscort = 0;
           $NOSHOW_TIME_UPDATE = 0;
           $sql2 = 0;
           $Cab_Allocation = 0;
           $SENDNOSHOWSMS = 0;
           $curdatetime = date('Y-m-d H:i:s');
           $userid = Auth::user()->id;
           $BRANCH_ID = Auth::user()->BRANCH_ID;
           $vendorid = Auth::user()->vendor_id;
           $dbname = Auth::user()->dbname;
           $RPS_NOSHOW = MyHelper::$RPS_NOSHOW;
           $RPS_TTLNOSHOW = MyHelper::$RPS_TTLNOSHOW;
           $ESCORT_NEW = MyHelper::$ESCORT_NEW;
           $AES_KEY = env("AES_ENCRYPT_KEY");
           $ESCORT_REMOVE = MyHelper::$ESCORT_REMOVE;
           $RS_ACTIVE = MyHelper::$RS_ACTIVE;
           $RS_AUTOCANCEL = MyHelper::$RS_AUTOCANCEL;
           $RP_DROPROUTE = MyHelper::$RP_DROPROUTE;
           $RS_MANUALTRIPCLOSE = MyHelper::$RS_MANUALTRIPCLOSE;
           $RS_MGENDER = MyHelper::$RS_MGENDER;
           $RS_FGENDER = MyHelper::$RS_FGENDER;
           $RS_MANUALTRIPCLOSE = MyHelper::$RS_MANUALTRIPCLOSE;
           $PR_ESCORT_ENABLE = MyHelper::$PR_ESCORT_ENABLE;
           $PR_ESCORT_START_TIME = MyHelper::$PR_ESCORT_START_TIME;
           $PR_ESCORT_END_TIME = MyHelper::$PR_ESCORT_END_TIME;
           $PR_HELPLINENO = MyHelper::$PR_HELPLINENO;
           
           $propertie = property::on("$dbname")->where([['BRANCH_ID', '=', $BRANCH_ID], ['ACTIVE', '=', $RS_ACTIVE]])->get();
   
           for ($ii = 0; $ii < count($propertie); $ii++)
           {
   
               $PROPERTIE_NAME = $propertie[$ii]->PROPERTIE_NAME;
               $PROPERTIE_VALUE = $propertie[$ii]->PROPERTIE_VALUE;
   
               switch ($PROPERTIE_NAME) {
                   case $PR_ESCORT_ENABLE:
                       $PR_ESCORTENABLE = $PROPERTIE_VALUE;
                       break;
                   case $PR_ESCORT_START_TIME:
                       $PR_ESCORTSTART_TIME = $PROPERTIE_VALUE;
                       break;
                   case $PR_ESCORT_END_TIME:
                       $PR_ESCORTEND_TIME = $PROPERTIE_VALUE;
                       break;
                   case 'SMS_ALERT':
                       $PR_SMS_ALERT = $PROPERTIE_VALUE;
                       break;
                   case 'SMS_LAST_TAG':
                       $SMS_LAST_TAG = $PROPERTIE_VALUE;
                       break;
                   case $PR_HELPLINENO:
                       $HELP_LINE = $PROPERTIE_VALUE;
                       break;
                   default:
                       break;
               }
           }
           
           $sql = "SELECT conv(bin(RP.ROSTER_PASSENGER_STATUS)+bin($RPS_NOSHOW),2,10) as upstatus,RP.ROSTER_ID,RP.ROSTER_PASSENGER_ID,R.ROUTE_ID,RP.EMPLOYEE_ID,
           if(R.TRIP_TYPE = 'P',R.ESTIMATE_END_TIME,R.ESTIMATE_START_TIME) AS LOGINDATETIME,R.TRIP_TYPE,
           if(R.PASSENGER_CLUBING_COUNT > 0,'PASSENGER_CLUBING_COUNT','PASSENGER_ALLOT_COUNT') AS decrementfield,E.MOBILE,B.BRANCH_NAME,
           VE.VEHICLE_REG_NO,if(R.ROSTER_STATUS & 64,1,0) as executestatus,E.MOBILE_GCM,E.MOBILE_CATEGORY,E.SMS_ALERT,R.CAB_ID,L.LOCATION_NAME,
           E.NAME as empname,D.MOBILE_GCM AS DRIVERGCMID
           FROM roster_passengers RP 
           INNER JOIN rosters R ON R.ROSTER_ID = RP.ROSTER_ID
           INNER JOIN employees E on E.EMPLOYEES_ID = RP.EMPLOYEE_ID AND E.BRANCH_ID = $BRANCH_ID
           INNER JOIN branch B on B.BRANCH_ID = R.BRANCH_ID
           LEFT JOIN cab C ON C.CAB_ID = R.CAB_ID
           LEFT JOIN devices D ON D.DEVICE_ID = C.DEVICE_ID
           LEFT JOIN vehicles VE ON VE.VEHICLE_ID = C.VEHICLE_ID
           LEFT JOIN locations L ON L.LOCATION_ID = RP.LOCATION_ID 
           WHERE RP.ACTIVE = $RS_ACTIVE AND RP.ROSTER_PASSENGER_ID='$roster_passenger_id'";
           
           $rosterstatus = DB::connection("$dbname")->select($sql);
           //print_r($rosterstatus);exit;

           $upstatus = $rosterstatus[0]->upstatus;
           $ROSTER_ID = $rosterstatus[0]->ROSTER_ID;
           $TRIP_TYPE = $rosterstatus[0]->TRIP_TYPE;
           $executestatus = $rosterstatus[0]->executestatus;
           $EMP_LOCATION_NAME = $rosterstatus[0]->LOCATION_NAME;
           $MOBILE_GCM = $rosterstatus[0]->MOBILE_GCM;
           $MOBILE_CATEGORY = $rosterstatus[0]->MOBILE_CATEGORY;
           $TRIPID = $rosterstatus[0]->ROUTE_ID;
           $EMPLOYEE_ID = $rosterstatus[0]->EMPLOYEE_ID;
           $DRIVER_CAB_ID = $rosterstatus[0]->CAB_ID;
           $DRIVERGCMID = $rosterstatus[0]->DRIVERGCMID;
           $empname = $rosterstatus[0]->empname;
           $empmobile = $rosterstatus[0]->MOBILE;
           $VEHICLEREGNO = $rosterstatus[0]->VEHICLE_REG_NO;
           $LOGINDATETIME = $rosterstatus[0]->LOGINDATETIME;
           $BRANCH_NAME = $rosterstatus[0]->BRANCH_NAME;
           $decrementfield = $rosterstatus[0]->decrementfield;
           $SMS_ALERT = $rosterstatus[0]->SMS_ALERT;
           $ROSTER_PASSENGER_ID = $rosterstatus[0]->ROSTER_PASSENGER_ID;
          
           //$EMP_NAME = $this->commonFunction->AES_DECRYPT($empname, $AES_KEY);
           $EMP_NAME ="AKS";
           
          if($sts=='NOSHOW')
           {
               $result1 = RosterPassenger::where([["ROSTER_PASSENGER_ID", "=", $roster_passenger_id],['ACTIVE', '=', $RS_ACTIVE]])
                       ->update(array("ROSTER_PASSENGER_STATUS" => $upstatus));
               $Empcnt_Decrement = Roster::where([["ROSTER_ID", "=", $ROSTER_ID],["ACTIVE", "=", $RS_ACTIVE]])->decrement("$decrementfield");
               Roster::where([["ROSTER_ID", "=", $ROSTER_ID], ["ACTIVE", "=", $RS_ACTIVE]])->increment("PASSENGER_NOSHOW_COUNT");
               DB::update("update driver_billing_summary set UPDATED_AT='".date('Y-m-d H:i:s')."',FIRST_POINT_DATETIME='".date("Y-m-d H:i:s")."',FIRST_POINT_LAT='0',FIRST_POINT_LONG='0' where FIRST_POINT_LAT is NULL and FIRST_POINT_LONG is NULL and  FIRST_POINT_DATETIME is NULL and ROSTER_ID='".$ROSTER_ID."'");
           }
           else if($sts=='DEACTIVE')
           {
               $result1 = RosterPassenger::where([["ROSTER_PASSENGER_ID", "=", $roster_passenger_id],['ACTIVE', '=', $RS_ACTIVE]])
                       ->update(array("ACTIVE" => 3));
               $Empcnt_Decrement = Roster::where([["ROSTER_ID", "=", $ROSTER_ID],["ACTIVE", "=", $RS_ACTIVE]])->decrement("$decrementfield");
               Roster::where([["ROSTER_ID", "=", $ROSTER_ID], ["ACTIVE", "=", $RS_ACTIVE]])->increment("PASSENGER_DEACTIVE_COUNT");
               
               $mask_enable = $this->commonFunction->GetPropertyValue('CALL MASKING OPTION');
               
                   if($mask_enable=='Y')
                   {
                       
                      // $mask= new MaskNumberClearController();
                       //$mask->Clear_MaskNumber($ROSTER_PASSENGER_ID,$BRANCH_ID,$ROSTER_ID,'Passenger');
                   }
               
           }
           else
           {
               $result1 = RosterPassenger::where([["ROSTER_PASSENGER_ID", "=", $roster_passenger_id],['ACTIVE', '=', $RS_ACTIVE]])
                       ->update(array("ROSTER_PASSENGER_STATUS" => $upstatus));
               $Empcnt_Decrement = Roster::where([["ROSTER_ID", "=", $ROSTER_ID],["ACTIVE", "=", $RS_ACTIVE]])->decrement("$decrementfield");
               Roster::where([["ROSTER_ID", "=", $ROSTER_ID], ["ACTIVE", "=", $RS_ACTIVE]])->increment("PASSENGER_NOSHOW_COUNT");
           }
           if($executestatus ==1 && $sts=='NOSHOW'){
               $SENDSMSTIME = date('Y-m-d H:i:s');
               $notifymsg = "You are noshow for this route($TRIPID-$LOGINDATETIME)";
               $notifymsg1 = "$EMP_NAME - $EMP_LOCATION_NAME was noshow in your trip";
               $cabnotification = "INSERT INTO `notification` (`branch_id`, `empid_cabid`, `fcm_id`, `heading`, `message`, `category_value`,`app_category`,`created_at`) VALUES ('$BRANCH_ID', '$EMPLOYEE_ID', '$MOBILE_GCM', 'Noshow', '$notifymsg', '3','$MOBILE_CATEGORY','$SENDSMSTIME')";
           $insert_notify = DB::connection("$dbname")->insert($cabnotification);
               $cabnotification1 = "INSERT INTO `notification` (`branch_id`, `empid_cabid`, `fcm_id`, `heading`, `message`, `category_value`,`app_category`,`created_at`) VALUES ('$BRANCH_ID', '$DRIVER_CAB_ID', '$DRIVERGCMID', 'Emp Noshow', '$notifymsg1', '1','ANDROID','$SENDSMSTIME')";
               $insert_notify1 = DB::connection("$dbname")->insert($cabnotification1);
           }
           
           $insert_reason = array("ROSTER_PASSENGER_ID" => $roster_passenger_id, "REASON_ID" => $emp_reason_id, "CREATE_BY" => $userid, "CREATED_DATE" => $curdatetime);
           $result = Reason_Log::on("$dbname")->insert($insert_reason);
           
           $check_escort = "SELECT COUNT(*) as ttlcnt FROM route_escorts WHERE ROSTER_ID='$ROSTER_ID' AND EMPLOYEE_ID='$EMPLOYEE_ID'";
           $checkescort = DB::connection("$dbname")->select($check_escort);
           $ttlcnt = $checkescort[0]->ttlcnt;
           if($ttlcnt != 0)
           {
              $EscortRemove = RouteEscorts::on("$dbname")->where([["ROSTER_ID", "=", $ROSTER_ID],["EMPLOYEE_ID", "=", $EMPLOYEE_ID]])->update(array("ESCORT_ID" => NULL, "STATUS" => $ESCORT_REMOVE)); 
           }
           $escort_check = "SELECT R.TRIP_TYPE,if(R.CAB_ID is NULL,0,R.CAB_ID) AS CABID,rp.ROSTER_ID,rp.EMPLOYEE_ID,e.BRANCH_ID,e.GENDER,
           rp.EMPLOYEE_ID,rp.ROSTER_PASSENGER_STATUS,R.PASSENGER_CLUBING_COUNT,R.PASSENGER_ALLOT_COUNT,R.ESTIMATE_END_TIME,R.ESTIMATE_START_TIME
           ,R.PASSENGER_ALLOT_IN_ROUT_COUNT
           FROM roster_passengers as rp
           INNER JOIN rosters R ON R.ROSTER_ID = '$ROSTER_ID'
           INNER JOIN employees as e on e.EMPLOYEES_ID=rp.EMPLOYEE_ID AND e.BRANCH_ID = $BRANCH_ID
           WHERE R.BRANCH_ID = $BRANCH_ID and rp.ROSTER_ID = '$ROSTER_ID' and R.ACTIVE = $RS_ACTIVE and rp.ROSTER_PASSENGER_STATUS NOT IN ($RPS_TTLNOSHOW) and rp.ACTIVE = $RS_ACTIVE
            ORDER BY rp.ROUTE_ORDER desc LIMIT 1";
           $escortcheck = DB::connection("$dbname")->select($escort_check);
           $count = count($escortcheck);
           $TTLCNTEMP = 0;
           if($count != 0)
           {
               $GENDER = $escortcheck[0]->GENDER;
               $EEMPLOYEE_ID = $escortcheck[0]->EMPLOYEE_ID;
               $TRIP_TYPE = $escortcheck[0]->TRIP_TYPE;
               $CABID = $escortcheck[0]->CABID;
               $ESTIMATE_END_TIME = $escortcheck[0]->ESTIMATE_END_TIME;
               $ESTIMATE_START_TIME = $escortcheck[0]->ESTIMATE_START_TIME;
               $PASSENGER_CLUBING_COUNT = $escortcheck[0]->PASSENGER_CLUBING_COUNT;
               $PASSENGER_ALLOT_COUNT = $escortcheck[0]->PASSENGER_ALLOT_COUNT;
               $PASSENGER_ALLOT_IN_ROUT_COUNT = $escortcheck[0]->PASSENGER_ALLOT_IN_ROUT_COUNT;
               $TTLCNTEMP = $PASSENGER_CLUBING_COUNT + $PASSENGER_ALLOT_COUNT;
               if($CABID != 0){
                   /* Call masking disable */
                   
                   //$RemoveMaskno = $CommonController->property_check($roster_passenger_id);
                   
                   $mask_enable = $this->commonFunction->GetPropertyValue('CALL MASKING OPTION');
                       if($mask_enable=='Y')
                       {
                           $mask= new MaskNumberClearController();
                           $mask->Clear_MaskNumber($roster_passenger_id,$BRANCH_ID,$ROSTER_ID,'Passenger');
                       }
                   /* Call masking disable */
               }
               if($GENDER == $RS_FGENDER){
                   $CHECKESCORT_TIME = '';
                   if($TRIP_TYPE == $RP_DROPROUTE){
                       $CHECKESCORT_TIME = $ESTIMATE_START_TIME;
                   }else{
                       $CHECKESCORT_TIME = $ESTIMATE_END_TIME;
                   }
                   $isEscort = '';
                   $is_Escort_Time = date('H:i:s', strtotime($CHECKESCORT_TIME));
                   
                   if ($PR_ESCORTENABLE == 'Y') {
                       $ret1 = $this->check_time($PR_ESCORTSTART_TIME, $PR_ESCORTEND_TIME, $is_Escort_Time) ? "yes" : "no";
                       if ($ret1 == "yes") {
                           $isEscort = 'true';
                       }
                   }
                   if($isEscort == 'true'){
                       $checkescort = "SELECT COUNT(*) as ttlcnt FROM route_escorts WHERE ROSTER_ID='$ROSTER_ID' AND EMPLOYEE_ID='$EEMPLOYEE_ID' and STATUS='".$ESCORT_NEW."'";
                       $checkescorts = DB::connection("$dbname")->select($checkescort);
                       $ttlcnts = $checkescorts[0]->ttlcnt;
                       if($ttlcnts == 0 && $PASSENGER_ALLOT_IN_ROUT_COUNT==0 && $TRIP_TYPE == env('RP_PICKROUTE')){
                       $insert_escort = array("BRANCH_ID" => $BRANCH_ID, "ROSTER_ID" => $ROSTER_ID, "EMPLOYEE_ID" => $EEMPLOYEE_ID, "STATUS" => $ESCORT_NEW,
                           "CREATED_BY" => $userid, "created_at" => date('Y-m-d H:i:s'));
                       RouteEscorts::on("$dbname")->insert($insert_escort);
                       $NewEscort = DB::connection("$dbname")->getPdo()->lastInsertId();
                       }
                       else if($ttlcnts == 0  && $TRIP_TYPE == $RP_DROPROUTE){
                       $insert_escort = array("BRANCH_ID" => $BRANCH_ID, "ROSTER_ID" => $ROSTER_ID, "EMPLOYEE_ID" => $EEMPLOYEE_ID, "STATUS" => $ESCORT_NEW,
                           "CREATED_BY" => $userid, "created_at" => date('Y-m-d H:i:s'));
                       RouteEscorts::on("$dbname")->insert($insert_escort);
                       $NewEscort = DB::connection("$dbname")->getPdo()->lastInsertId();
                       }
                   }
               }
               
           }
           else
           {
               $roster_sts = "SELECT TRIP_TYPE,if(CAB_ID is NULL,0,CAB_ID) AS CABID,PASSENGER_CLUBING_COUNT,PASSENGER_ALLOT_COUNT FROM rosters WHERE ROSTER_ID = '$ROSTER_ID' AND ACTIVE = $RS_ACTIVE";
               $rostersts = DB::connection("$dbname")->select($roster_sts);
               $TRIP_TYPE = $rostersts[0]->TRIP_TYPE;
               $CABID = $rostersts[0]->CABID;
           }
           
           if($TRIP_TYPE == $RP_DROPROUTE)
           {
               $PICKDROPNOSHOW = "ACTUAL_END_TIME";
           }
           else{
               $PICKDROPNOSHOW = "ACTUAL_START_TIME";  
           }
           $NOSHOW_TIME_UPDATE = RosterPassenger::on("$dbname")->where([["ROSTER_PASSENGER_ID", "=", $roster_passenger_id],['ACTIVE', '=', $RS_ACTIVE]])
                   ->update(array($PICKDROPNOSHOW => date('Y-m-d H:i:s')));
           
           
           
           $sql1 = "SELECT count(*) as empcount,SUM(if(ROSTER_PASSENGER_STATUS & $RPS_NOSHOW,1,0)) as pendingcnt FROM roster_passengers WHERE
           ROSTER_ID = '$ROSTER_ID' AND ACTIVE ='$RS_ACTIVE'";
           $RPstatus = DB::connection("$dbname")->select($sql1);
           $RPCNTSTS = count($RPstatus);
           if($RPCNTSTS != 0){
               $empcount = $RPstatus[0]->empcount;
               $pendingcnt = $RPstatus[0]->pendingcnt!=''?$RPstatus[0]->pendingcnt:0;
           }
            $status = $ROSTER_ID;
           if($empcount == $pendingcnt && $CABID == 0)
           {
               $sql2 =  DB::connection("$dbname")->update("UPDATE rosters SET ROSTER_STATUS = ROSTER_STATUS + $RS_AUTOCANCEL WHERE ROSTER_ID = $ROSTER_ID");
               $status = 0;
           }
           else
           { 
               if($empcount == $pendingcnt && $TRIP_TYPE == $RP_DROPROUTE)
               {
                   $sql2 =  DB::connection("$dbname")->update("UPDATE rosters SET ROSTER_STATUS = ROSTER_STATUS + $RS_AUTOCANCEL WHERE ROSTER_ID = $ROSTER_ID");
                   $status = 0;
                   if($CABID != 0)
                   {
                       $Cab_Allocation = DB::connection("$dbname")->update("UPDATE `cab_allocation`  SET ACCEPTANCE_REJECT_STATE = ACCEPTANCE_REJECT_STATE + $RS_AUTOCANCEL
                        WHERE ROSTER_ID = $ROSTER_ID AND CAB_ID = $CABID");
                   }
               }else if($empcount == $pendingcnt){
                   $sql2 =  DB::connection("$dbname")->update("UPDATE rosters SET ROSTER_STATUS = ROSTER_STATUS + $RS_MANUALTRIPCLOSE WHERE ROSTER_ID = $ROSTER_ID");
                   $status = 0;
                   if($CABID != 0)
                   {
                      $Cab_Allocation = DB::connection("$dbname")->update("UPDATE `cab_allocation`  SET ACCEPTANCE_REJECT_STATE = ACCEPTANCE_REJECT_STATE + $RS_MANUALTRIPCLOSE
                        WHERE ROSTER_ID = $ROSTER_ID AND CAB_ID = $CABID");
                   }
               }
           }
           
           $SENDSMSTIME = date('Y-m-d H:i:s');
           $SMSTAG = $this->commonFunction->GetPropertyValue('SMS TAG');
           $AES_KEY = env("AES_ENCRYPT_KEY");
           //$emp_mobile = $this->commonFunction->AES_DECRYPT($empmobile,$AES_KEY);
           $emp_mobile=9543417214;
           if($VEHICLEREGNO != ''){
               $TRIPDET = $TRIPID.', Cab No '.$VEHICLEREGNO;
           }else{
               $TRIPDET = $TRIPID;
           }
           if($BRANCH_ID==32 )
           {
               //$message="Hi $emp_name, you have been marked as NO SHOW for your trip from office/Home at ".date("H:i",strtotime($LOGINDATETIME))." for ".date("d/m/Y",strtotime($LOGINDATETIME)).". Need Support? Helpline - $helpline_no.".$SMS_LAST_TAG;
               if($sts=='NOSHOW')
               {
                   $message = "Hi $EMP_NAME, you have been marked as NO SHOW for your trip from office/Home at ".date("H:i",strtotime($LOGINDATETIME))." for ".date("d/m/Y",strtotime($LOGINDATETIME)).". Need Support? Helpline - $HELP_LINE. ".$SMS_LAST_TAG;
               }
               elseif($sts=='DEACTIVE')
               {
                   
                   $message ="Hi $EMP_NAME, your cab booking for pickup/drop has been cancelled for ".date("d/m/Y",strtotime($LOGINDATETIME)).".  Need support? - Helpline - $HELP_LINE. ".$SMS_LAST_TAG;
                   
               }
               else
               {
                   
                   $message = "Hi $EMP_NAME, you have been marked as 'NO SHOW' for your trip from office/Home at ".date("H:i",strtotime($LOGINDATETIME))." for ".date("d/m/Y",strtotime($LOGINDATETIME)).". Need Support? Helpline - $HELP_LINE. ".$SMS_LAST_TAG;
               }
           }
           else{
              
              if($sts=='NOSHOW')
               {
                   $message="Greetings from $BRANCH_NAME, you are noshow for the trip id $TRIPID on $LOGINDATETIME.$SMS_LAST_TAG";
                   //$message = 'Greetings from '.$BRANCH_NAME.', you are "No show" for the Trip '.$TRIPDET.', on '.$LOGINDATETIME;
               }
               elseif($sts=='DEACTIVE')
               {
                   $message="Greetings from $BRANCH_NAME, you are cancel for the trip id $TRIPID on $LOGINDATETIME.$SMS_LAST_TAG";
                   //$message = 'Greetings from '.$BRANCH_NAME.', you are "Cancel" for the Trip '.$TRIPDET.', on '.$LOGINDATETIME;
               }
               else
               {
                   $message = 'Greetings from '.$BRANCH_NAME.', you are "No show" for the Trip '.$TRIPDET.', on '.$LOGINDATETIME;
               }
           }
           $noshow_sms = array("BRANCH_ID" => $BRANCH_ID, "ORIGINATOR" => $SMSTAG, "RECIPIENT" => $emp_mobile, "MESSAGE" => $message,
                           "SENT_DATE" => '1900-01-01 00:00:00', "REF_NO" => '--', "CREATED_BY" => $userid, "CREATED_DATE" => $SENDSMSTIME);
           if($SMS_ALERT==1 && $PR_SMS_ALERT==1)
           {	
               $SENDNOSHOWSMS = Sms::insert($noshow_sms);
           }
           
           $this->commonFunction->noshow_change_roster_approve_km($ROSTER_ID,$TRIP_TYPE);
           // Web Noshow log 
           $date_f =  $this->commonFunction->date_format_add();
           $log_arr = array("BRANCH_ID"=>$BRANCH_ID,"VENDOR_ID"=>$vendorid,"ACTION"=>'WEB NOSHOW',"CATEGORY"=>'TRACKING DASHBOARD',"NOSHOW_PASSENGERID"=>$roster_passenger_id,"REASON_ID"=>$emp_reason_id,"ROSTER_PASSENGERSTATUS" => $upstatus,"ROSTER_PASSANGERSTATUS_UPDATE"=>$result1,"ROSTER_DECREMENT"=>$Empcnt_Decrement,"REASON_LOG"=>$result,"ESCORT_REMOVE"=>$EscortRemove,"REMOVE_MASKNUMBER"=>$RemoveMaskno,"NEW_ESCORT_INSERT"=>$NewEscort,"NOSHOW_TIME_UPDATE"=>$NOSHOW_TIME_UPDATE,"ROSTER_AUTOCANCEL"=>$sql2,"CAB_ALLOCATION_UPDATE"=>$Cab_Allocation,"SEND_NOSHOWSMS"=>$SENDNOSHOWSMS,"USER_ID"=>$userid,"PROCESS_DATE"=>$date_f);
           //$ret=$CommonController->weblogs($log_arr);
           
           // Web Noshow log End 
           
            DB::commit();

            return response([
                'success' => true,
                'status' => 5,
                'noshow' =>$sts.'Update Successfully',
            ]);
       
    }
    catch (\Throwable|\Exception $e) {
        DB::rollBack();
        $this->commonFunction->logException($e);
       
        return response([
            'success' => false,
            'status' => 4,
            'message' => 'No data',
            'validation_controller' => true,
            'error' => $e->getMessage(),
        ], 500);
    }
       

    }

	public function empclubmob($request) : FoundationApplication|Response|ResponseFactory 
    {
        try
        {
        $roster_id=$request->roster_id;
        $BRANCH_ID = Auth::user()->BRANCH_ID;
        $RS_ACTIVE = MyHelper::$RS_ACTIVE;
        $dbname = Auth::user()->dbname;
        // $sql = "SELECT EMPLOYEE_ID FROM roster_passengers WHERE ROSTER_ID=$roster_id and ACTIVE ='".$RS_ACTIVE."' ";
        // $result = DB::connection("$dbname")->select($sql);

        $result = DB::connection($dbname)->table('roster_passengers')
        ->where('ACTIVE', $RS_ACTIVE)
        ->where('ROSTER_ID', $roster_id)
        ->select('EMPLOYEE_ID')->get();

        $count = count($result);
	    $EMPID = 'N/A';
        for($i=0;$i<$count;$i++)
        {
            if($i == 0)
            {
                $EMPID = "'".$result[$i]->EMPLOYEE_ID."'";
            }
            else
            {
                $EMPID = $EMPID.",'".$result[$i]->EMPLOYEE_ID."'";
            }
            
        }
        // $sql1 = "SELECT EMPLOYEES_ID,`NAME` AS EMPNAME,MOBILE FROM employees WHERE 
        // BRANCH_ID=$BRANCH_ID AND ACTIVE ='".$RS_ACTIVE."' AND EMPLOYEES_ID NOT IN ($EMPID)";
        // $rs1 = DB::connection("$dbname")->select($sql1);
        // return $rs1;
        $employees = DB::connection($dbname)->table('employees')
            ->select('EMPLOYEES_ID', 'NAME as EMPNAME', 'MOBILE')
            ->where('BRANCH_ID', $BRANCH_ID)
            ->where('ACTIVE', $RS_ACTIVE)
            ->whereNotIn('EMPLOYEES_ID', explode(',', $EMPID)) // Assuming $EMPID is a comma-separated string
            ->get();
           
            return response([
                'success' => true,
                'status' => 5,
                'employees' =>$employees,
            ]);
    }
    catch (\Throwable|\Exception $e) {
        DB::rollBack();
        $this->commonFunction->logException($e);
       
        return response([
            'success' => false,
            'status' => 4,
            'message' => 'No data',
            'validation_controller' => true,
            'error' => $e->getMessage(),
        ], 500);
    }
    }
	public function club_emp_details($request) : FoundationApplication|Response|ResponseFactory 
    {
        try
        {
        $empid=$request->emp_id;
        $login_datetime=$request->login_datetime;
        $trip_type=$request->trip_type;
        $BRANCH_ID = Auth::user()->BRANCH_ID;
        $RS_ACTIVE = MyHelper::$RS_ACTIVE;
        $dbname = Auth::user()->dbname;
        $rosterid = "SELECT RP.ROSTER_ID,RP.EMPLOYEE_ID FROM roster_passengers RP
		inner join rosters R on R.ROSTER_ID=RP.ROSTER_ID
		WHERE RP.EMPLOYEE_ID='$empid' AND RP.ACTIVE=$RS_ACTIVE and R.BRANCH_ID=".$BRANCH_ID."
        and if(R.TRIP_TYPE='P',R.ESTIMATE_END_TIME='$login_datetime',R.ESTIMATE_START_TIME='$login_datetime')";
        $roster_id = DB::connection("$dbname")->select($rosterid);
        $ttlcnt = count($roster_id);
        if ($ttlcnt != 0) 
        {
            $id = $roster_id[0]->ROSTER_ID;
            $qry = DB::connection($dbname)
            ->table('roster_passengers as RS')
            ->select('RS.ROSTER_PASSENGER_ID','RS.EMPLOYEE_ID','RS.ROSTER_PASSENGER_STATUS','RS.ESTIMATE_END_TIME',
            'R.ROSTER_ID', 'R.ROUTE_ID', 'R.TRIP_TYPE','E.NAME','E.GENDER','E.MOBILE','E.PROJECT_NAME','B.BRANCH_NAME',  'L.LOCATION_NAME','E.ADDRESS', DB::raw('IF(R.CAB_ID IS NULL, 0, 1) as CABSTATUS') )
            ->join('rosters as R', function($join) use ($RS_ACTIVE, $trip_type) {
                $join->on('R.ROSTER_ID', '=', 'RS.ROSTER_ID')
                    ->where('R.ACTIVE', $RS_ACTIVE)
                    ->where('R.TRIP_TYPE', $trip_type);
            })
            ->join('employees as E', function($join) use ($BRANCH_ID) {
                $join->on('E.EMPLOYEES_ID', '=', 'RS.EMPLOYEE_ID')
                ->where('E.BRANCH_ID', $BRANCH_ID);
            })
            ->join('branch as B', 'B.BRANCH_ID', '=', 'R.BRANCH_ID')
            ->join('locations as L', 'L.LOCATION_ID', '=', 'RS.LOCATION_ID')
            ->where('RS.ROSTER_ID', $id)
            ->where('RS.ACTIVE', $RS_ACTIVE)
            ->get();
    } else {
        $qry = array();
    }
    if(count($qry)>0)
    {
        $TRIP_TYPE = $qry[0]->TRIP_TYPE;
        $ROUTE_ID = $qry[0]->ROUTE_ID;
        $data=array(["msg"=> "This Employee already in another route. Route Id: ".$TRIP_TYPE."-".$ROUTE_ID.". Reclub using following link"]);
        $locations=[];
    }
    else
    {
            $data=$this->getempdetails($empid);
            $division_id=$this->commonFunction->getDivisionId($BRANCH_ID);
            $locations=Location::where("DIVISION_ID",$division_id)->WHERE("ACTIVE",$RS_ACTIVE)->get();


    }
            return response([
                'success' => true,
                'status' => 5,
                'employees' =>$data,
                'locations'=>$locations
            ]);
    }
    catch (\Throwable|\Exception $e) {
        DB::rollBack();
        $this->commonFunction->logException($e);
       
        return response([
            'success' => false,
            'status' => 4,
            'message' => 'No data',
            'validation_controller' => true,
            'error' => $e->getMessage(),
        ], 500);
    }
    }
    public function getempdetails($empid) {
        try
        {
        $dbname = Auth::user()->dbname;
        $branchid = Auth::user()->BRANCH_ID;
		$RS_ACTIVE = MyHelper::$RS_ACTIVE;
		 $employeeData=DB::connection("$dbname")->table('employees AS E')
        ->select(
            'E.NAME AS EMPNAME',
            'E.MOBILE',
            'E.EMAIL',
            'E.GENDER',
            'E.EMPLOYEES_ID',
            'E.PROJECT_NAME',
            'E.ADDRESS',
            'E.MOBILE_GCM',
            'L.LOCATION_ID',
            'L.LOCATION_NAME',
            'L.LATITUDE',
            'L.LONGITUDE',
            'MOBILE_CATEGORY'
        )
        ->join('locations AS L', 'L.LOCATION_ID', '=', 'E.LOCATION_ID')
        ->where('E.BRANCH_ID', $branchid)
        ->where('E.EMPLOYEES_ID', $empid)
        ->where('E.ACTIVE', $RS_ACTIVE)
        ->get();
        // return response([
        //     'success' => true,
        //     'status' => 3,
        //     'employee' =>$employeeData,
        // ]);
        return $employeeData;

    }
    catch (\Throwable|\Exception $e) {
        DB::rollBack();
        $this->commonFunction->logException($e);
       
        return response([
            'success' => false,
            'status' => 4,
            'message' => 'No data',
            'validation_controller' => true,
            'error' => $e->getMessage(),
        ], 500);
    }
       
    }


    public function cab_allot_list($request) : FoundationApplication|Response|ResponseFactory 
    {
        try
        {
        $emp_count=$request->emp_count;
        $status=$request->status;
        $currtDate = date('Y-m-d');
        $user_type = Auth::user()->user_type;
        $BRANCH_ID = Auth::user()->BRANCH_ID;
        $dbname = Auth::user()->dbname;
        $CAB_ATTANDANCE = explode(',',MyHelper::$CAB_ATTANDANCE);
        $BC_BILLABLE = MyHelper::$BC_BILLABLE;
        $ADMIN = MyHelper::$ADMIN;
        $RS_ACTIVE = MyHelper::$RS_ACTIVE;
        $CAPACITY4 = MyHelper::$CAPACITY4;
        $CAPACITY9 = MyHelper::$CAPACITY9;
        $RS_TOTALREJECT = MyHelper::$RS_TOTALREJECT;
        $RS_TOTALBREAKDOWN = MyHelper::$RS_TOTALBREAKDOWN;
        $RS_TOTALTRIPCLOSE = MyHelper::$RS_TOTALTRIPCLOSE;
        $RS_TOTALDELAYROUTES = MyHelper::$RS_TOTALDELAYROUTES;
        $RS_TOTALMANUALTRIPCLOSE = MyHelper::$RS_TOTALMANUALTRIPCLOSE;
        $RS_TOTALTRIPSHEETACCEPT = MyHelper::$RS_TOTALTRIPSHEETACCEPT;
        $RS_TOTALTRIPSHEETREJECT = MyHelper::$RS_TOTALTRIPSHEETREJECT;
        $RS_TOTALTRIPSHEET_CANCEL = MyHelper::$RS_TOTALTRIPSHEET_CANCEL;
        $RS_TOTAL_AUTOCANCEL = MyHelper::$RS_TOTAL_AUTOCANCEL;
        
        $TTLSTATUS = $RS_TOTALREJECT.','.$RS_TOTALBREAKDOWN.','.$RS_TOTALTRIPCLOSE.','.$RS_TOTALDELAYROUTES.','.$RS_TOTALMANUALTRIPCLOSE.','.$RS_TOTALTRIPSHEETACCEPT.','.$RS_TOTALTRIPSHEETREJECT.','.$RS_TOTALTRIPSHEET_CANCEL.','.$RS_TOTAL_AUTOCANCEL;
        $vendor_id = Auth::user()->vendor_id;
        $vendorid = "";
        if ($user_type == $ADMIN) {
            $vendorid = "";
        } else {
            //$vendorid = "AND C.VENDOR_ID='" . $vendor_id . "'";
            $vendorid = $vendor_id;
        }
        
        if ($status == $BC_BILLABLE) {
            if ($emp_count <= $CAPACITY4) {
                $CAPACITY = " where VM.CAPACITY > $CAPACITY4";
            } else if ($emp_count > $CAPACITY4 && $emp_count < $CAPACITY9) {
                $CAPACITY = " where  VM.CAPACITY >= $CAPACITY9";
            } else if ($emp_count >= $CAPACITY9) {
                $CAPACITY = " where VM.CAPACITY >= $CAPACITY9";
            }
           // $noninduct = " AND C.CAB_STATUS = 0";
            $noninduct = "0";
        }else if($status == 'notinducted'){
            $CAPACITY = '';
           // $noninduct = " AND C.CAB_STATUS = 1";
            $noninduct = "1";
        } else
        {
            if ($emp_count <= $CAPACITY4) {
                $CAPACITY = "  where VM.CAPACITY <= $CAPACITY4";
            } else if ($emp_count > $CAPACITY4 && $emp_count < $CAPACITY9) {
                $CAPACITY = "  where VM.CAPACITY > $CAPACITY4 and VM.CAPACITY < $CAPACITY9";
            } else if ($emp_count >= $CAPACITY9) {
                $CAPACITY = "  where VM.CAPACITY >= $CAPACITY9";
            }
           // $noninduct = " AND C.CAB_STATUS = 0";
            $noninduct = "0";
        }
		
		$data = Cab::query()
    ->select([
        'cab.CAB_ID as cabid',
        'vehicle.VEHICLE_REG_NO',
        'VMS.MODEL',
        'VMS.CAPACITY',
        'driver.DRIVERS_NAME',
        'driver.DRIVER_MOBILE',
        DB::raw("IF(ca.LOGIN_TIME IS NULL, '--', ca.LOGIN_TIME) AS LOGIN_TIME"),
        DB::raw("ca2.LOGIN_TIME as ATT_LOGIN_TIME"),
        DB::raw("ca2.LOGOUT_TIME as ATT_LOGOUT_TIME"),
        DB::raw("'--' AS expiredstatus"),
        DB::raw("'--' AS cabdetails"),
        DB::raw("'--' AS cabstatus"),
        DB::raw("'--' AS assigncab"),
        DB::raw("DATEDIFF(vehicle.FC_EXPIRY, '$currtDate') as fcdiff"),
        DB::raw("DATEDIFF(vehicle.INSURANCE_EXPIRY, '$currtDate') as insurancediff"),
        DB::raw("DATEDIFF(vehicle.PERMIT_EXPIRY, '$currtDate') as permitdiff"),
        DB::raw("DATEDIFF(vehicle.TAX_EXPIRY, '$currtDate') as taxdiff"),
        DB::raw("DATEDIFF(driver.BADGE_EXPIRY, '$currtDate') as badgediff"),
        DB::raw("DATEDIFF(driver.LICENCE_EXPIRY, '$currtDate') as licencediff"),
        DB::raw("SUBTIME(CONCAT('$currtDate', ' ', driver.SHIFT_IN_TIME), 10000) as SHIFT_IN_TIME"),
        'driver.SHIFT_OUT_TIME',
        DB::raw("IF(ct.DATE_TIME IS NULL, '1900-01-01 00:00:00', ct.DATE_TIME) AS DATE_TIME"),
        'ct.ROSTER_ID',
        'ct.ACCEPTANCE_REJECT_STATE',
        'cab.CAB_ID'
    ])
    ->join('branch', 'branch.BRANCH_ID', '=', 'cab.BRANCH_ID')
    ->leftJoin('cab_attandance as ca', function($join) use ($currtDate) {
        $join->on('ca.CAB_ID', '=', 'cab.CAB_ID')
            ->whereDate('ca.LOGIN_TIME', '=', $currtDate)
            ->whereIn('ca.CAB_STATUS', [1,2]);
    })
    ->join('vehicles as vehicle', 'vehicle.VEHICLE_ID', '=', 'cab.VEHICLE_ID')
    ->join('drivers as driver', 'driver.DRIVERS_ID', '=', 'cab.DRIVER_ID')
   // ->join('vehicle_models as vehicle_model', 'vehicle_model.VEHICLE_MODEL_ID', '=', 'vehicle.VEHICLE_MODEL_ID')
   
	->Join(DB::raw("(SELECT VM.* FROM vehicle_models VM   $CAPACITY) VMS"), 'VMS.VEHICLE_MODEL_ID', '=', 'vehicle.VEHICLE_MODEL_ID')
	 
    ->leftJoin(DB::raw("(SELECT * FROM cab_allocation WHERE DATE(DATE_TIME) = '$currtDate' AND ACTION IS NULL AND ACCEPTANCE_REJECT_STATE NOT IN ($TTLSTATUS ) GROUP BY CAB_ID) ct"), 'ct.CAB_ID', '=', 'cab.CAB_ID')
    ->leftJoin(DB::raw("(SELECT c1.* FROM cab_attandance c1 JOIN (SELECT CAB_ID, MAX(LOGIN_TIME) AS max_login_time FROM cab_attandance WHERE BRANCH_ID = '$BRANCH_ID' GROUP BY CAB_ID) c2 ON c1.CAB_ID = c2.CAB_ID AND c1.LOGIN_TIME = c2.max_login_time WHERE c1.BRANCH_ID = '$BRANCH_ID') ca2"), 'ca2.CAB_ID', '=', 'cab.CAB_ID')
    ->where('cab.BRANCH_ID', '=', $BRANCH_ID)
   
	->when($vendorid, function ($query, $vendorid) {
						return $query->where('cab.VENDOR_ID', $vendorid);
					})
    ->where('cab.ACTIVE', '=', '1')
    ->where('cab.CAB_STATUS', '=', $noninduct)
    ->groupBy('cab.CAB_ID');
   // ->get();
   // print_r($cabs);exit;

    $filterModel = $request->input('filterModel');
    if ($filterModel) {
        foreach ($filterModel as $field => $filter) {
            if (isset($filter['filter']) && $filter['filter'] !== '') {
                $value = $filter['filter'];
                $type = $filter['type'];

                switch ($field) {
                case 'VEHICLE_REG_NO':
                        $data->where('vehicle.VEHICLE_REG_NO', 'like', "%{$value}%");
                        break;
                    case 'MODEL':
                        $data->where('VMS.MODEL', 'like', "%{$value}%");
                        break;
                    case 'DRIVERS_NAME':
                        $data->where('driver.DRIVERS_NAME', 'like', "%{$value}%");
                        break;
                    case 'DRIVER_MOBILE':
                        $data->where('driver.DRIVER_MOBILE', 'like', "%{$value}%");
                        break;
                
                }
            }
        }
    }


    if ($request->has('orderBy') && !empty($request->input('orderBy'))) {
        $orderBy = $request->input('orderBy');
        $order = $request->input('order', 'asc');
        $data->orderBy($orderBy, $order);
    } else {
        
         $data->orderBy("LOGIN_TIME");
        // $cabs->groupBy('cab.CAB_ID');
    }
    $perPage = $request->input('per_page', MyHelper::$PAGINATION_PER_PAGE);

    if ($perPage === MyHelper::$PAGINATION_PER_PAGE_ALL) {
        $paginatedData = $data->paginate($cabs->count());
    } else {
        $perPage = is_numeric($perPage) ? (int)$perPage : MyHelper::$PAGINATION_PER_PAGE;
        
        $paginatedData = $data->paginate($perPage);
    }
   

            return response([
                'success' => true,
                'status' => 5,
                'cab_list' =>$paginatedData,
                
            ]);
    }
    catch (\Throwable|\Exception $e) {
        DB::rollBack();
        $this->commonFunction->logException($e);
       
        return response([
            'success' => false,
            'status' => 4,
            'message' => 'No data',
            'validation_controller' => true,
            'error' => $e->getMessage(),
        ], 500);
    }
    }

    public function insert_clubdata($request) : FoundationApplication|Response|ResponseFactory 
    {
        try
        {
        
        $empid=$request->empid;
        $location_id=$request->location_id;
        $roster_id=$request->roster_id;
        $mobile=$request->mobile;
        $logindatetime=$request->logindatetime;
        $pickdroptime=$request->pickdroptime;
        $asset_movement=$request->asset_movement;

        $maskinsertnew = 0;
        $otpinsert = 0;
        $passenger_estimatetime = 0;
        $insertsms = 0;
        $insert_notify = 0;
        $remove_escort = 0;
        $insertescort = 0;
        $reset_execute = 0;
        $reset_passenger = 0;
        $roster_location_update = 0;
        $EMP_OTP = 0;
        $currentTime = date('Y-m-d H:i:s');
        $branchid = Auth::user()->BRANCH_ID;
        $vendorid = Auth::user()->vendor_id;
        $userid = Auth::user()->id;
        $dbname = Auth::user()->dbname;
        $RP_PICKROUTE = MyHelper::$RP_PICKROUTE;
        $RS_TOTALEXECUTE = MyHelper::$RS_TOTALEXECUTE;
        $RS_EXECUTE = MyHelper::$RS_EXECUTE;
        $RS_ACTIVE = MyHelper::$RS_ACTIVE;
        $VERIFIED_STATUS = MyHelper::$VERIFIED_STATUS;
        $VERIFIEDSTATUS = MyHelper::$VERIFIEDSTATUS;
        $RP_DROPROUTE = MyHelper::$RP_DROPROUTE;
        $ESCORT_NEW = MyHelper::$ESCORT_NEW;
        $ESCORT_REMOVE = MyHelper::$ESCORT_INTER;
        $AES_KEY = env('AES_ENCRYPT_KEY');
        $PR_ESCORT_ENABLE = MyHelper::$PR_ESCORT_ENABLE;
        $PR_ESCORT_START_TIME = MyHelper::$PR_ESCORT_START_TIME;
        $PR_ESCORT_END_TIME = MyHelper::$PR_ESCORT_END_TIME;
        $TOTALEXECUTE = explode(',', $RS_TOTALEXECUTE);
		$RPS_TTLNOSHOW=MyHelper::$RPS_TTLNOSHOW;
		$SMS_PICKUP=MyHelper::$SMS_PICKUP;
		$SMS_DROP=MyHelper::$SMS_DROP;
        //$this->commonFunction = new CommonController;
        $result = Employee::on("$dbname")->where([["EMPLOYEES_ID", "=", $empid],["BRANCH_ID",$branchid]])
                ->update(array("LOCATION_ID" => $location_id, "UPDATED_BY" => $userid));
        
		/* $str = "SELECT APPROVED_DISTANCE FROM approve_distances WHERE BRANCH_ID=$branchid AND LOCATION_ID=$location";
        $routeorder = DB::connection("$dbname")->select($str); */
		/* $str = "SELECT DISTANCE,SMS_ALERT FROM employees WHERE BRANCH_ID=$branchid AND ACTIVE=1 and EMPLOYEES_ID='".$empid."'";
        $routeorder = DB::connection("$dbname")->select($str);
		 */
		$routeorder = DB::connection($dbname)->table('employees')
		->select('DISTANCE', 'SMS_ALERT')
		->where('BRANCH_ID', $branchid)
		->where('ACTIVE', $RS_ACTIVE)
		->where('EMPLOYEES_ID', $empid)
		->get(); 
		$SMS_ALERT=$routeorder[0]->SMS_ALERT;
        $pickdrop_time = date('Y-m-d H:i:s', strtotime($pickdroptime));
        $insert_passenger = array("EMPLOYEE_ID" => $empid, "ROSTER_ID" => $roster_id, "ESTIMATE_START_TIME" => $pickdrop_time, "ACTIVE" => $RS_ACTIVE,
        "ESTIMATE_END_TIME" => $logindatetime,"ROUTE_ORDER" => $routeorder[0]->DISTANCE,"LOCATION_ID" => $location_id,
        "CREATED_BY" => $userid, "CREATED_DATE" => $currentTime,"ASSET_MOVEMENT"=>$asset_movement);
        $result1 = RosterPassenger::on("$dbname")->insert($insert_passenger);
        $lastid = DB::connection("$dbname")->getPdo()->lastInsertId();
        
       /*  $str1 = "SELECT RP.EMPLOYEE_ID,RP.ROUTE_ORDER,R.PASSENGER_ALLOT_IN_ROUT_COUNT,R.ROSTER_STATUS,R.TRIP_TYPE,R.ROUTE_ID,
        if(R.CAB_ID IS NULL,0,1) AS DESTSTATUS,RP.ESTIMATE_START_TIME,RP.ESTIMATE_END_TIME,R.CAB_ID,R.DRIVER_MASK_NUMBER,
        R.ESTIMATE_START_TIME AS SENDSMS,R.PASSENGER_ALLOT_IN_ROUT_COUNT,L.LOCATION_NAME,
        if(R.TRIP_TYPE = 'P',R.ESTIMATE_END_TIME,R.ESTIMATE_START_TIME) AS LOGINDATETIME,D.MOBILE_GCM,
        if(R.ROSTER_STATUS & 64,1,0) as executestatus
        FROM roster_passengers RP 
        INNER JOIN rosters R ON R.ROSTER_ID = RP.ROSTER_ID
        INNER JOIN locations L ON L.LOCATION_ID = RP.LOCATION_ID 
        LEFT JOIN cab C ON C.CAB_ID = R.CAB_ID 
        LEFT JOIN devices D ON D.DEVICE_ID = C.DEVICE_ID 
        WHERE R.BRANCH_ID = $branchid AND RP.ROSTER_ID = $route and RP.ROSTER_PASSENGER_STATUS not in($RPS_TTLNOSHOW) ORDER BY RP.ROUTE_ORDER DESC LIMIT 1";
        $escortcheck = DB::connection("$dbname")->select($str1); */
		$escortcheck = DB::connection($dbname)->table('roster_passengers AS RP')
		->select('RP.EMPLOYEE_ID','RP.ROUTE_ORDER','R.PASSENGER_ALLOT_IN_ROUT_COUNT','R.ROSTER_STATUS','R.TRIP_TYPE','R.ROUTE_ID',
		DB::raw("IF(R.CAB_ID IS NULL, 0, 1) AS DESTSTATUS"),'RP.ESTIMATE_START_TIME','RP.ESTIMATE_END_TIME','R.CAB_ID','R.DRIVER_MASK_NUMBER',      'R.ESTIMATE_START_TIME AS SENDSMS','R.PASSENGER_ALLOT_IN_ROUT_COUNT','L.LOCATION_NAME', DB::raw("IF(R.TRIP_TYPE = 'P', R.ESTIMATE_END_TIME, R.ESTIMATE_START_TIME) AS LOGINDATETIME"),'D.MOBILE_GCM', DB::raw("IF(R.ROSTER_STATUS & 64, 1, 0) AS executestatus") )
		->join('rosters AS R', 'R.ROSTER_ID', '=', 'RP.ROSTER_ID')
		->join('locations AS L', 'L.LOCATION_ID', '=', 'RP.LOCATION_ID')
		->leftJoin('cab AS C', 'C.CAB_ID', '=', 'R.CAB_ID')
		->leftJoin('devices AS D', 'D.DEVICE_ID', '=', 'C.DEVICE_ID')
		->where('R.BRANCH_ID', $branchid)
		->where('RP.ROSTER_ID', $roster_id)
		->whereNotIn('RP.ROSTER_PASSENGER_STATUS', explode(',', $RPS_TTLNOSHOW)) // Assuming it's a comma-separated string
		->orderBy('RP.ROUTE_ORDER', 'DESC')
		->limit(1)
		->get();
		
        $ROSTER_STATUS = $escortcheck[0]->ROSTER_STATUS;
        $TRIPID = $escortcheck[0]->ROUTE_ID;
        $triptype = $escortcheck[0]->TRIP_TYPE;
        $DRIVER_MOBILE_GCM = $escortcheck[0]->MOBILE_GCM;
        $DESTSTATUS = $escortcheck[0]->DESTSTATUS;
        $LOGINOUTDATETIME = $escortcheck[0]->LOGINDATETIME;
        $executestatus = $escortcheck[0]->executestatus;
        $UPDATELOCATIONNAME = $escortcheck[0]->LOCATION_NAME;
        $empdetail = $this->getempdetails($empid);
        $MOBILE_GCM = $empdetail[0]->MOBILE_GCM;
        /* $empmobile = $common_control->AES_DECRYPT($empdetail[0]->MOBILE,$AES_KEY);
        $EMPNAME = $common_control->AES_DECRYPT($empdetail[0]->EMPNAME,$AES_KEY); */
        $empmobile = 9543417214;
        $EMPNAME = 'selvam';
        $LATITUDE = $empdetail[0]->LATITUDE;
        $LONGITUDE = $empdetail[0]->LONGITUDE;
        $LOCATION_NAME = $empdetail[0]->LOCATION_NAME;
        $CAB_ID = $escortcheck[0]->CAB_ID;
        $GENDER = $empdetail[0]->GENDER;
        $EMPLOYEES_ID = $empdetail[0]->EMPLOYEES_ID;
        $MOBILE_CATEGORY = $empdetail[0]->MOBILE_CATEGORY;
        $MOBILE_GCM = $empdetail[0]->MOBILE_GCM;
        $LOGINDATETIME = $escortcheck[0]->ESTIMATE_START_TIME;
        $SENDSMS = $escortcheck[0]->SENDSMS;
        $DROPLOGINTIME = $escortcheck[0]->ESTIMATE_END_TIME;
        $DRIVER_MASK_NUMBER = $escortcheck[0]->DRIVER_MASK_NUMBER;
        
        if($executestatus ==1){
            $SENDSMSTIME = date('Y-m-d H:i:s');
            $notifymsg1 = "$EMPNAME - $LOCATION_NAME was clubbed in your trip";
            $cabnotification1 = "INSERT INTO `notification` (`branch_id`, `empid_cabid`, `fcm_id`, `heading`, `message`, `category_value`,`app_category`,`created_at`) VALUES ('$branchid', '$CAB_ID', '$DRIVER_MOBILE_GCM', 'New Club', '$notifymsg1', '1','ANDROID','$SENDSMSTIME')";
            $insert_notify1 = DB::connection("$dbname")->insert($cabnotification1);
        }
        
      //  if($DRIVER_MASK_NUMBER != '' && $DRIVER_MASK_NUMBER != '--' && $CAB_ID != ''){
            /* Enable call masking in clubbing */
          //  $maskinsertnew = $common_control->new_clubbing_property_check($lastid,$route);
            /* Enable call masking in clubbing */
       // }
        $CHECKESCORT_TIME = '';
        if ($triptype == $RP_PICKROUTE) {
            $OTP_CATEGORY = $SMS_PICKUP;
          //  $CHECKESCORT_TIME = $LOGINDATETIME;
            $CHECKESCORT_TIME = $LOGINOUTDATETIME;
        } else {
            $OTP_CATEGORY = $SMS_DROP;
           // $CHECKESCORT_TIME = $DROPLOGINTIME;
            $CHECKESCORT_TIME = $LOGINOUTDATETIME;
        }
        $SMS_PICKDROP = $OTP_CATEGORY;
        if($DESTSTATUS == 1){
            
            $EMP_OTP = substr(number_format(time() * rand(), 0, '', ''), 0, 4);
            $PROP_AUTOPICK = MyHelper::$PROP_AUTOPICK;
            $PR_AUTOPICKUPTIME = MyHelper::$PR_AUTOPICKUPTIME;
            $PR_HELPLINENO = MyHelper::$PR_HELPLINENO;
            $PR_SMSTAG = MyHelper::$PR_SMSTAG;
            $PR_LEVEL2 = MyHelper::$PR_LEVEL2;
            $PR_ESTIMATECABALLOT = MyHelper::$PR_ESTIMATECABALLOT;
            $PR_LEVEL1 = MyHelper::$PR_LEVEL1;
            $PR_MASKCHECK = MyHelper::$PR_MASKCHECK;
            $PROP_YES = MyHelper::$PROP_YES;
            
//            $insert_otp = array("MOBILE_NO" => $mobile,"ROSTER_PASSENGER_ID" => $lastid,"OTP" => $EMP_OTP,"VERIFIED_STATUS" => $VERIFIED_STATUS,"OTP_CATEGORY" => $OTP_CATEGORY,"CREATED_BY" => $userid,"CREATED_DATE" => date('Y-m-d H:i:s'));
           /* $insertotp = "INSERT INTO `otp_verification` (`MOBILE_NO`, `ROSTER_PASSENGER_ID`, `OTP`, `VERIFIED_STATUS`, `OTP_CATEGORY`, `CREATED_BY`, `CREATED_DATE`) VALUES ('$mobile','$lastid','$EMP_OTP','$VERIFIED_STATUS','$OTP_CATEGORY','$userid','$currentTime')";
            $otpinsert = DB::connection("$dbname")->insert($insertotp); */
			
			$otpinsert = DB::connection($dbname)->table('otp_verification')->insert([
				'MOBILE_NO' => $empmobile,
				//'MOBILE_NO' => $mobile,
				'ROSTER_PASSENGER_ID' => $lastid,
				'OTP' => $EMP_OTP,
				'VERIFIED_STATUS' => $VERIFIED_STATUS,
				'OTP_CATEGORY' => $OTP_CATEGORY,
				'CREATED_BY' => $userid,
				'CREATED_DATE' => $currentTime
			]);

           /*  $cabdetails = "SELECT V.VEHICLE_REG_NO,VM.MODEL,VM.CAPACITY,B.BRANCH_NAME,D.DRIVERS_NAME,D.DRIVER_MOBILE FROM cab C
            INNER JOIN vehicles V ON V.VEHICLE_ID = C.VEHICLE_ID
            INNER JOIN vehicle_models VM ON VM.VEHICLE_MODEL_ID = V.VEHICLE_MODEL_ID
            INNER JOIN branch B ON B.BRANCH_ID = C.BRANCH_ID
			inner join drivers D on D.DRIVERS_ID=C.DRIVER_ID
            WHERE C.CAB_ID = '$CAB_ID'";
            $cab_details = DB::connection("$dbname")->select($cabdetails); */
			$cab_details = DB::connection($dbname)->table('cab AS C')
			->select('V.VEHICLE_REG_NO', 'VM.MODEL', 'VM.CAPACITY', 'B.BRANCH_NAME', 'D.DRIVERS_NAME', 'D.DRIVER_MOBILE')
			->join('vehicles AS V', 'V.VEHICLE_ID', '=', 'C.VEHICLE_ID')
			->join('vehicle_models AS VM', 'VM.VEHICLE_MODEL_ID', '=', 'V.VEHICLE_MODEL_ID')
			->join('branch AS B', 'B.BRANCH_ID', '=', 'C.BRANCH_ID')
			->join('drivers AS D', 'D.DRIVERS_ID', '=', 'C.DRIVER_ID')
			->where('C.CAB_ID', $CAB_ID)
			->get();

            $VEHICLE_REG_NO = $cab_details[0]->VEHICLE_REG_NO;
            $MODEL = $cab_details[0]->MODEL;
            $SITENAME = $cab_details[0]->BRANCH_NAME;
            $DRIVERS_NAME = $cab_details[0]->DRIVERS_NAME;
            $DRIVER_MOBILE = $cab_details[0]->DRIVER_MOBILE;

            /* $branchdetail = "SELECT LAT,`LONG` FROM branch WHERE BRANCH_ID = '$branchid'";
            $branch_latlong = DB::connection("$dbname")->select($branchdetail); */
			
			$branch_latlong = DB::connection($dbname)->table('branch')->select('LAT', 'LONG')->where('BRANCH_ID', $branchid)->get();
            $destination = $branch_latlong[0]->LAT . ',' . $branch_latlong[0]->LONG;
            $propertie = property::on("$dbname")->where([['BRANCH_ID', '=', $branchid], ['ACTIVE', '=', $RS_ACTIVE]])->get();
            $EMAIL = "";                                
            for ($ii = 0; $ii < count($propertie); $ii++) {

                $PROPERTIE_NAME = $propertie[$ii]->PROPERTIE_NAME;
                $PROPERTIE_VALUE = $propertie[$ii]->PROPERTIE_VALUE;

                switch ($PROPERTIE_NAME) {
                    case $PR_AUTOPICKUPTIME:
                        $AUTOPICKUPTIME = $PROPERTIE_VALUE;
                        break;
                    case $PR_HELPLINENO:
                        $HELP_LINE = $PROPERTIE_VALUE;
                        break;
                    case $PR_SMSTAG:
                        $SMSTAG = $PROPERTIE_VALUE;
                        break;
                    case $PR_LEVEL2:
                        $ESTIMATE_INTERVAL60 = $PROPERTIE_VALUE;
                        break;
                    case $PR_ESTIMATECABALLOT:
                        $ESTIMATE_CABALLOT = $PROPERTIE_VALUE;
                        break;
                    case $PR_LEVEL1:
                        $ESTIMATE_INTERVAL30 = $PROPERTIE_VALUE;
                        break;
                    case $PR_MASKCHECK:
                        $CALLMASKING = $PROPERTIE_VALUE;
                        break;
                    case "SMS_LAST_TAG":
                        $SMS_LAST_TAG = $PROPERTIE_VALUE;
                        break;
                    case 'CAB ESCALATE EMAIL':
                        $EMAIL = $PROPERTIE_VALUE;
                        break;
                    default:
                        break;
                }
            }

            if ($AUTOPICKUPTIME == $PROP_AUTOPICK) {
                if ($triptype == $RP_PICKROUTE) {
                    $origin = $LATITUDE . ',' . $LONGITUDE;
                    $duration = $common_control->getDistance($origin, $destination);
                    $qry = "SELECT  SUBTIME( '$LOGINDATETIME',SEC_TO_TIME('$duration') ) as pickuptime ";
                    $pickup_time = DB::connection("$dbname")->select($qry);
                    $pickuptime = $pickup_time[0]->pickuptime;
                    $passenger_estimatetime = RosterPassenger::on("$dbname")->where([["ROSTER_PASSENGER_ID", "=", $lastid],['ACTIVE', '=', $RS_ACTIVE]])
                            ->update(array("ESTIMATE_START_TIME" => $pickuptime, "UPDATED_BY" => $userid));
                } else {
                    $origin = $LATITUDE . ',' . $LONGITUDE;
                    $duration = $common_control->getDistance($origin, $destination);
                    $qry = "SELECT  ADDTIME( '$DROPLOGINTIME',SEC_TO_TIME('$duration') ) as pickuptime ";
                    $pickup_time = DB::connection("$dbname")->select($qry);
                    $pickuptime = $pickup_time[0]->pickuptime;
                    $passenger_estimatetime = RosterPassenger::on("$dbname")->where([["ROSTER_PASSENGER_ID", "=", $lastid],['ACTIVE', '=', $RS_ACTIVE]])
                            ->update(array("ESTIMATE_END_TIME" => $pickuptime, "UPDATED_BY" => $userid));
                }
            }

           /*  $empstarttime = "SELECT ESTIMATE_START_TIME,ESTIMATE_END_TIME,SUBTIME('$SENDSMS',10000) AS SMSTIME FROM roster_passengers WHERE ROSTER_PASSENGER_ID= '$lastid'";
            $empstart_time = DB::connection("$dbname")->select($empstarttime); */
			
			$empstart_time = DB::connection($dbname)
			->table('roster_passengers')
			->select('ESTIMATE_START_TIME', 'ESTIMATE_END_TIME', DB::raw("SUBTIME('$SENDSMS', 10000) AS SMSTIME"))
			->where('ROSTER_PASSENGER_ID', $lastid)
			->get();
					
            $ESTIMATE_START_TIME = $empstart_time[0]->ESTIMATE_START_TIME;
            $ESTIMATE_END_TIME = $empstart_time[0]->ESTIMATE_END_TIME;
            $SMSTIME = $empstart_time[0]->SMSTIME;
            if ($triptype == $RP_PICKROUTE) {
                $ESTIMATESTARTDATE = date('Y-m-d',strtotime($ESTIMATE_START_TIME));
                $ESTIMATESTARTTIME = date('H:i:s',strtotime($ESTIMATE_START_TIME));
                $PICKDROPINTIME = date('H:i:s', strtotime($ESTIMATE_END_TIME));
            }else
            {
                $ESTIMATESTARTDATE = date('Y-m-d',strtotime($DROPLOGINTIME));
                $ESTIMATESTARTTIME = date('H:i:s',strtotime($DROPLOGINTIME));
                $PICKDROPINTIME = date('H:i:s', strtotime($ESTIMATE_START_TIME));
            }
            
            if($SMSTIME > $currentTime){
                $SENDSMSTIME = $SMSTIME;
            }else{
                $SENDSMSTIME = $currentTime;
            }
			$VEHICLE_REG_NO=str_replace(' ', '', $VEHICLE_REG_NO);
			$MODEL=str_replace(' ', '', $MODEL);
						
			//$EMPNAME=substr(str_replace(' ', '', $EMPNAME), 0, 5);
			$EMPNAME='Selvam';
			$DRIVERS_NAME=substr(str_replace(' ', '', $DRIVERS_NAME), 0, 5);
						
			if($branchid==32)
				{
					if($triptype=='P')
					{
					$message="Hi $EMPNAME, your estimated pickup time on ".date('d/m/Y',strtotime($ESTIMATESTARTDATE))." is ".$ESTIMATESTARTTIME.". Cab ($MODEL) $VEHICLE_REG_NO, $DRIVERS_NAME, $DRIVER_MOBILE, OTP - $EMP_OTP. Helpline - $HELP_LINE.".$SMS_LAST_TAG;
					}
					else
					{
						$message="Hi $EMPNAME, your drop to $LOCATION_NAME is scheduled for ".date('H:i',strtotime($ESTIMATESTARTTIME))." on ".date('d/m/Y',strtotime($ESTIMATESTARTDATE))." Cab ($MODEL) $VEHICLE_REG_NO, $DRIVERS_NAME, $DRIVER_MOBILE and your Route no. is $TRIPID. OTP - $EMP_OTP. Reporting time ".date('H:i',strtotime($ESTIMATESTARTTIME)).". Helpline - $HELP_LINE.".$SMS_LAST_TAG;
					}
				}
			else
			{
				$date=$ESTIMATESTARTDATE." ".$ESTIMATESTARTTIME;
				$sms_fmt= date('jS M g:ia', strtotime($date));
				$message="Hi, $SMS_PICKDROP at $LOCATION_NAME on $sms_fmt Cab Details: $DRIVERS_NAME-$DRIVER_MOBILE-$VEHICLE_REG_NO OTP-$EMP_OTP Help-$HELP_LINE. $SMS_LAST_TAG";
				//$message = "Dear $EMPNAME, Your $SMS_PICKDROP: $ESTIMATESTARTDATE on $ESTIMATESTARTTIME, Cab: $VEHICLE_REG_NO,Model : $MODEL, OTP: $EMP_OTP would report your $SMS_PICKDROP at $LOCATION_NAME, Help Line: $HELP_LINE.";
			}
            if($branchid == 18 || $branchid == 48)
            {
                $sms_fmt= date('d.m.Y H:i', strtotime($date));
                $message= "Zingo your ride is here! Your driver is ready for pickup today $DRIVERS_NAME/ $DRIVER_MOBILE/ $VEHICLE_REG_NO $sms_fmt. We're here to make your journey smooth and comfortable. Remember, our commitment to punctuality is our priority. Your driver will wait for up to 5 Minutes. So, please be ready to hop on and enjoy your ride! Your feedback matters. If you've faced any issue, before, please let us know $HELP_LINE Help desk mail id $EMAIL. We're eager to improve our service to better serve you better. Thanks have a great day ahead! Best regards. Transport Team.NTL";
            }

            if($CALLMASKING != $PROP_YES){
				if($SMS_ALERT==1)
				{
					$insert_sms = array("BRANCH_ID" => $branchid, "ORIGINATOR" => $SMSTAG, "RECIPIENT" => $mobile, "MESSAGE" => $message,
					"SENT_DATE" => '1900-01-01 00:00:00', "REF_NO" => '--', "CREATED_BY" => $userid, "CREATED_DATE" => $SENDSMSTIME);
					$insertsms = sms::on("$dbname")->insert($insert_sms);
				}
            }else{
				$date=$ESTIMATESTARTDATE." ".$ESTIMATESTARTTIME;
				$sms_fmt= date('jS M g:ia', strtotime($date));
                if ($triptype == $RP_PICKROUTE) {
					
                $message = "Hi,Your estimated pickup is $sms_fmt,Cab detail ".$VEHICLE_REG_NO."(OTP-".$EMP_OTP.").Helpline $HELP_LINE.".$SMS_LAST_TAG;
                //$message = "Dear $EMPNAME, cab $VEHICLE_REG_NO has been assigned for you, Pickup at $ESTIMATESTARTTIME, In Time $PICKDROPINTIME, OTP $EMP_OTP.";
				}
				else
				{
					$message = "Hi,Your drop to $LOCATION_NAME scheduled at $LOGINOUTDATETIME,Cab detail ".$VEHICLE_REG_NO."(OTP-".$EMP_OTP.") Helpline $HELP_LINE.".$SMS_LAST_TAG;
					//$message = "Dear $EMPNAME, cab $VEHICLE_REG_NO has been assigned for you,Drop at out Time $LOGINOUTDATETIME, OTP $EMP_OTP.";
				}

                if($branchid == 18 || $branchid == 48)
                {
                    $sms_fmt= date('d.m.Y H:i', strtotime($date));
                    $message= "Zingo your ride is here! Your driver is ready for pickup today $DRIVERS_NAME/ $DRIVER_MOBILE/ $VEHICLE_REG_NO $sms_fmt. We're here to make your journey smooth and comfortable. Remember, our commitment to punctuality is our priority. Your driver will wait for up to 5 Minutes. So, please be ready to hop on and enjoy your ride! Your feedback matters. If you've faced any issue, before, please let us know $HELP_LINE Help desk mail id $EMAIL. We're eager to improve our service to better serve you better. Thanks have a great day ahead! Best regards. Transport Team.NTL";
                }
                
				if($SMS_ALERT==1)
				{
					$insert_sms = array("BRANCH_ID" => $branchid, "ORIGINATOR" => $SMSTAG, "RECIPIENT" => $empmobile, "MESSAGE" => $message,
						"SENT_DATE" => '1900-01-01 00:00:00', "REF_NO" => '--', "CREATED_BY" => $userid, "CREATED_DATE" => $SENDSMSTIME);
					$insertsms = sms::on("$dbname")->insert($insert_sms);
				}
            }
            
            if($MOBILE_GCM != ''){
                if($executestatus ==1){
                    $notifymsg = "Your $SMS_PICKDROP Time : $ESTIMATESTARTTIME,\nYour $SMS_PICKDROP Location : $LOCATION_NAME,\nCab No : $VEHICLE_REG_NO";
                    $cabnotification = "INSERT INTO `notification` (`branch_id`, `empid_cabid`, `fcm_id`, `heading`, `message`, `category_value`,`app_category`,`created_at`) VALUES ('$branchid', '$EMPLOYEES_ID', '$MOBILE_GCM', 'Cab Assigned', '$notifymsg', '1','$MOBILE_CATEGORY','$SENDSMSTIME')";
					$insert_notify = DB::connection("$dbname")->insert($cabnotification);
                }
                }
				
				$mask_enable = $this->commonFunction->GetPropertyValue('CALL MASKING OPTION');
		
		if($mask_enable=='Y')
		{
			//$mask= new MaskNumberController_new();
			//$mask->masknumber_clubbing_reallot($route,$empid,$branchid,'clubbing');
		}
		
				
				
        
        }

        if($escortcheck[0]->EMPLOYEE_ID == $empid)
        {
            $RS_MGENDER = env('RS_MGENDER');
            if($GENDER == $RS_MGENDER)
            {
                /* $check_escort = "SELECT COUNT(*) as ttlcnt FROM route_escorts WHERE ROSTER_ID='$route'";
                $checkescortsts = DB::connection("$dbname")->select($check_escort); */
				$checkescortsts = DB::connection($dbname)
				->table('route_escorts')
				->select(DB::raw('COUNT(*) as ttlcnt'))
				->where('ROSTER_ID', $roster_id)->get();
				//->value('ttlcnt'); // Retrieves the count directly
				
                $ttlcnt = $checkescortsts[0]->ttlcnt;
                if($ttlcnt != 0)
                {
                    $remove_escort = RouteEscorts::on("$dbname")->where([["ROSTER_ID", "=", $roster_id]])->update(array("ESCORT_ID" => NULL, "STATUS" => $ESCORT_REMOVE)); 
                }
            }
            else
            {
                $isEscort = '';
                $is_Escort_Time = date('H:i:s', strtotime($CHECKESCORT_TIME));
                $propertie = property::on("$dbname")->where([['BRANCH_ID', '=', $branchid], ['ACTIVE', '=', $RS_ACTIVE]])->get();

                for ($ii = 0; $ii < count($propertie); $ii++) {

                    $PROPERTIE_NAME = $propertie[$ii]->PROPERTIE_NAME;
                    $PROPERTIE_VALUE = $propertie[$ii]->PROPERTIE_VALUE;

                    switch ($PROPERTIE_NAME) {
                        case $PR_ESCORT_ENABLE:
                            $PR_ESCORTENABLE = $PROPERTIE_VALUE;
                            break;
                        case $PR_ESCORT_START_TIME:
                            $PR_ESCORTSTART_TIME = $PROPERTIE_VALUE;
                            break;
                        case $PR_ESCORT_END_TIME:
                            $PR_ESCORTEND_TIME = $PROPERTIE_VALUE;
                            break;
                        default:
                            break;
                    }
                }
                if ($PR_ESCORTENABLE == 'Y') {
                    $ret1 = $this->check_time($PR_ESCORTSTART_TIME, $PR_ESCORTEND_TIME, $is_Escort_Time) ? "yes" : "no";
                    if ($ret1 == "yes") {
                        $isEscort = 'true';
                    }
                }
                if($isEscort == 'true'){
				
					/* $already_escort="select count(*) as cnt,ESCORT_ID,ROUTE_ESCORT_ID from route_escorts where ROSTER_ID='".$route."'";
					$already_escortres=DB::select($already_escort); */
					$already_escortres = DB::connection($dbname)
					->table('route_escorts')
					->select(DB::raw('COUNT(*) as cnt, ESCORT_ID, ROUTE_ESCORT_ID'))
					->where('ROSTER_ID', $roster_id)
					->get();
					$count=count($already_escortres);
					$assigned_escort='';
					if($count > 0)
					{
						$assigned_escort=$already_escortres[0]->ESCORT_ID;
						$ROUTE_ESCORT_ID=$already_escortres[0]->ROUTE_ESCORT_ID;
						$remove_already_escort=RouteEscorts::where("ROSTER_ID", "=", $roster_id)->update(array("STATUS" => 6,"ESCORT_ID"=>NULL,"UPDATED_BY"=>$userid));
					}
							
                    if($RP_PICKROUTE == $escortcheck[0]->TRIP_TYPE && $escortcheck[0]->PASSENGER_ALLOT_IN_ROUT_COUNT == 0)
                    {
                        $Route_Escort = array("BRANCH_ID" => $branchid, "ROSTER_ID" => $roster_id, "EMPLOYEE_ID" => $empid, "STATUS" => $ESCORT_NEW,
                        "CREATED_BY" => $userid, "created_at" => $currentTime);
                        RouteEscorts::on("$dbname")->insert($Route_Escort);
                        $insertescort = DB::connection("$dbname")->getPdo()->lastInsertId();
                        if(in_array($ROSTER_STATUS, $TOTALEXECUTE))
                        {
                            $status = " SELECT conv(bin($ROSTER_STATUS)-bin($RS_EXECUTE),2,10) as rosterstatus";
                            $returnsts = DB::connection("$dbname")->select($status);
                            $reset_execute = Roster::on("$dbname")->where([["ROSTER_ID", "=", $roster_id], ["ACTIVE", "=", "$RS_ACTIVE"]])
                            ->update(array("ROSTER_STATUS" => $ROSTER_STATUS, "UPDATED_BY" => $userid));
                            $reset_passenger = RosterPassenger::on("$dbname")->where([["ROSTER_ID", "=", $roster_id],['ACTIVE', '=', $RS_ACTIVE]])
                            ->update(array("ROSTER_PASSENGER_STATUS" => 1));
                        }
                    }
                    else if($RP_DROPROUTE == $escortcheck[0]->TRIP_TYPE)
                    {
						if($assigned_escort=='')
						{
							$Route_Escort = array("BRANCH_ID" => $branchid, "ROSTER_ID" => $roster_id, "EMPLOYEE_ID" => $empid, "STATUS" => $ESCORT_NEW,
							"CREATED_BY" => $userid, "created_at" => $currentTime);
							RouteEscorts::on("$dbname")->insert($Route_Escort);
							$insertescort = DB::connection("$dbname")->getPdo()->lastInsertId();
						}
						else
						{
							$track_ctrl=new Trackcontroller();
							$track_ctrl->SetreassingEscortRoster($ROUTE_ESCORT_ID,$assigned_escort);
							//$this->setescortvalid($assigned_escort, '1624', 'Escort Validated', 'Escort Ok');
							$insertescort='reassigned';
						}
                        /* $Route_Escort = array("BRANCH_ID" => $branchid, "ROSTER_ID" => $route, "EMPLOYEE_ID" => $empid, "STATUS" => $ESCORT_NEW,
                        "CREATED_BY" => $userid, "created_at" => $currentTime);
                        Route_Escort::on("$dbname")->insert($Route_Escort);
                        $insertescort = DB::connection("$dbname")->getPdo()->lastInsertId(); */
                        if(in_array($ROSTER_STATUS, $TOTALEXECUTE))
                        {
                            $status = " SELECT conv(bin($ROSTER_STATUS)-bin($RS_EXECUTE),2,10) as rosterstatus";
                            $returnsts = DB::connection("$dbname")->select($status);
                            $reset_execute = Roster::on("$dbname")->where([["ROSTER_ID", "=", $roster_id], ["ACTIVE", "=", "$RS_ACTIVE"]])
                            ->update(array("ROSTER_STATUS" => $ROSTER_STATUS, "UPDATED_BY" => $userid));
                            $reset_passenger = RosterPassenger::on("$dbname")->where([["ROSTER_ID", "=", $roster_id],['ACTIVE', '=', $RS_ACTIVE]])
                            ->update(array("ROSTER_PASSENGER_STATUS" => 1));
                        }
                    }
               }
            }
        }
        
        if($triptype == $RP_PICKROUTE){
             $roster_location_update = DB::connection("$dbname")->update("UPDATE rosters SET START_LOCATION='$UPDATELOCATIONNAME',PASSENGER_CLUBING_COUNT = PASSENGER_CLUBING_COUNT + 1 WHERE ROSTER_ID = $roster_id");
         }else{
             $roster_location_update = DB::connection("$dbname")->update("UPDATE rosters SET END_LOCATION='$UPDATELOCATIONNAME',PASSENGER_CLUBING_COUNT = PASSENGER_CLUBING_COUNT + 1 WHERE ROSTER_ID = $roster_id");
         }
        
        $date_f = $this->commonFunction->date_format_add();
        $log_arr = array("BRANCH_ID"=>$branchid,"VENDOR_ID"=>$vendorid,"ACTION"=>'WEB NEWCLUB',"CATEGORY"=>'TRACKING DASHBOARD',"UPDATE_EMPLOYEE_LOCATION"=>$result,"NEW_PASSENGERID"=>$lastid,"MASKNUMBER_INSERT" => $maskinsertnew,"OTP_INSERT"=>$otpinsert,"OTP_VALUE"=> $EMP_OTP,"PASSENGER_ESTIMATETIME_UPDATE"=>$passenger_estimatetime,"INSERT_SMS"=>$insertsms,"INSERT_NOTIFICATION"=>$insert_notify,"REMOVE_ESCORT"=>$remove_escort,"INSERT_NEWESCORT"=>$insertescort,"ROUTE_EXECUTE_RESET"=>$reset_execute,"RESET_PASSENGER"=>$reset_passenger,"ROSTER_LOCATION_UPDATE"=>$roster_location_update,"USER_ID"=> $userid,"PROCESS_DATE"=>$date_f);
		
		//masknumber_clubbing_reallot($rosterid,$empid,$branchid,$category)

       // $ret=$common_control->weblogs($log_arr);
       
            return response([
                'success' => true,
                'status' => 5,
                'club_status' =>'Employee Clubbing Successfully Added',
            ]);
    }
    catch (\Throwable|\Exception $e) {
        DB::rollBack();
        $this->commonFunction->logException($e);
       
        return response([
            'success' => false,
            'status' => 4,
            'message' => 'No data',
            'validation_controller' => true,
            'error' => $e->getMessage(),
        ], 500);
    }
    }

    private function check_time($t1, $t2, $tn) {
         
        $t1 = +str_replace(":", "", $t1);        
        $t2 = +str_replace(":", "", $t2);        
        $tn = +str_replace(":", "", $tn);          
        if ($t2 <= $t1) {                
              $ret = $t2 <= $tn && $tn >= $t1;
              if($ret){                 
                  return $ret ;
              }else{                    
                   return $t1 >= $tn && $tn <= $t2; 
              }
        } 
    }
    
    
    
	
    

}
