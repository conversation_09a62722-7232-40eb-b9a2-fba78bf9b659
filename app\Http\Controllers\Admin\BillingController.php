<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Services\Admin\BillingService;
use App\Http\Requests\Admin\ZohoMisReportRequest;
use App\Http\Requests\Admin\BillkmApproveUpdateRequest;
use App\Http\Requests\Admin\TollPaymentsRequest;
use App\Http\Requests\Admin\EditTollAmountRequest;
use App\Http\Requests\Admin\GenerateMisReportRequest;
use App\Http\Requests\Admin\ExportMisReportRequest;
use App\Http\Requests\Admin\MisReportDetailsRequest;
use App\Http\Requests\Admin\DriverMisReportGenerateDetailsRequest;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class BillingController extends Controller
{
    protected BillingService $billingservice;

    public function __construct(BillingService $billingservice)
    {
        $this->billingservice = $billingservice;
    }

    public function dataForCreate(): FoundationApplication|Response|ResponseFactory
    {
        return $this->billingservice->dataForCreate();
    }

    public function drivermisreportdataForCreate(): FoundationApplication|Response|ResponseFactory
    {
        return $this->billingservice->drivermisreportdataForCreate();
    }

    public function ZohoMisReport(ZohoMisReportRequest $request)
    {
        return $this->billingservice->ZohoMisReport($request);
    }

    public function ZohoMisApprovel(ZohoMisReportRequest $request)
    {
        return $this->billingservice->ZohoMisApprovel($request);
    }

    public function billkm_approve_update(BillkmApproveUpdateRequest $request)
    {
        return $this->billingservice->billkm_approve_update($request);
    }

    public function tollpayments(TollPaymentsRequest $request)
    {
        return $this->billingservice->tollpayments($request);
    }

    public function edit_toll_amount(EditTollAmountRequest $request)
    {
        return $this->billingservice->edit_toll_amount($request);
    }

    public function drivermisreportgenerate(): Application|Response|ResponseFactory
    {
        return $this->billingservice->drivermisreportgenerate();
    }

    public function GenerateMisReport(GenerateMisReportRequest $request): Application|Response|ResponseFactory
    {
        return $this->billingservice->GenerateMisReport($request);
    }

    public function exportmisreport(ExportMisReportRequest $request): Application|Response|ResponseFactory
    {
        return $this->billingservice->exportmisreport($request);
    }

    public function MisReportDetails(MisReportDetailsRequest $request): Application|Response|ResponseFactory
    {
        return $this->billingservice->MisReportDetails($request);
    }

    public function DriverMisReportGenerateNew(): Application|Response|ResponseFactory
    {
        return $this->billingservice->DriverMisReportGenerateNew();
    }

    public function DriverMisReportGenerateDetails(DriverMisReportGenerateDetailsRequest $request): Application|Response|ResponseFactory
    {
        return $this->billingservice->DriverMisReportGenerateDetails($request);
    }
}
