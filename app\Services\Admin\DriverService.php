<?php

namespace App\Services\Admin;

use App\Helpers\CommonFunction;
use App\Helpers\MyHelper;
use Carbon\Carbon;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use App\Models\Driver;

class DriverService
{
    protected CommonFunction $commonFunction;
    protected ?Branch $currentBranch = null;

    public function __construct(CommonFunction $commonFunction)
    {
        $this->commonFunction = $commonFunction;
    }
	
	 public function indexDriver(): FoundationApplication|Response|ResponseFactory
    {
        $authUser = Auth::user();

        try 
		{
            $driver_data = Driver::select('drivers.DRIVERS_ID',
                'drivers.DRIVERS_NAME',
                'drivers.DRIVERS_ADRESS',
                'drivers.DRIVERS_ADDR_LAT',
                'drivers.DRIVERS_ADDR_LONG',
                'drivers.DRIVER_LOC_LAT',
                'drivers.DRIVER_LOC_LONG',
                'drivers.DRIVER_MOBILE',
                'drivers.DRIVER_LICENSE',
                'drivers.LICENCE_EXPIRY',
                'drivers.BADGE_EXPIRY',
                'drivers.LOCATION_NAME',
                'drivers.SHIFT_IN_TIME',
                'drivers.SHIFT_OUT_TIME',
				'O.NAME as ORG_NAME',
				'O.LOCATION'
            )
              
			    ->join("branch", "branch.BRANCH_ID", "=", DB::Raw($authUser->BRANCH_ID))
                ->Join("organization as O", "O.ORGANIZATIONID", "=", "drivers.ORG_ID")
                ->where('drivers.ACTIVE', MyHelper::$RS_ACTIVE)
              //  ->where("O.ORGANIZATIONID","drivers.ORG_ID")
                ->get();

            return response([
                'success' => true,
                'status' => 3,
                'drivers' => $driver_data,
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Driver Index Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ],500);
        }
    }

	

    public function storeDriver($request): FoundationApplication|Response|ResponseFactory
    {

        try {

            DB::beginTransaction();
            $auth_user = Auth::user();

            $driverData = $this->prepareDriverData($request, $auth_user, MyHelper::$RS_ACTIVE, $auth_user->id);
			
			if ($request->hasFile('driver_image') && $request->file('driver_image') !== null) {
                $image = $request->file('driver_image');
                $imageName = $request->DRIVER_MOBILE . '.' . $image->getClientOriginalExtension();
                $image->move(public_path('driver-images'), $imageName);
                //$image->move('assets/img/driver_img/'.$imageName);
                $driverData['DRIVER_IMAGE'] = $imageName;
            }
			
           // print_r( $driverData );exit;
            $driverResult = Driver::create($driverData);



            return response([
                'success' => true,
                'status' => 3,
                'message' => 'Driver Created Successfully',

            ]);

        } catch (\Throwable|\Exception $e) 
		{
            DB::rollBack();
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'validation_controller' => true,
                'status' => 4,
                'message' => 'Driver Created UnSuccessfully',
                'errorM' => $e->getMessage(),
            ],500);
        } finally {
            DB::commit();
        }


    }
	
    /**
     * @throws ConnectionException
     */
    private function prepareDriverData($request, $auth_user, $active, $userId): array
    {
        $date = Carbon::now();
		 $org_id=$this->commonFunction->getOrgId($auth_user->BRANCH_ID);

        return [
		
			'DRIVERS_NAME' => $request->DRIVERS_NAME,
            'DRIVER_MOBILE' => $request->DRIVER_MOBILE,
            'DRIVERS_ADRESS' => $request->DRIVERS_ADRESS,
            'DRIVERS_ADDR_LAT' => $request->DRIVERS_ADDR_LAT,
            'DRIVERS_ADDR_LONG' => $request->DRIVERS_ADDR_LONG,
            'DRIVER_LICENSE' => $request->DRIVER_LICENSE,
            'LICENCE_EXPIRY' => $request->LICENCE_EXPIRY,
            'BADGE_EXPIRY' => $request->BADGE_EXPIRY,
            'MEDICAL_STATUS' => $request->MEDICAL_STATUS,
            'LOCATION_NAME' => $request->LOCATION_NAME,
            'SHIFT_IN_TIME' => $request->SHIFT_IN_TIME,
            'SHIFT_OUT_TIME' => $request->SHIFT_OUT_TIME,
            'ORG_ID' => $org_id,
           
            'COMPLIANT_STATUS' => $active,
			"ACTIVE" => $active,
			"CREATED_BY" => $auth_user->id,
            "created_at" => $date->format("Y-m-d H:i:s"),
		
                     
        ];
    }

    public function deleteDriver($request, $driverAutoIdCrypt): FoundationApplication|Response|ResponseFactory
    {
        $authUser = Auth::user();
       
        try {
            DB::beginTransaction();

            $driverId = Crypt::decryptString($driverAutoIdCrypt);

            $driver = Driver::where('ACTIVE', MyHelper::$RS_ACTIVE)
               // ->where('BRANCH_ID', $authUser->BRANCH_ID)
                ->findOrFail($driverId);

            $driver->update([
                'REMARKS' => $request->input('remark'),
                'ACTIVE' => MyHelper::$RS_INACTIVE,
                'UPDATED_BY' => Auth::user()->id,
            ]);

            DB::commit();

            return response([
                'success' => true,
                'status' => 3,
                'message' => 'Driver Deleted Successfully',
            ]);
        } catch (\Throwable|\Exception $e) {
            DB::rollBack();
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Driver Delete Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ]);
        }
    }



    public function editDriver($driverAutoIdCrypt): FoundationApplication|Response|ResponseFactory
    {
        $authUser = Auth::user();

        try {
            $driverAutoId = Crypt::decryptString($driverAutoIdCrypt);
            $driver = Driver::where('drivers.ACTIVE', MyHelper::$RS_ACTIVE)
                 //->where('drivers.ORG_ID', "=","O.ORGANIZATIONID")
				 ->where('drivers.Drivers_ID', $driverAutoId)
			   
                ->select(
                    'drivers.DRIVERS_ID',
                    'drivers.DRIVERS_NAME',
                    'drivers.DRIVER_MOBILE',
                    'drivers.DRIVERS_ADRESS',
                    'drivers.DRIVERS_ADDR_LAT',
                    'drivers.DRIVERS_ADDR_LONG',
                    'drivers.DRIVER_LICENSE',
                    'drivers.LICENCE_EXPIRY',
                    'drivers.BADGE_EXPIRY',
                    'drivers.MEDICAL_STATUS',
                    'drivers.LOCATION_NAME',
                    'drivers.SHIFT_IN_TIME',
                    'drivers.SHIFT_OUT_TIME',
                    'drivers.DRIVER_IMAGE'
					
                )
				->join("branch", "branch.BRANCH_ID", "=", DB::Raw($authUser->BRANCH_ID))
                ->Join("organization as O", "O.ORGANIZATIONID", "=", "branch.ORG_ID")
                ->firstOrFail();

            return response([
                'success' => true,
                'status' => 3,
                'driver' => $driver,
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Driver Edit Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ],500);
        }
    }

    public function updateDriver($request, $driverAutoIdCrypt): FoundationApplication|Response|ResponseFactory
    {
        $authUser = Auth::user();
        try {
            DB::beginTransaction();
            $driverAutoId = Crypt::decryptString($driverAutoIdCrypt);
            $driver = Driver::findOrFail($driverAutoId);
            $auth_user = Auth::user();
            

            $driverData = $this->prepareDriverDataForUpdate($request, $auth_user, $driver);

            if ($request->hasFile('driver_image') && $request->file('driver_image') !== null) {
                $image = $request->file('driver_image');
                $imageName = $request->DRIVER_MOBILE . '.' . $image->getClientOriginalExtension();
                $image->move(public_path('driver-images'), $imageName);
                //$image->move('assets/img/driver_img/'.$imageName);
                $driverData['DRIVER_IMAGE'] = $imageName;
            }
			
            $driver->update($driverData);

            DB::commit();


            return response([
                'success' => true,
                'status' => 3,
                'message' => 'Driver Updated Successfully',
            ]);

        } catch (\Throwable|\Exception $e) {
            DB::rollBack();
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'message' => 'Driver Update Unsuccessful',
                'status' => 4,
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ],500);
        }
    }

    /**
     * @throws ConnectionException
     */
    private function prepareDriverDataForUpdate($request, $auth_user, $driver): array
    {
        $date = Carbon::now();

        return [
            'DRIVERS_NAME' => $request->DRIVERS_NAME,
            'DRIVER_MOBILE' => $request->DRIVER_MOBILE,
            'DRIVERS_ADRESS' => $request->DRIVERS_ADRESS,
            'DRIVERS_ADDR_LAT' => $request->DRIVERS_ADDR_LAT,
            'DRIVERS_ADDR_LONG' => $request->DRIVERS_ADDR_LONG,
            'DRIVER_LICENSE' => $request->DRIVER_LICENSE,
            'LICENCE_EXPIRY' => $request->LICENCE_EXPIRY,
            'BADGE_EXPIRY' => $request->BADGE_EXPIRY,
            'MEDICAL_STATUS' => $request->MEDICAL_STATUS,
            'LOCATION_NAME' => $request->LOCATION_NAME,
            'SHIFT_IN_TIME' => $request->SHIFT_IN_TIME,
            'SHIFT_OUT_TIME' => $request->SHIFT_OUT_TIME,
            "UPDATED_BY" => $auth_user->id,
            "updated_at" => $date->format("Y-m-d H:i:s"),
        ];
    }

    


    public function dataForCreateDriver(): FoundationApplication|Response|ResponseFactory
    {

        try {
            $user = Auth::user();
            $rsActive = MyHelper::$RS_ACTIVE;
            $branchId = $user->BRANCH_ID;

            $branch = DB::table('branch')
                ->select('BRANCH_ID', 'DIVISION_ID')
                ->where('ACTIVE', $rsActive)
                ->where('BRANCH_ID', $branchId)
                ->get();

            $location = DB::table('locations')
                ->select('LOCATION_ID', 'LOCATION_NAME')
                ->where('ACTIVE', $rsActive)
                ->where('DIVISION_ID', $branch[0]->DIVISION_ID)
                ->get();
				
				$medical_status = array(
                    array(
                        'value' => 'Received',
                        'name' => 'Received'
                    ),
                    array(
                        'value' => 'Not Received',
                        'name' => 'Not Received'
                    )
                );

                      return response([
                'success' => true,
                'status' => 3,
                'locations' => $location,
                'medical_status' => $medical_status,
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Data For Create for Driver  Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ],500);
        }
    }
	
  

    public function paginationDriver($request, bool $active = true): FoundationApplication|Response|ResponseFactory
    {
        try {
           
            $perPage = $request->input('per_page', MyHelper::$PAGINATION_PER_PAGE);
            $branch_id = Auth::user()->BRANCH_ID;
            $activeStatus = $active ? MyHelper::$RS_ACTIVE : MyHelper::$RS_INACTIVE;
            $authUser = Auth::user();
           // echo $authUser->BRANCH_ID;exit;
            $driver = Driver::select('drivers.DRIVERS_ID',
            'drivers.DRIVERS_NAME',
            'drivers.DRIVERS_ADRESS',
            'drivers.DRIVERS_ADDR_LAT',
            'drivers.DRIVERS_ADDR_LONG',
            'drivers.DRIVER_LOC_LAT',
            'drivers.DRIVER_LOC_LONG',
            'drivers.DRIVER_MOBILE',
            'drivers.DRIVER_LICENSE',
            'drivers.LICENCE_EXPIRY',
            'drivers.BADGE_EXPIRY',
            'drivers.LOCATION_NAME',
            'drivers.MEDICAL_STATUS',
            'drivers.SHIFT_IN_TIME',
            'drivers.SHIFT_OUT_TIME',
            'drivers.DRIVER_IMAGE',
            'O.NAME as ORG_NAME',
            'O.LOCATION')
            ->where('drivers.ACTIVE', MyHelper::$RS_ACTIVE)
           //->where('drivers.ORG_ID', "=","32")
            ->join("branch", "branch.BRANCH_ID", "=", DB::Raw($authUser->BRANCH_ID))
            ->join("organization as O", "O.ORGANIZATIONID", "=", "drivers.ORG_ID");
           

            $filterModel = $request->input('filterModel');
            if ($filterModel) {
                foreach ($filterModel as $field => $filter) {
                    if (isset($filter['filter']) && $filter['filter'] !== '') {
                        $value = $filter['filter'];
                        $type = $filter['type'];

                        switch ($field) {
                            case 'DRIVER_MOBILE':
                                $driver->where('drivers.DRIVER_MOBILE', 'like', "%{$value}%");
                                break;
                            case 'DRIVERS_NAME':
                                $driver->where('drivers.DRIVERS_NAME', 'like', "%{$value}%");
                                break;
                            
                        }
                    }
                }
            }


            if ($request->has('orderBy') && !empty($request->input('orderBy'))) 
            {
                $orderBy = $request->input('orderBy');
                $order = $request->input('order', 'asc');
                $driver->orderBy($orderBy, $order);
            } else {
                $driver->orderBy('drivers.created_at', 'desc');
            }
// echo $driver->count();exit;
            if ($perPage === MyHelper::$PAGINATION_PER_PAGE_ALL) {
                $paginatedDriver = $driver->paginate($driver->count());
            } else {
                $perPage = is_numeric($perPage) ? (int)$perPage : MyHelper::$PAGINATION_PER_PAGE;
                $paginatedDriver = $driver->paginate($perPage);
            }
           // print_r($paginatedDriver);exit;
            return response([
                'success' => true,
                'status' => 3,
                'Drivers' => $paginatedDriver,
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'Driver Pagination Unsuccessful' : 'Deactivate Driver Pagination Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ],500);
        }
    } 

}
