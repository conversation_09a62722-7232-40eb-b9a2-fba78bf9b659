<?php

namespace App\Services\Admin;

use App\Helpers\CommonFunction;
use App\Helpers\MyHelper;
use App\Models\Branch;
use App\Models\property;
use App\Models\Roster;
use App\Models\RosterPassenger;
use App\Models\email_notification;
use App\Models\Cab_allocation;
use App\Models\OtpVerify;
use App\Models\RouteEscorts;
use App\Models\Reason_Log;
use App\Models\Cab;
use App\Models\Sms;
use App\Models\Location;
use App\Models\CabAttendance;
use App\Models\Vehicle;
use App\Models\VehicleModel;
use App\Models\Driver;
use App\Models\Employee;
use App\Models\AlertSignalFailure;
use App\Models\File_upload;
use App\Models\Input_data;
use App\Models\Vendor;
use Validator;
use Redirect;


use Carbon\Carbon;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Crypt;

use Illuminate\Http\Request;

use App\Http\Controllers\ElasticController;


class RosterUploadService
{
    protected CommonFunction $commonFunction;
    protected ?Branch $currentBranch = null;
     

    public function __construct(CommonFunction $commonFunction)
    {
        $this->commonFunction = $commonFunction;
       
    }

    private function getPropertyValue()
    {
        $branch_id= Auth::user()->BRANCH_ID;
        //    exit;
        $property = property::where('BRANCH_ID',  $branch_id)
            ->where('ACTIVE', MyHelper::$RS_ACTIVE)
            //->where('PROPERTIE_NAME', $propertyName)
            ->get();
			
        return $property;
    }

    public function post_roster_upload($request): FoundationApplication|Response|ResponseFactory
    {
        
                
        try {
            $dbname = Auth::user()->dbname;
            $branch_id = Auth::user()->BRANCH_ID;
            // getting all of the post data
            $request->file('file');
            $file = array('file' => $request->file('file'));
            //echo "test";exit;
            // setting up rules
            $rules = array('file' => 'required|max:10000',); //mimes:csv,bmp,png and for max size max:10000
            // doing the validation, passing post data, rules and the messages
            $validator = Validator::make($file, $rules);

            if ($validator->fails()) {
                // send back to the page with the input data and errors
                return response([
                    'success' => true,
                    'status' => 3,
                    'message' => "Invalid input",
                ]);
               
              //  return Redirect::to('upload')->withInput()->withErrors($validator);
            } else {
                // checking file is valid.                   
                if ($request->file('file')->isValid()) {

                    // $destinationPath = 'public/uploads'; // upload path

                    $extension = $request->file('file')->getClientOriginalExtension(); // getting image extension                       
                    $filename = $request->file('file')->getClientOriginalName();
                    $size = $request->file('file')->getSize();

                    // Input::file('file')->move($destinationPath, $filename); // uploading file to given path 

                   
                    
                    if ($filename == MyHelper::$FILE_UPLOAD_NAME) {
                       
                        $user_id = Auth::user()->id;   //get session user name
                        $file_upload = new File_upload;
                        //$file_upload=$file_upload1->setConnection("$dbname");
                        $file_upload->FILE_NAME = $filename;
                        $file_upload->FILE_SIZE = $size;
                        $file_upload->FILE_TYPE = $extension;
                        $file_upload->CREATED_BY = $user_id;
                        $file_upload->UPDATED_BY = $user_id;
                        $file_upload->BRANCH_ID = $branch_id;
                        $file_upload->STATUS = 0;
                        $file_upload->save();
                        $FILE_ID = $file_upload->FILE_ID;
                       //
                        $customerArr = $this->importCsv($request->file('file')->getRealPath(), $FILE_ID);
                        // $customerArr = $this->importCsv('D://eclipse development/' . $filename, $FILE_ID);                    
                        if ($customerArr) {
                          
                            return $customerArr;
                            //return $this->locationupdate();
                        } else {
                            $this->InsertRoster();
                        
                            return Redirect::to('upload');
                        }
                    }
                    else
                    {
                        return response([
                           'success' => false,
                           'status' => 3,
                           'message' => "Invalid file name",
                        ]);
                    }
                }
            }
           
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'No data',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function importCsv($file, $FILE_ID) 
    {
        return $this->csvToArray($FILE_ID, $file);
    }

    function csvToArray($FILE_ID, $filename = '', $delimiter = ',')
    {
        ini_set('max_execution_time', 0);
        $dbname = Auth::user()->dbname;
        try {
          //  echo "file_upload_uplosdaadd ad";exit;
            if (!file_exists($filename) || !is_readable($filename))
                return false;

            $header = null;
            $data = array();
            $j = 0;
            $isCorrectLocation = false;
            $input_Arr = array();
            $location_list_Arr = array();
            $check_excel_data = array();
            $employees_list_Arr = array();
            $is_input_data_Arr = array();
            $vendor_list_Arr = array();
           // $obj2 = new CommonController();

            $branch_id = Auth::user()->BRANCH_ID; //get session branch id 
            $TRAVEL_TYPE_PICKUP = MyHelper::$RP_PICKROUTE;
            $TRAVEL_TYPE_DROP = MyHelper::$RP_DROPROUTE;

            $branch_det = DB::connection("$dbname")->table("branch")->select("BRANCH_ID", "BRANCH_NAME", "DIVISION_ID")->where("BRANCH_ID", "=", $branch_id)->get();
            $site_name = $branch_det[0]->BRANCH_NAME;
            $DIVISION_ID = $branch_det[0]->DIVISION_ID;

            $location_rs_list = Location::on("$dbname")->where([['ACTIVE', '=', 1], ['DIVISION_ID', "=", $DIVISION_ID]])->get();
            foreach ($location_rs_list as $location_list) {
                $location_list_Arr[] = $location_list->LOCATION_NAME;
            }

            $employees_rs_list = Employee::on("$dbname")->where([['ACTIVE', '=', 1], ['BRANCH_ID', '=', $branch_id],])->get();
            foreach ($employees_rs_list as $employees) {
                $employees_list_Arr[] = $employees->EMPLOYEES_ID;
            }

            $vendor_rs_list = Vendor::on("$dbname")->where([['ACTIVE', '=', 1], ['BRANCH_ID', '=', $branch_id],])->get();
            foreach ($vendor_rs_list as $vendors) {
                $vendor_list_Arr[] = $vendors->NAME;
            }


            $input_data_rs_list = Input_data::on("$dbname")->where([['BRANCH_ID', '=', $branch_id],])
                    ->whereDate('ESTIMATE_START_TIME', '>=', Carbon::now()->format("Y-m-d"))
                    ->get();
           // echo "<pre>";	print_r($input_data_rs_list);exit;
            foreach ($input_data_rs_list as $input_datas) {
                $is_input_data_Arr[] = $input_datas->EMPLOYEE_ID . '|' . $input_datas->ROUTE_ID . '|' . $input_datas->TRIP_TYPE . '|' . $input_datas->ESTIMATE_START_TIME;
            }
            $isinserted='';
            if (($handle = fopen($filename, 'r')) !== false) {
                while (($row = fgetcsv($handle, 1000, $delimiter)) !== false) {
                    if (!$header) {
                        $header = $row;
                        $err_msg = '';
                        for ($i = 0; $i < count($row); $i++) {

                            if ($i == 0) {
                                if ($row[$i] != 'RouteId') {
                                    $err_msg = "Don't Change or Replace the " . $row[$i] . ", It Should be in Cell No " . $i . "th Position";
                                  //  return $err_msg;
                                }
                            } else if ($i == 1) {
                                if ($row[$i] != 'TripType') {
                                    $err_msg = "Don't Change or Replace the " . $row[$i] . ", It Should be in Cell No " . $i . "th Position";
                                   // return $err_msg;
                                }
                            } else if ($i == 2) {
                                if ($row[$i] != 'LoginDate') {
                                    $err_msg = "Don't Change or Replace the " . $row[$i] . ", It Should be in Cell No " . $i . "th Position";
                                   // return  $err_msg;
                                }
                            } else if ($i == 3) {
                                if ($row[$i] != 'PickupTime') {
                                    $err_msg = "Don't Change or Replace the " . $row[$i] . ", It Should be in Cell No " . $i . "th Position";
                                 //   return $err_msg;
                                }
                            } else if ($i == 4) {
                                if ($row[$i] != 'EmpId') {
                                    $err_msg = "Don't Change or Replace the " . $row[$i] . ", It Should be in Cell No " . $i . "th Position";
                                  //  return  $err_msg;
                                }
                            } else if ($i == 5) {
                                if ($row[$i] != 'VendorName') {
                                    $err_msg = "Don't Change or Replace the " . $row[$i] . ", It Should be in Cell No " . $i . "th Position";
                                    //return Session::flash('error', $err_msg);
                                  //  return $err_msg;
                                }
                            } else if ($i == 6) {
                                if ($row[$i] != 'TariffType') {
                                    $err_msg = "Don't Change or Replace the " . $row[$i] . ", It Should be in Cell No " . $i . "th Position";
                                   // return Session::flash('error', $err_msg);
                                    //return  $err_msg;
                                }
                            }

                            if ($err_msg != '') {
                                return response([
                                    'success' => false,
                                    'status' => 3,
                                    'message' => $err_msg,
                                ]);
                               
                            }
                        }
                    } else {

                        // $data[] = array_combine($header, $row);

                        for ($i = 0; $i < count($row); $i++) {
                            if ($i == 0) {
                                if ($row[$i] == '') {
                                    $err_msg = "Don't upload empty rows";
                                    return response([
                                        'success' => false,
                                        'status' => 3,
                                        'message' => $err_msg,
                                    ]);
                                }
                                $route_id = $row[$i];
                            } else if ($i == 1) {
                                $trip_type = strtoupper($row[$i]);
                                if ($trip_type != $TRAVEL_TYPE_PICKUP && $trip_type != $TRAVEL_TYPE_DROP) {
                                    $err_msg = "Don't upload invalid trip type upload P or D  ";
                                    return response([
                                        'success' => false,
                                        'status' => 3,
                                        'message' => $err_msg,
                                    ]);
                                }
                            } else if ($i == 2) {
                                $estimate_start_time = date('Y-m-d H:i:s', strtotime($row[$i]));
                                $estimate_date = date('Y-m-d', strtotime($row[$i]));
                                $in_time = date('H', strtotime($row[$i]));
                                $dt = Carbon::parse($estimate_start_time);
                                   if ($dt->isPast()) {
                                  $err_msg = "please upload future datetime roster Details ";
                                  return response([
                                    'success' => false,
                                    'status' => 3,
                                    'message' => $err_msg,
                                ]);
                                  
                                  }

                                  if ($dt->diffInDays(Carbon::now()) > MyHelper::$FILE_UPLOAD_DAYS_LIMIT) {
                                  $err_msg = "Don't upload future above 3 Days roster Details ";
                                  return response([
                                    'success' => false,
                                    'status' => 3,
                                    'message' => $err_msg,
                                ]);
                                  } 
                            } else if ($i == 3) {
                                //$current_datetime = $date->format("Y-m-d");
                                //$estimate_time = $estimate_date . ' ' . date('H:i:s', strtotime($row[$i]));
                                if ($trip_type == $TRAVEL_TYPE_PICKUP) {
                                    $pickup_time = date('H', strtotime($row[$i]));
                                    $diff = (int) $pickup_time - (int) $in_time;
                                    if ($diff == 23 || $diff == 22 || $diff == 21 || $diff == 20) {
                                        $date1 = Carbon::parse($estimate_date)->subDay();
                                        $cur_date = $date1->format("Y-m-d");
                                        $estimate_time = $cur_date . ' ' . date('H:i:s', strtotime($row[$i]));
                                    } else {
                                        $estimate_time = $estimate_date . ' ' . date('H:i:s', strtotime($row[$i]));
                                    }
                                } else {
                                    $estimate_time = $estimate_date . ' ' . date('H:i:s', strtotime($row[$i]));
                                }
                                $dt = Carbon::parse($estimate_time);
                                  if ($dt->gte(Carbon::parse($estimate_start_time))) {
                                  $err_msg = "Don't upload pickup time greater than to logindatetime  ";
                                  return response([
                                    'success' => false,
                                    'status' => 3,
                                    'message' => $err_msg,
                                ]);
                                 
                                  }
                            } else if ($i == 4) {
                                $emp_id = trim($row[$i]);
                            } else if ($i == 5) {
                                $vendor_name = $row[$i];
                            } else if ($i == 6) {
                                $tariff_type = $row[$i];
                            }
                        }



                        $is_already_route = $emp_id . '|' . $route_id . '|' . $trip_type . '|' . $estimate_start_time;

                        $date = Carbon::now();
                        $current_datetime = $date->format("Y-m-d H:i:s");
                        $user_id = Auth::user()->id;   //get session user name
                        $branch_id = Auth::user()->BRANCH_ID; //get session branch id 

                        /*
                         * below inarray check uploaded file location valid or invalid
                         */
                        /*  if (in_array($location, $location_list_Arr, true)) {
                          $status = 1;
                          } else {
                          $status = 0;
                          $isCorrectLocation = true;
                          } */

                        /*
                         * below inarray check uploaded file employees valid or invalid
                         */

                        if (in_array($emp_id, $employees_list_Arr, true)) {
                           
                            
                        } else {
                           // return Session::flash('error', '(' . $emp_id . ')' . ' invalid employee user , please upload valid employee user ');
                           $err_msg =  $emp_id . ' invalid employee user , please upload valid employee user ';

                           return response([
                            'success' => false,
                            'status' => 3,
                            'message' => $err_msg,
                        ]);
                        }

                        /*
                         * below inarray check uploaded file vendorname valid or invalid
                         */

                        if (in_array($vendor_name, $vendor_list_Arr, true)) {
                            
                        } else {
                           // return Session::flash('error', '(' . $vendor_name . ')' . ' invalid vendor name , please upload valid vendor name ');
                            $err_msg =  $vendor_name. 'invalid vendor name , please upload valid vendor name ';
                            return response([
                                'success' => false,
                                'status' => 3,
                                'message' => $err_msg,
                            ]);
                        }
                        if (in_array($is_already_route, $check_excel_data, true)) {
                            $err_msg = "Duplicate Excel Data " . $route_id . '/' . $trip_type . '/' . $emp_id . '/' . $estimate_start_time;
                            return response([
                                'success' => false,
                                'status' => 3,
                                'message' => $err_msg,
                            ]);
                        }
                        

                        if (in_array($is_already_route, $is_input_data_Arr, true)) {
                            $err_msg = "Already Uploaded Data " . $route_id . '/' . $trip_type . '/' . $emp_id . '/' . $estimate_start_time;
                            return response([
                                'success' => false,
                                'status' => 3,
                                'message' => $err_msg,
                            ]);
                        } 
                        else {
                            
                            $check_excel_data[] = $emp_id . '|' . $route_id . '|' . $trip_type . '|' . $estimate_start_time;
                            $status = 1;

                            foreach ($employees_rs_list as $employees) {
                                if ($employees->EMPLOYEES_ID == $emp_id) {

                                    $emp_name = "Test";
                                    $emp_mobile = 1231313;
                                    //$emp_name = $this->commonFunction->AES_DECRYPT($employees->NAME, env('AES_ENCRYPT_KEY'));
                                   // $emp_mobile = $this->commonFunction->AES_DECRYPT($employees->MOBILE, env('AES_ENCRYPT_KEY'));
                                    $project_name = $employees->PROJECT_NAME;
                                    $address = $employees->ADDRESS;
                                    $gender = $employees->GENDER;
                                    if ($employees->LOCATION_ID == '0' || $employees->LOCATION_ID == NULL) {
                                        $err_msg = "The employee location not mapped " . $route_id . '/' . $trip_type . '/' . $emp_id;
                                        return response([
                                            'success' => false,
                                            'status' => 3,
                                            'message' => $err_msg,
                                        ]);
                                    }

                                    if ($employees->ADDRESS_TYPE == 'S') {
                                        $query = "select EA.*,L2.LOCATION_ID,L2.LOCATION_NAME as sec_loc from employee_address EA 
											LEFT JOIN locations L2 on L2.LOCATION_ID=EA.LOCATION_ID and EA.LOCATION_ID=L2.LOCATION_ID and EA.ACTIVE=1
											where EA.EMP_AUTO_ID=" . $employees->id . " and EA.ACTIVE=1";
                                        $addr_res = DB::select($query);
                                        if (count($addr_res) > 0) {
                                            $location = $addr_res[0]->sec_loc;
                                        } else {
                                            $location = '';
                                        }
                                    } else {
                                        foreach ($location_rs_list as $location_list) {
                                            if ($location_list->LOCATION_ID == $employees->LOCATION_ID) {
                                                $location = $location_list->LOCATION_NAME;
                                                break;
                                            }
                                            /*  else
                                              {
                                              $err_msg = "The employee location not mapped " . $route_id . '/' . $trip_type . '/' . $emp_id ;
                                              return Session::flash('error', $err_msg);
                                              } */
                                        }
                                    }
                                }
                            }
                            
                           

                            $input_Arr[$j] = array("ROUTE_ID" => $route_id,
                                "FILE_ID" => $FILE_ID,
                                "BRANCH_ID" => $branch_id,
                                "TRIP_TYPE" => $trip_type,
                                "ESTIMATE_START_TIME" => $estimate_start_time,
                                "ESTIMATE_TIME" => $estimate_time,
                                "SITE_NAME" => $site_name,
                                "LOCATION" => $location,
                                'EMPLOYEE_ID' => $emp_id,
                                "EMPLOYEE_NAME" => $emp_name,
                                "GENDER" => $gender,
                                "EMPLOYEE_MOBILE" => $emp_mobile,
                                "PROJECT_NAME" => $project_name,
                                "ADDRESS" => $address,
                                "VENDOR_NAME" => $vendor_name,
                                "TARIFF_TYPE" => $tariff_type,
                                "STATUS" => $status,
                                "REQUESTED_TYPE" => "Regular",
                                "CREATED_BY" => $user_id,
                                "UPDATED_BY" => $user_id,
                                "created_at" => $current_datetime,
                                "updated_at" => $current_datetime);
                        }
                        $j++;
                    }
                }


                fclose($handle);
            }
           

            foreach (array_chunk($input_Arr,1000) as $input)
            {                              
                $isinserted = Input_data::on("$dbname")->insert($input);
            }


          //  $isinserted = Input_data::on("$dbname")->insert($input_Arr);
            if ($isinserted) {
                if ($isCorrectLocation) {
                    return $isCorrectLocation;
                } else {
                        $this->InsertRoster();
                    //return Session::flash('success', $j . ' Rows Uploaded successfully');
                    return response([
                        'success' => true,
                        'status' => 3,
                        'message' => 'success '. $j. ' Rows Uploaded successfully',
                    ]);
                  //  return 'success'. $j. ' Rows Uploaded successfully';
                }
            }
        } catch (Exception $exc) {
            echo $exc->getTraceAsString();
        }
    }

    public function InsertRoster() {
        ini_set('max_execution_time', 0);
        //uploadrun
        try {
            $dbname = Auth::user()->dbname;
            $branchid = Auth::user()->BRANCH_ID;
            $current_datetime=date('Y-m-d H:i:s');
            $filelist = File_upload::on("$dbname")->where([['STATUS', '=', '0'], ['BRANCH_ID', "=", $branchid]])
                    ->get();
                  //  echo "<pre>";print_r( $filelist);exit;
                  
            for ($j = 0; $j < count($filelist); $j++) {

                $file_id = $filelist[$j]['FILE_ID'];
                File_upload::on("$dbname")->where('FILE_ID', '=', $file_id)
                        ->update(array('STATUS' => 1));

                /* log */
                $errorlogpath = pathinfo(ini_get('error_log'));
                //$errorlogfile = "D:/xampp/htdocs/TMS_BACKUP/storage/logs/" . date('Y-m-d') . "-upload.log";
                $errorlogfile = "/var/www/html/TMS/storage/logs/" . date('Y-m-d') . "-upload.log";
                if (file_exists($errorlogfile)) {
                    ini_set('error_log', $errorlogfile);
                } else {
                    $errfh = fopen($errorlogfile, "a+");
                    if (is_resource($errfh)) {
                        ini_set('error_log', $errorlogfile);
                        fclose($errfh);
                    }
                }
                error_log("~~~~~~~  upload File  " . $file_id . " BRANCH_ID " . $branchid . "  ~~~~~~", 0);
                /* log */

                $input_datas = Input_data::on("$dbname")->where([['FILE_ID', '=', $file_id], ['STATUS', '=', '1'],]) //fetch input datas table group by route id
                                ->groupBy('ROUTE_ID')
                                ->groupBy('TRIP_TYPE')
                                ->groupBy('ESTIMATE_START_TIME')
                                ->orderBy('INPUT_ID', 'asc')->get();
                //->chunk(1000, function ($input_datas) {
                // echo '<pre>';
                // print_r($input_datas);exit;
                if (count($input_datas) > 0) {
                    error_log("~~~~~~~  uploadeddd data " . json_encode($input_datas) . " ~~~~~~", 0);
                    foreach ($input_datas as $input_data) {
                        $dbname = Auth::user()->dbname;
                        $route_id = $input_data['ROUTE_ID'];
                        $estimate_start_time = $input_data['ESTIMATE_START_TIME'];
                        $vendor_name = $input_data['VENDOR_NAME'];
                        $site_name = $input_data['SITE_NAME'];
                        $location = $input_data['LOCATION'];
                        $trip_type = $input_data['TRIP_TYPE'];
                        $branch_id = $input_data['BRANCH_ID'];
                        $file_id = $input_data['FILE_ID'];
                        $user_id = $input_data['CREATED_BY'];


                        $vendor_id_rs = vendor::on("$dbname")->where([
                                        ['BRANCH_ID', '=', $branch_id],
                                        ['NAME', '=', $vendor_name]])->first();
                        $vendor_id = $vendor_id_rs->VENDOR_ID;

                        //INSERT ROSTER TABLE 
                        $roster1 = new Roster;
                        $roster = $roster1->setConnection("$dbname");
                        $roster->BRANCH_ID = $branch_id;
                        $roster->FILE_ID = $file_id;
                        $roster->ROUTE_ID = $route_id;
                        $roster->TRIP_TYPE = $trip_type;
                        if ($trip_type == "P") {
                            $roster->ESTIMATE_END_TIME = $estimate_start_time;
                        } else {
                            $roster->ESTIMATE_START_TIME = $estimate_start_time;
                        }
                        $roster->VENDOR_ID = $vendor_id;
                        $roster->ACTIVE = 1;
                        $roster->ROSTER_STATUS = 1;
                        $roster->CREATED_BY = $user_id;
                        $roster->UPDATED_BY = $user_id;
                        $roster->created_at = $current_datetime;
                        $roster->save();
                        $roster_id = $roster->ROSTER_ID;



                        $SMS_TAG = '';
                        $SMS_STS = '';
                        $HELP_LINE = '';
                        $ESCORT_VALUE = '';
                        $ESCORT_ENABLE = '';
                        $ESCORT_START_TIME = '';
                        $ESCORT_END_TIME = '';
                        $ADDRESSTIME = '';
                        $EMAIL_NOTIFICATION = '';


                        //fetch SMS property in  properties  table 
                        $SMS_datas = property::on("$dbname")->where([
                                        ['BRANCH_ID', '=', $branchid],
                                        ['ACTIVE', '=', '1'],
                                ])
                                ->get();
                        for ($i = 0; $i < count($SMS_datas); $i++) {
                            $property_name = $SMS_datas[$i]['PROPERTIE_NAME'];
                            $property_value = $SMS_datas[$i]['PROPERTIE_VALUE'];

                            switch ($property_name) {
                                case "SMS TAG":
                                    $SMS_TAG = 'RRDCAB';
                                    //$SMS_TAG = $property_value;
                                    break;
                                case "HELPLINE NO":
                                    $HELP_LINE = $property_value;
                                    break;
                                case "UPLOAD SMS":
                                    $SMS_STS = $property_value;
                                    break;
                                case "ESCORT":
                                    $ESCORT_VALUE = $property_value;
                                    break;
                                case "ESCORT ENABLE":
                                    $ESCORT_ENABLE = $property_value;
                                    break;
                                case "ESCORT START TIME":
                                    $ESCORT_START_TIME = $property_value;
                                    break;
                                case "ESCORT END TIME":
                                    $ESCORT_END_TIME = $property_value;
                                    break;
                                case "ADDRESS TIME":
                                    $ADDRESSTIME = $property_value;
                                    break;
                                case "EMP_UPLOAD_EMAILL_NOTIFICATION":
                                    $EMAIL_NOTIFICATION = $property_value;
                                    break;
                                default:
                                    break;
                            }
                        }


                        /**
                         * is escort time check validation
                         * */
                        $isEscort = 'false';
                        if ($ESCORT_ENABLE == 'Y') {
                            $is_Escort_Time = date('H:i:s', strtotime($estimate_start_time));
                            $ret1 = $this->commonFunction->check_time($ESCORT_START_TIME, $ESCORT_END_TIME, $is_Escort_Time) ? "yes" : "no";
                            if ($ret1 == "yes") {
                                $isEscort = 'true';
                            }
                        }



                        if ($roster_id > 0) {
                            /**
                             * fetch input datas  table route_id wise list
                             * */
                            $dbname = Auth::user()->dbname;
                            $input_child_datas = Input_data::on("$dbname")->where([
                                            ['FILE_ID', '=', $file_id],
                                            ['BRANCH_ID', '=', $branch_id],
                                            ['STATUS', '=', '1'],
                                            ['ROUTE_ID', '=', $route_id],
                                            ['TRIP_TYPE', '=', $trip_type],
                                            ['ESTIMATE_START_TIME', '=', $estimate_start_time]])
                                    ->orderBy('INPUT_ID', 'asc')
                                    ->get();
                            //  error_log("~~~~~~~  input_child_datas " . json_encode($input_child_datas) . " ~~~~~~", 0);
                            // print_r($input_child_datas);
                            //exit;
                            $roster_passanger_arr = array();
                            $passanger_sms_arr = array();
                            $location_list = array();
                            $location_km_list = array();
                            $gender_list = array();
                            $notification_array = array();
                            $passenger_allot_count = count($input_child_datas);
                            $input_id_list = array();
                            $email_arr = array();
                            $date = Carbon::now();
                           // $common = new CommonController();

                            for ($i = 0; $i < count($input_child_datas); $i++) {

                                $current_datetime = $date->format("Y-m-d H:i:s");
                                $emp_id = $input_child_datas[$i]['EMPLOYEE_ID'];
                                $estimate_time = $input_child_datas[$i]['ESTIMATE_TIME'];
                                $estimate_start_time = $input_child_datas[$i]['ESTIMATE_START_TIME'];
                                $location = $input_child_datas[$i]['LOCATION'];
                                $input_id = $input_child_datas[$i]['INPUT_ID'];
                                $gender = $input_child_datas[$i]['GENDER'];
                                $emp_mobile = $input_child_datas[$i]['EMPLOYEE_MOBILE'];

                                // error_log("~~~~~~~  emp_mobile " . $emp_mobile . " ~~~~~~", 0);

                                /**
                                 * GET LOCATION WISE APPROVED KM
                                 * */
                                $empmobile_gcm = "";
                                $location_id = "";
                                $mobile_category = "";

                                if ($branch_id == 1) {
                                    $location_km_rs = location::on("$dbname")->where([
                                                    ['LOCATION_NAME', '=', $location], ['ACTIVE', '=', 1],])
                                            ->join('approve_distances as AD', 'AD.LOCATION_ID', '=', 'locations.LOCATION_ID')
                                            ->where("AD.BRANCH_ID", "=", $branch_id)
                                            ->first();
                                } else {
                                    $checkdatatime = $this->address_time_check($ADDRESSTIME, $estimate_start_time);
                                    if ($checkdatatime == 'LOCATION') {
                                        $location_km_rs = Location::on("$dbname")->where([
                                                        ['LOCATION_NAME', '=', $location], ['ACTIVE', '=', 1],])
                                                ->join('approve_distances as AD', 'AD.LOCATION_ID', '=', 'locations.LOCATION_ID')
                                                ->where("AD.BRANCH_ID", "=", $branch_id)
                                                ->first();
                                    } else {
                                        $location_km_rs = Employee::on("$dbname")->where([['EMPLOYEES_ID', '=', $emp_id], ['ACTIVE', '=', 1], ['BRANCH_ID', '=', $branch_id],])->first();
                                    }
                                }
                                if ($location_km_rs == null) {
                                    $km = 0;
                                    $empmobile_gcm = "";
                                    $mobile_category = "";
                                } else {
                                    if ($branch_id == 1) {
                                        $km = $location_km_rs->APPROVED_DISTANCE;
                                        $location_id = $location_km_rs->LOCATION_ID;
                                    } else {
                                        if ($checkdatatime == 'LOCATION') {
                                            $km = $location_km_rs->APPROVED_DISTANCE;
                                        } else {
                                            $km = $location_km_rs->DISTANCE;
                                        }
                                        if ($location_km_rs->ADDRESS_TYPE == 'S') {
                                            $emp_sec_addr = Employee_address::on("$dbname")->where([['EMP_AUTO_ID', '=', $location_km_rs->id], ['ACTIVE', '=', 1], ['ADDRESS_TYPE', '=', 'S'],])->first();
                                            if ($emp_sec_addr == null) {
                                                $location_id = $location_km_rs->LOCATION_ID;
                                            } else {
                                                $location_id = $emp_sec_addr->LOCATION_ID;
                                                $km = $emp_sec_addr->DISTANCE;
                                            }
                                        } else {
                                            $location_id = $location_km_rs->LOCATION_ID;
                                        }
                                        $empmobile_gcm = $location_km_rs->MOBILE_GCM;
                                        $mobile_category = $location_km_rs->MOBILE_CATEGORY;
                                        $email = $location_km_rs->EMAIL;
                                        $email_id = '<EMAIL>';
                                       // $email_id = $this->commonFunction->AES_DECRYPT($email, env('AES_ENCRYPT_KEY'));
                                    }
                                }

                                $est_start_time = '';
                                $est_end_time = '';
                                if ($trip_type == "P") {
                                    $est_start_time = $estimate_time;
                                    $est_end_time = $estimate_start_time;
                                    $pickdropsms = 'Pickup';
                                } else {
                                    $est_start_time = $estimate_start_time;
                                    $est_end_time = null;
                                    $pickdropsms = 'Drop';
                                }



                                $roster_passanger_arr[$i] = array("ROSTER_ID" => $roster_id,
                                    "EMPLOYEE_ID" => $emp_id,
                                    "ESTIMATE_START_TIME" => $est_start_time,
                                    "ESTIMATE_END_TIME" => $est_end_time,
                                    "LOCATION_ID" => $location_id,
                                    "ACTIVE" => 1,
                                    "CREATED_BY" => $user_id,
                                    "UPDATED_BY" => $user_id,
                                    "CREATED_DATE" => $current_datetime,
                                    "ROUTE_ORDER" => $km,
                                    "updated_at" => $current_datetime);
                                $location_list[$i] = $location;
                                $location_km_list[$i] = $km;
                                $input_id_list[$i] = $input_id;
                                $gender_list[$i] = $emp_id . '-' . $gender;


                                if ($SMS_STS == 'Y' && $emp_mobile != '0') {

                                    //  error_log("~~~~~~~ SMS_STS " .$SMS_STS . " ~~~~~~", 0);
                                    $msg = "Greetings from " . $site_name . " Transport, Your " . $pickdropsms . " on " . date('Y-m-d', strtotime($estimate_start_time)) . " at " . date('H:i', strtotime($estimate_start_time)) . " hrs Log in. for any help contact: " . $HELP_LINE;

                                    $passanger_sms_arr[$i] = array("BRANCH_ID" => $branch_id,
                                        "ORIGINATOR" => $SMS_TAG,
                                        "RECIPIENT" => $emp_mobile,
                                        "MESSAGE" => $msg,
                                        "STATUS" => 'U',
                                        "SENT_DATE" => '1900-01-01 00:00:00',
                                        "REF_NO" => '--',
                                        "CREATED_BY" => $user_id,
                                        "CREATED_DATE" => $current_datetime);
                                } else {

                                    $msg = "Greetings from " . $site_name . " Transport, Your " . $pickdropsms . " on " . date('Y-m-d', strtotime($estimate_start_time)) . " at " . date('H:i', strtotime($estimate_start_time)) . " hrs Log in. for any help contact: " . $HELP_LINE;

                                    // error_log("~~~~~~~ ELSE SMS_STS " .$msg . " ~~~~~~", 0);
                                }
                                if ($EMAIL_NOTIFICATION == 'Y' && $email_id != '') {
                                    // error_log("~~~~~~~ EMAIL_NOTIFICATION " .$EMAIL_NOTIFICATION . " ~~~~~~", 0);

                                    $headers = '';
                                    $message = "Greetings from " . $site_name . " Transport, Your " . $pickdropsms . " on " . date('Y-m-d', strtotime($estimate_start_time)) . " at " . date('H:i', strtotime($estimate_start_time)) . " hrs Log in. for any help contact: " . $HELP_LINE;

                                    $email_arr[$i] = array("BRANCH_ID" => $branch_id,
                                        "EMPLOYEE_ID" => $emp_id,
                                        "EMAIL_ID" => $email_id,
                                        "MOBILE" => $emp_mobile,
                                        "MESSAGE" => $message,
                                        "STATUS" => 'S',
                                        "SENT_DATE" => date("Y-m-d H:i:S"),
                                        "CREATED_BY" => $user_id,
                                        "CREATED_DATE" => $current_datetime);
                                    $to = $email_id;
                                    $headers .= 'MIME-Version: 1.0' . "\r\n";
                                    $headers .= "Content-Type: text/html; charset=ISO-8859-1\r\n";
                                    $headers .= 'From: New Travel Liiens INDIA PRIVATE LTD<<EMAIL>>' . "\r\n";
                                    $subject = "Employee Email Notification";
                                    mail($to, $subject, $message, $headers);
                                }


                                if ($empmobile_gcm != "" && $empmobile_gcm != null) {
                                    //  error_log("~~~~~~~ empmobile_gcm " .$empmobile_gcm . " ~~~~~~", 0);

                                    $notification_array[] = "('$branch_id', '$emp_id', '$empmobile_gcm', 'Trip Scheduled', '$msg', '0','$mobile_category','$current_datetime')";

                                    //    error_log("~~~~~~~ AFTER empmobile_gcm  ~~~~~~", 0);
                                }
                            }
                        }




                        $approve_km_mapping = array_combine($location_list, $location_km_list);
                        arsort($approve_km_mapping);
                        $start_location = '';
                        $end_location = '';
                        $app_km = 0;

                        /**
                         * get approved km and first pickup point
                         * */
                        foreach ($approve_km_mapping as $key => $row) {

                            if ($trip_type == 'P' || $trip_type == 'p') {
                                $start_location = $key;
                                $end_location = $site_name;
                                $app_km = $row;
                            } else {
                                $end_location = $key;
                                $start_location = $site_name;
                                $app_km = $row;
                            }
                            break;
                        }
                        if (count($email_arr) > 0) {
                            email_notification::on("$dbname")->insert($email_arr);
                        }
                        if ($isEscort == 'true' && $ESCORT_ENABLE = "Y") {
                            $isescort_mapping = array_combine($gender_list, $location_km_list);

                            arsort($isescort_mapping);

                            /**
                             * get first pickup point is female
                             * */
                            foreach ($isescort_mapping as $key => $row) {
                                $escortUser = explode("-", $key);
                                if ($escortUser[1] == 'F') {
                                    $emp_id = array_search($row, $location_km_list);
                                    $this->EscortRoute($branch_id, $roster_passanger_arr[$emp_id]);
                                }
                                break;
                            }
                        }





                        /**
                         * INSERT ROSTER_PASSANGER TABLE 
                         * */
                        $isinsert = RosterPassenger::on("$dbname")->insert($roster_passanger_arr);
                        if ($isinsert) {

                            if (count($notification_array) > 1) {
                                $cabnotification = "INSERT INTO `notification` (`branch_id`, `empid_cabid`, `fcm_id`, `heading`, `message`, `category_value`,`app_category`,`created_at`) VALUES " . implode(',', $notification_array);
                                DB::connection("$dbname")->insert($cabnotification);
                            }

                            if ($SMS_STS == 'Y' && count($passanger_sms_arr) > 1) {
                                Sms::on("$dbname")->insert($passanger_sms_arr);
                            }

                            /**
                             * UPDATE ROSTER TABLE 
                             * */
                            $rosterUpdate = Roster::on("$dbname")->where('ROSTER_ID', '=', $roster_id)->first();
                            $rosterUpdate->PASSENGER_ALLOT_COUNT = $passenger_allot_count;
                            $rosterUpdate->PASSENGER_ALLOT_IN_ROUT_COUNT = 0;
                            $rosterUpdate->PASSENGER_CLUBING_COUNT = 0;
                            $rosterUpdate->CREATED_BY = $user_id;
                            $rosterUpdate->UPDATED_BY = $user_id;
                            $rosterUpdate->START_LOCATION = $start_location;
                            $rosterUpdate->END_LOCATION = $end_location;
                            $rosterUpdate->TRIP_APPROVED_KM = $app_km;
                            $rosterUpdate->save();

                            foreach ($input_id_list as $input_id) {
                                Input_data::where('INPUT_ID', '=', $input_id)
                                        ->update(array('STATUS' => 2));
                            }
                        }
                    }
                } else {
                    error_log("~~~~~~~ Nothing uploaded data   ~~~~~~", 0);
                }
                //});
            }
        } catch (Exception $exc) {
            echo $exc->getTraceAsString();
        }
    }

    public function EscortRoute($branch_id, $roster_passanger_arr) 
    {
        try {

            $dbname = Auth::user()->dbname;

            $gender_rs = Employee::on("$dbname")->where([['EMPLOYEES_ID', '=', $roster_passanger_arr['EMPLOYEE_ID']], ['ACTIVE', '=', 1], ['BRANCH_ID', '=', $branch_id],])->first();
            if (($gender_rs != null) && ($gender_rs->GENDER == 'F')) {

                $escortRoute1 = new RouteEscorts;
                $escortRoute = $escortRoute1->setConnection("$dbname");
                $escortRoute->BRANCH_ID = $branch_id;
                $escortRoute->ROSTER_ID = $roster_passanger_arr['ROSTER_ID'];
                $escortRoute->EMPLOYEE_ID = $roster_passanger_arr['EMPLOYEE_ID'];
                $escortRoute->STATUS = 1;
                $escortRoute->CREATED_BY = $roster_passanger_arr['CREATED_BY'];
                $escortRoute->UPDATED_BY = $roster_passanger_arr['UPDATED_BY'];
                $escortRoute->save();
            }
        } catch (Exception $exc) {
            echo $exc->getTraceAsString();
        }
    }

    public function address_time_check($value, $checktime) 
    {
        $ret = "";
        $timevalue = unserialize($value);
        foreach ($timevalue as $test) {
            list($t1, $t2, $t3) = $test;
            $ret1 = $this->prop_check_time($t1, $t2, $checktime) ? "yes" : "no";
            if ($ret1 == "yes") {
                $ret = $t3;
            }
        }
        return $ret;
    }
    public function prop_check_time($t1, $t2, $tn) 
    {
        if ($t2 >= $t1) {
            return $t1 <= $tn && $tn <= $t2;
        } else {
            return !($t2 <= $tn && $tn <= $t1);
        }
    }

    
    
    public function dataForCreate(): FoundationApplication|Response|ResponseFactory
    {

        try {
            $user = Auth::user();
            $RS_ACTIVE = MyHelper::$RS_ACTIVE;
            $branchId = $user->BRANCH_ID;
            $vendor_id = $user->vendor_id;
            $curdate = date("Y-m-d");
			
            $reason=DB::table("reason_master")->select('REASON','REASON_ID')
			 	->where([["active","=",$RS_ACTIVE],["CATEGORY","=",'WebOverSpeed'],["BRANCH_ID","=",$branchId]])->get();

                
            $panic_reason=DB::table("reason_master")->select('REASON','REASON_ID')
			 	->where([["active","=",$RS_ACTIVE],["CATEGORY","=",'WebPanicEmp'],["BRANCH_ID","=",$branchId]])->get();

                
                return response([
                'success' => true,
                'status' => 3,
                'reason' => $reason,
                'panic_reason' => $panic_reason
                
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Data For Create Roster Upload Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ],500);
        }
    }
    
    
}
