<?php

namespace App\Http\Requests\Admin;

use App\Helpers\CommonFunction;
use Closure;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;
use Illuminate\Contracts\Validation\Validator;

class DriverUpdateRequest extends FormRequest
{
    private CommonFunction $commonFunction;
    private ?object $driver = null;
    private ?int $driverId = null;

    public function __construct(CommonFunction $commonFunction)
    {
        parent::__construct();
        $this->commonFunction = $commonFunction;
    }

    public function authorize(): bool
    {
        return true;
    }

    protected function prepareForValidation(): void
    {
        try {
            $this->driverId = Crypt::decryptString($this->route('driverAutoIdCrypt'));
        } catch (\Throwable $e) {
            throw new HttpResponseException(response()->json([
                'success' => false,
                'validation_error' => true,
                'status' => 1,
                'message' => 'Invalid Driver ID',
            ], 500));
        }

        $this->driver = DB::table('drivers')->where('DRIVERS_ID', $this->driverId)->first();

        if (!$this->driver) {
            throw new HttpResponseException(response()->json([
                'success' => false,
                'validation_error' => true,
                'status' => 1,
                'message' => 'Driver not found',
            ], 500));
        }
    }

    public function rules(): array
    {
       

        return [
            'DRIVERS_NAME' => 'required',
            'DRIVER_MOBILE' => [
                'required',
                'numeric',
             
                Rule::unique('drivers', 'DRIVER_MOBILE')
				 ->ignore($this->driverId, 'DRIVERS_ID'),
            ],
            'DRIVERS_ADRESS' => 'required',
            'DRIVERS_ADDR_LAT' => 'required',
            'DRIVERS_ADDR_LONG' => 'required',
            'DRIVER_LICENSE' => 'required',
            'LICENCE_EXPIRY' => 'required',
            'BADGE_EXPIRY' => 'required',
            'MEDICAL_STATUS' => 'required',
            'LOCATION_NAME' => 'required',
            'SHIFT_IN_TIME' => 'required',
            'SHIFT_OUT_TIME' => 'required',
			];
    }

    public function messages(): array
    {
        return [
            'DRIVERS_NAME.required' => 'Driver Name field is required',
            'DRIVER_MOBILE.required' => 'Driver Mobile field is required',
            'DRIVERS_ADRESS.required' => 'Driver Address field is required',
            'DRIVERS_ADDR_LAT.required' => 'Latitude field is required',
            'DRIVERS_ADDR_LONG.required' => 'Longitude field is required',
            'DRIVER_LICENSE.required' => 'License field is required',
            'LICENCE_EXPIRY.required' => 'License Expiry field is required',
            'BADGE_EXPIRY.required' => 'Badge Expiry field is required',
            'MEDICAL_STATUS.required' => 'Medical field is required',
            'LOCATION_NAME.required' => 'Location field is required',
            'SHIFT_IN_TIME.required' => 'In Time field is required',
            'SHIFT_OUT_TIME.required' => 'Out Time field is required',
			
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json([
            'success' => false,
            'validation_error' => true,
            'status' => 2,
            'message' => 'Validation errors',
            'data' => $validator->errors()
        ]));
    }

    
}
