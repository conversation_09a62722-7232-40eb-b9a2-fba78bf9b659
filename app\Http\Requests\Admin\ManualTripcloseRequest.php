<?php

//namespace App\Http\Requests\Admin;
namespace App\Http\Requests\Admin;

use App\Helpers\CommonFunction;
use Closure;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;

class ManualTripcloseRequest extends FormRequest
{
    protected CommonFunction $commonFunction;

    public function __construct(CommonFunction $commonFunction)
    {
        
    }

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        
        return [
            'roster_id' => ['required','numeric'],
            'trip_sheet_sts' => ['required','string'],
            'selected_date' => ['required','date_format:Y-m-d'],
            'selected_vendor' => ['required','numeric'],
            'trip_close_time' => ['required','date_format:H:i'],
            'accept_reject_reason' => ['required','numeric']
        ];
    }

    public function messages(): array
    {
        return [
            'roster_id.required' => 'roster id field is required',
            'trip_sheet_sts.required' => 'trip_sheet_sts field is required',
            'selected_date.required' => 'selected_date field is required',
            'selected_vendor.required' => 'selected_vendor  field is required',
            'trip_close_time.required' => 'trip_close_time  field is required',
            'accept_reject_reason.required' => 'accept_reject_reason  field is required',
            
        ];
    }
   

    public function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json([
            'success' => false,
            'validation_error' => true,
            'message' => 'Validation errors',
            'data' => $validator->errors()
        ]));
    }
   
}
