<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;

use App\Services\Admin\TripHistoryService;
use App\Http\Requests\Admin\DateShiftTimeRequest;
use App\Http\Requests\Admin\TripHistoryRequest;


use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class TripHistoryController extends Controller
{
    protected TripHistoryService $vehicletrackingService;

    public function __construct(TripHistoryService $triphistoryService)
    {
        $this->triphistoryService = $triphistoryService;
    }
    
    public function dataForCreate(): FoundationApplication|Response|ResponseFactory
    {
        return $this->triphistoryService->dataForCreate();
    }
    public function date_shifttime(DateShiftTimeRequest $request): FoundationApplication|Response|ResponseFactory
    {

        return $this->triphistoryService->date_shifttime($request);
    }
    public function getfilter_trip_history(TripHistoryRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->triphistoryService->getfilter_trip_history($request);
    }
   
 
}
