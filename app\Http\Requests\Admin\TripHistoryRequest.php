<?php

//namespace App\Http\Requests\Admin;
namespace App\Http\Requests\Admin;

use App\Helpers\CommonFunction;
use Closure;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;

class TripHistoryRequest extends FormRequest
{
    protected CommonFunction $commonFunction;

    public function __construct(CommonFunction $commonFunction)
    {
        
    }

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {   
        return [
                'selected_trip_type' => ['required','string'],
                'selected_date' => ['required'],
                'selected_shift_time' => ['required'],
                'selected_vendor' => ['required','string'],
            ];
    }

    public function messages(): array
    {
        return [
               'selected_trip_type.required' => 'Selected Trip Type field is required',
               'selected_date.required' => 'Selected date field is required',
               'selected_shift_time.required' => 'selected shift time field is required',
               'selected_vendor.required' => 'selected vendor field is required',
        ];
    }
   

    public function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json([
            'success' => false,
            'validation_error' => true,
            'message' => 'Validation errors',
            'data' => $validator->errors()
        ]));
    }
   
}
