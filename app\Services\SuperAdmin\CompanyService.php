<?php

namespace App\Services\SuperAdmin;


use App\Helpers\CommonFunction;
use App\Helpers\MyHelper;
use App\Models\Company;
use Carbon\Carbon;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;

class CompanyService
{


    protected CommonFunction $commonFunction;


    public function __construct(CommonFunction $commonFunction)
    {
        $this->commonFunction = $commonFunction;
    }


    public function indexCompany(): FoundationApplication|Response|ResponseFactory
    {
        $authUser = Auth::user();
        try {

            $companys = Company::select(
                'company.COMPANY_ID',
                'company.NAME',
                'company.LOCATION',
                'company.LAT',
                "company.LONG",
                'company.ACTIVE'
            )
                ->where('company.ACTIVE', MyHelper::$RS_ACTIVE)
                ->get();

            return response([
                'success' => true,
                'status' => 200,
                'companys' => $companys,
            ]);
        } catch (\Throwable | \Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Comapny Index Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ]);
        }
    }

    public function storeCompany($request): FoundationApplication|Response|ResponseFactory
    {

        try {

            DB::beginTransaction();
            $auth_user = Auth::user();
            $companyData = $this->prepareCompanyData($request, $auth_user, MyHelper::$RS_ACTIVE);
            $companyResult = Company::create($companyData);
            return response([
                'success' => true,
                'status' => 200,
                'message' => 'Company Created Successfully',

            ]);
        } catch (\Throwable | \Exception $e) {
            DB::rollBack();
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'validation_controller' => true,
                'status' => 4,
                'message' => 'Company Created UnSuccessfully',
                'errorM' => $e->getMessage(),
            ]);
        } finally {
            DB::commit();
        }
    }


    private function prepareCompanyData($request, $auth_user, $active): array
    {
        $date = Carbon::now();
        return [
            "NAME" => $request->name,
            "LOCATION" => $request->location,
            "LAT" => $request->lat,
            "LONG" => $request->long,
            "ACTIVE" => $active,
            "CREATED_BY" => $auth_user->id,
            "created_at" => $date->format("Y-m-d H:i:s"),
        ];
    }



    public function editCompany($companyAutoIdCrypt): FoundationApplication|Response|ResponseFactory
    {
        $authUser = Auth::user();

        try {
            $companyAutoId = Crypt::decryptString($companyAutoIdCrypt);
            $company = Company::where('company.COMPANY_ID', $companyAutoId)
                ->where('company.ACTIVE', MyHelper::$RS_ACTIVE)
                ->select(
                    'company.COMPANY_ID',
                    'company.NAME',
                    'company.LOCATION',
                    'company.LAT',
                    'company.LONG',
                )->firstOrFail();

            return response([
                'success' => true,
                'status' => 200,
                'employee' => $company,
            ]);
        } catch (\Throwable | \Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Company Edit Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ]);
        }
    }


    public function updateCompany($request, $companyAutoIdCrypt): FoundationApplication|Response|ResponseFactory
    {

        try {

            $authUser = Auth::user();

            DB::beginTransaction();
            $companyAutoId = Crypt::decryptString($companyAutoIdCrypt);
            $company = Company::where('company.COMPANY_ID', $companyAutoId)
                ->where('company.ACTIVE', MyHelper::$RS_ACTIVE)
                ->firstOrFail();

            $companyData = $this->prepareComapnyDataForUpdate($request, $authUser);
            $company->update($companyData);

            DB::commit();

            return response([
                'success' => true,
                'status' => 200,
                'message' => 'Company Updated Successfully',
            ]);
        } catch (\Throwable | \Exception $e) {
            DB::rollBack();
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'message' => 'Company Update Unsuccessful',
                'status' => 4,
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ]);
        }
    }


    private function prepareComapnyDataForUpdate($request, $auth_user): array
    {
        $date = Carbon::now();
        return [
            "NAME" => $request->name,
            "LOCATION" => $request->location,
            "LAT" => $request->lat,
            "LONG" => $request->long,
            "UPDATED_BY" => $auth_user->id,
            "updated_at" => $date->format("Y-m-d H:i:s"),
        ];
    }




    public function deleteCompany($request, $companyAutoIdCrypt): FoundationApplication|Response|ResponseFactory
    {
        $authUser = Auth::user();
        $date = Carbon::now();
        try {
            DB::beginTransaction();

            $companyAutoId = Crypt::decryptString($companyAutoIdCrypt);
            $company = Company::where('company.COMPANY_ID', $companyAutoId)
                ->where('company.ACTIVE', MyHelper::$RS_ACTIVE)
                ->firstOrFail();

            $company->update([
                'REMARKS' => $request->input('remark'),
                'ACTIVE' => MyHelper::$RS_INACTIVE,
                'UPDATED_BY' => Auth::user()->id,
                'updated_at' => $date->format("Y-m-d H:i:s"),
            ]);

            DB::commit();

            return response([
                'success' => true,
                'status' => 200,
                'message' => 'Company Deleted Successfully',
            ]);
        } catch (\Throwable | \Exception $e) {
            DB::rollBack();
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Company Delete Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ]);
        }
    }



    public function paginationCompany($request, bool $active = true): FoundationApplication|Response|ResponseFactory
    {
        try {
            $perPage = $request->input('per_page', MyHelper::$PAGINATION_PER_PAGE);
            $companys = Company::query()
                ->select(
                    'company.COMPANY_ID',
                    'company.NAME',
                    'company.LOCATION',
                    'company.LAT',
                    'company.LONG',                   
                )
                ->where('company.ACTIVE', MyHelper::$RS_ACTIVE) ;

            $filterModel = $request->input('filterModel');
            if ($filterModel) {
                foreach ($filterModel as $field => $filter) {
                    if (isset($filter['filter']) && $filter['filter'] !== '') {
                        $value = $filter['filter'];
                        $type = $filter['type'];

                        switch ($field) {
                            case 'NAME':
                                $companys->where('company.NAME', 'like', "%{$value}%");
                                break;
                           
                            case 'LOCATION':
                                $companys->where('company.LOCATION', 'like', "%{$value}%");
                                break;
                          
                        }
                    }
                }
            }


            if ($request->has('orderBy') && !empty($request->input('orderBy'))) {
                $orderBy = $request->input('orderBy');
                $order = $request->input('order', 'asc');
                $companys->orderBy($orderBy, $order);
            } else {
                $companys->orderBy('company.created_at', 'desc');
            }


            if ($perPage === MyHelper::$PAGINATION_PER_PAGE_ALL) {
                $paginatedCompanys = $companys->paginate($companys->count());
            } else {
                $perPage = is_numeric($perPage) ? (int)$perPage : MyHelper::$PAGINATION_PER_PAGE;
                $paginatedCompanys = $companys->paginate($perPage);
            }

            return response([
                'success' => true,
                'status' => 200,
                'companys' => $paginatedCompanys,
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'Companys Pagination Unsuccessful' : 'Deactivate Companys Pagination Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ]);
        }
    }

}
