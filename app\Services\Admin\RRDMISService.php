<?php

namespace App\Services\Admin;

use App\Helpers\CommonFunction;
use App\Helpers\MyHelper;
use App\Http\Controllers\ElasticController;
use App\Models\Cab_allocation;
use App\Models\Roster;
use App\Models\Vendor;
use Carbon\Carbon;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use DateTime;

class RRDMISService
{
    protected CommonFunction $commonFunction;
    protected ?Branch $currentBranch = null;

    public function __construct(CommonFunction $commonFunction)
    {
        $this->commonFunction = $commonFunction;
    }

    public function dataForCreate(): FoundationApplication|Response|ResponseFactory
    {
        try {

            $user = Auth::user();
            $RS_ACTIVE = MyHelper::$RS_ACTIVE;
            $ADMIN = MyHelper::$ADMIN;
            $branch_id = Auth::user()->BRANCH_ID;
            $vendor_id = Auth::user()->vendor_id;
            $dbname = Auth::user()->dbname;
            $date = carbon::now();
            $from_date = date('Y-m-d');
            $user_type = Auth::user()->user_type;
            $RS_TOTAL_AUTOCANCEL = MyHelper::$RS_TOTAL_AUTOCANCEL;

            if (Auth::user()->user_type == MyHelper::$ADMIN) {
                $vendor_list = Vendor::where('ACTIVE', '=', $RS_ACTIVE)->where('BRANCH_ID', '=', $branch_id)->get();
            } else {
                $vendor_list = Vendor::where('ACTIVE', '=', $RS_ACTIVE)->where('VENDOR_ID', '=', $vendor_id)->get();
            }

            return response([
                'success' => true,
                'status' => 3,
                'vendor_list' => $vendor_list,
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Data For Create for MIS Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function vendorwise_vehicle_find($request): FoundationApplication|Response|ResponseFactory
    {
        try {

            $branch_id = Auth::user()->BRANCH_ID;
            $userid = Auth::user()->id;
            $db_name = Auth::user()->dbname;
            $RS_ACTIVE=MyHelper::$RS_ACTIVE;
            $active=true;
           $authUser = Auth::user();

           $vendor_id=$request->selected_vendor;
           
           if ($vendor_id == 'ALL') {
               $vendorid = '';
           } else {
               // $vendorid = "AND C.VENDOR_ID='" . $vendor_id . "'";
               $vendorid = $vendor_id;
           } 

          
           $cab_data = DB::connection("$db_name")->table('cab as C')->select('C.CAB_ID','C.VENDOR_ID', 'V.VEHICLE_ID', 'V.VEHICLE_REG_NO')
						->join('vehicles as V', 'V.VEHICLE_ID', '=', 'C.VEHICLE_ID')
                        ->when($vendorid, function ($query, $vendorid) {
                            return $query->where('C.VENDOR_ID', '=', $vendorid);
                        })
						
						->where('C.BRANCH_ID', '=', $branch_id) ->where('C.ACTIVE', '=', $RS_ACTIVE)->distinct('C.CAB_ID')->get();

        
            return response([
                'success' => true,
                'status' => 3,
                'cab_data' => $cab_data,
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'Vehicle  status Unsuccessful' : 'Vehicle  status Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ],500);
        }
    } 


    public function fetch_RRDMis_Details($request): FoundationApplication|Response|ResponseFactory
    {
        try {
            $RS_ACTIVE = MyHelper::$RS_ACTIVE;
            $selected_vendor = $request->selected_vendor;
            $selected_cab = $request->selected_cab;
            $from_date = $request->from_date;
            $to_date = $request->to_date;

            $branch_id = Auth::user()->BRANCH_ID;
            $RS_TOTAL_AUTOCANCEL = MyHelper::$RS_TOTAL_AUTOCANCEL;
           
            $dbname = Auth::user()->dbname;
            $branch_id = Auth::user()->BRANCH_ID;
            $branch_id = 32;
            $user_type = Auth::user()->user_type;
            $ADMIN = MyHelper::$ADMIN;
            $vendor_condition = $selected_vendor == 'ALL' ? '' : " R.VENDOR_ID = '$selected_vendor' ";

            $RS_MANUALTRIPCLOSE=MyHelper::$RS_MANUALTRIPCLOSE;
            $RS_TRIPCLOSE=MyHelper::$RS_TRIPCLOSE;
            $RS_TOTALTRIPSHEET_CANCEL=MyHelper::$RS_TOTALTRIPSHEET_CANCEL;
            $query = DB::connection($dbname)
            ->table('rosters as R')
             ->select([
            DB::raw("IF(R.TRIP_TYPE = 'P', DATE(R.ESTIMATE_END_TIME), DATE(R.ESTIMATE_START_TIME)) as LOGIN_DATE"),
            DB::raw("IF(R.TRIP_TYPE = 'P', TIME(R.ESTIMATE_END_TIME), TIME(R.ESTIMATE_START_TIME)) as LOGIN_TIME"),
            'R.ROSTER_ID',
            'R.TRIP_TYPE',
            'C.CAB_ID',
            'T.PACKAGE_PRICE','T.EXTRA_KMS_CHARGE',
            'V.VEHICLE_REG_NO',
            'VM.MODEL',
            'VM.CAPACITY',
            'VM.VEHICLE_MODEL_ID',
            'VN.NAME as VENDOR_NAME',
            'VN.VENDOR_ID',
            DB::raw("T.PACKAGE_PRICE * R.TOTAL_KM as TOTAL_AMOUNT"),
            'L.LOCATION_NAME',
            'ES.ESCORT_ID',
            'ES.ESCORT_NAME',
            DB::raw("IF(RE.ROSTER_ID IS NULL, 'NO', 'YES') as ESCORTROUTE"),
            DB::raw("TIME(R.ACTUAL_START_TIME) AS START_TIME"),
            'E.PROJECT_NAME',
            'E.DISTANCE',
            'E.GENDER',
            'E.NAME AS EMPNAME',
            DB::raw("(R.PASSENGER_ALLOT_COUNT + R.PASSENGER_CLUBING_COUNT) as route_count"),
            'R.PASSENGER_CLUBING_COUNT',
            'R.PASSENGER_ALLOT_IN_ROUT_COUNT as SCHEDULE_COUNT',
            'R.PASSENGER_ALLOT_COUNT',
            'RP.EMPLOYEE_ID',
            DB::raw("TIME(R.ACTUAL_END_TIME) AS END_TIME"),
            'RP.ROSTER_PASSENGER_STATUS',
            'RP.REMARKS',
            'DS.DEVICE_KM',
            DB::raw("(R.TOTAL_KM + IF(DS.SHED_KM IS NULL OR DS.SHED_KM = 0, 0, DS.SHED_KM)) as TOTAL_KM"),
            'DS.GOOGLE_KM',
            'DS.GOOGLE_SHED_KM',
            'DS.DEVIATION_KM',
            'DS.SHED_KM',
            'DS.TRIP_STATUS',
            'R.ROSTER_STATUS',
            'R.TOTAL_KM as ROSTER_TOTAL_KM',
            'R.ACTUAL_START_TIME',
            'R.ACTUAL_END_TIME',
            'EC.EMPTY_KM',
            'RP.ROUTE_ORDER',
            'RP.ASSET_MOVEMENT',
            'TP.TOLL_CHARGE'
        ])
        ->join('roster_passengers as RP', function ($join) {
            $join->on('RP.ROSTER_ID', '=', 'R.ROSTER_ID')
                 ->where('RP.ACTIVE', 1);
        })
        ->join('cab as C', 'C.CAB_ID', '=', 'R.CAB_ID')
        ->join('vehicles as V', 'V.VEHICLE_ID', '=', 'C.VEHICLE_ID')
        ->join('vehicle_models as VM', 'VM.VEHICLE_MODEL_ID', '=', 'V.VEHICLE_MODEL_ID')
        ->join('vendors as VN', 'VN.VENDOR_ID', '=', 'C.VENDOR_ID')
        ->leftJoin('employees as E', function ($join) use ($branch_id) {
            $join->on('E.EMPLOYEES_ID', '=', 'RP.EMPLOYEE_ID')
                 ->where('E.BRANCH_ID', '=', $branch_id);
        })
        ->leftJoin('locations as L', 'L.LOCATION_ID', '=', 'RP.LOCATION_ID')
        ->leftJoin('route_escorts as RE', function ($join) {
            $join->on('RE.ROSTER_ID', '=', 'R.ROSTER_ID')
                 ->where('RE.BRANCH_ID', '=', 'R.BRANCH_ID')
                 ->whereNotIn('RE.STATUS', [5, 6]);
        })
        ->leftJoin('escorts as ES', 'ES.ESCORT_ID', '=', 'RE.ESCORT_ID')
        ->leftJoin('driver_billing_summary as DS', function ($join) {
            $join->on('DS.ROSTER_ID', '=', 'R.ROSTER_ID')
                 ->where('DS.ACTIVE', 1);
        })
        ->leftJoin('tariff as T', 'T.TARIFF_ID', '=', 'DS.TARIFF_ID')
        ->leftJoin('empty_cab_km as EC', function ($join) use ($from_date, $to_date) {
            $join->on('EC.CAB_ID', '=', 'R.CAB_ID')
                 ->where('EC.APPROVE_STATUS', 2)
                 ->whereBetween(DB::raw('DATE(EC.IN_OUT_TIME)'), [$from_date, $to_date]);
        })
        ->leftJoin('toll_payment as TP', 'TP.ROSTER_ID', '=', 'R.ROSTER_ID')
        ->where('R.BRANCH_ID', $branch_id);

    // Apply dynamic filters
    if ($selected_vendor != 'ALL') {
        $query->where('VN.VENDOR_ID', $selected_vendor);
    }

    if ($selected_cab != 'ALL') {
        $query->where('R.CAB_ID', $selected_cab);
    }
    
    $query->whereBetween(DB::raw("IF(R.TRIP_TYPE='P', DATE(R.ESTIMATE_END_TIME), DATE(R.ESTIMATE_START_TIME))"), [$from_date, $to_date])
          ->where('R.ACTIVE', 1)
          ->where(function ($query) use ($RS_TRIPCLOSE, $RS_MANUALTRIPCLOSE) {
              $query->whereRaw("R.ROSTER_STATUS & {$RS_TRIPCLOSE}")
                    ->orWhereRaw("R.ROSTER_STATUS & {$RS_MANUALTRIPCLOSE}");
          })
          ->whereNotIn('R.ROSTER_STATUS', explode(',', $RS_TOTALTRIPSHEET_CANCEL));
          $query->whereRaw("
          IF(
              (R.TRIP_TYPE = 'P' AND RP.ROSTER_PASSENGER_STATUS & 16 AND RP.START_LAT != ''),
              RP.ROSTER_PASSENGER_STATUS IN (16, 17, 19, 21, 23, 29, 31, 85),
              RP.ROSTER_PASSENGER_STATUS NOT IN (16, 17, 19, 21, 23, 29, 31, 85)
          )
      ");		  
            $query->groupBy('RP.ROSTER_PASSENGER_ID');
                // ->orderBy('R.ROSTER_ID', 'ASC')
                // ->orderBy('RP.ROUTE_ORDER', 'DESC')
               //  ->get();
          //  $result = $query->get();
          if ($request->has('orderBy') && !empty($request->input('orderBy'))) {
            $orderBy = $request->input('orderBy');
            $order = $request->input('order', 'asc');
            $query->orderBy($orderBy, $order);
           
        } else {
            $query->orderBy('RP.ROUTE_ORDER', 'DESC');
            $query->orderBy('RP.ROSTER_ID', 'ASC');
        }
        $perPage = $request->input('per_page', MyHelper::$PAGINATION_PER_PAGE);

        if ($perPage === MyHelper::$PAGINATION_PER_PAGE_ALL) {
            $paginatedData = $query->paginate($query->count());
        } else {
            $perPage = is_numeric($perPage) ? (int) $perPage : MyHelper::$PAGINATION_PER_PAGE;

            $paginatedData = $query->paginate($perPage);
        }
        $RPS_TTLNOSHOW=explode(',',MyHelper::$RPS_TTLNOSHOW);
        $RPS_TTLARRIVAL=explode(',',MyHelper::$RPS_TTLARRIVAL);
        

        $paginatedData->getCollection()->transform(function ($dataV) use ($RPS_TTLNOSHOW) {
            $old_roster_id='';
            $ESCORTROUTE = '';
            $START_TIME='';
            $END_TIME='';
            $LOGIN_TIME='';
            $TOTAL_KM=0;
            $DEVICE_KM=0;
            $GOOGLE_KM = 0;
            $DEVIATION_KM = 0;
            $SHED_KM = 0;
            $TRIP_STATUS='--';
            $GOOGLE_SHED_KM='0';
            $EMPTY_KM ='';
            $approve_km='';
            $TOLL_CHARGE='';
            $ROSTER_STATUS=$dataV->ROSTER_STATUS;
            $ROSTER_PASSENGER_STATUS=$dataV->ROSTER_PASSENGER_STATUS;
            $ROSTER_ID=$dataV->ROSTER_ID;
            $TRIP_TYPE=$dataV->TRIP_TYPE;
            $TRIP_STATUS=$dataV->TRIP_STATUS;
            $VENDOR_ID=$dataV->VENDOR_ID;
            $VEHICLE_MODEL_ID=$dataV->VEHICLE_MODEL_ID;
            $GOOGLE_KM=$dataV->GOOGLE_KM;
            $EXTRA_KMS_CHARGE=$dataV->EXTRA_KMS_CHARGE;
            $TOLL_CHARGE=$dataV->TOLL_CHARGE;
            $REMARKS=$dataV->REMARKS;

			$close_sts=$this->commonFunction->TripStatus($ROSTER_STATUS);
            $RPS_TTLNOSHOW=explode(",",env('RPS_TTLNOSHOW'));
            if($TRIP_TYPE=='P' && (in_array($ROSTER_PASSENGER_STATUS,$RPS_TTLNOSHOW)==true))
            {
                $REMARKS='Noshow Pickup';
            }
            if($old_roster_id!=$ROSTER_ID)
            {
                $route_count1=$this->passenger_cnt($ROSTER_ID,$TRIP_TYPE);
                $route_count=$route_count1;
            }
            else
            {
                $route_count=$route_count1;
            }
            if($TRIP_STATUS=='FT' || $TRIP_STATUS=='LT')
            {
                if($VENDOR_ID==39 || $VENDOR_ID==129)
                {
                    $SHED_KM ='15';
                }
                else{

                    $SHED_KM ='5';
                }
            }
            else
            {
                $SHED_KM ='0';
            }

            $DIVIDEDKM=round(($GOOGLE_KM+$SHED_KM)/($route_count),6);
            if($route_count==1 && $TRIP_TYPE=='D')
                    {
                        if($VENDOR_ID==40 || $VENDOR_ID==83 || $VENDOR_ID==124 )
                        {
                            //$trip_amount=round(($DIVIDEDKM*10.25),2);
                            //$trip_amount=round(($DIVIDEDKM*15),2);
                            $trip_amount=round(($DIVIDEDKM*$EXTRA_KMS_CHARGE),2);
                            $billable_sts='Non Billable';
                            $PACKAGE_PRICE=$EXTRA_KMS_CHARGE;
                        }
                        elseif($VENDOR_ID==82 || $VENDOR_ID==39 || $VENDOR_ID==81 || $VENDOR_ID==102 || $VENDOR_ID==119)
                        {
                            $trip_amount=round(($DIVIDEDKM*$EXTRA_KMS_CHARGE),2);
                            $billable_sts='Non Billable';
                            $PACKAGE_PRICE=$EXTRA_KMS_CHARGE;
                          
                        }else{
                            $PACKAGE_PRICE=$this->packageprice($VENDOR_ID,$VEHICLE_MODEL_ID);
                            //$PACKAGE_PRICE=$normal_array[$i]['PACKAGE_PRICE'];
                            $trip_amount=round(($DIVIDEDKM*$PACKAGE_PRICE),2);
                            $billable_sts='';
                        }
                        
                    }
                    else
                    {
                        $PACKAGE_PRICE=$this->packageprice($VENDOR_ID,$VEHICLE_MODEL_ID);
                        //$PACKAGE_PRICE=$normal_array[$i]['PACKAGE_PRICE'];
                        $trip_amount=round(($DIVIDEDKM*$PACKAGE_PRICE),2);
                        $billable_sts='';
                        
                    }
                    $tax_with_amount=round($trip_amount*1.05,2);

                    if($old_roster_id!=$ROSTER_ID)
                    {
                        
                        $SCHEDULE_COUNT=$dataV->SCHEDULE_COUNT;
                        $ESCORTROUTE = $dataV->ESCORTROUTE;
                        $DEVICE_KM = $dataV->ROSTER_TOTAL_KM;
                        $GOOGLE_KM =$dataV->GOOGLE_KM;
                        $DEVIATION_KM =$dataV->DEVIATION_KM;
                        $TRIP_STATUS =$dataV->TRIP_STATUS;
                        //$SHED_KM = $normal_array[$i]['SHED_KM'];
                        //$SHED_KM = $SHED_KM;
                        $GOOGLE_SHED_KM =$dataV->GOOGLE_SHED_KM;
                        $EMPTY_KM = $dataV->EMPTY_KM;
                        //$approve_km=$GOOGLE_KM+$GOOGLE_SHED_KM;
                        $approve_km=$GOOGLE_KM+$SHED_KM;
                        $TOLL_CHARGE=$TOLL_CHARGE;
                    }
                    else
                    {
                        $SCHEDULE_COUNT=0;
                        $route_count=0;
                        $ESCORTROUTE = '';
                        $START_TIME='';
                        $END_TIME='';
                        $LOGIN_TIME='';
                        $TOTAL_KM=0;
                        $DEVICE_KM=0;
                        $GOOGLE_KM = 0;
                        $DEVIATION_KM = 0;
                        $SHED_KM = 0;
                        $TRIP_STATUS='--';
                        $GOOGLE_SHED_KM='0';
                        $EMPTY_KM ='';
                        $approve_km='';
                        $TOLL_CHARGE='';
                    }


            $dataV->rosterstatus=$close_sts;
            $dataV->remarks=$REMARKS;
            $dataV->SCHEDULE_COUNT=$SCHEDULE_COUNT;
            $dataV->route_count=$route_count;
            $dataV->ESCORTROUTE=$ESCORTROUTE;
            $dataV->START_TIME=$START_TIME;
            $dataV->END_TIME=$END_TIME;
            $dataV->LOGIN_TIME=$LOGIN_TIME;
            $dataV->TOTAL_KM=$TOTAL_KM;
            $dataV->DEVICE_KM=$DEVICE_KM;
            $dataV->GOOGLE_KM=$GOOGLE_KM;
            $dataV->DEVIATION_KM=$DEVIATION_KM;
            $dataV->SHED_KM=$SHED_KM;
            $dataV->TRIP_STATUS=$TRIP_STATUS;
            $dataV->GOOGLE_SHED_KM=$GOOGLE_SHED_KM;
            $dataV->EMPTY_KM=$EMPTY_KM;
            $dataV->approve_km=$approve_km;
            $dataV->TOLL_CHARGE=$TOLL_CHARGE;
            $old_roster_id=$ROSTER_ID;
            return $dataV;
        });

         
            return response([
                'success' => true,
                'status' => 3,
                'result' => $paginatedData,
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'RRD MIS Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }
    public function passenger_cnt($roster_id,$TRIP_TYPE)
	{
		$sql="select count(RP.ROSTER_ID) as pas_cnt from roster_passengers RP
		inner join rosters R on R.ROSTER_ID=RP.ROSTER_ID 
		where
		RP.ACTIVE=1 and RP.ROSTER_ID='".$roster_id."' and 
		if(R.TRIP_TYPE='P' and RP.ROSTER_PASSENGER_STATUS &16 
		and RP.START_LAT!='',RP.ROSTER_PASSENGER_STATUS in(16,17,19,21,23,29,31,85),RP.ROSTER_PASSENGER_STATUS not in(16,17,19,21,23,29,31,85))";
		$result=DB::select($sql);
		return $result[0]->pas_cnt;
	}
    public function packageprice($vendor_id,$model_id)
	{
		 $sql="select PACKAGE_PRICE from tariff where VENDOR_ID='".$vendor_id."' and ACTIVE=1 and VEHICLE_MODEL_ID='".$model_id."' ";
		$result=DB::select($sql);
		if(count($result)>0)
		{
			$package_price= $result[0]->PACKAGE_PRICE;
		}
		else
		{
			return 0;
			// return "RosterM id: ".$model_id;
			
		}
		if($package_price==0 || $package_price=='')
			{
				$errorlogpath = pathinfo(ini_get('error_log'));
				//$errorlogfile = "D:/xampp/htdocs/TMS_BACKUP/storage/logs/" . date('Y-m-d') . "-upload.log";
				$errorlogfile = "/var/www/html/TMS/storage/logs/APII_LOGS.log";
				if (file_exists($errorlogfile)) {
					ini_set('error_log', $errorlogfile);
				} else {
					$errfh = fopen($errorlogfile, "a+");
					if (is_resource($errfh)) {
						ini_set('error_log', $errorlogfile);
						fclose($errfh);
					}
				}
				error_log("~~~~~~~  URL    ~~~~~~  ".$ROSTER_ID , 0); 
				echo "Roster id MM: ".$model_id;
				exit;
			}  
		return $package_price;
	}

}
