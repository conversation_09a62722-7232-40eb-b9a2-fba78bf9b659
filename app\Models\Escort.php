<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Crypt;

class Escort extends Model
{
    use HasFactory;

    protected $table = 'escorts';
    protected $primaryKey = 'ESCORT_ID';
	public $timestamps = false;
	protected $appends = ['ESCORT_ID_crypt','image_path'];
	
	protected $fillable = [
        'ESCORT_NAME',
        'ESCORT_MOBILE',
        'BRANCH_ID',
        'ESCORT_BATCH_NUMBER',
        'ESCORT_PHOTO',
        'ESCORT_TYPE',
        'ACTIVE',
        'REMARKS',
        'CREATED_BY',
        'CREATED_DATE',
        'UPDATED_BY',
        'updated_at',
        'REMARKS',
        'MEDICAL_STATUS',
        'DRIVER_IMAGE',
        'LOCATION_NAME',
        'SHIFT_IN_TIME',
        'SHIFT_OUT_TIME	',
        'ACTIVE',
        'CREATED_BY',
        'created_at',
        'UPDATED_BY',
        'updated_at',
        'ORG_ID',
    ];

    public function getImagePathAttribute(): ?string
    {
        if ($this->ESCORT_PHOTO && $this->ESCORT_PHOTO !== '--') 
		{
            return asset('escort-images/' . $this->ESCORT_PHOTO);
        }
        return null;
    }
	
	public function getESCORTIDCryptAttribute(): string
    {
        return Crypt::encryptString($this->ESCORT_ID);
    }
}
