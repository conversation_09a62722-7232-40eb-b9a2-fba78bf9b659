<?php

namespace App\Http\Requests\Admin;

use App\Helpers\CommonFunction;
use Closure;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;
use Illuminate\Contracts\Validation\Validator;

class VehicleUpdateRequest extends FormRequest
{
    private CommonFunction $commonFunction;
    private ?object $vehicle = null;
    private ?int $vehicleId = null;

    public function __construct(CommonFunction $commonFunction)
    {
        parent::__construct();
        $this->commonFunction = $commonFunction;
    }

    public function authorize(): bool
    {
        return true;
    }

    protected function prepareForValidation(): void
    {
        try {
          $this->vehicleId = Crypt::decryptString($this->route('vehicleAutoIdCrypt'));
         
        } catch (\Throwable $e) {
            throw new HttpResponseException(response()->json([
                'success' => false,
                'validation_error' => true,
                'status' => 1,
                'message' => 'Invalid Vehicle ID',
            ], 400));
        }

        $this->vehicle = DB::table('vehicles')->where('VEHICLE_ID', $this->vehicleId)->first();

        if (!$this->vehicle) {
            throw new HttpResponseException(response()->json([
                'success' => false,
                'validation_error' => true,
                'status' => 1,
                'message' => 'Vehicle not found',
            ], 404));
        }
    }

    public function rules(): array
    {
       

        return [
            'VEHICLE_MODEL_ID' => 'required',
            'VEHICLE_REG_NO' => 'required',
            'VEHICLE_JOIN_DATE' => 'required',
            'REG_DATE' => 'required',
            'PERMIT_EXPIRY' => 'required',
            'POLLUTION_EXPIRY' => 'required',
            'INSURANCE_EXPIRY' => 'required',
            'FC_EXPIRY' => 'required',
            'TAX_EXPIRY' => 'required',
            'MILEAGE_KM' => 'required',
			];
    }

    public function messages(): array
    {
        return [
            'VEHICLE_MODEL_ID.required' => 'Vehicle Model field is required',
            'VEHICLE_REG_NO.required' => 'Register number field is required',
            'VEHICLE_JOIN_DATE.required' => 'Join Date field is required',
            'REG_DATE.required' => 'Register date field is required',
            'PERMIT_EXPIRY.required' => 'Permit expiry field is required',
            'POLLUTION_EXPIRY.required' => 'Pollution expiry field is required',
            'INSURANCE_EXPIRY.required' => 'nsurance expiry field is required',
            'FC_EXPIRY.required' => 'FC expiry field is required',
            'TAX_EXPIRY.required' => 'Tax expiry field is required',
            'MILEAGE_KM.required' => 'Mileage field is required',
            
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json([
            'success' => false,
            'validation_error' => true,
            'status' => 2,
            'message' => 'Validation errors',
            'data' => $validator->errors()
        ], 422));
    }

    
}
