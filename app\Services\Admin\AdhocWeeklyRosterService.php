<?php

namespace App\Services\Admin;

use App\Helpers\CommonFunction;
use App\Helpers\MyHelper;
use Carbon\Carbon;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use App\Models\Vendor;
use App\Services\Admin\RosterUploadService;
use App\Models\EmployeesRosterRequest;

class AdhocWeeklyRosterService
{
    protected CommonFunction $commonFunction;
    protected  RosterUploadService $rosteruploadservice;

    public function __construct(CommonFunction $commonFunction, RosterUploadService $rosteruploadservice)
    {
        $this->commonFunction = $commonFunction;
        $this->rosteruploadservice = $rosteruploadservice;
    }

    public function postAdhocWeeklyRosterRequest($request): FoundationApplication|Response|ResponseFactory
    {
        try {
            $branch_id = Auth::user()->BRANCH_ID;
            $userid = Auth::user()->id;
            $db_name = Auth::user()->dbname;
            $RS_ACTIVE=MyHelper::$RS_ACTIVE;
            $active=true;
            $curdate=date('Y-m-d');
            $curdatetime = date('Y-m-d H:i:s');
            $RS_INACTIVE = MyHelper::$RS_INACTIVE;
            $id = Auth::user()->id;
            $AES_KEY = env("AES_ENCRYPT_KEY");
            //$obj=new RosterUploadService();
           
            $selected_employees=implode("','",$request->selected_emp_id);
             $selected_employees="'".$selected_employees."'";
            $trip_type = $request->triptype_id;
            $in_out=$request->selectedDate . ' ' . $request->selectedTime; 
            $selected_vendor_id = $request->vendor_id;
            $req_type = $request->req_type;

            $vendor_id_rs = vendor::on("$db_name")->where([
                ['BRANCH_ID', '=', $branch_id],
                ['VENDOR_ID', '=', $selected_vendor_id]])->first();
                $vendor_name = $vendor_id_rs->NAME;
               
            $branch_det = DB::connection($db_name)->table("branch")->select("BRANCH_ID","BRANCH_NAME","DIVISION_ID")->where("BRANCH_ID", "=", $branch_id)->get();
            $site_name = $branch_det[0]->BRANCH_NAME;
            if($trip_type=='P')
            {
                $already_exist="select RP.ROSTER_PASSENGER_ID,RP.ROSTER_PASSENGER_STATUS from roster_passengers RP where RP.EMPLOYEE_ID in($selected_employees) and RP.ACTIVE=1 and RP.ROSTER_PASSENGER_STATUS=1 and RP.ESTIMATE_END_TIME='".$in_out."'";
            }
            else
            {
                $already_exist="select RP.ROSTER_PASSENGER_ID,RP.ROSTER_PASSENGER_STATUS from roster_passengers RP where RP.EMPLOYEE_ID in($selected_employees) and RP.ACTIVE=1 and RP.ROSTER_PASSENGER_STATUS=1 and RP.ESTIMATE_START_TIME='".$in_out."'";
            }
            
            $exist_employee=DB::connection($db_name)->select($already_exist);
            if(count($exist_employee) >0 )
            {
               // return 'Exist';
               return response([
                'success' => false,
                'status' => 4,
                'message' => "Already exist",
                'validation_controller' => true,
                
             ],200);
            }
            
           /*  $selected_vendor_id=$request->selected_vendor_id;
            $emp="select E.EMPLOYEES_ID,E.BRANCH_ID,E.NAME as EMP_NAME,E.MOBILE,E.GENDER,E.PROJECT_NAME,L.LOCATION_NAME from employees E
            inner join locations L ON L.LOCATION_ID=E.LOCATION_ID
            where E.BRANCH_ID='".$branch_id."' and E.ACTIVE='".$RS_ACTIVE."' and E.EMPLOYEES_ID in($selected_employees)";
            $emp_data=DB::select($emp);
             */
            $emp="select E.EMPLOYEES_ID,E.BRANCH_ID,E.NAME as EMP_NAME,E.MOBILE,E.GENDER,E.PROJECT_NAME,L.LOCATION_NAME,E.ADDRESS_TYPE
            ,EA.DISTANCE,EA.ADDRESS,L2.LOCATION_ID,L2.LOCATION_NAME as sec_loc  from employees E
            
            inner join locations L ON L.LOCATION_ID=E.LOCATION_ID
            
            left join employee_address EA on EA.EMP_AUTO_ID=E.id and EA.ACTIVE=1
            
            LEFT JOIN locations L2 on L2.LOCATION_ID=EA.LOCATION_ID and EA.LOCATION_ID=L2.LOCATION_ID and EA.ACTIVE=1
            
            where E.BRANCH_ID='".$branch_id."' and E.ACTIVE='".$RS_ACTIVE."' and E.EMPLOYEES_ID in($selected_employees)";
            $emp_data=DB::connection($db_name)->select($emp);
            
            
            $insert_file = array("FILE_NAME" => $req_type, "FILE_SIZE" => 0, "FILE_SIZE" => 0,"FILE_TYPE" => 'csv', "STATUS" => 0, "CREATED_BY" => $id, "created_at" => date("Y-m-d H:i:s"),"BRANCH_ID"=>$branch_id);
            $last_file_id=DB::table("file_uploads")->insertGetId($insert_file);
            $roster_ids="SELECT ROSTER_ID from rosters where BRANCH_ID='".$branch_id."' ORDER BY ROSTER_ID desc limit 1";
            $last_roster_id=DB::connection($db_name)->select($roster_ids);
            if(count($last_roster_id)>0)
            {
                $last_route_id=$last_roster_id[0]->ROSTER_ID;
                $new_route_id=strtoupper(substr($req_type, 0, 1)).$last_route_id;
            }
            else
            {
                $new_route_id=strtoupper(substr($req_type, 0, 1)).'1';
            }
            $arr = array();
            $arr2 = array();
           // $obj=new CommonController();
           
            for($i=0;$i<count($emp_data);$i++)
            {
                $EMPLOYEES_ID=$emp_data[$i]->EMPLOYEES_ID;
                $EMP_NAME='topsa Test';
                $MOBILE=7200389436;
               // $EMP_NAME=$obj->AES_DECRYPT($emp_data[$i]->EMP_NAME,$AES_KEY);
                //$MOBILE=$obj->AES_DECRYPT($emp_data[$i]->MOBILE,$AES_KEY);
                $GENDER=$emp_data[$i]->GENDER;
                $PROJECT_NAME=$emp_data[$i]->PROJECT_NAME;
                $ADDRESS_TYPE=$emp_data[$i]->ADDRESS_TYPE;
                $ESTIMATE_START_TIME=$in_out;
                $TRIP_TYPE=$trip_type;
                if($TRIP_TYPE=='P')
                {
                    $ESTIMATE_TIME=date("Y-m-d H:i:s", strtotime("-3 hours", strtotime($ESTIMATE_START_TIME)));
                }
                else
                {
                    $ESTIMATE_TIME=NULL;
                }
                if($ADDRESS_TYPE=='S')
                {
                    $LOCATION_NAME=$emp_data[$i]->sec_loc;
                }
                else
                {
                    $LOCATION_NAME=$emp_data[$i]->LOCATION_NAME;
                }
                 
                $arr2[] = "('" . $last_file_id . "','" . $branch_id . "','".$new_route_id."','" . $TRIP_TYPE. "','" . $ESTIMATE_START_TIME . "','".$ESTIMATE_TIME."','".$site_name."','".$LOCATION_NAME."','".$EMPLOYEES_ID."','".$EMP_NAME."','".$GENDER."','".$MOBILE."','".$PROJECT_NAME."','".$LOCATION_NAME."','".$vendor_name."','KM','1','".$id."','".date("Y-m-d H:i:s")."','".$req_type."')";
            }
             $QUERY2 = "insert into input_datas(FILE_ID,BRANCH_ID,ROUTE_ID,TRIP_TYPE,ESTIMATE_START_TIME,ESTIMATE_TIME,SITE_NAME,LOCATION,EMPLOYEE_ID,EMPLOYEE_NAME,GENDER,EMPLOYEE_MOBILE,PROJECT_NAME,ADDRESS,VENDOR_NAME,TARIFF_TYPE,STATUS,CREATED_BY,created_at,REQUESTED_TYPE) values " . implode(',', $arr2) . "";
             $result = DB::insert($QUERY2);
             $res=$this->rosteruploadservice->InsertRoster();
            
            return response([
                'success' => true,
                'status' => 3,
                'data' => $result,
                
            ]);
        } catch (\Throwable|\Exception $e) {
            DB::rollBack();
            $this->commonFunction->logException($e);
            $message = $active ? $req_type.' Adhoc Roster status Unsuccessful' : $req_type.' Roster status Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ],500);
        }
    } 

    


    public function fetchAdhocWeeklyDateWiseRequestTime($request): FoundationApplication|Response|ResponseFactory
    {

        try {
            $user = Auth::user();
            $RS_ACTIVE = MyHelper::$RS_ACTIVE;
            $branchId = $user->BRANCH_ID;
            

            $triptype_id = $request->selected_trip_type;

            if ($triptype_id == 'ALL' || $triptype_id == 'all') {
                $triptype = ['P', 'D'];
            } else {
                $triptype = [$triptype_id];
            }

            $selected_date = $request->selected_date;

            $logintime = DB::table('employees_roster_request')
                ->select(   DB::raw(" TIME(ESTIMATE_START_TIME)  as logintime"))
                ->where(function ($query) use ($selected_date) {
                    $query->whereDate('ESTIMATE_START_TIME', $selected_date);
                })                
                ->where('BRANCH_ID', $branchId)               
                ->whereIn('TRIP_TYPE', $triptype)
                ->groupBy('ESTIMATE_START_TIME')
                ->get();

            return response([
                'success' => true,
                'status' => 3,
                'logintime' => $logintime,
            ]);
        } catch (\Throwable | \Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Datewise shift time  Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function fetch_AdhocWeeklyRosterRequest_Report_Details($request, bool $active = true): FoundationApplication|Response|ResponseFactory
    {
        try {
            
            $perPage = $request->input('per_page', MyHelper::$PAGINATION_PER_PAGE);
            $branch_id = Auth::user()->BRANCH_ID;

            $triptype_id = $request->triptype_id;

            if ($triptype_id == 'ALL' || $triptype_id == 'all') {
                $triptype = ['P', 'D'];
            } else {
                $triptype = [$triptype_id];
            }


            $req_type = $request->req_type;
            $login_time = $request->selectedTime;
            $login_date = $request->selectedDate;
            $login_datetime = $request->selectedDate . ' ' . $request->selectedTime;

            if ($login_time == 'ALL' || $login_time == 'all') {
                 $login_datetime=$login_date;
              }

            $date = Carbon::now();

            $data = EmployeesRosterRequest::query()
                ->select(
                    'employees_roster_request.ROSTER_REQ_ID',
                    'employees_roster_request.EMPLOYEE_ID',
                    'employees_roster_request.TRIP_TYPE',
                    'employees_roster_request.START_LOCATION',
                    'employees_roster_request.END_LOCATION',
                    'employees_roster_request.ESTIMATE_START_TIME',
                    'E.NAME as EMPNAME',
                    'E.MOBILE',
                    'E.GENDER',
                    'E.ADDRESS',
                    'E.DISTANCE',
                    'employees_roster_request.created_at',
                    'employees_roster_request.BOOKING_STATUS',
                    'employees_roster_request.ADHOC_OTHER_REASON',
                    DB::raw("'{$date->format('Y-m-d H:i:s')}' > 
                            IF(
                                employees_roster_request.TRIP_TYPE = 'P', 
                                DATE_SUB(employees_roster_request.ESTIMATE_START_TIME, INTERVAL 4 HOUR), 
                                DATE_SUB(employees_roster_request.ESTIMATE_START_TIME, INTERVAL 2 HOUR)
                            ) AS cancelation_status
                        "),
                    DB::raw("case 
                            when employees_roster_request.STATUS=1 then 'Created'
                            when employees_roster_request.STATUS=6 then 'Cancelled'
                            when employees_roster_request.STATUS=3 then 'Alloted'
                            when employees_roster_request.STATUS=2 then 'Roster Moved'
                            end status"),
                )
                ->Join('branch as B', 'B.BRANCH_ID', '=', 'employees_roster_request.BRANCH_ID')
                ->Join('employees as E', function ($join) use ($branch_id) {
                    $join->on('E.EMPLOYEES_ID', '=', 'employees_roster_request.EMPLOYEE_ID')
                        ->where('E.BRANCH_ID',  '=', $branch_id)
                        ->where('E.ACTIVE',  '=', 1);
                })
                ->where('employees_roster_request.BRANCH_ID', $branch_id)
                ->whereIn('employees_roster_request.TRIP_TYPE', $triptype)
                ->where('employees_roster_request.CATEGORY',  '=', $req_type)
                ->when($login_datetime, function ($query, $login_datetime) {
                    if (preg_match('/^\d{4}-\d{2}-\d{2}$/', $login_datetime)) {
        // Only date (YYYY-MM-DD)
        return $query->whereDate('employees_roster_request.ESTIMATE_START_TIME', '=', $login_datetime);
    } elseif (preg_match('/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/', $login_datetime)) {
        // Datetime (YYYY-MM-DD HH:MM:SS)
        return $query->where('employees_roster_request.ESTIMATE_START_TIME', '=', $login_datetime);
    } else {
        // Invalid format (handle error if needed)
        return $query;
    }                });
                
               

            $filterModel = $request->input('filterModel');
            if ($filterModel) {
                foreach ($filterModel as $field => $filter) {
                    if (isset($filter['filter']) && $filter['filter'] !== '') {
                        $value = $filter['filter'];
                        $type = $filter['type'];

                        switch ($field) {
                            case 'EMPLOYEE_ID':
                                $data->where('employees_roster_request.EMPLOYEE_ID', 'like', "%{$value}%");
                                break;
                        }
                    }
                }
            }


            if ($request->has('orderBy') && !empty($request->input('orderBy'))) {
                $orderBy = $request->input('orderBy');
                $order = $request->input('order', 'DESC');
                $data->orderBy($orderBy, $order);
            } else {
                $data->orderBy('employees_roster_request.ROSTER_REQ_ID', 'DESC');
            }


            if ($perPage === MyHelper::$PAGINATION_PER_PAGE_ALL) {
                $paginateddata = $data->paginate($data->count());
            } else {
                $perPage = is_numeric($perPage) ? (int)$perPage : MyHelper::$PAGINATION_PER_PAGE;
                $paginateddata = $data->paginate($perPage);
            }

            return response([
                'success' => true,
                'status' => 3,
                'adhoc_request_report' => $paginateddata,
            ]);
        } catch (\Throwable | \Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'Adhoc  Report Pagination Unsuccessful' : 'Adhoc  Report Pagination Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ], 500);
        }
    }


    public function DeleteAdhocWeeklyRosterRequest($request, $roster_req_Id): FoundationApplication|Response|ResponseFactory
    {
        $authUser = Auth::user();
       
        try {
            DB::beginTransaction();
      
            $req_type = $request->req_type;


            $sim = EmployeesRosterRequest::where('STATUS', MyHelper::$RS_ACTIVE)
                ->findOrFail($roster_req_Id);

            $sim->update([
                'CANCEL_REMARKS' => $request->input('remark'),
                'STATUS' => '6',
                'UPDATED_BY' => Auth::user()->id,
            ]);

            DB::commit();

            return response([
                'success' => true,
                'status' => 3,
                'message' => $req_type.'RosterRequest Deleted Successfully',
            ]);
        } catch (\Throwable|\Exception $e) {
            DB::rollBack();
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => $req_type.'RosterRequest Delete Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ],500);
        }
    }


    public function dataForCreate(): FoundationApplication|Response|ResponseFactory
    {

        try {
            $user = Auth::user();
            $RS_ACTIVE = MyHelper::$RS_ACTIVE;
            $db_name = Auth::user()->dbname;
            $branch_id = Auth::user()->BRANCH_ID;
            $vendor_id = Auth::user()->vendor_id;

            if (Auth::user()->user_type == MyHelper::$ADMIN) {
                $vendor_list = Vendor::on($db_name)->where('ACTIVE', '=', $RS_ACTIVE)->where("BRANCH_ID", "=", $branch_id)->get();
            } else {
                $vendor_list = Vendor::on($db_name)->where('ACTIVE', '=', $RS_ACTIVE)->where("VENDOR_ID", "=", $vendor_id)->get();
            }

            return response([
                'success' => true,
                'status' => 3,
                'vendor_list' => $vendor_list,

            ]);
        } catch (\Throwable | \Exception $e) {
            DB::rollBack();
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Data For Create for Route Order  Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}
