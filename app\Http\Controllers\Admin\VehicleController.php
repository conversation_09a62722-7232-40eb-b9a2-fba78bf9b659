<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\VehicleRequest;
use App\Http\Requests\Admin\VehicleDeleteRequest;
use App\Http\Requests\Admin\VehicleUpdateRequest;
use App\Services\Admin\VehicleService;

use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class VehicleController extends Controller
{
    protected VehicleService $vehicleService;

    public function __construct(VehicleService $vehicleService)
    {
        $this->vehicleService = $vehicleService;
    }

    public function index(): ResponseFactory|Application|Response
    {
        return $this->vehicleService->indexVehicle();
    }

   public function store(vehicleRequest $request): ResponseFactory|Application|Response
    {
        return $this->vehicleService->storeVehicle($request);
    }
    public function delete(VehicleDeleteRequest $request, $vehicleAutoIdCrypt): FoundationApplication|Response|ResponseFactory
    {
        return $this->vehicleService->deleteVehicle($request, $vehicleAutoIdCrypt);
    }
	
	 public function edit($vehicleAutoIdCrypt): Application|Response|ResponseFactory
    {
        return $this->vehicleService->editvehicle($vehicleAutoIdCrypt);
    }

    public function update(VehicleUpdateRequest $request, $vehicleAutoIdCrypt): Application|Response|ResponseFactory
    {
        return $this->vehicleService->updateVehicle($request, $vehicleAutoIdCrypt);
    }


    public function activeVehiclePagination(Request $request): ResponseFactory|Application|Response
    {
        return $this->vehicleService->paginationVehicle($request);
    }
    public function dataForCreate(): FoundationApplication|Response|ResponseFactory
    {
        return $this->vehicleService->dataForCreateVehicle();
    }

 
}
