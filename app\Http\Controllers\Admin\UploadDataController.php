<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\PostEmpLocationRequest;
use App\Services\Admin\UploadDataService;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class UploadDataController extends Controller
{
    protected UploadDataService $uploadDataService;

    public function __construct(UploadDataService $uploadDataService)
    {
        $this->uploadDataService = $uploadDataService;
    }

    public function GetEmpLocationUpdate(): Application|Response|ResponseFactory
    {
        return $this->uploadDataService->GetEmpLocationUpdate();
    }
    
    public function PostEmpLocationUpdate(PostEmpLocationRequest $request): Application|Response|ResponseFactory
    {
        return $this->uploadDataService->PostEmpLocationUpdate($request);
    }
}
