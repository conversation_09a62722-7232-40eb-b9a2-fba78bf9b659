<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MaskCallLogs extends Model
{
    use HasFactory;

    protected $table = 'MASK_CALL_LOG';
    protected $primaryKey = 'CALL_LOG_ID';

    public $timestamps = false;

    protected $fillable = [
        'BRANCH_ID',
        'ROSTER_ID',
        'EMPLOYEE_ID',
        'CALL_DATE',
        'ANSWER_TIME',
        'END_TIME',
        'CUSTOMER_NUMBER',
        'DRIVER_NUMBER',
        'CALL_TYPE',
        'DURATION',
        'RECORDING_URL',
        'ACTIVE',
        'STATUS',
        'CREATED_AT',
    ];
}
