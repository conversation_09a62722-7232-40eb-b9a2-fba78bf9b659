<?php

namespace App\Services\Elastic;

use App\Facades\Elasticsearch;
use App\Helpers\CommonFunction;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Response;
use Auth;

class ElasticsearchDriverGpsLoggingService
{
    private $index = 'gts_track';
    private $type = 'driver_gps_logging';
    protected CommonFunction $commonFunction;

    public function __construct(CommonFunction $commonFunction)
    {
        $this->commonFunction = $commonFunction;
    }

    public function createIndex(): FoundationApplication|Response|ResponseFactory
    {
        try {
            $params = [
                'index' => $this->index,
                'body' => [
                    'mappings' => [
                        $this->type => [
                            'properties' => [
                                'BEARING' => ['type' => 'text'],
                                'BRANCH_ID' => ['type' => 'keyword'],
                                'CAB_ID' => ['type' => 'keyword'],
                                'CAB_NO' => ['type' => 'keyword'],
                                'GPS_DATE' => ['type' => 'date', 'format' => 'yyyy-MM-dd HH:mm:ss'],
                                'GPS_ORDER_ID' => ['type' => 'integer'],
                                'KILOMETER' => ['type' => 'float'],
                                'POSITION' => ['type' => 'geo_point'],
                                'ROUTE_ID' => ['type' => 'keyword'],
                                'ROUTE_STATUS' => ['type' => 'integer'],
                                'SPEED' => ['type' => 'integer'],
                                'VENDOR_ID' => ['type' => 'keyword'],
                                'PROCESS_DATE' => ['type' => 'date', 'format' => 'date_hour_minute_second']
                            ]
                        ]
                    ]
                ]
            ];

            $response = Elasticsearch::indices()->create($params);
            return response([
                'success' => true,
                'message' => 'Index created successfully',
                'data' => $response
            ]);
        } catch (\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'message' => 'Failed to create index',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function indexLogs(): FoundationApplication|Response|ResponseFactory
    {
        try {
           echo "yy ". $id=Auth::user()->id;
           exit;
            $params = [
                'index' => $this->index,
                'type' => $this->type,
                'body' => [
                    'query' => [
                        'match_all' => new \stdClass()
                    ]
                ]
            ];

            $response = Elasticsearch::search($params);
            return response([
                'success' => true,
                'logs' => $response['hits']['hits']
            ]);
        } catch (\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'message' => 'Failed to retrieve logs',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function showLog($id): FoundationApplication|Response|ResponseFactory
    {
        try {
            $params = [
                'index' => $this->index,
                'type' => $this->type,
                'id' => $id
            ];

            $response = Elasticsearch::get($params);
            return response([
                'success' => true,
                'log' => $response['_source']
            ]);
        } catch (\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'message' => 'Log not found',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function storeLog($request): FoundationApplication|Response|ResponseFactory
    {
        try {
            $data = $request->all();
            
         
            if (isset($data['PROCESS_DATE'])) {
                $data['PROCESS_DATE'] = date('Y-m-d\TH:i:s', strtotime($data['PROCESS_DATE']));
            }

            $params = [
                'index' => $this->index,
                'type' => $this->type,
                'body' => $data
            ];

            $response = Elasticsearch::index($params);
            return response([
                'success' => true,
                'message' => 'Log created successfully',
                'data' => $response
            ]);
        } catch (\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'message' => 'Failed to create log',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function updateLog($request, $id): FoundationApplication|Response|ResponseFactory
    {
        try {
            $params = [
                'index' => $this->index,
                'type' => $this->type,
                'id' => $id,
                'body' => [
                    'doc' => $request->all()
                ]
            ];

            $response = Elasticsearch::update($params);
            return response([
                'success' => true,
                'message' => 'Log updated successfully',
                'data' => $response
            ]);
        } catch (\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'message' => 'Failed to update log',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function deleteLog($id): FoundationApplication|Response|ResponseFactory
    {
        try {
            $params = [
                'index' => $this->index,
                'type' => $this->type,
                'id' => $id
            ];

            $response = Elasticsearch::delete($params);
            return response([
                'success' => true,
                'message' => 'Log deleted successfully',
                'data' => $response
            ]);
        } catch (\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'message' => 'Failed to delete log',
                'error' => $e->getMessage()
            ], 500);
        }
    }


  




}
