<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;

use App\Services\Admin\RRDMISService;

use App\Http\Requests\Admin\VendorFindVehicleRequest;
use App\Http\Requests\Admin\FetchRRDMisRequest;


use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class RRDMISController extends Controller
{
    protected RRDMISService $rrdmisService;

    public function __construct(RRDMISService $rrdmisService)
    {
        $this->rrdmisService = $rrdmisService;
    }
    
    
    public function dataForCreate(): FoundationApplication|Response|ResponseFactory
    {
        return $this->rrdmisService->dataForCreate();
    }
    public function vendorwise_vehicle_find(VendorFindVehicleRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->rrdmisService->vendorwise_vehicle_find($request);
    }
    public function fetch_RRDMis_Details(FetchRRDMisRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->rrdmisService->fetch_RRDMis_Details($request);
    }
   
 
}
