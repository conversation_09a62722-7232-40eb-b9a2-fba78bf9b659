{"gts_track": {"mappings": {"driver_gps_logging": {"properties": {"BEARING": {"type": "text"}, "BRANCH_ID": {"type": "keyword"}, "CAB_ID": {"type": "keyword"}, "CAB_NO": {"type": "keyword"}, "GPS_DATE": {"type": "date", "format": "yyyy-MM-dd HH:mm:ss"}, "GPS_ORDER_ID": {"type": "integer"}, "KILOMETER": {"type": "float"}, "POSITION": {"type": "geo_point"}, "ROUTE_ID": {"type": "keyword"}, "ROUTE_STATUS": {"type": "integer"}, "SPEED": {"type": "integer"}, "VENDOR_ID": {"type": "keyword"}, "`PROCESS_DATE": {"type": "date", "format": "date_hour_minute_second"}}}, "customer_satus_logs": {"properties": {"EMPLOYEE_ID": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "PREVIOUS_STATUS": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "PROCESS_DATE": {"type": "date"}, "ROSTER_ID": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "ROSTER_PASSENGER_ID": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "UPDATED_STATUS": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "query": {"properties": {"bool": {"properties": {"filter": {"properties": {"range": {"properties": {"PROCESS_DATE": {"properties": {"gte": {"type": "date"}, "lte": {"type": "date"}}}}}}}}}}}, "sort": {"properties": {"PROCESS_DATE": {"properties": {"order": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}}}}}}}, "distance_alert": {"properties": {"ACTION_TYPE": {"type": "keyword"}, "BRANCH_ID": {"type": "keyword"}, "CAB_ID": {"type": "keyword"}, "COM_EMP_POSITION": {"type": "geo_point"}, "CURRENT_POSITION": {"type": "geo_point"}, "DISTANCE": {"type": "text"}, "EMP_ID": {"type": "keyword"}, "GPS_DATE": {"type": "date", "format": "yyyy-MM-dd HH:mm:ss"}, "GPS_TYPE": {"type": "keyword"}, "RADIUS": {"type": "text"}, "ROUTE_ID": {"type": "keyword"}, "VENDOR_ID": {"type": "keyword"}, "`PROCESS_DATE": {"type": "date", "format": "date_hour_minute_second"}}}, "shuttle_api_logs": {"properties": {"API_CASE_ID": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "API_REQUEST": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "NO": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "REQUEST_DATE": {"type": "date"}}}, "driver_gps_alert": {"properties": {"ACTION_REMARK": {"type": "keyword"}, "ALERT_STATUS": {"type": "keyword"}, "BRANCH_ID": {"type": "keyword"}, "CAB_ID": {"type": "keyword"}, "CAB_NO": {"type": "keyword"}, "GPS_DATE": {"type": "date", "format": "yyyy-MM-dd HH:mm:ss"}, "ORDER_ID": {"type": "integer"}, "ROUTE_ID": {"type": "keyword"}, "ROUTE_STATUS": {"type": "integer"}, "VENDOR_ID": {"type": "keyword"}, "`PROCESS_DATE": {"type": "date", "format": "date_hour_minute_second"}}}, "driver_msg_alert": {"properties": {"ACKNOWLEDGE_TIME": {"type": "date", "format": "yyyy-MM-dd HH:mm:ss"}, "ACTION": {"type": "keyword"}, "BRANCH_ID": {"type": "keyword"}, "CAB_ID": {"type": "keyword"}, "MESSAGE": {"type": "keyword"}, "REQUEST_TIME": {"type": "date", "format": "yyyy-MM-dd HH:mm:ss"}, "ROUTE_ID": {"type": "keyword"}, "SPEED_INTERVAL": {"type": "keyword"}, "STATUS": {"type": "keyword"}, "VENDOR_ID": {"type": "keyword"}, "`PROCESS_DATE": {"type": "date", "format": "date_hour_minute_second"}}}, "google_address": {"properties": {"ADDRESS": {"type": "keyword"}, "POSITION": {"type": "geo_point"}}}, "web_action_logs": {"properties": {"ACCEPTANCE_REJECT_STATE": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "ACTION": {"type": "keyword"}, "ACTIVE": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "ACTUAL_END_TIME": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "ADDRESS": {"type": "keyword"}, "ALLOT_ROSTERID": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "ATTACHMENT_DATE": {"type": "date"}, "BIT_ALLOTINSERT": {"type": "long"}, "BRANCH_ID": {"type": "keyword"}, "BREAKDOWN_ESCORTUPDATE": {"type": "long"}, "BREAKDOWN_INACTIVEROSTER": {"properties": {"ACTIVE": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "ROSTER_STATUS": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "UPDATED_BY": {"type": "long"}}}, "BREAKDOWN_INSERTDATE": {"properties": {"ACTIVE": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "ACTUAL_START_TIME": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "BRANCH_ID": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "CAB_ALLOT_TIME": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "CAB_CAPACITY_COUNT": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "CAB_ID": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "CREATED_BY": {"type": "long"}, "DRIVER_MASK_NUMBER": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "END_LOCATION": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "ESTIMATE_END_TIME": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "ESTIMATE_START_TIME": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "FILE_ID": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "PASSENGER_ALLOT_COUNT": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "PASSENGER_ALLOT_IN_ROUT_COUNT": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "PASSENGER_CLUBING_COUNT": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "ROSTER_STATUS": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "ROUTE_ID": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "START_LOCATION": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "TOTAL_KM": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "TRIP_APPROVED_KM": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "TRIP_TYPE": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "VENDOR_ID": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "created_at": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}}}, "BREAKDOWN_MASKINGUPDATE": {"type": "long"}, "BREAKDOWN_NEWROSTERID": {"properties": {"ROSTER_ID": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "UPDATED_BY": {"type": "long"}}}, "BREAKDOWN_PASSENGERUPDATE": {"type": "long"}, "CABALLOTINSERT": {"properties": {"ACCEPTANCE_REJECT_DATE_TIME": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "ACCEPTANCE_REJECT_STATE": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "CAB_ID": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "CREATED_BY": {"type": "long"}, "DATE_TIME": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "ROSTER_ID": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "created_at": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}}}, "CABID": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "CAB_ALLOCATION_SUCCESS": {"type": "long"}, "CAB_ALLOCATION_UPDATE": {"type": "long"}, "CATEGORY": {"type": "keyword"}, "CREATED_BY": {"type": "long"}, "DEVICE_ADD_SUCCESS": {"type": "boolean"}, "DEVICE_ID": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "DEVICE_MODEL": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "DISTANCE": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "DRIVER_ID": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "EMAIL": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "EMPLOYEES_ID": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "EMPLOYEE_ID": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "EMPLOYEE_SERIAL_NO": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "ENCRYPT_MOBILE": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "ENCRYPT_PASSWORD": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "ESCORT INSERT": {"type": "long"}, "ESCORT_EMPLOYEE_ID": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "ESCORT_OTP_DATA": {"properties": {"CREATED_BY": {"type": "long"}, "CREATED_DATE": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "MOBILE_NO": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "OTP": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "OTP_CATEGORY": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "ROSTER_PASSENGER_ID": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "VERIFIED_STATUS": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}}}, "ESCORT_OTP_STATUS": {"type": "boolean"}, "ESCORT_REASON": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "ESCORT_REMARKS": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "ESCORT_REMOVE": {"type": "long"}, "ESCORT_ROSTER_ID": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "ESCORT_SMS_DATA": {"properties": {"BRANCH_ID": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "CREATED_BY": {"type": "long"}, "CREATED_DATE": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "MESSAGE": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "ORIGINATOR": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "RECIPIENT": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "REF_NO": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "SENT_DATE": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}}}, "ESCORT_SMS_STATUS": {"type": "boolean"}, "ESCORT_UPDATE_STATUS": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "ESCORT_VALIDATE_STATUS": {"type": "long"}, "FC_EXPIRY": {"type": "date"}, "GENDER": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "IMEI_NO_1": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "IMEI_NO_2": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "INCREMENT_ROSTER_SUCCESS": {"type": "long"}, "INSERTNOTIFY": {"type": "boolean"}, "INSERTOTP": {"type": "boolean"}, "INSERTSMS": {"type": "boolean"}, "INSERT_BIT_ALLOT": {"type": "long"}, "INSERT_CABALLOT": {"properties": {"ACCEPTANCE_REJECT_DATE_TIME": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "ACCEPTANCE_REJECT_STATE": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "CAB_ID": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "CREATED_BY": {"type": "long"}, "DATE_TIME": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "ROSTER_ID": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "created_at": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}}}, "INSERT_NEWESCORT": {"type": "long"}, "INSERT_NOTIFICATION": {"type": "long"}, "INSERT_PASSENGER_NOTIFY": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "INSERT_PASSENGER_SMS": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "INSERT_SMS": {"type": "long"}, "INSURANCE_EXPIRY": {"type": "date"}, "KM_RESULT": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "LAST_INSERT_ID": {"type": "long"}, "LATITUDE": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "LOCATION_ID": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "LONGITUDE": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "MASKNUMBER_INSERT": {"type": "long"}, "MILEAGE": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "MOBILE_NO": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "NAME": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "NEW ROSTER ID": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "NEW VENDOR ID": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "NEW VENDOR NAME": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "NEW_END_LOCATON_NEW_ROSTER": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "NEW_END_LOCATON_OLD_ROSTER": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "NEW_ESCORT_INSERT": {"type": "long"}, "NEW_PASSENGERID": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "NEW_ROSTER_ID": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "NEW_TRIP_APPROVE_KM_NEW_ROSTER": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "NEW_TRIP_APPROVE_KM_OLD_ROSTER": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "NEW_TRIP_APPROVE_KM_UPDATE": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "NOSHOW_PASSENGERID": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "NOSHOW_TIME_UPDATE": {"type": "long"}, "OLD ROSTER ID": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "OLD VENDOR ID": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "OLD_ROSTER_ID": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "OTP_DATA_INSERT": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "OTP_INSERT": {"type": "long"}, "OTP_INSERT_SUCCESS": {"type": "boolean"}, "OTP_VALUE": {"type": "long"}, "PASSENGER ROSTER UPDATE": {"type": "long"}, "PASSENGER_ESTIMATETIME_UPDATE": {"type": "long"}, "PASSENGER_ID": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "PASSENGER_NOTIFYINSERT": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "PASSENGER_SMSINSERT": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "PASSWORD": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "PASSWORDS": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "PERMIT_EXPIRY": {"type": "date"}, "PREVIOUSED_STATUS": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "PREVIOUS_ADDRESS": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "PREVIOUS_DISTANCE": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "PREVIOUS_EMAIL": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "PREVIOUS_GENDER": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "PREVIOUS_LATITUDE": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "PREVIOUS_LOCATION_ID": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "PREVIOUS_LONGITUDE": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "PREVIOUS_MOBILE": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "PREVIOUS_NEW_ROSTER_END_LOCATON_NAME": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "PREVIOUS_OLD_ROSTER_END_LOCATON_NAME": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "PREVIOUS_ORDER": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "PREVIOUS_PROJECT_NAME": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "PREVIOUS_STATUS": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "PREVIOUS_TOTAL_KM": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "PREVIOUS_TRIP_APPROVE_KM": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "PREVIOUS_TRIP_APPROVE_KM_NEW_ROSTER": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "PREVIOUS_TRIP_APPROVE_KM_OLD_ROSTER": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "PROCESS_DATE": {"type": "date"}, "PROJECT_NAME": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "REASON_ID": {"type": "keyword"}, "REASON_LOG": {"type": "boolean"}, "REG_DATE": {"type": "date"}, "REJECT_REASON_ID": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "REMARKS": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "REMOVE_ESCORT": {"type": "long"}, "REMOVE_MASKNUMBER": {"type": "long"}, "RESET_PASSENGER": {"type": "long"}, "ROSTER_AUTOCANCEL": {"type": "long"}, "ROSTER_DECREMENT": {"type": "long"}, "ROSTER_ID": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "ROSTER_LOCATION_UPDATE": {"type": "long"}, "ROSTER_PASSANGERSTATUS_UPDATE": {"type": "long"}, "ROSTER_PASSENGERSTATUS": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "ROSTER_PASSENGER_DEACTIVE_SUCCESS": {"type": "long"}, "ROSTER_PASSENGER_ID": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "ROSTER_PASSENGER_SUCCESS": {"type": "long"}, "ROUTE_ESCORT_ID": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "ROUTE_EXECUTE_RESET": {"type": "long"}, "ROUTE_ORDERS": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "SELECTED_DATE": {"type": "date"}, "SELECTED_VENDOR": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "SEND_NOSHOWSMS": {"type": "boolean"}, "SIM_ADD_SUCCESS": {"type": "boolean"}, "SIM_MOBILE_NO": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "SIM_PROVIDER": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "SIM_SERIAL_NO": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "SMSINSERT": {"type": "boolean"}, "TARIFF_TYPE": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "TAX_EXPIRY": {"type": "date"}, "TRIP_TYPE": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "UPDATED_BY": {"type": "long"}, "UPDATED_STATUS": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "UPDATE_EMPLOYEE_LOCATION": {"type": "long"}, "UPDATE_NEW_ROSTER_SUCCESS": {"type": "long"}, "UPDATE_OLD_ROSTER_SUCCESS": {"type": "long"}, "UPDATE_ROSTER": {"type": "long"}, "UPDATE_ROSTER_DATA": {"properties": {"CAB_ALLOT_TIME": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "CAB_CAPACITY_COUNT": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "CAB_ID": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "ESTIMATE_END_TIME": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "ESTIMATE_START_TIME": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "MASK_ASSIGN_TIME": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "ROSTER_STATUS": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "UPDATED_BY": {"type": "long"}}}, "UPDATE_ROSTER_STATUS": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "UPDATE_ROSTER_SUCCESS": {"type": "long"}, "UPDATE_Roster_SUCCESS": {"type": "long"}, "UPDATE_Roster_passanger_SUCCESS": {"type": "long"}, "UPDATE_SUCCESS": {"type": "boolean"}, "URL_RESULT": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "USER_ID": {"type": "long"}, "USER_NAME": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "USER_TYPE": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "VEHICLE_ADD_SUCCESS": {"type": "boolean"}, "VEHICLE_ID": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "VEHICLE_JOIN_DATE": {"type": "date"}, "VEHICLE_MODEL_ID": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "VEHICLE_REG_NO": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "VENDOR CHANGE REMARKS": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "VENDOR_ID": {"type": "keyword"}, "cab_Mapping_SUCCESS": {"type": "boolean"}}}, "action_logs": {"properties": {"ACTION": {"type": "keyword"}, "ACTION_DATE": {"type": "date", "format": "yyyy-MM-dd HH:mm:ss"}, "ACTION_IP": {"type": "text"}, "ACTION_REQUEST": {"type": "text"}, "ACTION_RESPONSE": {"type": "keyword"}, "ACTION_SCREEN": {"type": "keyword"}, "BRANCH_ID": {"type": "keyword"}, "CAB_NO": {"type": "keyword"}, "CATEGORY": {"type": "keyword"}, "CATEGORY_ID": {"type": "keyword"}, "ROUTE_ID": {"type": "keyword"}, "VENDOR_ID": {"type": "keyword"}, "VERSION": {"type": "keyword"}, "`PROCESS_DATE": {"type": "date", "format": "date_hour_minute_second"}}}, "exception_logs": {"properties": {"ACTION_IP": {"type": "text"}, "BRANCH_ID": {"type": "keyword"}, "CATEGORY": {"type": "keyword"}, "CATEGORY_ID": {"type": "keyword"}, "ERROR_ACTION": {"type": "text"}, "ERROR_INFO": {"type": "text"}, "ROUTE_ID": {"type": "keyword"}, "VENDOR_ID": {"type": "keyword"}, "`PROCESS_DATE": {"type": "date", "format": "date_hour_minute_second"}}}, "shuttle_route_path": {"properties": {"APROX_DURATION": {"type": "keyword"}, "BRANCH_ID": {"type": "keyword"}, "POSITION": {"type": "geo_point"}, "ROUTE_DATE": {"type": "date"}, "ROUTE_ID": {"type": "keyword"}, "ROUTE_ORDER": {"type": "integer"}, "`PROCESS_DATE": {"type": "date", "format": "date_hour_minute_second"}}}, "route_deviation_alert": {"properties": {"ACTION_REMARK": {"type": "keyword"}, "BRANCH_ID": {"type": "keyword"}, "CAB_ID": {"type": "keyword"}, "CAB_NO": {"type": "keyword"}, "DEVIATION_DISTANCE": {"type": "keyword"}, "DEVIATION_RADIUS": {"type": "keyword"}, "ENDED_GPS_DATE": {"type": "date", "format": "yyyy-MM-dd HH:mm:ss"}, "ENDED_LOCATION": {"type": "keyword"}, "ENDED_POSITION": {"type": "geo_point"}, "END_POSITION": {"type": "geo_point"}, "INITIATED_GPS_DATE": {"type": "date", "format": "yyyy-MM-dd HH:mm:ss"}, "INITIATED_LOCATION": {"type": "keyword"}, "INITIATED_POSITION": {"type": "geo_point"}, "ORDER_ID": {"type": "integer"}, "REASON_ID": {"type": "keyword"}, "ROUTE_ID": {"type": "keyword"}, "ROUTE_STATUS": {"type": "integer"}, "START_POSITION": {"type": "geo_point"}, "VENDOR_ID": {"type": "keyword"}, "`PROCESS_DATE": {"type": "date", "format": "date_hour_minute_second"}}}, "shuttle_unavailable": {"properties": {"BRANCH_ID": {"type": "keyword"}, "CATEGORY": {"type": "keyword"}, "DROP_POINT": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "DROP_TIME": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "EMP_ID": {"type": "keyword"}, "PICKUP_POINT": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "PICKUP_TIME": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "PROCESS_DATE": {"type": "date"}, "RESCHEDULE_TIME": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "TRAVEL_TYPE": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}}}, "driver_speed_alert": {"properties": {"ACTION_REMARK": {"type": "keyword"}, "ALERT_STATUS": {"type": "keyword"}, "BRANCH_ID": {"type": "keyword"}, "CAB_ID": {"type": "keyword"}, "CAB_NO": {"type": "keyword"}, "ORDER_ID": {"type": "integer"}, "REASON_ID": {"type": "keyword"}, "ROUTE_ID": {"type": "keyword"}, "ROUTE_STATUS": {"type": "integer"}, "SERVICE_STATUS": {"type": "keyword"}, "START_GPS_DATE": {"type": "date", "format": "yyyy-MM-dd HH:mm:ss"}, "START_LOCATION": {"type": "keyword"}, "START_POSITION": {"type": "geo_point"}, "STOP_GPS_DATE": {"type": "date", "format": "yyyy-MM-dd HH:mm:ss"}, "STOP_LOCATION": {"type": "keyword"}, "STOP_POSITION": {"type": "geo_point"}, "VENDOR_ID": {"type": "keyword"}, "`PROCESS_DATE": {"type": "date", "format": "date_hour_minute_second"}}}, "driver_status_logs": {"properties": {"CATEGORY": {"type": "keyword"}, "PREVIOUS_STATUS": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "PROCESS_DATE": {"type": "date"}, "ROSTER_ID": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "ROSTER_PASSENGER_ID": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "TABLE_NAME": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "UPDATED_STATUS": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}}}, "cab_live_status": {"properties": {"BEARING": {"type": "text"}, "BRANCH_ID": {"type": "keyword"}, "CAB_ID": {"type": "keyword"}, "CAB_NO": {"type": "keyword"}, "GPS_DATE": {"type": "date", "format": "yyyy-MM-dd HH:mm:ss"}, "GPS_ORDER_ID": {"type": "integer"}, "KILOMETER": {"type": "float"}, "POSITION": {"type": "geo_point"}, "ROUTE_ID": {"type": "keyword"}, "ROUTE_STATUS": {"type": "integer"}, "SPEED": {"type": "integer"}, "VENDOR_ID": {"type": "keyword"}, "`PROCESS_DATE": {"type": "date", "format": "date_hour_minute_second"}}}, "booking_log": {"properties": {"BRANCH_ID": {"type": "keyword"}, "CONFIRM_STATUS": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "CUSTOMER_ID": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "EMP_ID": {"type": "keyword"}, "MOBILE_NO": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "NO_OF_DAYS": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "PICKUP_LOC": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "PICKUP_POINT": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "PICKUP_STOP_ID": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "PICKUP_TAG": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "PICKUP_TIME": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "PICK_TRIP_ID": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "PROCESS_DATE": {"type": "date"}, "RESPONSE": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "RETURN_LOC": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "RETURN_POINT": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "RETURN_STOP_ID": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "RETURN_TAG": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "RETURN_TIME": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "RETURN_TRIP_ID": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "ROUTE_TYPE": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "SSO_TOKEN": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "TRAVEL_TYPE": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}}}}}}