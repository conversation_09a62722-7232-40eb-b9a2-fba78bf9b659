<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Crypt;

class Sim extends Model
{
    use HasFactory;

    protected $table = 'sim';
    protected $primaryKey = 'SIM_ID';
	public $timestamps = false;
	protected $appends = ['SIM_ID_crypt'];
	
	protected $fillable = [
        'SIM_MOBILE_NO',
        'SIM_SERIAL_NO',
        'SIM_PROVIDER',
        'SIM_MAP_STATUS',
        'SIM_REMARKS',
        'ACTIVE',
        'CREATED_BY',
        'CREATED_DATE',
        'UPDATED_BY',
        'UPDATED_AT',
        'ORG_ID',
    ];

	public function getSIMIDCryptAttribute(): string
    {
        return Crypt::encryptString($this->SIM_ID);
    }
}
