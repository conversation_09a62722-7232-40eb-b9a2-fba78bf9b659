<?php

namespace App\Http\Requests\Admin;

use App\Helpers\CommonFunction;
use Closure;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;
use Illuminate\Contracts\Validation\Validator;

class EscortUpdateRequest extends FormRequest
{
    private CommonFunction $commonFunction;
    private ?object $escort = null;
    private ?int $escortId = null;

    public function __construct(CommonFunction $commonFunction)
    {
        parent::__construct();
        $this->commonFunction = $commonFunction;
    }

    public function authorize(): bool
    {
        return true;
    }

    protected function prepareForValidation(): void
    {
        try {
            $this->escortId = Crypt::decryptString($this->route('escortAutoIdCrypt'));
        } catch (\Throwable $e) {
            throw new HttpResponseException(response()->json([
                'success' => false,
                'validation_error' => true,
                'status' => 1,
                'message' => 'Invalid Escort ID',
            ], 400));
        }

        $this->escort = DB::table('escorts')->where('ESCORT_ID', $this->escortId)->first();

        if (!$this->escort) {
            throw new HttpResponseException(response()->json([
                'success' => false,
                'validation_error' => true,
                'status' => 1,
                'message' => 'Escort not found',
            ], 404));
        }
    }

    public function rules(): array
    {
       

        return [
            'ESCORT_NAME' => 'required',
            'ESCORT_MOBILE' => 'required',
            'ESCORT_BATCH_NUMBER' => 'required',
            
			];
    }

    public function messages(): array
    {
        return [
            'ESCORT_NAME.required' => 'Escort Name field is required',
            'ESCORT_MOBILE.required' => 'Escort Mobile field is required',
            'ESCORT_BATCH_NUMBER.required' => 'Batch Number Address field is required',
			
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json([
            'success' => false,
            'validation_error' => true,
            'status' => 2,
            'message' => 'Validation errors',
            'data' => $validator->errors()
        ], 422));
    }

    
}
