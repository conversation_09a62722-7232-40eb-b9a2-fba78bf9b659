<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\EmployeeUploadRequest;
use App\Services\Admin\EmployeeUploadService;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application;
use Illuminate\Http\Response;
use Illuminate\Http\Request;

class EmployeeUploadController extends Controller
{
    protected EmployeeUploadService $employeeuploadService;

    public function __construct(EmployeeUploadService $employeeuploadService)
    {
        $this->employeeuploadService = $employeeuploadService;
    }


    public function post_employee_upload(EmployeeUploadRequest $request): Application|Response|ResponseFactory
    {
        return $this->employeeuploadService->post_employee_upload($request);
    }
	
}
