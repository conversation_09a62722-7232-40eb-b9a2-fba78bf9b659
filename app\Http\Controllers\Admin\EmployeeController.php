<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\ActivateEmployeeRequest;
use App\Http\Requests\Admin\EmployeeDeleteRequest;
use App\Http\Requests\Admin\EmployeeRequest;
use App\Http\Requests\Admin\EmployeeUpdateRequest;
use App\Http\Requests\Admin\NeedSpecializedTransportEmployeeRequest;
use App\Http\Requests\Admin\EnableEditAddressRequest;
use App\Services\Admin\EmployeeService;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class EmployeeController extends Controller
{
    protected EmployeeService $employeeService;

    public function __construct(EmployeeService $employeeService)
    {
        $this->employeeService = $employeeService;
    }

    public function index(): ResponseFactory|Application|Response
    {
        return $this->employeeService->indexEmployee();
    }

    public function store(EmployeeRequest $request): ResponseFactory|Application|Response
    {
        return $this->employeeService->storeEmployee($request);
    }

    public function activeEmployeePagination(Request $request): ResponseFactory|Application|Response
    {
        return $this->employeeService->paginationEmployee($request);
    }

    public function edit($employeeAutoIdCrypt): Application|Response|ResponseFactory
    {
        return $this->employeeService->editEmployee($employeeAutoIdCrypt);
    }

    public function update(EmployeeUpdateRequest $request, $employeeAutoIdCrypt): Application|Response|ResponseFactory
    {
        return $this->employeeService->updateEmployee($request, $employeeAutoIdCrypt);
    }


    public function delete(EmployeeDeleteRequest $request, $employeeAutoIdCrypt): FoundationApplication|Response|ResponseFactory
    {
        return $this->employeeService->deleteEmployee($request, $employeeAutoIdCrypt);
    }

    public function deActiveEmployeePagination(Request $request): ResponseFactory|Application|Response
    {
        return $this->employeeService->paginationEmployee($request, false);
    }

    public function activate(ActivateEmployeeRequest $request): ResponseFactory|Application|Response
    {
        return $this->employeeService->activateEmployee($request);
    }
    public function needspecializedtransport(NeedSpecializedTransportEmployeeRequest $request): ResponseFactory|Application|Response
    {
        return $this->employeeService->needspecializedtransport($request);
    }
    public function enableEditAddress(EnableEditAddressRequest $request): ResponseFactory|Application|Response
    {
        return $this->employeeService->enableEditAddress($request);
    }

    public function dataForCreate(): FoundationApplication|Response|ResponseFactory
    {
        return $this->employeeService->dataForCreateEmployee();
    }

}
