<?php
namespace App\Http\Requests\Admin;

use App\Helpers\CommonFunction;
use Closure;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;

class LocationRequest extends FormRequest
{
    protected CommonFunction $commonFunction;

    public function __construct(CommonFunction $commonFunction)
    {
        
    }

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        
        return [
            'DIVISION_ID' => 'required',
            'LOCATION_NAME' => ['required',Rule::unique('locations','LOCATION_NAME')->where('DIVISION_ID', 1)],
            'LATITUDE' => 'required',
            'LONGITUDE' => 'required',
           
        ];
    }
   

    public function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json([
            'success' => false,
            'validation_error' => true,
            'message' => 'Validation errors',
            'data' => $validator->errors()
        ]));
    }

    private function checkDeactivatedLocation($branchId): Closure
    {
        return function (string $attribute, mixed $value, Closure $fail) use ($branchId) {
            $deactivatedLocation = DB::table("location")
                ->where("LOCATION_ID", $value)
                ->where("ACTIVE", 2)
                ->exists();

            if ($deactivatedLocation) {
                $fail("Location already exists and is deactivated.");
            }
        };
    }

   
}
