<?php

namespace App\Services\Admin;

use App\Helpers\CommonFunction;
use App\Helpers\MyHelper;
use Carbon\Carbon;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use App\Models\Vendor;
use App\Models\Cab_allocation;
use App\Models\Roster;
use App\Models\RosterPassenger;
use App\Models\Cab;
use App\Models\Vehicle;
use App\Models\VehicleModel;
use App\Models\Driver;
use App\Models\property;
use App\Models\Driver_Billing_Summary;
use App\Services\Admin\RosterUploadService;
use App\Http\Controllers\MaskNumberClearController;
use App\Http\Controllers\ElasticController;

class NewRosterService
{
    protected CommonFunction $commonFunction;
    protected ?Branch $currentBranch = null;
    protected  $rosteruploadservice;

    public function __construct(CommonFunction $commonFunction, RosterUploadService $rosteruploadservice)
    {
        $this->commonFunction = $commonFunction;
        $this->RosterUploadService = $rosteruploadservice;
    }
	     
    
    public function newrosteremployee($request): FoundationApplication|Response|ResponseFactory
    {
        try {
            $branch_id = Auth::user()->BRANCH_ID;
            $userid = Auth::user()->id;
            $db_name = Auth::user()->dbname;
            $RS_ACTIVE=MyHelper::$RS_ACTIVE;
            $curdate=date('Y-m-d');
            $active=true;
            $authUser = Auth::user();
            $RS_NEWROSTER=MyHelper::$RS_NEWROSTER;
            $RS_TOTALALLOT=MyHelper::$RS_TOTALALLOT;
            $RS_TOTALACCEPT=MyHelper::$RS_TOTALACCEPT;
            $RS_TOTALEXECUTE=MyHelper::$RS_TOTALEXECUTE;

            $emp_id_mobile=$request->emp_id_mobile;
            $employee = DB::table('employees as E')->select('E.EMPLOYEES_ID', 'E.MOBILE', 'E.NAME')
                        ->where("E.ACTIVE", "=", 1)
                       // ->orwhere("E.MOBILE", "=", $emp_id_mobileno_val)
                        ->Where("E.EMPLOYEES_ID","=",$emp_id_mobile)
                        ->where("E.BRANCH_ID", "=", $branch_id)
                        ->get();

                        if (count($employee) > 0) {
                            $employee[0]->NAME = $this->commonFunction->AES_DECRYPT($employee[0]->NAME, config('app.aes_encrypt_key'));
                            $employee[0]->MOBILE = $this->commonFunction->AES_DECRYPT($employee[0]->MOBILE, config('app.aes_encrypt_key'));
                           /*  $EMPLOYEES_ID = $employee[0]->EMPLOYEES_ID;
                            $MOBILE_NO = $employee[0]->MOBILE;
                            $employee_mobile = $obj->AES_DECRYPT($MOBILE_NO, env('AES_ENCRYPT_KEY'));
                             $NAME = $employee[0]->NAME;
                            $employee_name = $obj->AES_DECRYPT($NAME, env('AES_ENCRYPT_KEY'));
     
                              $label_name = $EMPLOYEES_ID . " -- " . $employee_mobile. " -- ".$employee_name;
    
                              $data .=  "<option value='" . $EMPLOYEES_ID . "' selected>" . $label_name . "</option>"; */
                            
                        } else {
                            return response([
                                'success' => true,
                                'status' => 3,
                                'data' => [],
                                'message' => "Record Not Found",
                               
                                
                            ],200);
                        }
         
        
            return response([
                'success' => true,
                'status' => 3,
                'data' => $employee,
                
            ]);
        } catch (\Throwable|\Exception $e) {
            DB::rollBack();
            $this->commonFunction->logException($e);
            $message = $active ? 'Route order status Unsuccessful' : 'RouteOrder  status Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ],500);
        }
    } 
    public function PostRosterRequest($request): FoundationApplication|Response|ResponseFactory
    {
        try {
            $branch_id = Auth::user()->BRANCH_ID;
            $userid = Auth::user()->id;
            $db_name = Auth::user()->dbname;
            $RS_ACTIVE=MyHelper::$RS_ACTIVE;
            $active=true;
            $curdate=date('Y-m-d');
            $curdatetime = date('Y-m-d H:i:s');
            $RS_INACTIVE = MyHelper::$RS_INACTIVE;
            $id = Auth::user()->id;
            $AES_KEY = env("AES_ENCRYPT_KEY");
            //$obj=new RosterUploadService();
            $selected_employees=$request->selected_emp_id;
                 
                  if($branch_id == 18 || $branch_id=='62' || $branch_id =='63' || $branch_id=='64'){
                        $selected_employees=implode("','",$request['selected_emp_id']);
                        $selected_employees="'".$selected_employees."'";
                            
                   }else{
                          $selected_employees=implode(",",$request['emp_id']);                      
                   } 
            
            $trip_type=$request->trip_type;
            $in_out=$request->selected_shift_time;
            $selected_vendor_id=$request->selected_vendor_id;
            $vendor_id_rs = vendor::on("$db_name")->where([
                ['BRANCH_ID', '=', $branch_id],
                ['VENDOR_ID', '=', $selected_vendor_id]])->first();
                $vendor_name = $vendor_id_rs->NAME;
               
            $branch_det = DB::connection($db_name)->table("branch")->select("BRANCH_ID","BRANCH_NAME","DIVISION_ID")->where("BRANCH_ID", "=", $branch_id)->get();
            $site_name = $branch_det[0]->BRANCH_NAME;
            if($trip_type=='P')
            {
                $already_exist="select RP.ROSTER_PASSENGER_ID,RP.ROSTER_PASSENGER_STATUS from roster_passengers RP where RP.EMPLOYEE_ID in($selected_employees) and RP.ACTIVE=1 and RP.ROSTER_PASSENGER_STATUS=1 and RP.ESTIMATE_END_TIME='".$in_out."'";
            }
            else
            {
                $already_exist="select RP.ROSTER_PASSENGER_ID,RP.ROSTER_PASSENGER_STATUS from roster_passengers RP where RP.EMPLOYEE_ID in($selected_employees) and RP.ACTIVE=1 and RP.ROSTER_PASSENGER_STATUS=1 and RP.ESTIMATE_START_TIME='".$in_out."'";
            }
            
            $exist_employee=DB::connection($db_name)->select($already_exist);
            if(count($exist_employee) >0 )
            {
               // return 'Exist';
               return response([
                'success' => false,
                'status' => 4,
                'message' => "Already exist",
                'validation_controller' => true,
                
             ],200);
            }
            
           /*  $selected_vendor_id=$request->selected_vendor_id;
            $emp="select E.EMPLOYEES_ID,E.BRANCH_ID,E.NAME as EMP_NAME,E.MOBILE,E.GENDER,E.PROJECT_NAME,L.LOCATION_NAME from employees E
            inner join locations L ON L.LOCATION_ID=E.LOCATION_ID
            where E.BRANCH_ID='".$branch_id."' and E.ACTIVE='".$RS_ACTIVE."' and E.EMPLOYEES_ID in($selected_employees)";
            $emp_data=DB::select($emp);
             */
            $emp="select E.EMPLOYEES_ID,E.BRANCH_ID,E.NAME as EMP_NAME,E.MOBILE,E.GENDER,E.PROJECT_NAME,L.LOCATION_NAME,E.ADDRESS_TYPE
            ,EA.DISTANCE,EA.ADDRESS,L2.LOCATION_ID,L2.LOCATION_NAME as sec_loc  from employees E
            
            inner join locations L ON L.LOCATION_ID=E.LOCATION_ID
            
            left join employee_address EA on EA.EMP_AUTO_ID=E.id and EA.ACTIVE=1
            
            LEFT JOIN locations L2 on L2.LOCATION_ID=EA.LOCATION_ID and EA.LOCATION_ID=L2.LOCATION_ID and EA.ACTIVE=1
            
            where E.BRANCH_ID='".$branch_id."' and E.ACTIVE='".$RS_ACTIVE."' and E.EMPLOYEES_ID in($selected_employees)";
            $emp_data=DB::connection($db_name)->select($emp);
            
            
            $insert_file = array("FILE_NAME" => 'NEWROSTERCREATE', "FILE_SIZE" => 0, "FILE_SIZE" => 0,"FILE_TYPE" => 'csv', "STATUS" => 0, "CREATED_BY" => $id, "created_at" => date("Y-m-d H:i:s"),"BRANCH_ID"=>$branch_id);
            $last_file_id=DB::table("file_uploads")->insertGetId($insert_file);
            $roster_ids="SELECT ROSTER_ID from rosters where BRANCH_ID='".$branch_id."' ORDER BY ROSTER_ID desc limit 1";
            $last_roster_id=DB::connection($db_name)->select($roster_ids);
            if(count($last_roster_id)>0)
            {
                $last_route_id=$last_roster_id[0]->ROSTER_ID;
                $new_route_id='N'.$last_route_id;
            }
            else
            {
                $new_route_id='N1';
            }
            $arr = array();
            $arr2 = array();
           // $obj=new CommonController();
           
            for($i=0;$i<count($emp_data);$i++)
            {
                $EMPLOYEES_ID=$emp_data[$i]->EMPLOYEES_ID;
                $EMP_NAME='topsa Test';
                $MOBILE=7200389436;
               // $EMP_NAME=$obj->AES_DECRYPT($emp_data[$i]->EMP_NAME,$AES_KEY);
                //$MOBILE=$obj->AES_DECRYPT($emp_data[$i]->MOBILE,$AES_KEY);
                $GENDER=$emp_data[$i]->GENDER;
                $PROJECT_NAME=$emp_data[$i]->PROJECT_NAME;
                $ADDRESS_TYPE=$emp_data[$i]->ADDRESS_TYPE;
                $ESTIMATE_START_TIME=$in_out;
                $TRIP_TYPE=$trip_type;
                if($TRIP_TYPE=='P')
                {
                    $ESTIMATE_TIME=date("Y-m-d H:i:s", strtotime("-3 hours", strtotime($ESTIMATE_START_TIME)));
                }
                else
                {
                    $ESTIMATE_TIME=NULL;
                }
                if($ADDRESS_TYPE=='S')
                {
                    $LOCATION_NAME=$emp_data[$i]->sec_loc;
                }
                else
                {
                    $LOCATION_NAME=$emp_data[$i]->LOCATION_NAME;
                }
                 
                $arr2[] = "('" . $last_file_id . "','" . $branch_id . "','".$new_route_id."','" . $TRIP_TYPE. "','" . $ESTIMATE_START_TIME . "','".$ESTIMATE_TIME."','".$site_name."','".$LOCATION_NAME."','".$EMPLOYEES_ID."','".$EMP_NAME."','".$GENDER."','".$MOBILE."','".$PROJECT_NAME."','".$LOCATION_NAME."','".$vendor_name."','KM','1','".$id."','".date("Y-m-d H:i:s")."','ADHOC')";
            }
             $QUERY2 = "insert into input_datas(FILE_ID,BRANCH_ID,ROUTE_ID,TRIP_TYPE,ESTIMATE_START_TIME,ESTIMATE_TIME,SITE_NAME,LOCATION,EMPLOYEE_ID,EMPLOYEE_NAME,GENDER,EMPLOYEE_MOBILE,PROJECT_NAME,ADDRESS,VENDOR_NAME,TARIFF_TYPE,STATUS,CREATED_BY,created_at,REQUESTED_TYPE) values " . implode(',', $arr2) . "";
             $result = DB::insert($QUERY2);
             $res=$this->RosterUploadService->InsertRoster();
            
            return response([
                'success' => true,
                'status' => 3,
                'data' => $result,
                
            ]);
        } catch (\Throwable|\Exception $e) {
            DB::rollBack();
            $this->commonFunction->logException($e);
            $message = $active ? 'New Roster status Unsuccessful' : 'New Roster status Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ],500);
        }
    } 
    public function trip_type_wise_login_time($request): FoundationApplication|Response|ResponseFactory
    {

        try {
                $user = Auth::user();
                $RS_ACTIVE = MyHelper::$RS_ACTIVE;
                $db_name = Auth::user()->dbname;
                $branch_id = Auth::user()->BRANCH_ID;
                $vendor_id = Auth::user()->vendor_id;
                $trip_type = $request->selected_trip_type;
                $cur_date=date('Y-m-d');

                $cur_time=date("H:i:s",strtotime('-30 minutes'));
		if($branch_id==32 || $branch_id==53 || $branch_id== 54 || $branch_id== 48 || $branch_id== 62 || $branch_id== 63)
        {
		
			if($trip_type=='P')
			{
                    if($branch_id== 62 || $branch_id== 63)
                    { 
                        $query="select IN_TIME,CATEGORY,CONCAT('".$cur_date." ', IN_TIME) AS shift_date_time from shift_time where BRANCH_ID='".$branch_id."' and Active=1 and CATEGORY= 'SHIFTTIME' and IN_TIME is not null and IN_TIME!='00:00:00' GROUP BY IN_TIME order by IN_TIME ASC";

                    }else{
                            $query="select IN_TIME,CATEGORY,CONCAT('".$cur_date." ', IN_TIME) AS shift_date_time from  shift_time where BRANCH_ID='".$branch_id."' and Active=1 and CATEGORY= 'SHIFTTIME' and IN_TIME is not null GROUP BY IN_TIME order by IN_TIME ASC";

                    }
				
			}
			else
			{
                    if($branch_id== 62 || $branch_id== 63)
                    { 
                        $query="select OUT_TIME,CATEGORY,CONCAT('".$cur_date." ', OUT_TIME) AS shift_date_time from shift_time where BRANCH_ID='".$branch_id."' and Active=1 and  CATEGORY= 'SHIFTTIME' and OUT_TIME is not null and OUT_TIME!='00:00:00' GROUP BY OUT_TIME order by OUT_TIME ASC";

                    }
                    else
                    {
				    $query="select OUT_TIME,CATEGORY,CONCAT('".$cur_date." ', OUT_TIME) AS shift_date_time from shift_time where BRANCH_ID='".$branch_id."' and Active=1 and  CATEGORY= 'SHIFTTIME' and OUT_TIME is not null GROUP BY OUT_TIME order by OUT_TIME ASC";
                    }
			}
		}
		else
		{
			if($trip_type=='P')
			{
				$query="select IN_TIME,CATEGORY,CONCAT('".$cur_date." ', IN_TIME) AS shift_date_time from shift_time where BRANCH_ID='".$branch_id."' and Active=1 and  CATEGORY= 'SHIFTTIME'  and IN_TIME >= '".$cur_time."'";
			}
			else
			{
				$query="select OUT_TIME,CATEGORY,CONCAT('".$cur_date." ', OUT_TIME) AS shift_date_time from shift_time where BRANCH_ID='".$branch_id."' and Active=1 and  CATEGORY= 'SHIFTTIME'  and OUT_TIME >= '".$cur_time."' ";
			}
                  
		}
		$shift_time= DB::connection($db_name)->select($query);
                
                return response([
                'success' => true,
                'status' => 3,
                'shift_time' => $shift_time,
               
                
            ]);
        } catch (\Throwable|\Exception $e) {
            DB::rollBack();
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'new roster Shift time  Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ],500);
        }
    }
   
    
	
    public function dataForCreate(): FoundationApplication|Response|ResponseFactory
    {

        try {
                $user = Auth::user();
                $RS_ACTIVE = MyHelper::$RS_ACTIVE;
                $db_name = Auth::user()->dbname;
                $branch_id = Auth::user()->BRANCH_ID;
                $vendor_id = Auth::user()->vendor_id;
                
                if (Auth::user()->user_type == MyHelper::$ADMIN) {
                    $vendor_list = Vendor::on($db_name)->where('ACTIVE', '=', $RS_ACTIVE)->where("BRANCH_ID", "=", $branch_id)->get();
                } else {
                    $vendor_list = Vendor::on($db_name)->where('ACTIVE', '=', $RS_ACTIVE)->where("VENDOR_ID", "=", $vendor_id)->get();
                }
                
                return response([
                'success' => true,
                'status' => 3,
                'vendor_list' => $vendor_list,
               
                
            ]);
        } catch (\Throwable|\Exception $e) {
            DB::rollBack();
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Data For Create for Route Order  Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ],500);
        }
    }
   
    
	

   
}
