<?php

namespace App\Models;

use App\Helpers\CommonFunction;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Crypt;

class Employee_input_data extends Model
{
    use HasFactory;

    protected $table = 'employee_input_datas';
    protected $primaryKey = 'INPUT_ID';
    public $timestamps = false;

    protected $fillable = [
        'INPUT_ID',
        'FILE_ID',
        'BRANCH_ID',
        'LOCATION',
        'EMPLOYEE_ID',
        'EMPLOYEE_NAME',
        'EMPLOYEE_LAST_NAME',
        'GENDER',
        'EMPLOYEE_MOBILE',
        'PROJECT_NAME',
        'ADDRESS',
        'ACTIVE',
        'LATITUDE',
        'LONGITUDE',
        'MAIL_ID',
        'EMERGENCY_CONTACT_NO',
        'RFID_CARD',
        'STATUS',
        'CREATED_BY',
        'created_at',
        'UPDATED_BY',
        'updated_at',
        'LOGIN_TIME',
        'LOGOUT_TIME'
       
    ];

    protected CommonFunction $commonFunction;

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
        $this->commonFunction = new CommonFunction();
    }

    
}
