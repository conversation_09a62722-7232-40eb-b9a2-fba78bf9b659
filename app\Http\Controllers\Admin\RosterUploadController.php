<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\TrackingDashboardRequest;
use App\Http\Requests\Admin\TripDetailsRequest;

use App\Http\Requests\Admin\RosterUploadRequest;
use App\Services\Admin\RosterUploadService;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application;
use Illuminate\Http\Response;
use Illuminate\Http\Request;

class RosterUploadController extends Controller
{
    protected RosterUploadService $rosteruploadService;

    public function __construct(RosterUploadService $rosteruploadService)
    {
        $this->rosteruploadService = $rosteruploadService;
    }


    public function post_roster_upload(RosterUploadRequest $request): Application|Response|ResponseFactory
    {
        return $this->rosteruploadService->post_roster_upload($request);
    }
	
}
