<?php

namespace App\Helpers;

class EncryptionKeyManager
{
    const KEY_LENGTH = 32; // 256 bits

    public static function generateKey(): string
    {
        return bin2hex(random_bytes(self::KEY_LENGTH));
    }

    public static function saveKeyToFile(string $key, string $filePath): void
    {
        file_put_contents($filePath, $key);
    }

    public static function loadKeyFromFile(string $filePath): string
    {
        return file_get_contents($filePath);
    }

    public static function getKeyFromEnvironment(string $envName): string
    {
        $key = getenv($envName);
        if ($key === false || strlen($key) !== self::KEY_LENGTH * 2) { // hex encoded key is twice the length
            throw new \RuntimeException("Invalid or missing encryption key in environment variable: $envName");
        }
        return $key;
    }
}
