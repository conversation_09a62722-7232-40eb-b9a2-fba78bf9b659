<?php

namespace App\Services\Admin;

use App\Helpers\CommonFunction;
use App\Helpers\MyHelper;
use App\Models\Branch;
use App\Models\Roster;
use App\Models\TollPayment;
use Carbon\Carbon;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;

class TollPenaltyService
{
    protected CommonFunction $commonFunction;
    protected ?Branch $currentBranch = null;

    public function __construct(CommonFunction $commonFunction)
    {
        $this->commonFunction = $commonFunction;
    }

    public function fetch_Toll_Penalty_Details($request, bool $active = true): FoundationApplication|Response|ResponseFactory
    {
        try {
            $perPage = $request->input('per_page', MyHelper::$PAGINATION_PER_PAGE);
            $branch_id = Auth::user()->BRANCH_ID;
           


            $vendor_id = $request->vendor_id;

            if ($vendor_id == 'ALL' || $vendor_id == 'all') {
                $vendorid ="";
            } else {
                $vendorid = $vendor_id;
            }

           
            $from_date = Carbon::parse($request->from_date)->startOfDay()->toDateTimeString();
            $to_date = Carbon::parse($request->to_date)->endOfDay()->toDateTimeString();

            $RS_ACTIVE = MyHelper::$RS_ACTIVE;
            $RS_SHUTTLE_TYPE=MyHelper::$RS_SHUTTLE_TYPE;
            $RS_TRIPCLOSE=MyHelper::$RS_TRIPCLOSE;
            $RS_DELAYROUTES=MyHelper::$RS_DELAYROUTES;
            $RS_MANUALTRIPCLOSE=MyHelper::$RS_MANUALTRIPCLOSE;

            $data = Roster::query()
                ->select(

                    'rosters.ROSTER_ID',
                    'rosters.ROUTE_ID',
                    'rosters.TRIP_TYPE',
                    'rosters.START_LOCATION',
                    'rosters.END_LOCATION',
                    'rosters.PASSENGER_ALLOT_COUNT',
                    'rosters.TRIP_APPROVED_KM',
                    'rosters.UPDATED_BY',
                    'VM.MODEL',
                    'rosters.ESTIMATE_END_TIME',
                     'rosters.ESTIMATE_START_TIME',                     
		      'rosters.updated_at',
                    'V.VEHICLE_REG_NO',
                    'VE.NAME',
                    'D.DRIVERS_NAME',
                    'D.DRIVER_MOBILE',
                    'C.CAB_ID',	
                    DB::raw("if(rosters.TRIP_TYPE = 'P',DATE(rosters.ESTIMATE_END_TIME),DATE(rosters.ESTIMATE_START_TIME)) as INOUT_DATE"),
                    DB::raw("if(rosters.TRIP_TYPE = 'P',TIME(rosters.ESTIMATE_END_TIME),TIME(rosters.ESTIMATE_START_TIME)) AS INOUT_TIME"),
                    
                )

                ->Join('cab as  C', 'C.CAB_ID', '=', 'rosters.CAB_ID')
                ->Join('vehicles as V', 'V.VEHICLE_ID', '=', 'C.VEHICLE_ID')               
                ->Join('vendors as  VE', 'VE.VENDOR_ID', '=', 'C.VENDOR_ID')
                ->Join('drivers as D', 'D.DRIVERS_ID', '=', 'C.DRIVER_ID')
                ->Join('vehicle_models as VM', 'VM.VEHICLE_MODEL_ID', '=', 'V.VEHICLE_MODEL_ID')           


                ->where('rosters.ACTIVE', $RS_ACTIVE)
                ->where('rosters.BRANCH_ID', $branch_id)
                ->whereNotIn('rosters.TRIP_TYPE', [$RS_SHUTTLE_TYPE])
                ->whereRaw("(rosters.ROSTER_STATUS & $RS_TRIPCLOSE OR rosters.ROSTER_STATUS & $RS_DELAYROUTES OR rosters.ROSTER_STATUS & $RS_MANUALTRIPCLOSE)")
           
                ->when($vendorid, function ($query, $vendorid) {
                    return $query->where('rosters.VENDOR_ID', $vendorid);
                })

                ->havingRaw("(rosters.ESTIMATE_END_TIME BETWEEN  '$from_date' AND '$to_date' OR  rosters.ESTIMATE_START_TIME BETWEEN  '$from_date' AND '$to_date')");


            $filterModel = $request->input('filterModel');
            if ($filterModel) {
                foreach ($filterModel as $field => $filter) {
                    if (isset($filter['filter']) && $filter['filter'] !== '') {
                        $value = $filter['filter'];
                        $type = $filter['type'];

                        switch ($field) {
                            case 'VEHICLE_REG_NO':
                                $data->where('V.VEHICLE_REG_NO', 'like', "%{$value}%");
                                break;
                        }
                    }
                }
            }


            if ($request->has('orderBy') && !empty($request->input('orderBy'))) {
                $orderBy = $request->input('orderBy');
                $order = $request->input('order', 'DESC');
                $data->orderBy($orderBy, $order);
            } else {
                $data->orderBy('rosters.ROSTER_ID', 'DESC');
            }


            if ($perPage === MyHelper::$PAGINATION_PER_PAGE_ALL) {
                $paginateddata = $data->paginate($data->count());
            } else {
                $perPage = is_numeric($perPage) ? (int)$perPage : MyHelper::$PAGINATION_PER_PAGE;
                $paginateddata = $data->paginate($perPage);
            }

            return response([
                'success' => true,
                'status' => 3,
                'Penalty_Details' => $paginateddata,
            ]);
        } catch (\Throwable | \Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'Penalty List Pagination Unsuccessful' : 'Penalty List Pagination Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ], 500);
        }
    }



    public function storeTollCharges($request): FoundationApplication|Response|ResponseFactory
    {

        try {

            DB::beginTransaction();
            $auth_user = Auth::user();
            $tollChargeData = $this->prepareTollChargesData($request, $auth_user);
            $tollChargesResult = TollPayment::create($tollChargeData);
            return response([
                'success' => true,
                'status' => 200,
                'message' => 'Toll Charges Created Successfully',

            ]);
        } catch (\Throwable | \Exception $e) {
            DB::rollBack();
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'validation_controller' => true,
                'status' => 4,
                'message' => 'Toll Charges Created UnSuccessfully',
                'error' => $e->getMessage(),
            ]);
        } finally {
            DB::commit();
        }
    }


    private function prepareTollChargesData($request, $auth_user): array
    {
        $date = Carbon::now();
        return [

            "ROSTER_ID" => $request->ROSTER_ID,
            "TOLL_ID" => '1',
            "CAB_ID" => $request->CAB_ID,
            "TOLL_CHARGE" => $request->TOLL_CHARGES,
            "PAYMENT_DATE" => $request->PAYMENT_DATE,
            "TICKET_NO" => $request->TICKET_NO,
            "CREATED_DATE" => $date->format("Y-m-d H:i:s"),
            "CREATED_BY" => $auth_user->id,
        ];
    }   


   public function dataForCreatePenalties(): FoundationApplication|Response|ResponseFactory
    {

        try {
           
            $branch_id = Auth::user()->BRANCH_ID;
            $rsActive = MyHelper::$RS_ACTIVE;

            $penalties = DB::table('penalties')
                ->select('PENALTY_ID', 'PENALTY_REASON')
                ->where('BRANCH_ID', $branch_id)
                ->where('STATUS', $rsActive)
                ->get();

            return response([
                'success' => true,
                'status' => 200,
                'penalties' => $penalties,
            ]);
        } catch (\Throwable | \Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Data For Create for penalties  Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ]);
        }
    }

}
