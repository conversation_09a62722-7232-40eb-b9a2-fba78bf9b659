<?php

namespace App\Http\Controllers\Elastic;

use App\Http\Controllers\Controller;
use App\Services\Elastic\ElasticsearchDriverGpsLoggingService;
use Illuminate\Http\Request;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Response;
use Auth;

class ElasticsearchDriverGpsLoggingController extends Controller
{
    protected ElasticsearchDriverGpsLoggingService $elasticsearchDriverGpsLoggingService;

    public function __construct(ElasticsearchDriverGpsLoggingService $elasticsearchDriverGpsLoggingService)
    {
        $this->elasticsearchDriverGpsLoggingService = $elasticsearchDriverGpsLoggingService;
    }

    public function createIndex(): FoundationApplication|Response|ResponseFactory
    {
        return $this->elasticsearchDriverGpsLoggingService->createIndex();
    }

    public function index(): FoundationApplication|Response|ResponseFactory
    {
            echo "sss ". Auth::user()->BRANCH_ID;
            exit;
        return $this->elasticsearchDriverGpsLoggingService->indexLogs();
    }

    public function show($id): FoundationApplication|Response|ResponseFactory
    {
        return $this->elasticsearchDriverGpsLoggingService->showLog($id);
    }

    public function store(Request $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->elasticsearchDriverGpsLoggingService->storeLog($request);
    }

    public function update(Request $request, $id): FoundationApplication|Response|ResponseFactory
    {
        return $this->elasticsearchDriverGpsLoggingService->updateLog($request, $id);
    }

    public function destroy($id): FoundationApplication|Response|ResponseFactory
    {
        return $this->elasticsearchDriverGpsLoggingService->deleteLog($id);
    }
}
