<?php


namespace App\Http\Requests\Admin;
use Closure;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;

class MaskTransactionRequest extends FormRequest
{
    
    
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {        
        return [
            'triptype_id' => ['required'],
            'selectedDate' => 'required',
            'selectedTime' => 'required',
        ];
    }

    public function messages(): array
    {
        return [
            'triptype_id.required' => 'select trip type',
            'selectedDate.required' => 'Shift Date field is required',
            'selectedTime.required' => 'Shift Date field is required',
        ];
    }
   

    public function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json([
            'success' => false,
            'validation_error' => true,
            'message' => 'Validation errors',
            'data' => $validator->errors()
        ]));
    }
   
}
