<?php

namespace App\Services\SuperAdmin;


use App\Helpers\CommonFunction;
use App\Helpers\MyHelper;
use App\Models\Vendor;
use Carbon\Carbon;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;

class VendorService
{


    protected CommonFunction $commonFunction;


    public function __construct(CommonFunction $commonFunction)
    {
        $this->commonFunction = $commonFunction;
    }


    public function indexVendor(): FoundationApplication|Response|ResponseFactory
    {
        $authUser = Auth::user();
        try {

            $vendors = Vendor::select(
                'vendors.VENDOR_ID',
                'branch.BRANCH_NAME',
                'vendors.NAME',
                'vendors.ACTIVE'
            )
                ->where('vendors.ACTIVE', MyHelper::$RS_ACTIVE)
                ->join("branch", "branch.BRANCH_ID", "=", "vendors.BRANCH_ID")
                ->get();

            return response([
                'success' => true,
                'status' => 200,
                'vendors' => $vendors,
            ]);
        } catch (\Throwable | \Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Vendor Index Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ]);
        }
    }


    public function storeVendor($request): FoundationApplication|Response|ResponseFactory
    {
        try {

            DB::beginTransaction();
            $auth_user = Auth::user();
            $vendorData = $this->prepareVendorData($request, $auth_user, MyHelper::$RS_ACTIVE);
            $vendorResult = Vendor::create($vendorData);
            return response([
                'success' => true,
                'status' => 200,
                'message' => 'Vendor Created Successfully',
            ]);
        } catch (\Throwable | \Exception $e) {
            DB::rollBack();
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'validation_controller' => true,
                'status' => 4,
                'message' => 'Vendor Created UnSuccessfully',
                'errorM' => $e->getMessage(),
            ]);
        } finally {
            DB::commit();
        }
    }


    private function prepareVendorData($request, $auth_user, $active): array
    {
        $date = Carbon::now();
        return [
            "BRANCH_ID" => $request->branch_name,
            "NAME" => $request->name,
            "ACTIVE" => $active,
            "CREATED_BY" => $auth_user->id,
            "created_at" => $date->format("Y-m-d H:i:s"),

        ];
    }


    public function editVendor($vendorAutoIdCrypt): FoundationApplication|Response|ResponseFactory
    {
        $authUser = Auth::user();

        try {
            $vendorAutoId = Crypt::decryptString($vendorAutoIdCrypt);
            $vendor = Vendor::where('vendor.VENDOR_ID', $vendorAutoId)
                ->where('vendors.ACTIVE', MyHelper::$RS_ACTIVE)
                ->select(
                    'vendors.VENDOR_ID',
                    'vendors.BRANCH_ID',
                    'vendors.NAME',

                )->firstOrFail();

            return response([
                'success' => true,
                'status' => 200,
                'vendor' => $vendor,
            ]);
        } catch (\Throwable | \Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Vendor Edit Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ]);
        }
    }



    public function updateVendor($request, $vendorAutoIdCrypt): FoundationApplication|Response|ResponseFactory
    {

        try {

            $authUser = Auth::user();

            DB::beginTransaction();


            $vendorAutoId = Crypt::decryptString($vendorAutoIdCrypt);
            $vendor = Vendor::where('vendors.VENDOR_ID', $vendorAutoId)
                ->where('vendors.ACTIVE', MyHelper::$RS_ACTIVE)
                ->firstOrFail();

            $vendorData = $this->preparVendorDataForUpdate($request, $authUser);
            $vendor->update($vendorData);

            DB::commit();

            return response([
                'success' => true,
                'status' => 200,
                'message' => 'Vendor Updated Successfully',
            ]);
        } catch (\Throwable | \Exception $e) {
            DB::rollBack();
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'message' => 'Vendor Update Unsuccessful',
                'status' => 4,
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ]);
        }
    }


    private function preparVendorDataForUpdate($request, $auth_user): array
    {
        $date = Carbon::now();
        return [
            "BRANCH_ID" => $request->branch_name,
            "NAME" => $request->name,
            "UPDATED_BY" => $auth_user->id,
            "updated_at" => $date->format("Y-m-d H:i:s"),
        ];
    }


    public function deleteVendor($request, $vendorAutoIdCrypt): FoundationApplication|Response|ResponseFactory
    {
        $authUser = Auth::user();
        $date = Carbon::now();
        try {
            DB::beginTransaction();

            $vendorAutoId = Crypt::decryptString($vendorAutoIdCrypt);
            $vendor = Vendor::where('vendors.VENDOR_ID', $vendorAutoId)
                ->where('vendors.ACTIVE', MyHelper::$RS_ACTIVE)
                ->firstOrFail();

            $vendor->update([
                'REMARKS' => $request->input('remark'),
                'ACTIVE' => MyHelper::$RS_INACTIVE,
                'UPDATED_BY' => Auth::user()->id,
                'updated_at' => $date->format("Y-m-d H:i:s"),
            ]);

            DB::commit();

            return response([
                'success' => true,
                'status' => 200,
                'message' => 'Vendor Deleted Successfully',
            ]);
        } catch (\Throwable | \Exception $e) {
            DB::rollBack();
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Vendor Delete Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ]);
        }
    }




    public function paginationVendor($request, bool $active = true): FoundationApplication|Response|ResponseFactory
    {
        try {
            $perPage = $request->input('per_page', MyHelper::$PAGINATION_PER_PAGE);
            $vendors = Vendor::query()
                ->select(
                    'vendors.VENDOR_ID',
                    'vendors.NAME',
                    'branch.BRANCH_ID',
                    'branch.BRANCH_NAME',                   
                )
                ->where('vendors.ACTIVE', MyHelper::$RS_ACTIVE)               
                ->join("branch", "branch.BRANCH_ID", "=", "vendors.BRANCH_ID");


            $filterModel = $request->input('filterModel');
            if ($filterModel) {
                foreach ($filterModel as $field => $filter) {
                    if (isset($filter['filter']) && $filter['filter'] !== '') {
                        $value = $filter['filter'];
                        $type = $filter['type'];

                        switch ($field) {
                            case 'NAME':
                                $vendors->where('vendors.NAME', 'like', "%{$value}%");
                                break;
                           
                            case 'BRANCH_NAME':
                                $vendors->where('branch.BRANCH_NAME', 'like', "%{$value}%");
                                break;
                          
                        }
                    }
                }
            }


            if ($request->has('orderBy') && !empty($request->input('orderBy'))) {
                $orderBy = $request->input('orderBy');
                $order = $request->input('order', 'asc');
                $vendors->orderBy($orderBy, $order);
            } else {
                $vendors->orderBy('vendors.created_at', 'desc');
            }


            if ($perPage === MyHelper::$PAGINATION_PER_PAGE_ALL) {
                $paginatedVendors = $vendors->paginate($vendors->count());
            } else {
                $perPage = is_numeric($perPage) ? (int)$perPage : MyHelper::$PAGINATION_PER_PAGE;
                $paginatedVendors = $vendors->paginate($perPage);
            }

            return response([
                'success' => true,
                'status' => 200,
                'vendors' => $paginatedVendors,
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'Vendors Pagination Unsuccessful' : 'Deactivate Vendors Pagination Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ]);
        }
    }



    public function dataForCreateVendor(): FoundationApplication|Response|ResponseFactory
    {

        try {

            $rsActive = MyHelper::$RS_ACTIVE;
            $branch_List = DB::table('branch')
                ->select('BRANCH_ID', 'BRANCH_NAME')
                ->where('ACTIVE', $rsActive)
                ->get();

            return response([
                'success' => true,
                'status' => 200,
                'branch_lists' => $branch_List,

            ]);
        } catch (\Throwable | \Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Data For Create for Vendor  Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ]);
        }
    }
}
