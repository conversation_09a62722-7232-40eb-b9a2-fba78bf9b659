<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ApproveDistance extends Model
{
    use HasFactory;

    protected $table = 'approve_distances';
    protected $primaryKey = 'APPROVED_DISTANCE_ID';

    public $timestamps = false;

    protected $fillable = [
        'BRANCH_ID',
        'LOCATION_ID',
        'APPROVED_DISTANCE',
        'DURATION',
        'CREATE_BY',
        'CREATED_DATE',
        'UPDATED_BY',
        'UPDATED_DATE',
    ];

}
