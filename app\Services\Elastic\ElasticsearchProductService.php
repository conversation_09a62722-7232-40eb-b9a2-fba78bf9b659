<?php

namespace App\Services\Elastic;

use App\Facades\Elasticsearch;
use App\Helpers\CommonFunction;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Response;

class ElasticsearchProductService
{
    private $index = 'products';
    private $type = 'product';
    protected CommonFunction $commonFunction;

    public function __construct(CommonFunction $commonFunction)
    {
        $this->commonFunction = $commonFunction;
    }

    public function createIndex(): FoundationApplication|Response|ResponseFactory
    {
        try {
            $params = [
                'index' => $this->index,
                'body' => [
                    'mappings' => [
                        $this->type => [ 
                            'properties' => [
                                'name' => ['type' => 'text'],
                                'description' => ['type' => 'text'],
                                'price' => ['type' => 'float'],
                                'created_at' => ['type' => 'date']
                            ]
                        ]
                    ]
                ]
            ];

            $response = Elasticsearch::indices()->create($params);
            return response([
                'success' => true,
                'message' => 'Index created successfully',
                'data' => $response
            ]);
        } catch (\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'message' => 'Failed to create index',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function indexProducts(): FoundationApplication|Response|ResponseFactory
    {
        try {
            $params = [
                'index' => $this->index,
                'type' => $this->type,
                'body' => [
                    'query' => [
                        'match_all' => new \stdClass()
                    ]
                ]
            ];

            $response = Elasticsearch::search($params);
            return response([
                'success' => true,
                'products' => $response['hits']['hits']
            ]);
        } catch (\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'message' => 'Failed to retrieve products',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function showProduct($id): FoundationApplication|Response|ResponseFactory
    {
        try {
            $params = [
                'index' => $this->index,
                'type' => $this->type,
                'id' => $id
            ];

            $response = Elasticsearch::get($params);
            return response([
                'success' => true,
                'product' => $response['_source']
            ]);
        } catch (\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'message' => 'Product not found',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function storeProduct($request): FoundationApplication|Response|ResponseFactory
    {
        try {
            $params = [
                'index' => $this->index,
                'type' => $this->type,
                'body' => $request->all()
            ];

            $response = Elasticsearch::index($params);
            return response([
                'success' => true,
                'message' => 'Product created successfully',
                'data' => $response
            ]);
        } catch (\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'message' => 'Failed to create product',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function updateProduct($request, $id): FoundationApplication|Response|ResponseFactory
    {
        try {
            $params = [
                'index' => $this->index,
                'type' => $this->type,
                'id' => $id,
                'body' => [
                    'doc' => $request->all()
                ]
            ];

            $response = Elasticsearch::update($params);
            return response([
                'success' => true,
                'message' => 'Product updated successfully',
                'data' => $response
            ]);
        } catch (\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'message' => 'Failed to update product',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function deleteProduct($id): FoundationApplication|Response|ResponseFactory
    {
        try {
            $params = [
                'index' => $this->index,
                'type' => $this->type,
                'id' => $id
            ];

            $response = Elasticsearch::delete($params);
            return response([
                'success' => true,
                'message' => 'Product deleted successfully',
                'data' => $response
            ]);
        } catch (\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'message' => 'Failed to delete product',
                'error' => $e->getMessage()
            ], 500);
        }
    }


}