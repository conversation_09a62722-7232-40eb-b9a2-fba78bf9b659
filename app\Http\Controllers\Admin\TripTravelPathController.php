<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;

use App\Services\Admin\TripTravelPathService;

use App\Http\Requests\Admin\VendorFindVehicleRequest;
use App\Http\Requests\Admin\SelectedCabRouteRequest;
use App\Http\Requests\Admin\SelectedCabRosterRequest;
use App\Http\Requests\Admin\TripTravelPathRequest;
use App\Http\Requests\Admin\GpsActionListRequest;

use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class TripTravelPathController extends Controller
{
    protected TripTravelPathService $triptravelpathService;

    public function __construct(TripTravelPathService $triptravelpathService)
    {
        $this->triptravelpathService = $triptravelpathService;
    }
    
    public function dataForCreate(): FoundationApplication|Response|ResponseFactory
    {
        return $this->triptravelpathService->dataForCreate();
    }
   
    public function vendorwise_vehicle_find(VendorFindVehicleRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->triptravelpathService->vendorwise_vehicle_find($request);
        
    }
   
    public function select_cab_wise_route(SelectedCabRouteRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->triptravelpathService->select_cab_wise_route($request);
        
    }
    public function selected_route_details(SelectedCabRosterRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->triptravelpathService->selected_route_details($request);
        
    }

    public function trip_travel_path(TripTravelPathRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->triptravelpathService->trip_travel_path($request);
        
    }
   
    public function vehicle_travel_path_excel(VehicleTravelPathExcelRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->vehicletravelpathService->vehicle_travel_path_excel($request);
        
    }
   
    public function gpsactionlist(GpsActionListRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->triptravelpathService->gpsactionlist($request);   
    }
 
}
