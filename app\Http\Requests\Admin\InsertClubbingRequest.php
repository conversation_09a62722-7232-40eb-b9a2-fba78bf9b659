<?php

namespace App\Http\Requests\Admin;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;

class InsertClubbingRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        
        return [
            
            'empid' => 'required | numeric',
            'roster_id' => 'required | numeric',
            'mobile' => 'required | numeric',
            'logindatetime' => 'required | date_format:Y-m-d H:i:s',
            'pickdroptime' => 'required | date_format:Y-m-d H:i:s',
            'asset_movement' => 'required | string',
        ];
    }

    public function messages()
    {
        return [
            'empid.required' => 'EmployeeId is required',
            'roster_id.required' => 'roster_id is required',
            'mobile.required' => 'mobile is required',
            'logindatetime.required' => 'logindatetime is required',
            'pickdroptime.required' => 'pickdroptime is required',
            'asset_movement.required' => 'asset_movement is required',
        ];
    }

    public function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json([
            'success' => false,
            'validation_error' => true,
            'message' => 'Validation errors',
            'data' => $validator->errors()
        ]));
    }
}
