<?php


namespace App\Http\Requests\Admin;
use Closure;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;

class MisReportRequest extends FormRequest
{
    
    
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {        
        return [
            'vendor_id' => ['required'],
            'triptype_id' => ['required'],
            'from_date' => 'required',
            'to_date' => 'required',
        ];
    }

    public function messages(): array
    {
        return [
            'triptype_id.required' => 'select trip type',
            'vendor_id.required' => 'select Vendor Name',
            'from_date.required' => 'from date field is required',
            'to_date.required' => 'to date field is required',
        ];
    }
   

    public function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json([
            'success' => false,
            'validation_error' => true,
            'message' => 'Validation errors',
            'data' => $validator->errors()
        ]));
    }
   
}
