<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;

use App\Services\Admin\VehicleTravelPathService;

use App\Http\Requests\Admin\VendorFindVehicleRequest;
use App\Http\Requests\Admin\VehicleTravelPathRequest;
use App\Http\Requests\Admin\VehicleTravelPathExcelRequest;

use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class VehicleTravelPathController extends Controller
{
    protected VehicleTravelPathService $vehicletravelpathService;

    public function __construct(VehicleTravelPathService $vehicletravelpathService)
    {
        $this->vehicletravelpathService = $vehicletravelpathService;
    }
    
    public function dataForCreate(): FoundationApplication|Response|ResponseFactory
    {
        return $this->vehicletravelpathService->dataForCreate();
    }
   
    public function vendorwise_vehicle_find(VendorFindVehicleRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->vehicletravelpathService->vendorwise_vehicle_find($request);
        
    }
   
    public function vehicle_travel_path(vehicleTravelPathRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->vehicletravelpathService->vehicle_travel_path($request);
        
    }
   
    public function vehicle_travel_path_excel(VehicleTravelPathExcelRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->vehicletravelpathService->vehicle_travel_path_excel($request);
        
    }
   
   
 
}
