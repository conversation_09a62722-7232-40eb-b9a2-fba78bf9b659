<?php

//namespace App\Http\Requests\Admin;
namespace App\Http\Requests\Admin;

use App\Helpers\CommonFunction;
use Closure;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;

class PostRosterRequest extends FormRequest
{
    protected CommonFunction $commonFunction;

    public function __construct(CommonFunction $commonFunction)
    {
        
    }

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {   
        return [
            'selected_emp_id' => ['required','array'],
            'selected_vendor_id' => ['required','numeric'],
            'trip_type' => ['required','string'],
            'selected_shift_time' => ['required','string'],
            ];
    }

    public function messages(): array
    {
        return [
            'selected_emp_id.required' => 'selected_emp_id field is required',   
            'selected_vendor_id.required' => 'selected_vendor_id field is required',   
            'trip_type.required' => 'trip_type field is required',   
            'selected_shift_time.required' => 'selected_shift_time field is required',   
                  
        ];
    }
   

    public function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json([
            'success' => false,
            'validation_error' => true,
            'message' => 'Validation errors',
            'data' => $validator->errors()
        ]));
    }
   
}
