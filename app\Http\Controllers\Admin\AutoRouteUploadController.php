<?php
namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\AutorouteUploadRequest;
use App\Services\Admin\AutorouteUploadService;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application;
use Illuminate\Http\Response;
use Illuminate\Http\Request;

class AutoRouteUploadController extends Controller
{
    protected AutorouteUploadService $autorouteuploadService;

    public function __construct(AutorouteUploadService $autorouteuploadService)
    {
        $this->autorouteuploadService = $autorouteuploadService;
    }


    public function auto_route_upload(AutorouteUploadRequest $request): Application|Response|ResponseFactory
    {
        
        return $this->autorouteuploadService->auto_route_upload($request);
    }
	
}
