<?php

namespace App\Traits;

use App\Models\SmsTemplate;
use App\Models\SmsTemplateRules;
use App\Models\User;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

trait SmsTrait
{
    public function smscreation($request){
        try {
            $decode_request = json_decode($request);

            $sms_template = SmsTemplate::where('branch_id', $decode_request->branch_id)
                                ->where('sms_name', $decode_request->sms_name)
                                ->first();
            
            $sms_rules = SmsTemplateRules::where('sms_template_id', $sms_template->id)->first();

            $sms_rules_content = $sms_rules->content;

            $makejsonrule = json_decode($sms_rules_content,true);

            // Check for errors 
            if ($makejsonrule === null) { 
                echo "Error decoding JSON: ". json_last_error_msg(); 
            } else { 
                // Successfully decoded 
                $content_type = $decode_request->content_type;
                
                if($content_type == "1"){
                    $variable_values = [];
                    $final_message_v1 = $sms_template->message;

                    $otp = substr(number_format(time() * rand(), 0, '', ''), 0, 4);

                    $variable_values['otp'] = $otp;
                    $final_message_v1 = str_replace("@otp@",$otp,$final_message_v1);

                    foreach($makejsonrule['database'] as $rule){
                        $login_person_data = User::where('id', $decode_request->user_id)->first();
                        
                        $var_condition_value = $login_person_data->{$rule['condition']};
                        
                        foreach($rule['fields'] as $fields_data){
                            $query_data = DB::table($rule['database_name'])->select($fields_data)
                                                ->where($rule['condition'], $var_condition_value)
                                                ->first();

                            //Value stored in array but not use
                            // $variable_values[$fields_data] = $query_data->$fields_data;

                            $present_name = "@".$fields_data."@";

                            $final_message_v1 = str_replace($present_name,$query_data->$fields_data,$final_message_v1);
                        }
                    }
                    //Final Message Response
                    // print_r($final_message_v1);
                    $otpcheck = strpos($sms_template->message , '@otp@'); // $sms_template->message
                    if($otpcheck != NULL){
                        $ENCRYPT_OTP =  bcrypt($otp);
                        $inser_otp = "UPDATE `users` SET `password`='$ENCRYPT_OTP',`created_password`='$otp',`employee_set_password`='0'  WHERE id='$decode_request->user_id' and active_status=1";
                        DB::update($inser_otp);
                    }
                    
                    return response([
                        'success' => true,
                        'message' => $final_message_v1,
                    ], 200);
                }else{
                    //Statis Content SMS Creation
                    $final_message = $sms_template->message;

                    foreach($makejsonrule['database'] as $rule){
                        foreach($rule['fields'] as $fields_data){
                            $present_name = "@".$fields_data."@";
                            // dd($decode_request->values->$fields_data);
                            $final_message = str_replace($present_name,$decode_request->values->$fields_data,$final_message);
                            // $fields_data = $request;
                        }
                    }

                    return response([
                        'success' => true,
                        'message' => $final_message,
                    ], 200);
                }
            }
        } catch (Exception $exception) {
            return response([
                'success' => false,
                'message' => $exception->getMessage()
            ], 500);
        }
    }
}
