<?php

namespace App\Services\Admin;

use App\Helpers\CommonFunction;
use App\Helpers\MyHelper;
use App\Http\Controllers\ElasticController;
use App\Models\Branch;
use App\Models\Cab_allocation;
use App\Models\Roster;
use App\Models\User;
use App\Models\Vendor;
use Carbon\Carbon;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use DateTime;

class EmployeeRequestService
{
    protected CommonFunction $commonFunction;
    protected ?Branch $currentBranch = null;

    public function __construct(CommonFunction $commonFunction)
    {
        $this->commonFunction = $commonFunction;
    }

    public function getdate_webemprequest($request): FoundationApplication|Response|ResponseFactory
    {
        try {
            $branch_id = Auth::user()->BRANCH_ID;
            $userid = Auth::user()->id;
            $db_name = Auth::user()->dbname;
            $RS_ACTIVE = MyHelper::$RS_ACTIVE;
            $active = true;
            $authUser = Auth::user();
            $selected_date = $request->selected_date;
            $selected_employee = $request->selected_employee;

            $datas = "SELECT ER.ROSTER_REQ_ID,ER.EMPLOYEE_ID,ER.TRIP_TYPE,ER.START_LOCATION,ER.END_LOCATION,ER.ESTIMATE_END_TIME,
            ER.ESTIMATE_START_TIME,ER.`STATUS` as empstats,ER.created_at,E.`NAME` as  empname,E.MOBILE
            FROM employees_roster_request ER 
            INNER JOIN employees E ON E.EMPLOYEES_ID = ER.EMPLOYEE_ID AND E.BRANCH_ID = ER.BRANCH_ID
            WHERE ER.BRANCH_ID= $branch_id AND DATE(ER.ESTIMATE_START_TIME) = '$selected_date' AND ER.EMPLOYEE_ID = '$selected_employee'";
            $data=  DB::connection("$db_name")->select($datas);
            
            return response([
                'success' => true,
                'status' => 3,
                'request_data' => $data,
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'Employee requested history status Unsuccessful' : 'Employee requested history status Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ], 500);
        }
    }
    
    public function dataForCreate(): FoundationApplication|Response|ResponseFactory
    {
        try {
            $user = Auth::user();
            $RS_ACTIVE = MyHelper::$RS_ACTIVE;
            $ADMIN = MyHelper::$ADMIN;
            $branch_id = Auth::user()->BRANCH_ID;
            $vendor_id = Auth::user()->vendor_id;
            $dbname = Auth::user()->dbname;
            $date = carbon::now();
            $from_date = date('Y-m-d');
            $user_type = Auth::user()->user_type;
           
            $in_time = DB::table('shift_time')
            ->select('SHIFT_ID', 'IN_TIME')
            ->where('BRANCH_ID', $branch_id)
            ->where('ACTIVE', $RS_ACTIVE)
            ->where('CATEGORY', 'ADHOC')
            ->groupBy('IN_TIME')
            ->get();

            
            $out_time = DB::table('shift_time')
            ->select('SHIFT_ID', 'OUT_TIME')
            ->where('BRANCH_ID', $branch_id)
            ->where('ACTIVE', $RS_ACTIVE)
            ->where('CATEGORY', 'ADHOC')
            ->whereNotNull('OUT_TIME')
            ->groupBy('OUT_TIME')
            ->get();

            return response([
                'success' => true,
                'status' => 3,
                'InTime' => $in_time,
                'OutTime' => $out_time,
               
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Data For Create for Employee request history Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function weekRoster($request): FoundationApplication|Response|ResponseFactory
    {
        try {
            $branch_id = Auth::user()->BRANCH_ID;
            $emp_id=$request->emp_id;
            $empdetails = "SELECT E.EMPLOYEES_ID,E.`NAME`,E.MOBILE,EDM.MAP_EMPID,E.GENDER,E.PROJECT_NAME,E.ADDRESS,EDM.CREATED_DATE FROM employees E
            LEFT JOIN employee_designation_map EDM ON EDM.BRANCH_ID = E.BRANCH_ID AND EDM.ACTIVE = 1  AND EDM.MAP_EMPID = E.EMPLOYEES_ID
            WHERE E.BRANCH_ID = $branch_id and E.ACTIVE=1 and EDM.ACTIVE=1 and EDM.TL_EMPID='".$emp_id."'";
            $emp_details = DB::select($empdetails);
            $inout_time = "SELECT IN_TIME,OUT_TIME FROM shift_time WHERE BRANCH_ID='$branch_id' AND CATEGORY='SHIFTTIME' AND ACTIVE=1";
            $inouttime = DB::select($inout_time);

            $roster_enable_sql = "SELECT status from roster_enble WHERE branch_id = '$branch_id'";
            $roster_enable_res = DB::select($roster_enable_sql);
            $roster_enable_status = false;
            if(count($roster_enable_res) > 0)
            {
                $roster_enable_status = isset($roster_enable_res[0]) && $roster_enable_res[0]->status == 1 ? true : false;
            }

            //return view('WeekRoster.WeekRoster',compact('inouttime','emp_details','roster_enable_status'));

            $PR_WEEKROSTERENABLE = MyHelper::$PR_WEEKROSTERENABLE;
            $WEEKROSTER_ENABLE = $this->commonFunction->GetPropertyValue($PR_WEEKROSTERENABLE,$branch_id);

            $PR_WEEKROSTERENABLEOPTION = MyHelper::$PR_WEEKROSTERENABLEOPTION;
            $WEEKROSTER_ENABLEOPTION = $this->commonFunction->GetPropertyValue($PR_WEEKROSTERENABLEOPTION,$branch_id);

            return response([
                'success' => true,
                'status' => 3,
                'datetimecondition' => [
                    'weekrosterenable' => json_decode($WEEKROSTER_ENABLE,TRUE),
                    'weekrosterenableoption' => json_decode($WEEKROSTER_ENABLEOPTION,TRUE),
                ],
                'inouttime' => $inouttime,
                'emp_details' => $emp_details,
                'roster_enable_status' => $roster_enable_status,
            ]);
            
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Employee Index Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ]);
        }
    }

    public function RosterEmpWeekReq($emp_id,$pickdata, $dropdata): FoundationApplication|Response|ResponseFactory
    {
        try {
            $branchid = Auth::user()->BRANCH_ID;
            $branch_details = Branch::where('BRANCH_ID',$branchid)
                                ->where('ACTIVE', 1)
                                ->first();
            
            $div_id = $branch_details->DIVISION_ID;
            $branch_name = $branch_details-> BRANCH_NAME;
            //$emp_name = Session::get('emp_name');
            $emp_id = $emp_id;
            $dbname = Auth::user()->dbname;
            /*echo "<pre>";
            print_r($pickdata);
            print_r($dropdata);
            exit ;*/

            //$commoncontrol = new CommonController();
            //$PR_WEEKROSTERENABLEOPTION = env('PR_WEEKROSTERENABLEOPTION');
            $WEEKROSTER_ENABLEOPTION = $this->commonFunction->getPropertyValue('WEEK_ROSTER_ENABLE_OPTION', $branchid);
            $WEEKROSTERENABLEOPTION = json_decode($WEEKROSTER_ENABLEOPTION, TRUE);
            $day = $WEEKROSTERENABLEOPTION[0]['dayname'];

            $values = array();
            $curdate = date("Y-m-d");

            $sunday = date('Y-m-d', strtotime("next $day", strtotime($curdate)));

            $days = array();
            
            $values = array();

            $empdata = "SELECT L.LOCATION_NAME FROM employees E 
                        INNER JOIN locations L ON L.DIVISION_ID = '$div_id' AND L.LOCATION_ID = E.LOCATION_ID
                        WHERE BRANCH_ID = $branchid AND EMPLOYEES_ID = '$emp_id'";
            $dataemp = DB::select($empdata);

            $curdate = date('Y-m-d H:i:s');

            if (count($dataemp) > 0) {
                $locationname = $dataemp[0]->LOCATION_NAME;
            } else {
                $locationname = '';
            }

            if ($branchid == '18' || $branchid == '62' || $branchid == '63' || $branchid == '64') {
                $workingDays = [];
                $today = date('D');
                foreach ($WEEKROSTERENABLEOPTION as $workingDay) {
                    $workingDays[] = $workingDay['dayname'];
                }
                if (in_array($today, $workingDays)) {
                    $sunday = date('Y-m-d', strtotime("$today", strtotime($curdate)));
                }
            }

            for ($j = 0; $j < count($pickdata); $j++) {
                if ($pickdata[$j] != 'N/A') {
                    $pickdate_week = date("Y-m-d", strtotime("+$j day", strtotime($sunday)));
                    $pickdate = $pickdate_week . ' ' . $pickdata[$j];
                    $checkcnt = DB::connection("$dbname")->select("SELECT count(ROSTER_REQ_ID) as cnt FROM employees_roster_request WHERE EMPLOYEE_ID='$emp_id' AND date(ESTIMATE_START_TIME) = '" . $pickdate_week . "' AND TRIP_TYPE = 'P' AND `STATUS` != 6 ");

                    $count = $checkcnt[0]->cnt;
                    if ($count == 0) {
                        $values[] = "('$branchid', '$emp_id', 'P', '$locationname', '$branch_name', '$pickdate', '1', '$emp_id', '$curdate', 'roster')";
                    } else {
                        // echo  $pickdate . " login already requested";
                        // exit;
                        return response([
                            'success' => true,
                            'status' => 0,
                            'message' => "login already requested",
                        ]);
                    }
                }
            } 

            for ($k = 0; $k < count($dropdata); $k++) {
                if ($dropdata[$k] != 'N/A') {
                    $dropdate_week = date("Y-m-d", strtotime("+$k day", strtotime($sunday)));
                    $dropdate = $dropdate_week . ' ' . $dropdata[$k];

                    if ($branchid == 48 || $branchid == 18) {
                        if ($dropdata[$k] != 'N/A') {
                            if ($pickdata[$k] != 'N/A') {
                                $req_in_time = $dropdate_week . ' ' . $pickdata[$k];
                                $req_out_time = $dropdate_week . ' ' . $dropdata[$k];

                                $time1 = $pickdata[$k];
                                $time2 = $dropdata[$k];

                                $time1_parts = explode(':', $time1);
                                $time1_minutes = intval($time1_parts[0]) * 60 + intval($time1_parts[1]);

                                $time2_parts = explode(':', $time2);
                                $time2_minutes = intval($time2_parts[0]) * 60 + intval($time2_parts[1]);

                                $datetime1 = Carbon::parse($req_in_time);
                                $datetime2 = Carbon::parse($req_out_time);

                                if ($time2_minutes <= $time1_minutes) {
                                    $datetime2->modify('+1 day');
                                }

                                $interval = $datetime1->diff($datetime2);

                                $totalSeconds = $interval->s + $interval->i * 60 + $interval->h * 3600 + $interval->days * 86400;

                                $minAllowedSeconds = 8 * 3600 + 30 * 60;
                                $maxAllowedSeconds = 12 * 3600 + 30 * 60;


                                if ($totalSeconds >= $minAllowedSeconds && $totalSeconds <= $maxAllowedSeconds) {
                                } else {

                                    $datetime1->addHours(8)->addMinutes(30);
                                    $time_from = $datetime1->format("Y-m-d H:i:s");

                                    $datetime1->addHours(4);
                                    $time_to = $datetime1->format("Y-m-d H:i:s");

                                    // echo "Weekly Roster logout request must be raised b/w $time_from  to  $time_to hrs";
                                    // exit;
                                    return response([
                                        'success' => true,
                                        'status' => 0,
                                        'message' => "Weekly Roster logout request must be raised b/w $time_from  to  $time_to hrs",
                                    ]);
                                }
                            }
                        }
                    }

                    $checkcnt = DB::connection("$dbname")->select("SELECT count(ROSTER_REQ_ID) as cnt FROM employees_roster_request WHERE EMPLOYEE_ID='$emp_id' AND date(ESTIMATE_START_TIME) = '" . $dropdate_week . "' AND TRIP_TYPE = 'D' AND `STATUS` != 6 ");
                    $count = $checkcnt[0]->cnt;
                    if ($count == 0) {
                        $values[] = "('$branchid', '$emp_id', 'D', '$branch_name', '$locationname', '$dropdate', '1', '$emp_id', '$curdate', 'roster')";
                    } else {
                        // echo  $dropdate . " logout time already requested";
                        // exit;
                        return response([
                            'success' => true,
                            'status' => 0,
                            'message' => "$dropdate logout time already requested",
                        ]);
                    }
                }
            }
            
            if (count($values) > 0) {
                $datas = "INSERT INTO `employees_roster_request` (`BRANCH_ID`, `EMPLOYEE_ID`, `TRIP_TYPE`, `START_LOCATION`, `END_LOCATION`, `ESTIMATE_START_TIME`, `STATUS`, `CREATED_BY`, `created_at`, `CATEGORY`) VALUES " . implode(',', $values);
                $data = DB::insert($datas);
                if ($data >= 1) {
                    // echo 1;
                    // exit;
                    return response([
                        'success' => true,
                        'status' => 1,
                        'message' => 'Employee Roster Request Successful',
                    ]);
                } else {
                    // echo  " Failed";
                    // exit;
                    return response([
                        'success' => true,
                        'status' => 0,
                        'message' => 'Employee Roster Request Failed',
                    ]);
                }
            } else {
                // echo  " Select login/logout time";
                // exit;
                return response([
                    'success' => true,
                    'status' => 0,
                    'message' => 'Select login/logout time',
                ]);
            }
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Employee Index Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ]);
        }
    }

    public function PostUpdatePassword($request): FoundationApplication|Response|ResponseFactory
    {
        try {
            $mobileno = $request->mobileno;
            $curpass = $request->curpass;
            $newpass = $request->newpass;
            $confirmpass = $request->confirmpass;
            // $obj = new EmpRequestReportBusiness();
            // $checkpass = $obj->CheckEmpPass($mobileno, $curpass);
            $employee = User::where('users.name', $request->mobileno)
                                ->where('users.active_status', 1)
                                ->first();
            $checkpass = Hash::check($curpass, $employee->password);
            if ($checkpass == false) {
                return response([
                    'success' => true,
                    'status' => 3,
                    'message' => 'Current password is incorrect',
                ]);
            } else {
                if ($newpass == $confirmpass) {
                    //$obj->updatepassemp($mobileno, $newpass);
                    $encrypt_password = Hash::make($request->newpass);

                    $update_otp = "UPDATE `users` SET `password`='$encrypt_password',`created_password`='$request->newpass',`employee_set_password`='1' WHERE id='$employee->id' and active_status=1";
                    DB::update($update_otp);
                    return response([
                        'success' => true,
                        'status' => 1,
                        'message' => 'New password has been set sucessfully!!!',
                    ]);
                } else {
                    return response([
                        'success' => true,
                        'status' => 2,
                        'message' => 'Confirm password does not match with New password!!!',
                    ]);
                }
            }
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Employee Index Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ]);
        }
    }

    public function RemoveWebEmpRequest($request): FoundationApplication|Response|ResponseFactory
    {
        try {
            $curdtime = date('Y-m-d H:i:s');
            $dbname = Auth::user()->dbname;
            $id = Auth::user()->id;
            $remarks = stripslashes(trim($request->remarks));
            $remove_id = stripslashes(trim($request->remove_id));

            // $sql = "UPDATE employees_roster_request SET updated_at='$curdtime',UPDATED_BY='$id',`STATUS`=6,CANCEL_REMARKS='$remarks' WHERE ROSTER_REQ_ID = '$remove_id'";
            // $result = DB::connection("$dbname")->update($sql);
            DB::table('employees_roster_request')->where('ROSTER_REQ_ID', $remove_id)->update(['updated_at' => $curdtime, 'UPDATED_BY' => $id, 'STATUS' => 6, 'CANCEL_REMARKS' => $remarks]);

            return response([
                'success' => true,
                'status' => 1,
                'message' => 'Employee Web Request Removed sucessfully!!!',
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Employee Web Request Removed Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ]);
        }
    }

    public function WeekrosterAdmin (): FoundationApplication|Response|ResponseFactory
    {
        try {
            $branch_id = Auth::user()->BRANCH_ID;
            $empdetails = "SELECT E.EMPLOYEES_ID,E.`NAME`,E.MOBILE,E.GENDER,E.PROJECT_NAME,E.ADDRESS FROM employees E							
            WHERE E.BRANCH_ID =".$branch_id." and E.ACTIVE = 1 order by E.`NAME`";				
            $emp_details = DB::select($empdetails);	


            $inout_time = "SELECT IN_TIME,OUT_TIME FROM shift_time WHERE BRANCH_ID='$branch_id' AND CATEGORY='SHIFTTIME' AND ACTIVE=1";
            $inouttime = DB::select($inout_time);
            return response([
                'success' => true,
                'status' => 1,
                'emp_details' => $emp_details,
                'inouttime' => $inouttime,
                'message' => 'Week Roster Admin sucessfully!!!',
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Week Roster Admin Unsuccessful!!!',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ]);
        }
    }

    public function WeekRosterAdminData ($request): FoundationApplication|Response|ResponseFactory
    {
        try {
            $branch_id = Auth::user()->BRANCH_ID;

            $branch_details = Branch::where('BRANCH_ID',$branch_id)
                                ->where('ACTIVE', 1)
                                ->first();

            $branch_name = $branch_details->BRANCH_NAME;
            $emp_id = $request->emp_id;
            $dbname = Auth::user()->dbname;

            $day = "Tue";
            $values = array();
            $status = 'success';
            $message = 'Roster successfully Created!';
            $curdate = date("Y-m-d");
            $curdate_time = date('Y-m-d H:i:s');
            $nextweek = date('Y-m-d', strtotime("next $day", strtotime($curdate)));
            $days = array();
            $selected_emp = "'" . implode("', '", $request->selected_employees) . "'";
            $selected_date = $request->selected_date;
            $in_time = $request->in_time;
            $out_time = $request->out_time;
            $valid_intime_arr  = [];
            $valid_outtime_arr  = [];
                
            $query = "SELECT E.EMPLOYEES_ID,E.LOGIN_TIME,E.LOGOUT_TIME,E.GENDER,L.LOCATION_NAME from employees E                                     
            inner join branch as b on b.BRANCH_ID=E.BRANCH_ID
            INNER JOIN locations L ON L.DIVISION_ID = b.DIVISION_ID AND L.LOCATION_ID = E.LOCATION_ID
            WHERE E.BRANCH_ID = $branch_id AND E.EMPLOYEES_ID in($selected_emp)";

            
            $login_res = DB::select($query);
            for ($i = 0; $i < count($login_res); $i++) {
                $locationname = $login_res[$i]->LOCATION_NAME;
                $emp_id = $login_res[$i]->EMPLOYEES_ID;
                for ($j = 0; $j < count($selected_date); $j++) {
                    if($selected_date[$j] == '' || ($in_time[$j] == '' && $out_time[$j] == ''))
                    {
                        continue;
                    }
                    $values = array(); 
                    $dropdate_week = date("Y-m-d", strtotime($selected_date[$j]));
                    $pickdate_week = date("Y-m-d", strtotime($selected_date[$j]));
                    if ($branch_id == 48 || $branch_id == 18 || $branch_id == 61) {  

                        if ($out_time[$j] != '' || $in_time[$j] != '') { 
                            $totalSeconds = 0;
                            $minAllowedSeconds = 0;
                            $maxAllowedSeconds = 0;
                            $to_check = false;
                            if($out_time[$j] != '' && $in_time[$j] != '')
                            {
                                $to_check = true;
                                $req_in_time = $pickdate_week . ' ' . $in_time[$j];
                                $req_out_time = $dropdate_week . ' ' . $out_time[$j];
                                $time1 = $in_time[$j];
                                $time2 = $out_time[$j];

                                $time1_parts = explode(':', $time1);
                                $time1_minutes = intval($time1_parts[0]) * 60 + intval($time1_parts[1]);

                                $time2_parts = explode(':', $time2);
                                $time2_minutes = intval($time2_parts[0]) * 60 + intval($time2_parts[1]);

                                $datetime1 = Carbon::parse($req_in_time);
                                $datetime2 = Carbon::parse($req_out_time);

                                if ($time2_minutes <= $time1_minutes) {
                                    $datetime2->modify('+1 day');
                                }

                                $interval = $datetime1->diff($datetime2);

                                $totalSeconds = $interval->s + $interval->i * 60 + $interval->h * 3600 + $interval->days * 86400;

                                $minAllowedSeconds = 8 * 3600 + 30 * 60;
                                $maxAllowedSeconds = 12 * 3600 + 30 * 60;
                            }                                


                            if ($totalSeconds >= $minAllowedSeconds && $totalSeconds <= $maxAllowedSeconds || !$to_check) {


                    $checkcnt = DB::connection("$dbname")->select("SELECT count(ROSTER_REQ_ID) as cnt FROM employees_roster_request WHERE EMPLOYEE_ID='$emp_id' AND date(ESTIMATE_START_TIME) = '" . $pickdate_week . "'   AND `STATUS` != 6 ");

                                $count = $checkcnt[0]->cnt;
                                if ($count == 2) {
                                    $status = 'failed';
                                    $message = $pickdate_week . " Roster already requested for EmployeeId - " .$emp_id;
                                    $response = ['status' => $status, 'message' => $message];
                                    return response([
                                        'success' => true,
                                        'status' => $status,
                                        'message' => $message,
                                    ]);
                                } 

                                if ($in_time[$j] != '') {
                                    $pickdate_week = date("Y-m-d", strtotime($selected_date[$j]));
                                    $pickup_date_time = $pickdate_week . ' ' . $in_time[$j];

                                    $checkcnt = DB::connection("$dbname")->select("SELECT count(ROSTER_REQ_ID) as cnt FROM employees_roster_request WHERE EMPLOYEE_ID='$emp_id' AND date(ESTIMATE_START_TIME) = '" . $pickdate_week . "' AND TRIP_TYPE = 'P' AND `STATUS` != 6 ");

                                    $count = $checkcnt[0]->cnt;
                                    if ($count == 0) {
                                        $values[] = "('$branch_id', '$emp_id', 'P', '$locationname', '$branch_name', '$pickup_date_time', '1', '$emp_id', '$curdate_time', 'roster')";
                                    } else {
                                        $status = 'failed';
                                        $message = $pickdate_week . " login already requested";
                                        
                                        return response([
                                            'success' => true,
                                            'status' => $status,
                                            'message' => $message,
                                        ]);
                                    }
                                }
                                if ($out_time[$j] != '') {
                                    $dropdate_week = date("Y-m-d", strtotime($selected_date[$j]));
                                    $drop_date_time = $dropdate_week . ' ' . $out_time[$j];

                                    $checkcnt = DB::connection("$dbname")->select("SELECT count(ROSTER_REQ_ID) as cnt FROM employees_roster_request WHERE EMPLOYEE_ID='$emp_id' AND date(ESTIMATE_START_TIME) = '" . $dropdate_week . "' AND TRIP_TYPE = 'D' AND `STATUS` != 6 ");
                                    $count = $checkcnt[0]->cnt;
                                    if ($count == 0) {
                                        $values[] = "('$branch_id', '$emp_id', 'D', '$branch_name', '$locationname', '$drop_date_time', '1', '$emp_id', '$curdate_time', 'roster')";
                                    } else {
                                        $status = 'failed';
                                        $message = $dropdate_week . " logout time already requested";
                                        
                                        return response([
                                            'success' => true,
                                            'status' => $status,
                                            'message' => $message,
                                        ]);
                                    }
                                }


                                $datas = "INSERT INTO `employees_roster_request` (`BRANCH_ID`, `EMPLOYEE_ID`, `TRIP_TYPE`, `START_LOCATION`, `END_LOCATION`, `ESTIMATE_START_TIME`, `STATUS`, `CREATED_BY`, `created_at`, `CATEGORY`) VALUES " . implode(',', $values);
                                $data = DB::insert($datas);
                            } else {

                                $datetime1->addHours(8)->addMinutes(30);
                                $time_from = $datetime1->format("Y-m-d H:i:s");

                                $datetime1->addHours(4);
                                $time_to = $datetime1->format("Y-m-d H:i:s");
                                $status = 'failed';
                                $message = "Weekly Roster logout request must be raised b/w $time_from  to  $time_to hrs";

                                return response([
                                    'success' => true,
                                    'status' => $status,
                                    'message' => $message,
                                ]);
                            }
                        }
                    } else {
                        if ($in_time[$j] != '') {
                            $pickdate_week = date("Y-m-d", strtotime($selected_date[$j]));
                            $pickup_date_time = $pickdate_week . ' ' . $in_time[$j];

                            $checkcnt = DB::connection("$dbname")->select("SELECT count(ROSTER_REQ_ID) as cnt FROM employees_roster_request WHERE EMPLOYEE_ID='$emp_id' AND date(ESTIMATE_START_TIME) = '" . $pickdate_week . "' AND TRIP_TYPE = 'P' AND `STATUS` != 6 ");

                            $count = $checkcnt[0]->cnt;
                            if ($count == 0) {
                                $values[] = "('$branch_id', '$emp_id', 'P', '$locationname', '$branch_name', '$pickup_date_time', '1', '$emp_id', '$curdate_time', 'roster')";
                            } else {
                                $status = 'failed';
                                $message = $pickdate_week . " login already requested";
                                
                                return response([
                                    'success' => true,
                                    'status' => $status,
                                    'message' => $message,
                                ]);
                            }
                        }
                        if ($out_time[$j] != '') {
                            $dropdate_week = date("Y-m-d", strtotime($selected_date[$j]));
                            $drop_date_time = $dropdate_week . ' ' . $out_time[$j];

                            $checkcnt = DB::connection("$dbname")->select("SELECT count(ROSTER_REQ_ID) as cnt FROM employees_roster_request WHERE EMPLOYEE_ID='$emp_id' AND date(ESTIMATE_START_TIME) = '" . $dropdate_week . "' AND TRIP_TYPE = 'D' AND `STATUS` != 6 ");
                            $count = $checkcnt[0]->cnt;
                            if ($count == 0) {
                                $values[] = "('$branch_id', '$emp_id', 'D', '$branch_name', '$locationname', '$drop_date_time', '1', '$emp_id', '$curdate_time', 'roster')";
                            } else {
                                $status = 'failed';
                                $message = $dropdate_week . " logout time already requested";
                                
                                return response([
                                    'success' => true,
                                    'status' => $status,
                                    'message' => $message,
                                ]);
                            }
                        }

                        $datas = "INSERT INTO `employees_roster_request` (`BRANCH_ID`, `EMPLOYEE_ID`, `TRIP_TYPE`, `START_LOCATION`, `END_LOCATION`, `ESTIMATE_START_TIME`, `STATUS`, `CREATED_BY`, `created_at`, `CATEGORY`) VALUES " . implode(',', $values);
                        $data = DB::insert($datas);
                    }
                }
            }
            return response([
                'success' => true,
                'status' => $status,
                'message' => $message,
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Week Roster Admin Data Unsuccessful!!!',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ]);
        }
    }
}
