<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;

use App\Services\Admin\EmpLocationUpdateService;
use App\Http\Requests\Admin\EmpLocationUpdateRequest;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class EmpLocationUpdateController extends Controller
{
    protected EmpLocationUpdateService $EmpLocationUpdateService;

    public function __construct(EmpLocationUpdateService $EmpLocationUpdateService)
    {
        $this->empLocationUpdateService = $EmpLocationUpdateService;
    }
    
    public function dataForCreate(): FoundationApplication|Response|ResponseFactory
    {
        return $this->empLocationUpdateService->dataForCreate();
    }
    public function update_emplocation(EmpLocationUpdateRequest $request): FoundationApplication|Response|ResponseFactory
    {

        return $this->empLocationUpdateService->update_emplocation($request);
    }
 
}
