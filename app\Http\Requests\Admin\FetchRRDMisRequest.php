<?php

//namespace App\Http\Requests\Admin;
namespace App\Http\Requests\Admin;

use App\Helpers\CommonFunction;
use Closure;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;

class FetchRRDMisRequest extends FormRequest
{
    protected CommonFunction $commonFunction;

    public function __construct(CommonFunction $commonFunction)
    {
        
    }

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        
        return [
            'selected_vendor' => ['required','string'],
            'selected_cab' => ['required','string'],
            'from_date' => ['required','date'],
            'to_date' => ['required','date'],
        ];
    }

    public function messages(): array
    {
        return [
            'selected_vendor.required' => 'Selected Vendor field is required',
            'selected_cab.required' => 'Selected Cab field is required',
            'from_date.required' => 'From date field is required',
            'to_date.required' => 'To date field is required',
        ];
    }
   

    public function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json([
            'success' => false,
            'validation_error' => true,
            'message' => 'Validation errors',
            'data' => $validator->errors()
        ]));
    }
   
}
