<?php

namespace App\Http\Requests\Admin;

use App\Helpers\CommonFunction;
use Closure;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;
use Illuminate\Contracts\Validation\Validator;

class SimUpdateRequest extends FormRequest
{
    private CommonFunction $commonFunction;
    private ?object $sim = null;
    private ?int $simId = null;

    public function __construct(CommonFunction $commonFunction)
    {
        parent::__construct();
        $this->commonFunction = $commonFunction;
    }

    public function authorize(): bool
    {
        return true;
    }

    protected function prepareForValidation(): void
    {
        try {
            $this->simId = Crypt::decryptString($this->route('simAutoIdCrypt'));
        } catch (\Throwable $e) {
            throw new HttpResponseException(response()->json([
                'success' => false,
                'validation_error' => true,
                'status' => 1,
                'message' => 'Invalid Sim ID',
            ], 400));
        }

        $this->sim = DB::table('sim')->where('SIM_ID', $this->simId)->first();

        if (!$this->sim) {
            throw new HttpResponseException(response()->json([
                'success' => false,
                'validation_error' => true,
                'status' => 1,
                'message' => 'Sim not found',
            ], 404));
        }
    }

    public function rules(): array
    {
      
        return [
            'SIM_MOBILE_NO' => [
                'required',
                'numeric',
                 Rule::unique('sim', 'SIM_MOBILE_NO')
                 ->ignore($this->simId, 'SIM_ID'),
            ],
            'SIM_SERIAL_NO' => 'required',
            'SIM_PROVIDER' => 'required',
           
        ];
    }

    public function messages(): array
    {
        return [
            'SIM_MOBILE_NO.required' => 'Sim ID field is required',
            'SIM_SERIAL_NO.required' => 'Sim serial no field is required',
            'SIM_PROVIDER.required' => 'Sim provider field is required',
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json([
            'success' => false,
            'validation_error' => true,
            'status' => 2,
            'message' => 'Validation errors',
            'data' => $validator->errors()
        ], 422));
    }

    
}
