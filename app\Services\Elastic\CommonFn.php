<?php
namespace App\Services\Elastic;

class CommonFn{
     public static function date_time(){
        date_default_timezone_set('Asia/Kolkata');
        $cur_datetime = date('Y-m-d H:i:s');
        $cur_date = date('Y-m-d');
        $cur_time = date('H:i:s');
        return $cur_date.'T'.$cur_time;
    }
    
    public static function cur_date(){        
        $cur_date = date('Y-m-d');      
        return $cur_date;
    }
    
    public static function cur_date_time(){
        date_default_timezone_set('Asia/Kolkata');
        $cur_datetime = date('Y-m-d H:i:s');      
        return $cur_datetime;
    }
    public static function index_id_date_time(){
        date_default_timezone_set('Asia/Kolkata');
        $cur_datetime = date('Y-m-d H:i:s');
        $cur_date = date('Ymd');
        $cur_time = date('Hisu');
        return $cur_date.$cur_time;       
    }
    
    public static function freshTimestamp()
{
    date_default_timezone_set('Asia/Kolkata');
    $microtime = microtime(true);
    $milliseconds = sprintf("%03d", ($microtime - floor($microtime)) * 1000);
    return date('Y-m-d H:i:s.'. $milliseconds, $microtime)."";
}

 public static function freshTimestampDoc(){
        date_default_timezone_set('Asia/Kolkata');
        $microtime = microtime(true);
        $milliseconds = sprintf("%02d", ($microtime - floor($microtime)) * 1000);
        $cur_datetime = date('Y-m-d H:i:s');
        $cur_date = date('Y-m-d');
        $cur_time = date('H:i:s.');
        echo $cur_date.'T'.$cur_time.$milliseconds;
        return  $cur_date.'T'.$cur_time.$milliseconds;
//date('Y-m-d'.'T'.'H:i:s.'. $milliseconds, $microtime);
    }
    
}
