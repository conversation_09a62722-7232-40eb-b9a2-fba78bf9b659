<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Crypt;

class CabAttendance extends Model
{
    use HasFactory;

    protected $table = 'cab_attandance';
    protected $primaryKey = 'ATTANDANCE_ID';
	public $timestamps = false;
	//protected $appends = ['CAB_ALLOCATION_ID_crypt'];
	
	protected $fillable = [
        'CAB_ID',
        'BRANCH_ID',
        'LOGIN_TIME',
        'LOGINLAT',
        'LOGINLNG',
        'LOGOUT_TIME',
        'LOGOUTLAT',
        'LOGOUTLNG',
        'LOGIN_LOCATION',
        'LOGOUT_LOCATION',
        'LOGOUT_REASON_ID',
        'DURATION_HOUR',
        'REMARKS',
        'CAB_STATUS',
        'SIM_ID'
    ];

}
