<?php

namespace App\Helpers;
use Illuminate\Support\Facades\Auth;

class MyHelper
{
    public static int $PAGINATION_PER_PAGE = 10;

    public static string $PAGINATION_PER_PAGE_ALL = 'all';

    public static int $RS_ACTIVE = 1;
    public static int $RS_INACTIVE = 2;
    public static string $SMS_PICKUP = 'Pickup';
    public static string $SMS_DROP = 'Drop';
    
    public static string $FILE_UPLOAD_NAME = 'fms_pickup_rost.csv';
    public static string $EMP_UPLOAD_NAME = 'employee_master.csv';
    public static string $EMP_COMP_UPLOAD_NAME = 'company_employee_master.csv';
    public static string $AUTO_ROUTE_UPLOAD_NAME = 'auto_route_master.csv';
    public static string $DEACTIVE_EMP_UPLOAD_NAME = 'DEACTIVE_EMP_UPLOAD_NAME.csv';
    public static string $FILE_UPLOAD_DAYS_LIMIT = '3';
    
    //in minute
    public static int $OTP_EXPIRE_TIME = 15;
    

    public static string $PR_SMSTAG = 'SMS TAG';


    public static string $API_DIRECTIONS = 'https://maps.googleapis.com/maps/api/directions/json?';
    public static string $TRIP_CLOSE_API_KEY='AIzaSyA726lGlhUc0QdQMrQb8mDloSuPolUNE2Y';
    public static string $API_KEY='AIzaSyAzzKMQ0y1hSrMGJDkStw4QwgXlvoY0otA';

    public static string $RRD_SHED_DEFAULT_ENABLE='Y';
    public static string $RRD_SHED_DEFAULT_KM='5';
    



    //user type field in 'users' table
    public static string $ADMIN = 'ADMIN';
    public static string $VENDOR = 'VENDOR';
    public static string $SUPER_ADMIN = 'SUPERADMIN';
    public static string $ESCORT = 'ESCORT';
    public static string $EMPLOYEE = 'EMPLOYEE';
    public static string $ES_WEBESCORT = 'WebEscort';


    public static string $EMPLOYEE_CATEGORY = 'Emp';


    public static string $LOGIN_CATEGORY_ZINGO = 'ZINGO';

    public static string $SMS_CATEGORY_EMP_REG = 'EMP REG';
	
    public static string $RP_TTLTYPE = 'P,D';
    public static string $RP_PICKROUTE = 'P';
    public static string $RP_DROPROUTE = 'D';
    public static string $RP_PICKUP = 'IN';
    public static string $RP_DROP = 'OUT';
	
	
public static int $STATUS_UNMAPPED = 1;
public static int $STATUS_MAPPED = 2;

public static string $BD_DRIVER = 'Driver Issue';
public static string $BD_VEHICLE = 'Vehicle Issue';
public static string $BD_BREAKDOWN = 'Break Down';
public static string $BD_ACCIDENT = 'Accident' ;
public static string $BD_INCIDENT = 'Incident';

public static string $TD_NOTNOW = 'notnow';
public static string $TD_CHANGE = 'change';
public static string $TD_SAME = 'same';

    //Tracking Dashboard
    public static int $TD_NORMAL = 1;

    public static int $ESCORT_NEW = 1;
    public static int $ESCORT_ALLOT = 2;
    public static int $ESCORT_VALID = 3;
    public static int $ESCORT_SETOTP = 4;
    public static int $ESCORT_INTER = 5;
    public static int $ESCORT_REMOVE = 6;
    public static string $ES_REMOVEINTER = '5,6';
    public static string $ESCORT_YES = 'YES';
    public static string $ESCORT_NO = 'NO';
    public static string $TD_PANICEMP = 'PANICEMP';
    public static string $TD_PANICDRIVER = 'PANICDRIVER';
    public static string $PR_ESCORT_ENABLE='ESCORT ENABLE';
    public static string $PR_ESCORT_START_TIME='ESCORT START TIME';
    public static string $PR_ESCORT_END_TIME='ESCORT END TIME';
    public static string $RP_PERFORMANCE = 'performance';
	
	public static string $PR_EMPLOYEE_BUFFER = 'EMPLOYEE BUFFER';

    public static string $PR_ESTIMATESUBTIME = 'ESTIMATE SUBTIME';
    public static string $PR_ESTIMATEADDTIME = 'ESTIMATE ADDTIME';
    public static string $PR_ESTIMATECURTIME = 'ESTIMATE CURTIME';
    public static string $PR_LEVEL2 = 'ESTIMATE INTERVAL LEVEL2';
    public static string $PR_ESTIMATECABALLOT = 'ESTIMATE CABALLOT';
    public static string $PR_LEVEL1 = 'ESTIMATE INTERVAL LEVEL1';
    public static string $PR_TRIPNOTEXECUTED = 'TRIP NOT EXECUTED';

    public static string $PR_INTIMEBUFFER = 'INTIME BUFFER';
    public static string $PR_OUTTIMEBUFFER = 'OUTTIME BUFFER';
    public static string $PR_PANICEMPESCALATE = 'PANIC EMP ESCALATE NO';
    public static string $PR_PANICEMPESCALATEEMAIL = 'PANIC EMP ESCALATE EMAIL';
    public static string $PR_PANICDRIVERESCALATE = 'PANIC DRIVER ESCALATE NO';
    public static string $PR_PANICDRIVERESCALATEEMAIL = 'PANIC DRIVER ESCALATE EMAIL';

    public static string $PR_AUTOPICKUPTIME = 'AUTO PICKUPTIME';
    public static string $PR_HELPLINENO = 'HELPLINE NO';
    
    public static string $OD_DELAYBUFF = '00:15:00';
    public static string $PR_MASKCHECK = 'CALL MASKING OPTION';
    public static string $SHOW_ROUTES_BUFFER_TIME='18000';

   
	#Tracking Dashboard
		
public static string $TD_ALLNORMAL = 'Normal';
public static string $TD_NOTALLOTED = 'Not Alloted';
public static string $TD_ALLOTED = 'Alloted';
public static string $TD_TRIPACCEPTED = 'Trip Accepted';
public static string $TD_TRIPREJECTED = 'Trip Rejected';
public static string $TD_TRIPEXECUTED = 'Trip Executed';
public static string $TD_NORESPONSE = 'No Response';
public static string $TD_TRIPNOTEXECUTED = 'Trip Not Executed';
public static string $TD_BREAKDOWN = 'BreakDown';
public static string $TD_WAITINGATPICKUPPOINT = 'Waiting At PickUp Point';
public static string $TD_SLEEPALERT = 'Sleep Alert';
public static string $TD_SAFEDROP = 'Safe Drop';
public static string $TD_PANIC = 'Panic';
public static string $TD_OVERSPEED = 'Over Speed';
public static string $TD_ESCORT = 'Escort';
public static string $TD_TRIPCLOSE = 'Trip Close';
public static string $TD_OVERTIMEALERT = 'Over Time Alert';
public static string $TD_ALERTSIGNALFAILURE = 'Alert Signal Failure';
public static string $TD_DEVIATIONALERT = 'Deviation Alert';
public static string $TD_SEARCHALL = 'Search All';
public static string $TD_NOSHOW = 'No Show';

#Roster Active Status


public static int $VERIFIED_STATUS = 0;
public static int $VERIFIEDSTATUS = 1;
public static string $RS_SHUTTLE_TYPE='S';
public static string $RS_MGENDER = 'M';
public static string $RS_FGENDER = 'F';
public static int $CAPACITY4 = 4;
public static int $CAPACITY9 = 9;
public static string $PROP_AUTOPICK = 'Y';
public static string $PROP_YES = 'Y';

public static string $CAB_ATTANDANCE = '1,2';
public static string $BC_BILLABLE = 'change';

#User Type 
public static string $UT_OPTION = 'ADMIN';
public static string $UT_VENDOR = 'VENDOR';
public static string $EC_ESCORT = 'ESCORT';
public static string $UT_VENDORID = '0';

public static string $RP_SWINGSTART = '06:00:00';
public static string $RP_SWINGEND = '19:00:00';

// Total Roster Status 
public static string $RS_NEWROSTER = '1,3';

#Total Cab Allot Status
public static string $RS_TOTALALLOT = '9,11,13,15';

#Total Cab Accepted Status
public static string $RS_TOTALACCEPT = '25,27,29,31';

#Total Cab Rejected Status
public static string $RS_TOTALREJECT = '41,43,45,47';

#Total Cab Executed Status
public static string $RS_TOTALEXECUTE = '89,91,93,95';

#Total Can Breakdown Status
public static string $RS_TOTALBREAKDOWN = '217,219,221,223';

#Total Cab TripClose Status
public static string $RS_TOTALTRIPCLOSE = '345,347,349,351';

#Total Cab Delay Routes Status
public static string $RS_TOTALDELAYROUTES = '857,859,861,863';

#Total Cab Manual TripClose Status
public static string $RS_TOTALMANUALTRIPCLOSE = '1024,1113,1115,1117,1119,1033,1035,1037,1039,1049,1051,1053,1055';

#Total Cab TripSheetAccepted Status
public static string $RS_TOTALTRIPSHEETACCEPT = '2397,2399,2909,2911,3165,3167,3081,3083,3085,3087,3097,3101,3103';

#Total TripSheetRejected Status
public static string $RS_TOTALTRIPSHEETREJECT = '4445,4447,4957,4959,5213,5215,5129,5131,5133,5135,5145,5149';
#Total TripSheet Cancel
#RS_TOTALTRIPSHEET_CANCEL=16473,16475,16477,16479,16393,16395,16397,16399

public static string $RS_TOTALTRIPSHEET_CANCEL='17497,17499,17501,17503,17417,17419,17421,17423';

#Total AutoCancel
public static string $RS_TOTAL_AUTOCANCEL = '8193,8205,8283,8221,8281,8201,8225,8285,8195';
#AutoCancel
public static string $RS_AUTOCANCEL = '8192';

#Roster Creation
public static int $RS_ROSTER = 1;

#Vendorchange
public static int $RS_VENDORCHANGE = 2;

#Billable 
public static int $RS_BILLABLE = 4;

#Cab Alloted
public static int $RS_CABALLOT = 8;

#Accepted
public static int $RS_ACCEPT = 16;

#Rejected
public static int $RS_REJECT = 32;

#Executed
public static int $RS_EXECUTE = 64;

#Breakdown
public static int $RS_BREAKDOWN = 128;

#TripClose
public static int $RS_TRIPCLOSE = 256;

#Delay Routes
public static int $RS_DELAYROUTES = 512;

#Manual TripClose
public static int $RS_MANUALTRIPCLOSE = 1024;

#TripSheetAccepted
public static int $RS_TRIPSHEETACCEPTED = 2048;

#TripSheetRejected
public static int $RS_TRIPSHEETREJECTED = 4096;


#Trip Cancel
public static int $RS_TRIPCANCEL=16384;

#Roster Passenger Status
#Roster Passenger Created
public static int $RPS_CREATE = 1;

#Roster Passenger Clubbing  
public static int $RPS_CLUBBING = 2;

#Roster Passenger Arrived
public static int $RPS_ARRIVAL = 4;

#Roster Passenger Cab Delay
public static int $RPS_CABDELAY = 8;

#Roster Passenger NoShow
public static int $RPS_NOSHOW = 16;

#Roster Passenger SystemOTP
public static int $RPS_SYSTEMOTP = 32;

#Roster Passenger Employee Delay
public static int $RPS_EMPLOYEEDELAY = 64;

#Roster Passenger ManualOTP
public static int $RPS_MANUALOTP = 128;

#Roster Passenger Safe Drop
public static int $RP_safe_drop = 256;
#Total Roster Passenger Created
public static string $RPS_TTLCREATE = '1,3';

#Total Roster Passenger Arrived
public static string $RPS_TTLARRIVAL = '5,7';

#Total Roster Passenger Arrived
public static string $RPS_TTLCABDELAY = '13,15';

#Total Roster Passenger NoShow
public static string $RPS_TTLNOSHOW = '16,17,19,21,23,29,31,85';

#Total Roster Passenger SystemOTP
public static string $RPS_TTLSYSTEMOTP = '37,39';

public static string $RPS_TTLCABSYSTEMOTP = '45,47';

#Total Roster Passenger Employee Delay with SYSEMOTP
public static string $RPS_TTLEMPLOYEEDELAY_SYSTEMOTP = '101,103';

#Total Roster Passenger with out SystemOTP Employee Delay
public static string $RPS_TTLEMPLOYEEDELAY = '69,71';

#Total Roster Passenger Employee Delay
public static string $RPS_TTLCABEMPLOYEEDELAY = '109,111';

#Total Roster Passenger ManualOTP
public static string $RPS_TTLMANUALOTP = '133,135,141,143,229,231,237,239,197,199';
public static string $RPS_SAFE_DROP_OTP = '293,295,357,359,389,391,453,455';



#Roster Display Status
public static string $RS_Created_sts='Trip created';
public static string $RS_Alloted_sts='Trip Alloted';
public static string $RS_ACCEPTed_sts='Trip Accepted';
public static string $RS_Rejected_sts='Trip Rejected';
public static string $RS_Execute_sts='Trip Executed';
public static string $RS_Noresponse_sts='Trip NoResponse';
public static string $RS_Breakdown_sts='Trip Breakdown';
public static string $RS_Closed_sts='System Closed';
public static string $RS_Manual_Closed_sts='Trip Manual closed';
public static string $RS_Tripsheet_Accept_sts='Tripsheet Accepted';
public static string $RS_Tripsheet_Reject_sts='Tripsheet Rejected';
public static string $RS_Closed_Delay_sts='Cab Delay and Trip Closed';
public static string $RS_Tripsheet_cancelled_sts='Tripsheet Cancelled';
public static string $RS_Autocancelled_sts='Auto Cancelled';

#Roster Passenger Display Status
public static string $RP_Boarded_sts='Employee Boarded';
public static string $RP_Not_Boarded_sts='Employee not Boarded';
public static string $RP_Not_Enabled='Not Enabled';
public static string $RP_Created_sts='Created';
public static string $RP_Club_sts='Clubbed';
public static string $RP_Arrival_sts='Arrived';
public static string $RP_cab_delay_sts='Cab Delay';
public static string $RP_Noshow_sts='Noshow';
public static string $RP_system_otp_sts='System OTP';
public static string $RP_Manual_otp_sts='Manual OTP';
public static string $RP_employee_delay_sts='Employee Delay';
public static string $RP_cabemployee_delay_sts ='Cab and Employee Delay';
public static string $RP_manualclosed = 'Manual Closed';
public static string $RP_employee_delay_system_otp='Employee Delay and System OTP';
public static string $RP_employee_safe_drop='Employee Safe Drop';
public static string $RP_cab_delay_system_otp_sts='Cab Delay and System OTP';

public static string $RP_ACTIVESTS = 'ACTIVE';
public static string $RP_INACTIVESTS = 'IN-ACTIVE';

public static string $RPSYSOTP = 'S';
public static string $RPMANUALOTP = 'M';
public static string $RP_SYSOTP = 'SYSTEM OTP';
public static string $RP_MANUALOTP = 'MANUAL OTP';

public static string $RP_ALLREPORT = 'ALL';
public static string $RP_EMPLOYEEPANIC = 'EMPLOYEE';
public static string $RP_DRIVERPANIC = 'DRIVER';

# Manual OTP Notication Message
public static string $PICKUP_HEADING='Cab Arrived';
public static string $DROP_HEADING='Boarded';
public static string $OTP_PICKUP_HEADING='OTP';
public static string $OTP_DROP_HEADING='OTP';
public static string $NOTIFICATION_CAT='3';
public static string $NOSHOW_MSG='Noshow has been updated from admin';
public static string $NOSHOW_HEADING='Noshow';


#Vehicle Tracking Status
public static string $VT_LOGIN_STATUS='1';
public static string $VT_LOGOUT_STATUS='0';
public static string $VT_GPS_OFF='0';
public static string $VT_GPS_NOT_FIX='2';
public static string $VT_GPS_FIX='3';
public static string $VT_BATTERY_LOW='20';
public static string $VT_ONDUTY_STATUS='1';
public static string $VT_VACANT_STATUS='0';
public static string $VT_CASE_GPS_OFF='GPSOFF';
public static string $VT_CASE_GPSNOT_FIX='GPSNOTFIX';
public static string $VT_CASE_GPS_FIX='GPSFIX';
public static string $VT_CASE_LOGIN='LOGIN';
public static string $VT_CASE_LOGOUT='LOGOUT';
public static string $VT_CASE_VACANT='VACANT';
public static string $VT_CASE_ONDUTY='ONDUTY';
public static string $VT_CASE_Idle5min_to_1h='Idle 5min to 1h';
public static string $VT_CASE_Idle_1h_to_6h='Idle 1h to 6h';
public static string $VT_CASE_Idle_6h_above='Idle 6h above';
public static string $VT_CASE_ALLCAB_ALL_STATUS='ALLCAB-ALLSTATUS';
public static string $VT_CASE_SINGLECAB_ALL_STATUS='SINGLE-CAB-ALLSTATUS';


public static string $ES_STATUS_ALL='0';
public static string $ES_CAB_STATUS='1';
public static string $ES_STATUS_LOGOUT='Logout';
public static string $ES_STATUS_LOGIN='Login';
public static string $ES_STATUS_OVER_SPEED='Over Speeds';
public static string $ES_STATUS_IDLE='2';
public static string $ES_STATUS_GPS_OFF='GPS Off';
public static string $ES_STATUS_GPS_NOTFIX='GPS Not Fix';
public static string $ES_STATUS_GPS_FIX='GPS Fix';
public static string $ES_STATUS_BATTERY_LOW='41';
public static string $ES_STATUS_VACANT='Vacant';
public static string $ES_STATUS_ONTRIP='On Duty';
public static string $ES_STATUS_ONEHOUR_BELOW='GPS Not Fix 5min to 1hour';
public static string $ES_STATUS_SIXHOURS_BELOW='GPS Not Fix 1hour to 6hour';
public static string $ES_STATUS_SIXHOURS_ABOVE='GPS Not Fix 6hour above';
public static string $ES_CAB_FILTER='filter';


public static string $PR_WEEKROSTERENABLE = 'WEEK_ROSTER_ENABLE_DATE';
public static string $PR_WEEKROSTERENABLEOPTION = 'WEEK_ROSTER_ENABLE_OPTION';


}
