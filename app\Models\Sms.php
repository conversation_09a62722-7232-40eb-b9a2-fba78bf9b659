<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Sms extends Model
{
    use HasFactory;

    protected $table = 'sms';
    protected $primaryKey = 'SMS_ID';

    public $timestamps = false;

    protected $fillable = [
        'BRANCH_ID',
        'OR<PERSON><PERSON><PERSON><PERSON>',
        'RECIPIENT',
        'MESSAGE',
        'STATUS',
        'ROSTER_ID',
        'CATEGORY',
        'SENT_DATE',
        'REF_NO',
        'CREATED_BY',
        'CREATED_DATE',
        'UPDATED_BY',
        'UPDATED_DATE',
    ];
}
