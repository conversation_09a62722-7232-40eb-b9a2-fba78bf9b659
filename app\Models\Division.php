<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Crypt;

class Division extends Model
{
    use HasFactory;

    protected $table = 'division';
    protected $primaryKey = 'DIVISION_ID';



    protected $fillable = [
        'NAME',
        'LOCATION',
        'LAT',
        'LONG',
        'TARIFF_TYPE',        
        'ENTITY_PROPERTIE',
        'ACTIVE',
        'CREATED_BY',
        'UPDATED_BY',
        'REMARKS',
    ];


    protected $appends = ['id_crypt'];

    public function createdBy()
    {
        return $this->hasOne(User::class,'id','CREATED_BY');
    }
    public function updatedBy()
    {
        return $this->hasOne(User::class,'id','UPDATED_BY');
    }

    public function getIdCryptAttribute($value)
    {
        return Crypt::encryptString($this->DIVISION_ID);
    }
}
