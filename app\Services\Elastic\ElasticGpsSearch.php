<?php
namespace App\Services\Elastic;
use Elasticsearch\ClientBuilder;
class ElasticGpsSearch 
{    
    private $client;   
    
    public function __construct()
    {
         $ES_HOST = env('ES_HOST');
        $this->client = ClientBuilder::create()->setHosts(['hosts' => $ES_HOST])->build();
    }

    public function search($query)
    {
        $items = $this->searchOnElasticsearch($query);
        return $this->buildCollection($items);
    }   

  
    private function searchOnElasticsearch($query) {        
        $items = $this->client->search($query);
        return $items;
    }

  
    private function buildCollection($items) {        
       $scroll_id = $items['_scroll_id'];
        for ($i = 0; $i < count($items['hits']['hits']); $i++) {
            $doc[] = $items['hits']['hits'][$i]['_source'];
        }
        
        while (\true) {
            // Execute a Scroll request
            $response = $this->client->scroll([
                "scroll_id" => $scroll_id, //...using our previously obtained _scroll_id
                "scroll" => "30s"           // and the same timeout window
                    ]
            );
            if (count($response['hits']['hits']) > 0) {

                for ($i = 0; $i < count($response['hits']['hits']); $i++) {
                    $doc[] = $items['hits']['hits'][$i]['_source'];
                }
                $scroll_id = $response['_scroll_id'];
            } else {
                // No results, scroll cursor is empty.  You've exported all the data
                break;
            }
        }
        
        if(count($items['hits']['hits'])>0){
        $bulkf = $doc;
        }else{
         $bulkf =  'No Record';   
        }
        return $bulkf;
    }

}
