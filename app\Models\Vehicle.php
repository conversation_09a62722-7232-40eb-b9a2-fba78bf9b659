<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Crypt;

class Vehicle extends Model
{
    use HasFactory;

    protected $table = 'vehicles';
    protected $primaryKey = 'VEHICLE_ID';
	public $timestamps = false;
	protected $appends = ['VEHICLE_ID_crypt'];
	
	protected $fillable = [
        'VEHICLE_MODEL_ID',
        'VEHICLE_REG_NO',
        'VEHICLE_JOIN_DATE',
        'REG_DATE',
        'REG_STATUS',
        'PERMIT_EXPIRY',
        'POLLUTION_EXPIRY',
        'INSURANCE_EXPIRY',
        'FC_EXPIRY',
        'TAX_EXPIRY',
        'COMPLIANT_STATUS',
        'MILEAGE_KM',
        'REMARKS',
        'ACTIVE',
        'CREATED_BY',
        'created_at',
        'UPDATED_BY',
        'updated_at	',
        'CANCELLED_BY',
        'CANCELLED_AT',
        'APPROVED_BY',
        'APPROVED_ON',
        'REJECTED_BY',
        'REJECTED_ON',
        'ORG_ID',
        'GPS_NO',
        'RFID_IMEI_1',
        'RFID_IMEI_2',
        'ATTACH_TYPE',
    ];

	public function getVEHICLEIDCryptAttribute(): string
    {
        return Crypt::encryptString($this->VEHICLE_ID);
    }
}
