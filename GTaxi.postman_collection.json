{"info": {"_postman_id": "022e4149-a952-4217-b7c1-b2c5b3b84d05", "name": "GTaxi", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "5701663", "_collection_link": "https://restless-shadow-217180.postman.co/workspace/Team-Workspace~85d53571-e130-4187-8848-a8b4e5c7a8e8/collection/5701663-022e4149-a952-4217-b7c1-b2c5b3b84d05?action=share&source=collection_link&creator=5701663"}, "item": [{"name": "<PERSON><PERSON>", "item": [{"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"SUP<PERSON><PERSON><PERSON><PERSON>\",\r\n    \"password\": \"Superadmin@2019\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/login", "host": ["{{baseUrl}}"], "path": ["api", "login"]}}, "response": []}, {"name": "VerifyToken", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/verify/token", "host": ["{{baseUrl}}"], "path": ["api", "verify", "token"]}}, "response": []}, {"name": "AllDeviceLogout", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/logout", "host": ["{{baseUrl}}"], "path": ["api", "logout"]}}, "response": []}, {"name": "CurrentDeviceLogout", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/current/logout", "host": ["{{baseUrl}}"], "path": ["api", "current", "logout"]}}, "response": []}]}, {"name": "Organization", "item": [{"name": "Index", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/organization", "host": ["{{baseUrl}}"], "path": ["api", "organization"]}}, "response": []}, {"name": "Store", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/organization", "host": ["{{baseUrl}}"], "path": ["api", "organization"]}}, "response": []}, {"name": "Pagination", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"per_page\": \"1\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/organization/pagination", "host": ["{{baseUrl}}"], "path": ["api", "organization", "pagination"]}}, "response": []}, {"name": "Edit", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/organization/edit/eyJpdiI6IkltcHZsWFBOS1pBdWdyRHZWZEdQUkE9PSIsInZhbHVlIjoiUDZaNEpyMVN5dUljSmgyeGI3aktOdz09IiwibWFjIjoiMTcwZTU5Mzg3MGU4ZGI4OGQ5NmVhZGQ3OGEyZWI4ZmIzMzZkYTIwMjI2MDQ5MjZkZDc3YWRjZWFjZjcxZTg5YyIsInRhZyI6IiJ9", "host": ["{{baseUrl}}"], "path": ["api", "organization", "edit", "eyJpdiI6IkltcHZsWFBOS1pBdWdyRHZWZEdQUkE9PSIsInZhbHVlIjoiUDZaNEpyMVN5dUljSmgyeGI3aktOdz09IiwibWFjIjoiMTcwZTU5Mzg3MGU4ZGI4OGQ5NmVhZGQ3OGEyZWI4ZmIzMzZkYTIwMjI2MDQ5MjZkZDc3YWRjZWFjZjcxZTg5YyIsInRhZyI6IiJ9"]}}, "response": []}, {"name": "Update", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"tcs1\",\r\n    \"location\": \"Vijayawada\",\r\n    \"lat\": \"16.50617400\",\r\n     \"long\": \"80.64801500\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/organization/update/eyJpdiI6IkZORmhEaGw5aUwxRnVhTFVoNkRyUlE9PSIsInZhbHVlIjoiZmtzQ0xMbGpmQzFFOTJRMytIZHBkdz09IiwibWFjIjoiMTExMmEyZGExZTZiOWYxMDAyZWQ5N2FmNGIyMWM2NjBjOWRlOGUzZWU4MjZhNTdhYjQ0Zjc1NTQ1OWU0NzM2NSIsInRhZyI6IiJ9", "host": ["{{baseUrl}}"], "path": ["api", "organization", "update", "eyJpdiI6IkZORmhEaGw5aUwxRnVhTFVoNkRyUlE9PSIsInZhbHVlIjoiZmtzQ0xMbGpmQzFFOTJRMytIZHBkdz09IiwibWFjIjoiMTExMmEyZGExZTZiOWYxMDAyZWQ5N2FmNGIyMWM2NjBjOWRlOGUzZWU4MjZhNTdhYjQ0Zjc1NTQ1OWU0NzM2NSIsInRhZyI6IiJ9"]}}, "response": []}, {"name": "Delete", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"remark\": \"no longer needed\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/organization/delete/eyJpdiI6IkltcHZsWFBOS1pBdWdyRHZWZEdQUkE9PSIsInZhbHVlIjoiUDZaNEpyMVN5dUljSmgyeGI3aktOdz09IiwibWFjIjoiMTcwZTU5Mzg3MGU4ZGI4OGQ5NmVhZGQ3OGEyZWI4ZmIzMzZkYTIwMjI2MDQ5MjZkZDc3YWRjZWFjZjcxZTg5YyIsInRhZyI6IiJ9", "host": ["{{baseUrl}}"], "path": ["api", "organization", "delete", "eyJpdiI6IkltcHZsWFBOS1pBdWdyRHZWZEdQUkE9PSIsInZhbHVlIjoiUDZaNEpyMVN5dUljSmgyeGI3aktOdz09IiwibWFjIjoiMTcwZTU5Mzg3MGU4ZGI4OGQ5NmVhZGQ3OGEyZWI4ZmIzMzZkYTIwMjI2MDQ5MjZkZDc3YWRjZWFjZjcxZTg5YyIsInRhZyI6IiJ9"]}}, "response": []}, {"name": "DataForCreate", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/organization/data/for/create", "host": ["{{baseUrl}}"], "path": ["api", "organization", "data", "for", "create"]}}, "response": []}]}, {"name": "EmployeeApp", "item": [{"name": "OtpCreation", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "User-Agent", "value": "android", "type": "text"}, {"key": "Accept-Version", "value": "2", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"mobile\": \"9080711564\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/otp/creation", "host": ["{{baseUrl}}"], "path": ["api", "otp", "creation"]}}, "response": []}, {"name": "OtpCreationVerification", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "User-Agent", "value": "android", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Accept-Version", "value": "2", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n   \"otp\": \"460203\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/otp/verification", "host": ["{{baseUrl}}"], "path": ["api", "otp", "verification"]}}, "response": []}, {"name": "GcmStore", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "User-Agent", "value": "android", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Accept-Version", "value": "2", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"gcm\":\"CfrCfGaFUToSnhjeyuwZbRK:APA91bEZwTUrEvm4s2XTgwSBA8ETPd5qb-MCM4AxvKmmxjzlqmpLoRx29vLVIPSe47_Cc5pNnPlnD_hkUQXY7TxFdNvKhjvr-3I9I1xm1WC-DxZ5cgIjCZfug_sm7FGmu3_KvMgmAqlK\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/gcm/store", "host": ["{{baseUrl}}"], "path": ["api", "gcm", "store"]}}, "response": []}]}, {"name": "Employee", "item": [{"name": "Index", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/employee", "host": ["{{baseUrl}}"], "path": ["api", "employee"]}}, "response": []}, {"name": "Store", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n\"EMPLOYEES_ID\":\"9080711580\",\r\n\"BRANCH_ID\":\"18\",\r\n\"NAME\":\"kumar\",\r\n\"LNAME\":\"g\",\r\n\"MOBILE\":\"9080711581\",\r\n\"GENDER\":\"M\",\r\n\"EMP_EMAIL\":\"g<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com\",\r\n\"rfid_no\":\"Rfid\",\r\n\"PROJECT_NAME\":\"Project Name\",\r\n\"LOCATION\":\"2963\",\r\n\"ADDRESS\":\"velachery Primary Address\",\r\n\"LATITUDE\":\"12.976431435941752\",\r\n\"LONGITUDE\":\"80.21951418989727\",\r\n\"SEC_LOCATION\":\"16370\",\r\n\"SEC_ADDRESS\":\"vadapalani Secondary Address\",\r\n\"SEC_LATITUDE\":\"13.052148468981336\",\r\n\"SEC_LONGITUDE\":\"80.21254976870635\",\r\n\"addr_type\":\"S\",\r\n\"num_of_day\":\"2024-08-26\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/employee", "host": ["{{baseUrl}}"], "path": ["api", "employee"]}}, "response": []}, {"name": "ActiveEmployeePagination", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"per_page\": \"10\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/employee/active/pagination", "host": ["{{baseUrl}}"], "path": ["api", "employee", "active", "pagination"]}}, "response": []}, {"name": "Edit", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/employee/edit/eyJpdiI6IlFsYzE1SWJCRTEyR3FtNDc0ZGl3dVE9PSIsInZhbHVlIjoibjNrNUJrVjZXeXNLQkpMdjR3RWZUQT09IiwibWFjIjoiMzM5YWUyYmRlMjE0MGYyZGNhYTIzNDM1NWYzNWVmYTU4MTM5MGVlOTE1ODNjNmY4OThhMTY1ODBkNGMxOTA0YSIsInRhZyI6IiJ9", "host": ["{{baseUrl}}"], "path": ["api", "employee", "edit", "eyJpdiI6IlFsYzE1SWJCRTEyR3FtNDc0ZGl3dVE9PSIsInZhbHVlIjoibjNrNUJrVjZXeXNLQkpMdjR3RWZUQT09IiwibWFjIjoiMzM5YWUyYmRlMjE0MGYyZGNhYTIzNDM1NWYzNWVmYTU4MTM5MGVlOTE1ODNjNmY4OThhMTY1ODBkNGMxOTA0YSIsInRhZyI6IiJ9"]}}, "response": []}, {"name": "Update", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n\"EMPLOYEES_ID\":\"9080711550\",\r\n\"BRANCH_ID\":\"18\",\r\n\"NAME\":\"kumar\",\r\n\"LNAME\":\"g\",\r\n\"MOBILE\":\"9080711550\",\r\n\"GENDER\":\"M\",\r\n\"EMP_EMAIL\":\"g<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com\",\r\n\"rfid_no\":\"Rfid1\",\r\n\"PROJECT_NAME\":\"Project Name\",\r\n\"LOCATION\":\"2963\",\r\n\"ADDRESS\":\"velachery Primary Address2\",\r\n\"LATITUDE\":\"12.876431435941753\",\r\n\"LONGITUDE\":\"80.31951418989728\",\r\n\"SEC_LOCATION\":\"16370\",\r\n\"SEC_ADDRESS\":\"vadapalani Secondary Address2\",\r\n\"SEC_LATITUDE\":\"13.152148468981337\",\r\n\"SEC_LONGITUDE\":\"80.31254976870636\",\r\n\"addr_type\":\"P\",\r\n\"num_of_day\":\"\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/employee/update/eyJpdiI6ImdNTG1kYVpRZWJaemNtRWNzajRyQ0E9PSIsInZhbHVlIjoiNmMwK01XL2U0MS9TWlBMZGQzWHlLUT09IiwibWFjIjoiOTg4NGI1NDQ4NmZmZTQxYWY0ODBhNDk2ODE3YjM2NzA5ODcwMTc4NGQzZWE1MmI2MDhiM2NlMjk4MWMyM2Q0NCIsInRhZyI6IiJ9", "host": ["{{baseUrl}}"], "path": ["api", "employee", "update", "eyJpdiI6ImdNTG1kYVpRZWJaemNtRWNzajRyQ0E9PSIsInZhbHVlIjoiNmMwK01XL2U0MS9TWlBMZGQzWHlLUT09IiwibWFjIjoiOTg4NGI1NDQ4NmZmZTQxYWY0ODBhNDk2ODE3YjM2NzA5ODcwMTc4NGQzZWE1MmI2MDhiM2NlMjk4MWMyM2Q0NCIsInRhZyI6IiJ9"]}}, "response": []}, {"name": "Delete", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"remark\": \"no longer needed\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/employee/delete/eyJpdiI6IjA2dmhnK2NiZ1FWTlU4NE9oMzBBemc9PSIsInZhbHVlIjoiMlVjcHpUaThXbDd0Um4zR2lmUkVqdz09IiwibWFjIjoiNmY3NGQ2MzE2MTlkZTA2YTlhMjFhMzk3MTIzNGIxMGVlODEzZTYzNmQ1ODliYzMxMDE2ZjUzYzlmNGExZTc4YSIsInRhZyI6IiJ9", "host": ["{{baseUrl}}"], "path": ["api", "employee", "delete", "eyJpdiI6IjA2dmhnK2NiZ1FWTlU4NE9oMzBBemc9PSIsInZhbHVlIjoiMlVjcHpUaThXbDd0Um4zR2lmUkVqdz09IiwibWFjIjoiNmY3NGQ2MzE2MTlkZTA2YTlhMjFhMzk3MTIzNGIxMGVlODEzZTYzNmQ1ODliYzMxMDE2ZjUzYzlmNGExZTc4YSIsInRhZyI6IiJ9"]}}, "response": []}, {"name": "DeActiveEmployeePagination", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"per_page\": \"10\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/employee/de/active/pagination", "host": ["{{baseUrl}}"], "path": ["api", "employee", "de", "active", "pagination"]}}, "response": []}, {"name": "ActivateEmployee", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"id_crypts\": [\r\n        \"eyJpdiI6ImkybVdZaVhlUkwyNGtTeDJSQWVZRkE9PSIsInZhbHVlIjoiWEEwN1pBWlhkb0lHR3c5bFV4Szh3Zz09IiwibWFjIjoiMjU4NjA0MTM0ZDUwMTBhMmJlOGRiZjEzNTFjNThkYTNjNzM2MzNjYjdkNTBhOTA0ZjMzZWU0NTBmNmY1MzkzOCIsInRhZyI6IiJ9\"\r\n       \r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/employee/activate", "host": ["{{baseUrl}}"], "path": ["api", "employee", "activate"]}}, "response": []}, {"name": "DataForCreate", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/employee/data/for/create", "host": ["{{baseUrl}}"], "path": ["api", "employee", "data", "for", "create"]}}, "response": []}]}, {"name": "SampleCode", "item": [{"name": "CsvUpload", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "csv_file", "type": "file", "src": "/D:/Karthikg/Theme/fms_pickup_rost.csv"}]}, "url": {"raw": "{{baseUrl}}/api/csv/upload", "host": ["{{baseUrl}}"], "path": ["api", "csv", "upload"]}}, "response": []}]}, {"name": "TrackingDashboard", "item": [{"name": "Normal", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n\"tracking_type\":\"normal\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/tracking_dashboard/pagination", "host": ["{{baseUrl}}"], "path": ["api", "tracking_dashboard", "pagination"]}}, "response": []}]}]}