<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Crypt;

class Cab_allocation extends Model
{
    use HasFactory;

    protected $table = 'cab_allocation';
    protected $primaryKey = 'CAB_ALLOCATION_ID';
	public $timestamps = false;
	protected $appends = ['CAB_ALLOCATION_ID_crypt'];
	
	protected $fillable = [
        'CAB_ALLOCATION_ID',
        'CAB_ID',
        'DATE_TIME',
        'ROSTER_ID',
        'ACCEPTANCE_REJECT_STATE',
        'ACCEPTANCE_REJECT_DATE_TIME',
        'REJECT_REASON_ID',
        'ACTION',
        'CREATED_BY',
        'created_at',
        'UPDATED_BY',
        'CREATED_BY',
        'created_at',
        'UPDATED_BY',
        'updated_at'
    ];

	public function getCABALLOCATIONIDCryptAttribute(): string
    {
        return Crypt::encryptString($this->CAB_ALLOCATION_ID);
    }
}
