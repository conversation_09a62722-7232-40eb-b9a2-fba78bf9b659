<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Crypt;

class VehicleModel extends Model
{
    use HasFactory;

    protected $table = 'vehicle_models';
    protected $primaryKey = 'VEHICLE_MODEL_ID';
	public $timestamps = false;
	protected $appends = ['VEHICLE_MODEL_ID_crypt'];
	
	protected $fillable = [
        'VEHICLE_MODEL_ID',
        'BRAND',
        'MODEL',
        'YEAR',
        'CAPACITY',
        'CREATED_BY',
        'CREATED_DATE',
        'UPDATED_BY',
        'UPDATED_DATE',
        
    ];

}
