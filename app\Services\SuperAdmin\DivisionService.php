<?php

namespace App\Services\SuperAdmin;


use App\Helpers\CommonFunction;
use App\Helpers\MyHelper;
use App\Models\Branch;
use App\Models\Division;
use Carbon\Carbon;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;

class DivisionService
{


    protected CommonFunction $commonFunction;


    public function __construct(CommonFunction $commonFunction)
    {
        $this->commonFunction = $commonFunction;
    }


    public function indexDivision(): FoundationApplication|Response|ResponseFactory
    {
        $authUser = Auth::user();
        try {
            $divisions = Division::select(
                'division.DIVISION_ID',
                'division.NAME',
                'division.LOCATION',
                'division.LAT',
                'division.LONG',
                'division.ACTIVE'
            )
                ->where('division.ACTIVE', MyHelper::$RS_ACTIVE)
                ->get();

            return response([
                'success' => true,
                'status' => 200,
                'divisions' => $divisions,
            ]);
        } catch (\Throwable | \Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Division Index Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ]);
        }
    }


    public function storeDivision($request): FoundationApplication|Response|ResponseFactory
    {
        try {

            DB::beginTransaction();
            $auth_user = Auth::user();
            $divisionData = $this->prepareDivisionData($request, $auth_user, MyHelper::$RS_ACTIVE);
            $divisionResult = Division::create($divisionData);
            return response([
                'success' => true,
                'status' => 200,
                'message' => 'Division Created Successfully',
            ]);
        } catch (\Throwable | \Exception $e) {
            DB::rollBack();
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'validation_controller' => true,
                'status' => 4,
                'message' => 'Division Created UnSuccessfully',
                'errorM' => $e->getMessage(),
            ]);
        } finally {
            DB::commit();
        }
    }


    private function prepareDivisionData($request, $auth_user, $active): array
    {
        $date = Carbon::now();
        return [
            "NAME" => $request->name,
            "LOCATION" => $request->location,
            "LAT" => $request->lat,
            "LONG" => $request->long,
            "ACTIVE" => $active,
            "CREATED_BY" => $auth_user->id,
            "created_at" => $date->format("Y-m-d H:i:s"),
          
        ];
    }


    public function editDivision($divisionAutoIdCrypt): FoundationApplication|Response|ResponseFactory
    {
        $authUser = Auth::user();

        try {
            $divisionAutoId = Crypt::decryptString($divisionAutoIdCrypt);
            $division = Division::where('division.DIVISION_ID', $divisionAutoId)
                ->where('division.ACTIVE', MyHelper::$RS_ACTIVE)
                ->select(
                    'division.DIVISION_ID',
                    'division.NAME',
                    'division.LOCATION',
                    'division.LAT',
                    'division.LONG',

                )->firstOrFail();

            return response([
                'success' => true,
                'status' => 200,
                'employee' => $division,
            ]);
        } catch (\Throwable | \Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Division Edit Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ]);
        }
    }



    public function updateDivision($request, $divisionAutoIdCrypt): FoundationApplication|Response|ResponseFactory
    {

        try {

            $authUser = Auth::user();

            DB::beginTransaction();
            $divisionAutoId = Crypt::decryptString($divisionAutoIdCrypt);
            $division = Division::where('division.DIVISION_ID', $divisionAutoId)
                ->where('division.ACTIVE', MyHelper::$RS_ACTIVE)
                ->firstOrFail();
                
            $divisionData = $this->preparDivisionDataForUpdate($request, $authUser);
            $division->update($divisionData);

            DB::commit();

            return response([
                'success' => true,
                'status' => 200,
                'message' => 'Division Updated Successfully',
            ]);
        } catch (\Throwable | \Exception $e) {
            DB::rollBack();
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'message' => 'Division Update Unsuccessful',
                'status' => 4,
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ]);
        }
    }


    private function preparDivisionDataForUpdate($request, $auth_user): array
    {
        $date = Carbon::now();
        return [
            "NAME" => $request->name,
            "LOCATION" => $request->location,
            "LAT" => $request->lat,
            "LONG" => $request->long,
            "UPDATED_BY" => $auth_user->id,
            "updated_at" => $date->format("Y-m-d H:i:s"),
        ];
    }


    public function deleteDivision($request, $divisionAutoIdCrypt): FoundationApplication|Response|ResponseFactory
    {
        $authUser = Auth::user();
        $date = Carbon::now();
        try {
            DB::beginTransaction();

            $divisionAutoId = Crypt::decryptString($divisionAutoIdCrypt);
            $division = Division::where('division.DIVISION_ID', $divisionAutoId)
                ->where('division.ACTIVE', MyHelper::$RS_ACTIVE)
                ->firstOrFail();

            $division->update([
                'REMARKS' => $request->input('remark'),
                'ACTIVE' => MyHelper::$RS_INACTIVE,
                'UPDATED_BY' => Auth::user()->id,
                'updated_at' => $date->format("Y-m-d H:i:s"),
            ]);

            DB::commit();

            return response([
                'success' => true,
                'status' => 200,
                'message' => 'Division Deleted Successfully',
            ]);
        } catch (\Throwable | \Exception $e) {
            DB::rollBack();
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Division Delete Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ]);
        }
    }



    


    public function paginationDivision($request, bool $active = true): FoundationApplication|Response|ResponseFactory
    {
        try {
            $perPage = $request->input('per_page', MyHelper::$PAGINATION_PER_PAGE);
            $divisions = Division::query()
                ->select(
                    'division.DIVISION_ID',
                    'division.NAME',
                    'division.LOCATION',
                    'division.LAT',
                    'division.LONG',
                   
                )
                ->where('division.ACTIVE', MyHelper::$RS_ACTIVE) ;

            $filterModel = $request->input('filterModel');
            if ($filterModel) {
                foreach ($filterModel as $field => $filter) {
                    if (isset($filter['filter']) && $filter['filter'] !== '') {
                        $value = $filter['filter'];
                        $type = $filter['type'];

                        switch ($field) {
                            case 'NAME':
                                $divisions->where('division.NAME', 'like', "%{$value}%");
                                break;
                           
                            case 'LOCATION':
                                $divisions->where('division.LOCATION', 'like', "%{$value}%");
                                break;
                          
                        }
                    }
                }
            }


            if ($request->has('orderBy') && !empty($request->input('orderBy'))) {
                $orderBy = $request->input('orderBy');
                $order = $request->input('order', 'asc');
                $divisions->orderBy($orderBy, $order);
            } else {
                $divisions->orderBy('division.created_at', 'desc');
            }


            if ($perPage === MyHelper::$PAGINATION_PER_PAGE_ALL) {
                $paginatedDivisions = $divisions->paginate($divisions->count());
            } else {
                $perPage = is_numeric($perPage) ? (int)$perPage : MyHelper::$PAGINATION_PER_PAGE;
                $paginatedDivisions = $divisions->paginate($perPage);
            }

            return response([
                'success' => true,
                'status' => 200,
                'divisions' => $paginatedDivisions,
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'Divisions Pagination Unsuccessful' : 'Deactivate Divisions Pagination Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ]);
        }
    }
}
