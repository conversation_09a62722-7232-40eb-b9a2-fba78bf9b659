<?php

namespace App\Services\Admin;

use App\Helpers\CommonFunction;
use App\Helpers\MyHelper;
use App\Http\Controllers\ElasticController;
use App\Models\Location;
use App\Models\Employee;
use Carbon\Carbon;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use DateTime;

class EmpLocationUpdateService
{
    protected CommonFunction $commonFunction;
    protected ?Branch $currentBranch = null;

    public function __construct(CommonFunction $commonFunction)
    {
        $this->commonFunction = $commonFunction;
    }

    public function dataForCreate(): FoundationApplication|Response|ResponseFactory
    {
        try {
            $user = Auth::user();
            $user_id = Auth::user()->id;
           
            $RS_ACTIVE = MyHelper::$RS_ACTIVE;
            $ADMIN = MyHelper::$ADMIN;
            $branch_id = Auth::user()->BRANCH_ID;
            $vendor_id = Auth::user()->vendor_id;
            $dbname = Auth::user()->dbname;
            $date = carbon::now();
            $from_date = date('Y-m-d');
            $user_type = Auth::user()->user_type;
            $division_id = $this->commonFunction->getDivisionId($branch_id);
            $emp_data=Employee::where("user_id",$user_id)->where("ACTIVE",$RS_ACTIVE)->get();
           
            $locations = Location::where('DIVISION_ID', $division_id)->WHERE('ACTIVE', $RS_ACTIVE)->get();

            $category = array(
                array(
                    'value' => 'Login',
                    'name' => 'Login'
                ),
                array(
                    'value' => 'Logout',
                    'name' => 'Logout'
                )
            );
           
            
            return response([
                'success' => true,
                'status' => 3,
                'emp_data' => $emp_data,
                'category' => $category,
                'locations' => $locations,
               
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => '  Employee Adhoc request Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }
    public function update_emplocation($request): FoundationApplication|Response|ResponseFactory
    {
        try {
            $user = Auth::user();
            DB::beginTransaction();
            $RS_ACTIVE = MyHelper::$RS_ACTIVE;
            $ADMIN = MyHelper::$ADMIN;
            $branch_id = Auth::user()->BRANCH_ID;
            $vendor_id = Auth::user()->vendor_id;
            $dbname = Auth::user()->dbname;
            $date = carbon::now();
            $from_date = date('Y-m-d');
            $user_type = Auth::user()->user_type;
            $emp_id = $request->emp_id;
            $getlatlong = $request->getlatlong;
            $addresss = $request->addresss;
            $location_id = $request->location_id;
            $data = explode(',', $getlatlong);
          
            $sql = "UPDATE employees SET LATITUDE='$data[0]',LONGITUDE='$data[1]',ADDRESS='" . $addresss . "',LOCATION_ID='" . $location_id . "' WHERE BRANCH_ID='$branch_id' AND EMPLOYEES_ID='$emp_id'";
            DB::update($sql);
            DB::commit();
            
            return response([
                'success' => true,
                'status' => 3,
                'msg' => "Employee Location Successfully Updated",
               
            ]);
        } catch (\Throwable|\Exception $e) {
            DB::rollBack();
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => '  Employee Adhoc request Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}
