<?php

namespace App\Services\Admin;

use App\Helpers\CommonFunction;
use App\Helpers\MyHelper;
use Carbon\Carbon;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use App\Models\Vendor;
use App\Models\Cab_allocation;
use App\Models\Roster;
use App\Models\RosterPassenger;
use App\Models\RouteEscorts;
use App\Models\Reason_Log;
use App\Models\Cab;
use App\Models\Vehicle;
use App\Models\VehicleModel;
use App\Models\Driver;
use App\Models\property;
use App\Models\Driver_Billing_Summary;
use App\Http\Controllers\MaskNumberClearController;
use App\Http\Controllers\ElasticController;
use DateTime;

class VehicleTravelPathService
{
    protected CommonFunction $commonFunction;
    protected ?Branch $currentBranch = null;

    public function __construct(CommonFunction $commonFunction)
    {
        $this->commonFunction = $commonFunction;
    }
	     
    
    public function vendorwise_vehicle_find($request): FoundationApplication|Response|ResponseFactory
    {
        try {
            $branch_id = Auth::user()->BRANCH_ID;
            $userid = Auth::user()->id;
            $db_name = Auth::user()->dbname;
            $RS_ACTIVE=MyHelper::$RS_ACTIVE;
            $active=true;
           $authUser = Auth::user();

           $selected_date=$request->selected_date;
           $vendor_id=$request->vendor_id;
          
           $cab_data = DB::connection("$db_name")->table('cab as C')->select('C.CAB_ID','C.VENDOR_ID', 'V.VEHICLE_ID', 'V.VEHICLE_REG_NO')
						->join('vehicles as V', 'V.VEHICLE_ID', '=', 'C.VEHICLE_ID')
						->where('C.VENDOR_ID', '=', $vendor_id)
						->where('C.BRANCH_ID', '=', $branch_id) ->where('C.ACTIVE', '=', $RS_ACTIVE)->distinct('C.CAB_ID')->get();

        
            return response([
                'success' => true,
                'status' => 3,
                'cab_data' => $cab_data,
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'Manual otp  status Unsuccessful' : 'Manual otp  status Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ],500);
        }
    } 

    public function vehicle_travel_path($request): FoundationApplication|Response|ResponseFactory
		{
            try
            {

			$travelpath_control = new \App\Http\Controllers\ElasticController;
			
			$date=$request->selected_date;
			$from_time=$request->from_time;
			$to_time=$request->to_time;
			$fromdatetime=$date.' '.$from_time.':00';
			$todatetime=$date.' '.$to_time.':00';
			$VEHICLE_NO=$request->selected_vehicle;
			$vendor_id=$request->selected_vendor_id;
			$path=$travelpath_control->cabPathSearchFull($VEHICLE_NO,$vendor_id,$fromdatetime,$todatetime);
			
          
            /* $path = collect($path)->map(function($item) use ($travelpath_control) {

                $item['addr_pos'] = $item['POSITION']; // You can calculate or assign any value here
                $gps_pos = explode(',', $item['POSITION']);
                $get_address=$travelpath_control->getGpsAddress($gps_pos[0],$gps_pos[1]);
				$item['address']=$get_address!='No Record'?$get_address[0]['ADDRESS']:'--';
                return $item;
            }); */
            
			if($path!='No Record')
			{
				$vehicle_data= $path;
			}
			else
			{
				$vehicle_data =array();
				//$vehicle_data ="No Record";
				
			}
           
            return response([
                'success' => true,
                'status' => 3,
                'vehicle_data' => $vehicle_data
                
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Data For Create for Travelpath Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ],500);
        }
			
		}
    public function vehicle_travel_path_excel($request): FoundationApplication|Response|ResponseFactory
		{
            try
            {

			$travelpath_control = new \App\Http\Controllers\ElasticController;
			
			$date=$request->selected_date;
			$from_time=$request->from_time;
			$to_time=$request->to_time;
			$fromdatetime=$date.' '.$from_time.':00';
			$todatetime=$date.' '.$to_time.':00';
			$VEHICLE_NO=$request->selected_vehicle;
			$vendor_id=$request->selected_vendor_id;
			$path=$travelpath_control->cabPathSearchFull($VEHICLE_NO,$vendor_id,$fromdatetime,$todatetime);
            //print_r($path);exit;
			if($path!='No Record')
			{
                $path = collect($path)->map(function($item) use ($travelpath_control) {

                $item['addr_pos'] = $item['POSITION']; // You can calculate or assign any value here
                $gps_pos = explode(',', $item['POSITION']);
                $get_address=$travelpath_control->getGpsAddress($gps_pos[0],$gps_pos[1]);
				$item['address']=$get_address!='No Record'?$get_address[0]['ADDRESS']:'--';
                return $item;
            }); 
            
			
				$vehicle_data= $path;
			}
			else
			{
				//$vehicle_data =array("message"=>"No Record");
				$vehicle_data =array();
				
			}
           
            return response([
                'success' => true,
                'status' => 3,
                'vehicle_data' => $vehicle_data
                
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Data For Create for Travelpath Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ],500);
        }
			
		}
   
    
    /**
     * @throws ConnectionException
     */
    public function dataForCreate(): FoundationApplication|Response|ResponseFactory
    {

        try {
                $user = Auth::user();
                $RS_ACTIVE = MyHelper::$RS_ACTIVE;
            
                $branch_id = Auth::user()->BRANCH_ID;
                $vendor_id = Auth::user()->vendor_id;
                
                if (Auth::user()->user_type == MyHelper::$ADMIN) {
                    $vendor_list = Vendor::where('ACTIVE', '=', $RS_ACTIVE)->where("BRANCH_ID", "=", $branch_id)->get();
                } else {
                    $vendor_list = Vendor::where('ACTIVE', '=', $RS_ACTIVE)->where("VENDOR_ID", "=", $vendor_id)->get();
                }
                
                return response([
                'success' => true,
                'status' => 3,
                'vendor_list' => $vendor_list
                
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Data For Create for Travelpath Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ],500);
        }
    }
   
    
	

   
}
