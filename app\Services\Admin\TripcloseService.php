<?php

namespace App\Services\Admin;

use App\Helpers\CommonFunction;
use App\Helpers\MyHelper;
use Carbon\Carbon;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use App\Models\Vendor;
use App\Models\Cab_allocation;
use App\Models\Roster;
use App\Models\RosterPassenger;
use App\Models\Cab;
use App\Models\Vehicle;
use App\Models\VehicleModel;
use App\Models\Driver;
use App\Models\Driver_Billing_Summary;
use App\Http\Controllers\MaskNumberClearController;

class TripcloseService
{
    protected CommonFunction $commonFunction;
    protected ?Branch $currentBranch = null;

    public function __construct(CommonFunction $commonFunction)
    {
        $this->commonFunction = $commonFunction;
    }
	
	public function indexTripclose(): FoundationApplication|Response|ResponseFactory
    {
        $authUser = Auth::user();
       

        try 
		{
            $device = Vendor::select('vendors.VENDOR_ID',
                'vendors.NAME as vendor_name'
                
            )
                ->where('vendors.ACTIVE', MyHelper::$RS_ACTIVE)
                ->where('vendors.BRANCH_ID', DB::Raw($authUser->BRANCH_ID))
                ->get();

            return response([
                'success' => true,
                'status' => 3,
                'devices' => $device,
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Device Index Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ],500);
        }
    }


    
	
    /**
     * @throws ConnectionException
     */
  
         
    public function get_tripclose_details($request, bool $active = true): FoundationApplication|Response|ResponseFactory
    {
        try {
            $perPage = $request->input('per_page', MyHelper::$PAGINATION_PER_PAGE);
            $branch_id = Auth::user()->BRANCH_ID;
            $activeStatus = $active ? MyHelper::$RS_ACTIVE : MyHelper::$RS_INACTIVE;

           $authUser = Auth::user();

           $vendor_id=$request->vendor_id;
           $selected_date=$request->selected_date;
           $shift_date_time=$request->shift_date_time;
           $selected_date_time=$selected_date." ".$shift_date_time;
           $RS_ACTIVE = MyHelper::$RS_ACTIVE;
            $trip_close_sts=MyHelper::$RS_TOTALTRIPCLOSE;
            $manual_trip_close_sts=MyHelper::$RS_TOTALMANUALTRIPCLOSE;
            $RS_TOTALDELAYROUTES=MyHelper::$RS_TOTALDELAYROUTES;
           
            $RS_TOTALEXECUTE=MyHelper::$RS_TOTALEXECUTE;
            $RS_TOTALTRIPSHEETACCEPT=MyHelper::$RS_TOTALTRIPSHEETACCEPT;
            $RS_TOTALTRIPSHEETREJECT=MyHelper::$RS_TOTALTRIPSHEETREJECT;
            $RS_TOTALTRIPSHEET_CANCEL=MyHelper::$RS_TOTALTRIPSHEET_CANCEL;
            $RS_TOTALREJECT=explode(',',MyHelper::$RS_TOTALREJECT);
            $RS_TOTALBREAKDOWN=MyHelper::$RS_TOTALBREAKDOWN;
            //$RS_AUTOCANCEL=MyHelper::$RS_AUTOCANCEL;
            $RS_TOTAL_AUTOCANCEL=MyHelper::$RS_TOTAL_AUTOCANCEL;
            $total_trip_close_sts=$trip_close_sts.','.$manual_trip_close_sts.','.$RS_TOTALDELAYROUTES.','.$RS_TOTALTRIPSHEETACCEPT.','.$RS_TOTALTRIPSHEETREJECT.','.$RS_TOTALTRIPSHEET_CANCEL.','.$RS_TOTAL_AUTOCANCEL.','.$RS_TOTALBREAKDOWN;
            $billable_load_sts=$trip_close_sts.','.$manual_trip_close_sts.','.$RS_TOTALDELAYROUTES;
            if(Auth::user()->user_type == MyHelper::$ADMIN)
			{
				$vendorid = "";
			} else {
				//$vendorid = "AND R.VENDOR_ID='$vendor_id'";
				$vendorid = $vendor_id;
			}
            if($vendor_id=='AllVendor')
            {
                $vendorid = "";
            }
            else{
                $vendorid = $vendor_id;
            }
            
			
            if($shift_date_time=='Pending')
            {
                $data = DB::table('rosters as RS')
                    ->select(
                        'RS.ROSTER_STATUS','RS.VENDOR_ID','RS.ROSTER_ID','DS.TRIP_STATUS','RS.ROUTE_ID',
                        DB::raw("IF(RS.TRIP_TYPE = 'P', RS.ESTIMATE_END_TIME, RS.ESTIMATE_START_TIME) as LOGIN_DATE"),'RS.START_LOCATION','RS.END_LOCATION','RS.PASSENGER_ALLOT_COUNT',DB::raw("SUM(IF(RS.ACTIVE = 1, 1, 0)) as totalemp"),'RS.TRIP_TYPE',
                        'VM.MODEL','V.VEHICLE_REG_NO','DR.DRIVERS_NAME','DR.DRIVER_MOBILE','VD.NAME','RS.CAB_ID',
                        DB::raw("'{$shift_date_time}' as shift_wise_status"),
                        DB::raw("CASE 
                                    WHEN RS.TRIP_TYPE = 'P' THEN RS.ESTIMATE_END_TIME 
                                    WHEN RS.TRIP_TYPE = 'D' THEN RS.ESTIMATE_START_TIME 
                                END as LOGIN_TIME"),
                        DB::raw("CASE 
                                    WHEN RS.TRIP_TYPE = 'P' THEN CONCAT(RS.TRIP_TYPE, '--', RS.ROUTE_ID, '--', RS.ESTIMATE_END_TIME) 
                                    WHEN RS.TRIP_TYPE = 'D' THEN CONCAT(RS.TRIP_TYPE, '--', RS.ROUTE_ID, '--', RS.ESTIMATE_START_TIME) 
                                END as Route_order")
                    )
                    ->join('roster_passengers as RP', 'RP.ROSTER_ID', '=', 'RS.ROSTER_ID')
                    ->leftJoin('driver_billing_summary as DS', function ($join)
                    {
                        $join->on('DS.ROSTER_ID', '=', 'RS.ROSTER_ID')
                            ->where('DS.ACTIVE', 1);
                    })
                    ->leftJoin('cab as C', 'C.CAB_ID', '=', 'RS.CAB_ID')
                    ->leftJoin('vehicles as V', 'V.VEHICLE_ID', '=', 'C.VEHICLE_ID')
                    ->leftJoin('vehicle_models as VM', 'VM.VEHICLE_MODEL_ID', '=', 'V.VEHICLE_MODEL_ID')
                    ->leftJoin('drivers as DR', 'DR.DRIVERS_ID', '=', 'C.DRIVER_ID')
                    ->join('vendors as VD', 'VD.VENDOR_ID', '=', 'RS.VENDOR_ID')
                    ->whereNotIn('RS.ROSTER_STATUS', explode(',', $total_trip_close_sts))
                    ->where('RS.BRANCH_ID', $branch_id)
                    ->when($vendorid, function ($query, $vendorid) {
						return $query->where('RS.VENDOR_ID', $vendorid);
					})
                    ->where(function ($query) use ($selected_date) {
                        $query->whereDate('RS.ESTIMATE_END_TIME', $selected_date)
                            ->orWhereDate('RS.ESTIMATE_START_TIME', $selected_date);
                    })
                    ->groupBy('RS.ROSTER_ID');
                  //  ->get();

            }
         
            elseif($shift_date_time=='AllShift')
            {
                $data = DB::table('rosters as RS')
                    ->select(
                        'RS.ROSTER_STATUS','RS.VENDOR_ID','RS.ROSTER_ID','DS.TRIP_STATUS','RS.ROUTE_ID',
                        DB::raw("IF(RS.TRIP_TYPE = 'P', RS.ESTIMATE_END_TIME, RS.ESTIMATE_START_TIME) as LOGIN_DATE"),'RS.START_LOCATION','RS.END_LOCATION','RS.PASSENGER_ALLOT_COUNT',DB::raw("SUM(IF(RS.ACTIVE = 1, 1, 0)) as totalemp"),'RS.TRIP_TYPE',
                        'VM.MODEL','V.VEHICLE_REG_NO','DR.DRIVERS_NAME','DR.DRIVER_MOBILE','VD.NAME','RS.CAB_ID',
                        DB::raw("'{$shift_date_time}' as shift_wise_status"),
                        DB::raw("CASE 
                                    WHEN RS.TRIP_TYPE = 'P' THEN RS.ESTIMATE_END_TIME 
                                    WHEN RS.TRIP_TYPE = 'D' THEN RS.ESTIMATE_START_TIME 
                                END as LOGIN_TIME"),
                        DB::raw("CASE 
                                    WHEN RS.TRIP_TYPE = 'P' THEN CONCAT(RS.TRIP_TYPE, '--', RS.ROUTE_ID, '--', RS.ESTIMATE_END_TIME) 
                                    WHEN RS.TRIP_TYPE = 'D' THEN CONCAT(RS.TRIP_TYPE, '--', RS.ROUTE_ID, '--', RS.ESTIMATE_START_TIME) 
                                END as Route_order")
                    )
                    ->join('roster_passengers as RP', 'RP.ROSTER_ID', '=', 'RS.ROSTER_ID')
                    ->leftJoin('driver_billing_summary as DS', function ($join)
                    {
                        $join->on('DS.ROSTER_ID', '=', 'RS.ROSTER_ID')
                            ->where('DS.ACTIVE', 1);
                    })
                    ->leftJoin('cab as C', 'C.CAB_ID', '=', 'RS.CAB_ID')
                    ->leftJoin('vehicles as V', 'V.VEHICLE_ID', '=', 'C.VEHICLE_ID')
                    ->leftJoin('vehicle_models as VM', 'VM.VEHICLE_MODEL_ID', '=', 'V.VEHICLE_MODEL_ID')
                    ->leftJoin('drivers as DR', 'DR.DRIVERS_ID', '=', 'C.DRIVER_ID')
                    ->join('vendors as VD', 'VD.VENDOR_ID', '=', 'RS.VENDOR_ID')
                    ->whereIn('RS.ROSTER_STATUS', explode(',', $billable_load_sts))
                    ->where('RS.BRANCH_ID', $branch_id)
                    ->when($vendorid, function ($query, $vendorid) {
						return $query->where('RS.VENDOR_ID', $vendorid);
					})
                    ->where(function ($query) use ($selected_date) {
                        $query->whereDate('RS.ESTIMATE_END_TIME', $selected_date)
                            ->orWhereDate('RS.ESTIMATE_START_TIME', $selected_date);
                    })
                    ->groupBy('RS.ROSTER_ID');
                  //  ->get();

            }
            elseif($shift_date_time=='Closed')
            {
                $data = DB::table('rosters as RS')
                    ->select(
                        'RS.ROSTER_STATUS','RS.VENDOR_ID','RS.ROSTER_ID','DS.TRIP_STATUS','RS.ROUTE_ID',
                        DB::raw("IF(RS.TRIP_TYPE = 'P', RS.ESTIMATE_END_TIME, RS.ESTIMATE_START_TIME) as LOGIN_DATE"),'RS.START_LOCATION','RS.END_LOCATION','RS.PASSENGER_ALLOT_COUNT',DB::raw("SUM(IF(RS.ACTIVE = 1, 1, 0)) as totalemp"),'RS.TRIP_TYPE',
                        'VM.MODEL','V.VEHICLE_REG_NO','DR.DRIVERS_NAME','DR.DRIVER_MOBILE','VD.NAME','RS.CAB_ID','C.CAB_STATUS',DB::raw("'{$shift_date_time}' as shift_wise_status"),
                        DB::raw("CASE 
                                    WHEN RS.TRIP_TYPE = 'P' THEN RS.ESTIMATE_END_TIME 
                                    WHEN RS.TRIP_TYPE = 'D' THEN RS.ESTIMATE_START_TIME 
                                END as LOGIN_TIME"),
                        DB::raw("CASE 
                                    WHEN RS.TRIP_TYPE = 'P' THEN CONCAT(RS.TRIP_TYPE, '--', RS.ROUTE_ID, '--', RS.ESTIMATE_END_TIME) 
                                    WHEN RS.TRIP_TYPE = 'D' THEN CONCAT(RS.TRIP_TYPE, '--', RS.ROUTE_ID, '--', RS.ESTIMATE_START_TIME) 
                                END as Route_order")
                    )
                    ->join('roster_passengers as RP', 'RP.ROSTER_ID', '=', 'RS.ROSTER_ID')
                    ->leftJoin('driver_billing_summary as DS', function ($join)
                    {
                        $join->on('DS.ROSTER_ID', '=', 'RS.ROSTER_ID')
                            ->where('DS.ACTIVE', 1);
                    })
                    ->leftJoin('cab as C', 'C.CAB_ID', '=', 'RS.CAB_ID')
                    ->leftJoin('vehicles as V', 'V.VEHICLE_ID', '=', 'C.VEHICLE_ID')
                    ->leftJoin('vehicle_models as VM', 'VM.VEHICLE_MODEL_ID', '=', 'V.VEHICLE_MODEL_ID')
                    ->leftJoin('drivers as DR', 'DR.DRIVERS_ID', '=', 'C.DRIVER_ID')
                    ->join('vendors as VD', 'VD.VENDOR_ID', '=', 'RS.VENDOR_ID')
                    ->whereIn('RS.ROSTER_STATUS', explode(',', $total_trip_close_sts))
                    ->where('RS.BRANCH_ID', $branch_id)
                    ->when($vendorid, function ($query, $vendorid) {
						return $query->where('RS.VENDOR_ID', $vendorid);
					})
                    ->where(function ($query) use ($selected_date) {
                        $query->whereDate('RS.ESTIMATE_END_TIME', $selected_date)
                            ->orWhereDate('RS.ESTIMATE_START_TIME', $selected_date);
                    })
                    ->groupBy('RS.ROSTER_ID');
                  //  ->get();

            }

            else
			{
                $data = DB::table('rosters as RS')
                    ->select(
                        'RS.ROSTER_STATUS','RS.VENDOR_ID','RS.ROSTER_ID','DS.TRIP_STATUS','RS.ROUTE_ID',
                        DB::raw("IF(RS.TRIP_TYPE = 'P', RS.ESTIMATE_END_TIME, RS.ESTIMATE_START_TIME) as LOGIN_DATE"),'RS.START_LOCATION','RS.END_LOCATION','RS.PASSENGER_ALLOT_COUNT',DB::raw("SUM(IF(RS.ACTIVE = 1, 1, 0)) as totalemp"),'RS.TRIP_TYPE',
                        'VM.MODEL','V.VEHICLE_REG_NO','DR.DRIVERS_NAME','DR.DRIVER_MOBILE','VD.NAME','RS.CAB_ID',
                        DB::raw("'{$shift_date_time}' as shift_wise_status"),
                        DB::raw("CASE 
                                    WHEN RS.TRIP_TYPE = 'P' THEN RS.ESTIMATE_END_TIME 
                                    WHEN RS.TRIP_TYPE = 'D' THEN RS.ESTIMATE_START_TIME 
                                END as LOGIN_TIME"),
                        DB::raw("CASE 
                                    WHEN RS.TRIP_TYPE = 'P' THEN CONCAT(RS.TRIP_TYPE, '--', RS.ROUTE_ID, '--', RS.ESTIMATE_END_TIME) 
                                    WHEN RS.TRIP_TYPE = 'D' THEN CONCAT(RS.TRIP_TYPE, '--', RS.ROUTE_ID, '--', RS.ESTIMATE_START_TIME) 
                                END as Route_order")
                    )
                    ->join('roster_passengers as RP', 'RP.ROSTER_ID', '=', 'RS.ROSTER_ID')
                    ->leftJoin('driver_billing_summary as DS', function ($join)
                    {
                        $join->on('DS.ROSTER_ID', '=', 'RS.ROSTER_ID')
                            ->where('DS.ACTIVE', 1);
                    })
                    ->leftJoin('cab as C', 'C.CAB_ID', '=', 'RS.CAB_ID')
                    ->leftJoin('vehicles as V', 'V.VEHICLE_ID', '=', 'C.VEHICLE_ID')
                    ->leftJoin('vehicle_models as VM', 'VM.VEHICLE_MODEL_ID', '=', 'V.VEHICLE_MODEL_ID')
                    ->leftJoin('drivers as DR', 'DR.DRIVERS_ID', '=', 'C.DRIVER_ID')
                    ->join('vendors as VD', 'VD.VENDOR_ID', '=', 'RS.VENDOR_ID')
                    ->whereIn('RS.ROSTER_STATUS', explode(',', $billable_load_sts))
                    ->where('RS.BRANCH_ID', $branch_id)
                    ->when($vendorid, function ($query, $vendorid) {
						return $query->where('RS.VENDOR_ID', $vendorid);
					})
                    ->where(function ($query) use ($selected_date_time) {
                        $query->where('RS.ESTIMATE_END_TIME', $selected_date_time)
                            ->orWhere('RS.ESTIMATE_START_TIME', $selected_date_time);
                    })
                    ->groupBy('RS.ROSTER_ID');
				//$where_cond="And (RS.ESTIMATE_END_TIME=concat('".$selecteddate."',' ','".$shift_wise."') OR RS.ESTIMATE_START_TIME=concat('".$selecteddate."',' ','".$shift_wise."') )  And RS.ROSTER_STATUS & 4 and RS.ROSTER_STATUS in (".$billable_load_sts.")";
			}
               
            $filterModel = $request->input('filterModel');
            if ($filterModel) {
                foreach ($filterModel as $field => $filter) {
                    if (isset($filter['filter']) && $filter['filter'] !== '') {
                        $value = $filter['filter'];
                        $type = $filter['type'];

                        switch ($field) {
                            case 'VEHICLE_REG_NO':
                                $data->where('V.VEHICLE_REG_NO', 'like', "%{$value}%");
                                break;
							case 'DRIVERS_NAME':
                                $data->where('DR.DRIVERS_NAME', 'like', "%{$value}%");
                                break;
                        }
                    }
                }
            }


            if ($request->has('orderBy') && !empty($request->input('orderBy'))) {
                $orderBy = $request->input('orderBy');
                $order = $request->input('order', 'asc');
                $data->orderBy($orderBy, $order);
            } else {
                $data->orderBy('LOGIN_TIME', 'ASC');
            }


            if ($perPage === MyHelper::$PAGINATION_PER_PAGE_ALL) {
                $paginateddata = $data->paginate($data->count());
            } else {
                $perPage = is_numeric($perPage) ? (int)$perPage : MyHelper::$PAGINATION_PER_PAGE;
                $paginateddata = $data->paginate($perPage);
            }

             
        $paginateddata->getCollection()->transform(function ($dataV) use ($shift_date_time, $RS_TOTALREJECT) {
         
            $Route_ID_is_clickable = false;
            $Vendor_Name_is_clickable = false;
            $Trip_Status_is_clickable = true;

           
            if ($shift_date_time == 'Pending') {
                if (!in_array($dataV->ROSTER_STATUS, $RS_TOTALREJECT)) {
                    $Route_ID_is_clickable = true;
                }
            } elseif ($shift_date_time == 'Closed') {
                $Route_ID_is_clickable = false;
            }  

             
            if ($shift_date_time != 'Closed' && empty($dataV->CAB_ID)) {
                $Vendor_Name_is_clickable = true;
            }

             
            $dataV->Route_ID_is_clickable = $Route_ID_is_clickable;
            $dataV->Vendor_Name_is_clickable = $Vendor_Name_is_clickable;
            $dataV->Trip_Status_is_clickable = $Trip_Status_is_clickable;

            return $dataV;
        });

            return response([
                'success' => true,
                'status' => 3,
                'trip_login_details' => $paginateddata,
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'Tripclose Pagination Unsuccessful' : 'Tripclose Pagination Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ],500);
        }
    } 
    public function trip_status_update($request, bool $active = true): FoundationApplication|Response|ResponseFactory
    {
        try {
            $branch_id = Auth::user()->BRANCH_ID;
            $userid = Auth::user()->id;
            $activeStatus = $active ? MyHelper::$RS_ACTIVE : MyHelper::$RS_INACTIVE;

           $authUser = Auth::user();

           $trip_status=$request->trip_status;
           $trip_status_remarks=$request->trip_status_remarks;
           $roster_id=$request->roster_id;

           $arr=array("TRIP_STATUS"=>$trip_status,"TRIP_STATUS_REMARKS"=>$trip_status_remarks);
           $update_roster=Driver_Billing_Summary::where("ROSTER_ID","=",$roster_id)->update($arr);
           /* LOG */	
           //$object=$this->commonFunction();
           $date_f=$this->commonFunction->date_format_add();
           $log_arr=array("ROSTER_ID"=>$roster_id,"BRANCH_ID"=>$branch_id,"ACTION"=>'ManualTripclose/Trip_type_change',"PROCESS_DATE"=>$date_f,"CATEGORY"=>'Manual Tripclose',"USER_ID"=>$userid,"UPDATE_Roster_SUCCESS"=>$update_roster);
           //$res=$object->weblogs($log_arr);
         


            return response([
                'success' => true,
                'status' => 3,
                'message' => "Manual Trip  status successfully updated",
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'Manual Trip  status Unsuccessful' : 'Manual Trip  status Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ],500);
        }
    } 
    public function billable_trip_Approve($request, bool $active = true): FoundationApplication|Response|ResponseFactory
    {
        try {
            $date = Carbon::now();
					$update_time=$date->format("Y-m-d H:i:s");
					$branch_id=Auth::user()->BRANCH_ID;
					$user_id=Auth::user()->id;
					$trip_approve_status=$request->trip_approve_status;
					$roster_ids_appr=$request->selected_roster_id;
					
					$RS_TOTALTRIPSHEETACCEPT=explode(',',MyHelper::$RS_TOTALTRIPSHEETACCEPT);
					$RS_TOTALTRIPSHEETREJECT=explode(',',MyHelper::$RS_TOTALTRIPSHEETREJECT);
					$RS_TRIPSHEETACCEPTED=MyHelper::$RS_TRIPSHEETACCEPTED;
					$RS_TRIPSHEETREJECTED=MyHelper::$RS_TRIPSHEETREJECTED;
					
					$roster_appr_rejec_sts=$trip_approve_status=='approve'?$RS_TOTALTRIPSHEETACCEPT:$RS_TOTALTRIPSHEETREJECT;
					$i=0;
					$update_sts=array();
					$roster_id_list=array();
					$previoused_status=array();
					foreach($roster_ids_appr as $value)
					{
						$roster_id=$roster_ids_appr[$i];
						if($trip_approve_status=='approve')
						{
							$sql = "SELECT R.ROSTER_STATUS as previous_status,conv(bin(R.ROSTER_STATUS)+bin(" . $RS_TRIPSHEETACCEPTED . "),2,10) as update_roster_status,R.ROSTER_ID FROM rosters as R WHERE R.ROSTER_ID='$roster_id'";
							$approve_type='Approve Button';
						}
						elseif($trip_approve_status=='notapprove')
						{
							$sql = "SELECT R.ROSTER_STATUS as previous_status,conv(bin(R.ROSTER_STATUS)+bin(" . $RS_TRIPSHEETREJECTED . "),2,10) as update_roster_status,R.ROSTER_ID FROM rosters as R WHERE R.ROSTER_ID='$roster_id'";
							$approve_type='Not Approve Button';
						}
						$roster_status = DB::select($sql);
						$previous_status=$roster_status[0]->previous_status;
						$upstatus = $roster_status[0]->update_roster_status;
						$ROSTER_ID = $roster_status[0]->ROSTER_ID;
						//$obj=new TripcloseController;
						//$roster_table_sts=$obj->check_roster_status($roster_id);
						//if(in_array($roster_table_sts,$roster_appr_rejec_sts)==false)
						if(in_array($upstatus,$roster_appr_rejec_sts)==true)
						{
							$arr=array("ROSTER_STATUS"=>$upstatus,"UPDATED_BY"=>$user_id);
							$update_roster=Roster::where("ROSTER_ID","=",$roster_id)->update($arr);
							$cab_allocation=Cab_allocation::where("ROSTER_ID","=",$roster_id)->update(array("ACCEPTANCE_REJECT_STATE"=>$upstatus,"ACCEPTANCE_REJECT_DATE_TIME"=>$update_time,"UPDATED_BY"=>$user_id));
							array_push($update_sts,$upstatus);
							array_push($roster_id_list,$roster_id);
							array_push($previoused_status,$previous_status);
						}
						$i++;
					}
							/* log */
							//$obj2=new CommonController();
							$date_f=$this->commonFunction->date_format_add();
					
							$log_arr=array("ROSTER_ID"=>$roster_id_list,"BRANCH_ID"=>$branch_id,"ACTION"=>'ManualTripclose/Tripsheet '.$approve_type,"PROCESS_DATE"=>$date_f,"CATEGORY"=>'Manual Tripclose',"USER_ID"=>$user_id,"UPDATED_STATUS"=>$update_sts,"PREVIOUS_STATUS"=>$previoused_status);
							//$res=$obj2->weblogs($log_arr);
							/* log End */


            return response([
                'success' => true,
                'status' => 3,
                'message' => "Billable Tripsheet successfully updated",
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'Billable Tripsheet status Unsuccessful' : 'Billable Tripsheet status Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ],500);
        }
    } 

    
    public function manualtripclose($request, bool $active = true): FoundationApplication|Response|ResponseFactory
    {
        try {
            $date = Carbon::now();
				$branch_id=Auth::user()->BRANCH_ID;
				$user_id=Auth::user()->id;
				$update=$date->format("Y-m-d H:i:s");
				$trip_sheet_sts=$request->trip_sheet_sts;
				$rrd_branch=array("32","53","54","18","62","63","64");
				$roster_id=$request->roster_id;
				$accept_reject_reason=$request->accept_reject_reason;
				$trip_close_time=$request->trip_close_time;
				$selected_date=$request->selected_date;
				$selected_vendor=$request->selected_vendor;
				$trip_closed_time=$selected_date.' '.$trip_close_time;
				$roster_type=DB::table("rosters as R")->select("R.TRIP_TYPE","R.ROSTER_ID","R.ROUTE_ID","R.CAB_ID","DS.TRIP_STATUS")
				->leftjoin("driver_billing_summary as DS", "DS.ROSTER_ID", "=", "R.ROSTER_ID")
				->where("R.ROSTER_ID","=",$roster_id)->get();
					$cc=count($roster_type);
					$r_id=$roster_type[0]->ROUTE_ID;
					$CAB_ID=$roster_type[0]->CAB_ID;
					$TRIP_STATUS=$roster_type[0]->TRIP_STATUS!=''?$roster_type[0]->TRIP_STATUS:'N';
					$RS_MANUALTRIPCLOSE=MyHelper::$RS_MANUALTRIPCLOSE;
					$RS_TRIPCANCEL=MyHelper::$RS_TRIPCANCEL;
					$RS_TOTALTRIPSHEET_CANCEL=explode(',',MyHelper::$RS_TOTALTRIPSHEET_CANCEL);
					$RS_TOTALMANUALTRIPCLOSE=explode(',',MyHelper::$RS_TOTALMANUALTRIPCLOSE);
					$RS_TOTALTRIPCLOSE=explode(',',MyHelper::$RS_TOTALTRIPCLOSE);
					$RS_TOTALDELAYROUTES=explode(',',MyHelper::$RS_TOTALDELAYROUTES);
					$total_tripclosed_sts=array_merge($RS_TOTALDELAYROUTES, $RS_TOTALTRIPCLOSE, $RS_TOTALMANUALTRIPCLOSE);
					$accept_reject_sts=$trip_sheet_sts=='accept'?$total_tripclosed_sts:$RS_TOTALTRIPSHEET_CANCEL;
					if($trip_sheet_sts=='accept')
					{
						$sql = "SELECT R.ROSTER_STATUS as previous_status,conv(bin(R.ROSTER_STATUS)+bin(" . $RS_MANUALTRIPCLOSE . "),2,10) as update_roster_status,R.ROSTER_ID,R.TOTAL_KM FROM rosters as R WHERE R.ROSTER_ID='$roster_id'";
						$approve_status='Accept';
					}
					elseif($trip_sheet_sts=='cancel')
					{
					  $sql = "SELECT R.ROSTER_STATUS as previous_status,conv(bin(R.ROSTER_STATUS)+bin(".$RS_MANUALTRIPCLOSE.")+bin(" . $RS_TRIPCANCEL . "),2,10) as update_roster_status,R.ROSTER_ID,R.TOTAL_KM FROM  rosters as R WHERE ROSTER_ID='$roster_id'";

						$approve_status='Reject';
					}
					$roster_status = DB::select($sql);
					$upstatus = $roster_status[0]->update_roster_status;
					$previous_status = $roster_status[0]->previous_status;
					$previous_tot_km = $roster_status[0]->TOTAL_KM!=''?$roster_status[0]->TOTAL_KM:'0';
					$ROSTER_ID = $roster_status[0]->ROSTER_ID;
					//$obj=new TripcloseController;
					//$roster_table_sts=$obj->check_roster_status($roster_id);
					$update_roster='';
					$cab_allocation='';
					$previous_tot_km='';
					//if(in_array($roster_table_sts,$accept_reject_sts)==false)
					if(in_array($upstatus,$accept_reject_sts)==true)
					{
						$arr=array("ACTUAL_END_TIME" => $trip_closed_time,"REMARKS"=>$accept_reject_reason,"ROSTER_STATUS"=>$upstatus,"UPDATED_BY"=>$user_id);
						$update_roster=Roster::where("ROSTER_ID","=",$roster_id)->update($arr);
						$cab_allocation=Cab_allocation::where("ROSTER_ID","=",$roster_id)->update(array("ACCEPTANCE_REJECT_STATE"=>$upstatus,"ACCEPTANCE_REJECT_DATE_TIME"=>$update,"UPDATED_BY"=>$user_id));
						
						$update2 = DB::table("driver_billing_summary")
						->WHERE("ROSTER_ID", "=", $roster_id)
						->WHERE("ACTIVE", "=", 1)
						->update(array("ROSTER_STATUS" => $upstatus, "UPDATED_AT" => date("Y-m-d H:i:s"),"UPDATED_BY"=>$user_id ));
						
						
						//$obj3->close_onduty($CAB_ID);
						
						if(($previous_tot_km==0) && (in_array($branch_id,$rrd_branch)===false))
						{
							$gps_km=$this->commonFunction->CalcDeviceKm($roster_id);
						}
						else if(($previous_tot_km==0) && (in_array($branch_id,$rrd_branch)===true))
						{
							$gps_km=$this->commonFunction->RRD_CalcDeviceKm($roster_id,$roster_type[0]->TRIP_TYPE,$TRIP_STATUS);
							if($TRIP_STATUS=='FT' || $TRIP_STATUS=='LT')
							{
								$shedkm=$this->commonFunction->First_Last_UpdateShedDistance($roster_id,$TRIP_STATUS);
							}
							$update ="UPDATE driver_billing_summary SET GOOGLE_TOTAL_KM=GOOGLE_KM+if(GOOGLE_SHED_KM is null || GOOGLE_SHED_KM=0 ,0, GOOGLE_SHED_KM),UPDATED_BY='".$user_id."',UPDATED_AT='".date("Y-m-d H:i:s")."' WHERE ROSTER_ID='".$roster_id."' and ACTIVE=1 ";
							$updatepanic = DB::update($update);
						}
						 
						/* log */
						$date_f=$this->commonFunction->date_format_add();
						$log_arr=array("ROSTER_ID"=>$roster_id,"BRANCH_ID"=>$branch_id,"ACTION"=>'ManualTripclose/Tripsheet ' .$approve_status,"PROCESS_DATE"=>$date_f,"CATEGORY"=>'Manual Tripclose',"USER_ID"=>$user_id,"SELECTED_DATE"=>$selected_date,"SELECTED_VENDOR"=>$selected_vendor,"UPDATED_STATUS"=>$upstatus,"ACTUAL_END_TIME"=>$trip_closed_time,"TRIP_TYPE"=>$roster_type[0]->TRIP_TYPE,"PREVIOUS_STATUS"=>$previous_status,"UPDATE_ROSTER_SUCCESS"=>$update_roster,"CAB_ALLOCATION_SUCCESS"=>$cab_allocation ,"PREVIOUS_TOTAL_KM"=>$previous_tot_km);
						//$res=$this->commonFunction->weblogs($log_arr);
						/* log End */
					}
						$mask_enable = $this->commonFunction->GetPropertyValue('CALL MASKING OPTION');
						if($mask_enable=='Y')
						{
							//$mask= new MaskNumberClearController();
							//$mask->Clear_MaskNumber(0,$branch_id,$roster_id,'Roster');
						}


            return response([
                'success' => true,
                'status' => 3,
                'message' => "Trip status successfully updated",
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'Tripclose Pagination Unsuccessful' : 'Tripclose Pagination Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ],500);
        }
    } 

    
    
    public function tripclose_cab_allot($request, bool $active = true): FoundationApplication|Response|ResponseFactory
    {
        try {
           // print_r($request->all());exit;
                $date = Carbon::now();
				$update_time=$date->format("Y-m-d H:i:s");
				$branch_id=Auth::user()->BRANCH_ID;
				$user_id=Auth::user()->id;
				$vendorid = Auth::user()->vendor_id;
				$roster_id=$request->roster_id;
				$cab_id=$request->cabid;
				//$cabmodel=$request->cabmodel;
				//$driver_mobile=$request->driver_mobile;
				$billable_confirm=$request->billable_confirm;
				$vendor_id=$request->vendor_id;
				$chk_first_pickup=$request->chk_first_pickup;
				$get_trip_type=$request->get_trip_type;
				$RS_CABALLOT=MyHelper::$RS_CABALLOT;
				$RS_BILLABLE=MyHelper::$RS_BILLABLE;
				$RS_ROSTER=MyHelper::$RS_ROSTER;
				$RS_TOTALALLOT=explode(',',(MyHelper::$RS_TOTALALLOT));

                $checkCab=DB::select("select if(TRIP_TYPE='P',ESTIMATE_END_TIME,ESTIMATE_START_TIME) as shift_time,CAB_ID from rosters where ROSTER_ID=$roster_id");
                //print_r($checkCab);exit;
                if(count($checkCab)>0)
                {
                    $shift_time=$checkCab[0]->shift_time;
                  //  $cab_id=$checkCab[0]->CAB_ID;
                }
                if($get_trip_type=='P')
                {
                    $checkCnt=DB::select("select count(ROSTER_ID) as cnt,CAB_ID from rosters where ESTIMATE_END_TIME='".$shift_time."' and CAB_ID=$cab_id"); 
                }
                else
                {
                    $checkCnt=DB::select("select count(ROSTER_ID) as cnt,CAB_ID from rosters where ESTIMATE_START_TIME='".$shift_time."' and CAB_ID=$cab_id");  
                }
                if($checkCnt[0]->cnt>0)
                {
                    return response([
                        'success' => false,
                        'status' => 3,
                        'message' => "Cab already allocated for this shift!",
                    ]);
                   
                }


				if($vendor_id==0 || $vendor_id=='AllVendor')
				{
					echo "Please select vendor!";
					exit;
				}
				$cabdetails = "SELECT V.VEHICLE_REG_NO,VM.MODEL,VM.CAPACITY,T.PACKAGE_PRICE,T.TARIFF_ID FROM cab C
				INNER JOIN vehicles V ON V.VEHICLE_ID = C.VEHICLE_ID
				INNER JOIN vehicle_models VM ON VM.VEHICLE_MODEL_ID = V.VEHICLE_MODEL_ID
				left join tariff T on T.VEHICLE_MODEL_ID=V.VEHICLE_MODEL_ID and T.BRANCH_ID='".$branch_id."' and T.ACTIVE=1 and T.VENDOR_ID='".$vendor_id."'
				WHERE C.CAB_ID = '$cab_id'";
				$cab_details = DB::select($cabdetails);
				$tarrif_id=$cab_details[0]->TARIFF_ID!=''?$cab_details[0]->TARIFF_ID:'0';
				if($billable_confirm == '--')
				{
					 $sql = "SELECT R.ROSTER_STATUS as previous_status,conv(bin(R.ROSTER_STATUS)+bin(".$RS_CABALLOT."),2,10) as update_roster_status,R.ROSTER_ID FROM (SELECT ROSTER_STATUS,ROSTER_ID
					 FROM rosters WHERE ROSTER_ID='$roster_id') R";
				}
				elseif($billable_confirm =='yes')
				{
					$sql="SELECT R.ROSTER_STATUS as previous_status,R.*,if(ROSTER_STATUS!='',conv(bin(R.addstatus)+bin($RS_CABALLOT),2,10),1) as 	update_roster_status
					FROM (SELECT ROSTER_STATUS,ROSTER_ID, 
					if(ROSTER_STATUS!='',conv(bin(ROSTER_STATUS)+bin($RS_BILLABLE),2,10),0) as addstatus 
					FROM rosters WHERE ROSTER_ID = '$roster_id') R";
				}
				elseif($billable_confirm=='no')
				{
					/* $sql="SELECT R.ROSTER_STATUS as previous_status,R.*,if(ROSTER_STATUS!='',conv(bin(R.addstatus)+bin($RS_CABALLOT),2,10),1) as 	update_roster_status
					FROM (SELECT ROSTER_STATUS,ROSTER_ID, 
					if(ROSTER_STATUS!='',conv(bin(ROSTER_STATUS)+bin($RS_BILLABLE),2,10),0) as addstatus 
					FROM rosters WHERE ROSTER_ID = '$roster_id') R"; */
					
					$sql = "SELECT R.ROSTER_STATUS as previous_status,conv(bin(R.ROSTER_STATUS)+bin(".$RS_CABALLOT."),2,10) as update_roster_status,R.ROSTER_ID FROM (SELECT ROSTER_STATUS,ROSTER_ID
					FROM rosters WHERE ROSTER_ID='$roster_id') R";
				}
				$roster_status = DB::select($sql);
				$upstatus = $roster_status[0]->update_roster_status;
				$previous_status = $roster_status[0]->previous_status;
				$ROSTER_ID = $roster_status[0]->ROSTER_ID;
				//$obj=new TripcloseController;
				//$roster_table_sts=$obj->check_roster_status($roster_id);
				//if(in_array($roster_table_sts,$RS_TOTALALLOT)==false)
				if(in_array($upstatus,$RS_TOTALALLOT)==true)
				{
					$arr=array("ROSTER_STATUS" => $upstatus,"CAB_ID"=>$cab_id,"UPDATED_BY"=>$user_id,"updated_at"=>date('Y-m-d H:i:s'));
					$update_roster=Roster::where("ROSTER_ID","=",$roster_id)->update($arr);
					
					if($chk_first_pickup=='')
					{
						$chk_first_pickup='N';
					}
					$insert_billing = array("CAB_ID" => $cab_id, "BRANCH_ID" => $branch_id, "ROSTER_ID" => $roster_id,
						"VENDOR_ID" => $vendor_id, "TRIP_TYPE"=>$get_trip_type,
						"CREATED_BY" => $user_id, "CREATED_AT" => date('Y-m-d H:i:s'),"TRIP_STATUS"=>$chk_first_pickup,"ROSTER_STATUS"=>$upstatus,"TARIFF_ID"=>$tarrif_id);
					$insert_billing_result = Driver_Billing_Summary::insert($insert_billing);
					/* LOG */	
					
					$date_f=$this->commonFunction->date_format_add();
					$log_arr=array("ROSTER_ID"=>$roster_id,"BRANCH_ID"=>$branch_id,"ACTION"=>'ManualTripclose/CABALLOT',"PROCESS_DATE"=>$date_f,"CATEGORY"=>'Manual Tripclose',"USER_ID"=>$user_id,"SELECTED_VENDOR"=>$vendor_id,"UPDATED_STATUS"=>$upstatus,"PREVIOUSED_STATUS"=>$previous_status,"UPDATE_ROSTER_SUCCESS"=>$update_roster,"Billable"=>$billable_confirm);
					//$res=$this->commonFunction->weblogs($log_arr);
					/* LOG */
				}
				


            return response([
                'success' => true,
                'status' => 3,
                'message' => "Cab Allot successfully updated",
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'Tripclose Pagination Unsuccessfull' : 'Tripclose Pagination Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ],500);
        }
    } 

    public function tripclose_cab_allot_vehicle_list($request, bool $active = true): FoundationApplication|Response|ResponseFactory
    {
        try {
            $perPage = $request->input('per_page', MyHelper::$PAGINATION_PER_PAGE);
            $branch_id = Auth::user()->BRANCH_ID;
            $activeStatus = $active ? MyHelper::$RS_ACTIVE : MyHelper::$RS_INACTIVE;

           $authUser = Auth::user();

           $branch_id=Auth::user()->BRANCH_ID;
           $vendor_id=$request->vendor_id;
           $roster_id=$request->roster_id;
           $cab_status=$request->cab_status;
           $get_trip_type=$request->get_trip_type;
           $data=DB::table('cab as C')
                    ->join('vehicles as V', 'V.VEHICLE_ID', '=', 'C.VEHICLE_ID')
                    ->join('vehicle_models as VM', 'VM.VEHICLE_MODEL_ID', '=', 'V.VEHICLE_MODEL_ID')
                    ->join('vendors as VD', 'VD.VENDOR_ID', '=', 'C.VENDOR_ID')
                    ->join('drivers as D', 'D.DRIVERS_ID', '=', 'C.DRIVER_ID')
                    ->leftJoin('rosters as RS', 'RS.roster_id', '=', DB::Raw($roster_id))
                    ->select(
                        'D.DRIVER_MOBILE',
                        'RS.PASSENGER_ALLOT_COUNT',
                        'C.VENDOR_ID',
                        'C.CAB_ID',
                        'C.VEHICLE_ID',
                        'VM.CAPACITY',
                        'VM.MODEL',
                        'VD.NAME',
                        'V.VEHICLE_REG_NO'
                    )
                    ->where('C.ACTIVE', 1)
                    ->where('C.VENDOR_ID', $vendor_id)
                    ->where('C.BRANCH_ID', $branch_id)
                    ->where('CAB_STATUS', $cab_status);
         //  $result=DB::select($sql);
           
         
               
            $filterModel = $request->input('filterModel');
            if ($filterModel) {
                foreach ($filterModel as $field => $filter) {
                    if (isset($filter['filter']) && $filter['filter'] !== '') {
                        $value = $filter['filter'];
                        $type = $filter['type'];

                        switch ($field) {
                            case 'VEHICLE_REG_NO':
                                $data->where('V.VEHICLE_REG_NO', 'like', "%{$value}%");
                                break;
							case 'DRIVERS_NAME':
                                $data->where('DR.DRIVERS_NAME', 'like', "%{$value}%");
                                break;
                        }
                    }
                }
            }


            if ($request->has('orderBy') && !empty($request->input('orderBy'))) {
                $orderBy = $request->input('orderBy');
                $order = $request->input('order', 'asc');
                $data->orderBy($orderBy, $order);
            } else {
                $data->orderBy('V.VEHICLE_REG_NO', 'ASC');
            }


            if ($perPage === MyHelper::$PAGINATION_PER_PAGE_ALL) {
                $paginateddata = $data->paginate($data->count());
            } else {
                $perPage = is_numeric($perPage) ? (int)$perPage : MyHelper::$PAGINATION_PER_PAGE;
                $paginateddata = $data->paginate($perPage);
            }

            return response([
                'success' => true,
                'status' => 3,
                'tripclose_cab_allot_vehicle_list' => $paginateddata,
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'Tripclose Pagination Unsuccessful' : 'Tripclose Pagination Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ],500);
        }
    } 
    
    
    /**
     * @throws ConnectionException
     */
    public function dataForCreateTripclose(): FoundationApplication|Response|ResponseFactory
    {

        try {
            $user = Auth::user();
            $RS_ACTIVE = MyHelper::$RS_ACTIVE;
            $branchId = $user->BRANCH_ID;
            $vendor_id = $user->vendor_id;
            $curdate = date("Y-m-d");

            $noshow_reason=DB::table("reason_master")->select('REASON','REASON_ID')
            ->where([["active","=",$RS_ACTIVE],["CATEGORY","=",'WebNoShow'],["BRANCH_ID","=",$branchId]])->get();

            $tripclose_reason=DB::table("reason_master")->select('REASON','REASON_ID')
			 	->where([["active","=",$RS_ACTIVE],["CATEGORY","=",'ManualTripClose'],["BRANCH_ID","=",$branchId]])->get();

                $reason = DB::table('reason_master')
                ->select('REASON_ID', 'REASON')
                ->where('ACTIVE', $RS_ACTIVE)
                ->where('CATEGORY', 'TariffType')
                ->where('BRANCH_ID', $branchId)
                ->get()
                ->map(function ($item) {
                    return [
                        'tariff_id' => $item->REASON_ID,
                        'tariff_value' => $item->REASON
                    ];
                });
                
                $employeeStatus = [
                    [
                        'option' => 'Picked',
                        'value' => 'picked',
                    ],
                    [
                        'option' => 'NoShow',
                        'value' => 'noshow',
                    ]
                ];
                
                
                 
                $tripStatus = [
                    [
                        'option' => 'Last Trip',
                        'value' => 'LT',
                    ],
                    [
                        'option' => 'First Trip',
                        'value' => 'FT',
                    ],
                    [
                        'option' => 'Round Trip',
                        'value' => 'N',
                    ],
                    [
                        'option' => 'Drop & Pickup',
                        'value' => 'DP',
                    ]
                ];
                if(Auth::user()->user_type==MyHelper::$ADMIN)
                {
                       $vendor_list=vendor::where('ACTIVE','=',$RS_ACTIVE)->where("BRANCH_ID","=",$branchId)->get();
                }
                elseif(Auth::user()->user_type==MyHelper::$VENDOR)
                {
                    $vendor_list=vendor::where('ACTIVE','=',$RS_ACTIVE)->where("VENDOR_ID","=",$vendor_id)->get();
                }
                    //    $sql="select ROSTER_STATUS,DATE_FORMAT(ESTIMATE_END_TIME,'%H:%i:%s') as logindatetime2,ROSTER_ID,ESTIMATE_END_TIME,
                    //     case
                    //        when TRIP_TYPE='P'  then time(ESTIMATE_END_TIME)
                    //        when TRIP_TYPE='D' then time(ESTIMATE_START_TIME)
                    //    END logindatetime
                    //     from rosters where (date(ESTIMATE_END_TIME)='".date("Y-m-d")."' OR date(ESTIMATE_START_TIME)='".date("Y-m-d")."') and ACTIVE=1 and BRANCH_ID=".$branchId."  group by logindatetime";
                    //    $datetime = DB::select($sql);
                    $logintime = DB::table('rosters')
                     ->select('ROSTER_STATUS',DB::raw("DATE_FORMAT(ESTIMATE_END_TIME, '%H:%i:%s') as logindatetime2"),'ROSTER_ID','ESTIMATE_END_TIME',
                     DB::raw("CASE 
                                 WHEN TRIP_TYPE = 'P' THEN TIME(ESTIMATE_END_TIME) 
                                 WHEN TRIP_TYPE = 'D' THEN TIME(ESTIMATE_START_TIME) 
                                  END as logintime")
                     )
                     ->where(function ($query) {
                         $query->whereDate('ESTIMATE_END_TIME', date("Y-m-d"))
                             ->orWhereDate('ESTIMATE_START_TIME', date("Y-m-d"));
                     })
                     ->where('ACTIVE', 1)
                     ->where('BRANCH_ID', $branchId)
                     ->groupBy('logintime')
                     ->get();

            $branch = DB::table('branch')
                ->select('BRANCH_ID', 'DIVISION_ID','ORG_ID','BRANCH_NAME')
                ->where('ACTIVE', $RS_ACTIVE)
                ->where('BRANCH_ID', $branchId)
                ->get();
                
				
			$vendor = DB::table('vendors')
                ->select('VENDOR_ID', 'NAME as vendor_name')
                ->where('ACTIVE', $RS_ACTIVE)
				->where('BRANCH_ID', $branchId)
                ->get();

                $shift_time = collect([
                    //['value' => 'AllShift', 'option' => 'AllShift'],
                    ['value' => 'AllShift', 'option' => 'Billable Tripclosed'],
                    ['value' => 'Pending', 'option' => 'Pending'],
                    ['value' => 'Closed', 'option' => 'Closed'],
                ])->merge(
                    $logintime->map(function ($time) {
                        return [
                            'value' => $time->logintime, 
                            'option' => $time->logintime,
                            'ROSTER_ID' => $time->ROSTER_ID,
                            'ROSTER_STATUS' => $time->ROSTER_STATUS,
                            'logindatetime2' => $time->logindatetime2,
                            'ESTIMATE_END_TIME' => $time->ESTIMATE_END_TIME,
                        ];
                    })
                );
                


                 $cab_status = array(
                    array(
                         'value' => 1,
                         'name' => 'Non-Inducted'
                     ),
                     array(
                         'value' => 0,
                         'name' => 'Inducted'
                     )
                 );
				

             

                return response([
                'success' => true,
                'status' => 3,
                'vendor_list' => $vendor_list,
                'tariff_type' => $reason,
                'logintime' => $logintime,
                'shift_time' => $shift_time,
                'tripclose_reason' => $tripclose_reason,
                'employeeStatus' => $employeeStatus,
                'tripStatus' => $tripStatus,
                'cab_status' => $cab_status,
                'noshow_reason' => $noshow_reason,
                
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Data For Create for Tripclose  Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ],500);
        }
    }
    public function tripclose_date_wise_shiftlogin($request): FoundationApplication|Response|ResponseFactory
    {

        try {
            $user = Auth::user();
            $RS_ACTIVE = MyHelper::$RS_ACTIVE;
            $branchId = $user->BRANCH_ID;
            $vendor_id = $request->vendor_id;
            $selected_date = $request->selected_date;
            $curdate = date("Y-m-d");
                if(Auth::user()->user_type==MyHelper::$ADMIN)
                {
                       $vendor_list=vendor::where('ACTIVE','=',$RS_ACTIVE)->where("BRANCH_ID","=",$branchId)->get();
                }
                elseif(Auth::user()->user_type==MyHelper::$VENDOR)
                {
                    $vendor_list=vendor::where('ACTIVE','=',$RS_ACTIVE)->where("VENDOR_ID","=",$vendor_id)->get();
                }
                   
                    $logintime = DB::table('rosters')
                     ->select('ROSTER_STATUS',DB::raw("DATE_FORMAT(ESTIMATE_END_TIME, '%H:%i:%s') as logindatetime2"),'ROSTER_ID','ESTIMATE_END_TIME',
                     DB::raw("CASE 
                                 WHEN TRIP_TYPE = 'P' THEN TIME(ESTIMATE_END_TIME) 
                                 WHEN TRIP_TYPE = 'D' THEN TIME(ESTIMATE_START_TIME) 
                                  END as logintime")
                     )
                     ->where(function ($query) use ($selected_date) {
                         $query->whereDate('ESTIMATE_END_TIME', $selected_date)
                             ->orWhereDate('ESTIMATE_START_TIME',  $selected_date);
                     })
                     ->where('ACTIVE', 1)
                  //   ->where('BRANCH_ID', 18)
                     ->where('BRANCH_ID', $branchId)
                     ->where('VENDOR_ID', $vendor_id)
                     ->groupBy('logintime')
                     ->get();

          
                $shift_time = collect([
                    ['value' => 'AllShift', 'option' => 'Billable Tripclosed'],
                    ['value' => 'Pending', 'option' => 'Pending'],
                    ['value' => 'Closed', 'option' => 'Closed'],
                ])->merge(
                    $logintime->map(function ($time) {
                        return [
                            'value' => $time->logintime, 
                            'option' => $time->logintime,
                            'ROSTER_ID' => $time->ROSTER_ID,
                            'ROSTER_STATUS' => $time->ROSTER_STATUS,
                            'logindatetime2' => $time->logindatetime2,
                            'ESTIMATE_END_TIME' => $time->ESTIMATE_END_TIME,
                        ];
                    })
                );



             

                return response([
                'success' => true,
                'status' => 3,
                'logintime' => $logintime,
                'shift_time' => $shift_time,
                
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Datewise shift time Tripclose  Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ],500);
        }
    }

    
    public function tripclose_boarded_noshow($request): FoundationApplication|Response|ResponseFactory
    {

        try {
                $date = Carbon::now();
				$userid = Auth::user()->id;
				$branch_id=Auth::user()->BRANCH_ID;
				$current_time=date("H:i:s");
				$closetime=$request->closetime;
				$passenger_sts=$request->roster_passenger_status;
				$passenger_type=$request->passenger_type;
				$roster_id=$request->roster_id;
				$passenger_id=$request->roster_passenger_id;
				$emp_status=$request->employee_status;
				$noshow_reason=$request->noshow_reason;
				$admin_remark=$request->admin_remarks;
				$login_date=$request->employee_login_date;
				$login_date_close_time=$login_date." ".$closetime;
				$passenger_create_clubing=array(MyHelper::$RPS_CREATE,MyHelper::$RPS_CLUBBING);
				//$obj=new TripcloseController;
				$p_chked_sts=$this->check_passenger_status($passenger_id);
				$update_noshow=MyHelper::$RPS_NOSHOW;
				$update_pick_board=MyHelper::$RPS_MANUALOTP;
				$update_sts=$emp_status=='noshow'?$update_noshow:$update_pick_board;
				$remark=$emp_status=='noshow'?$noshow_reason.','.$admin_remark:$admin_remark;
				$noshow_status=explode(',',MyHelper::$RPS_TTLNOSHOW);
				$RPS_TTLARRIVAL=explode(',',MyHelper::$RPS_TTLARRIVAL);
				$RPS_TTLCABDELAY=explode(',',MyHelper::$RPS_TTLCABDELAY);
				$RPS_TTLEMPLOYEEDELAY=explode(',',MyHelper::$RPS_TTLEMPLOYEEDELAY);
				$total_drop_arrival_delay=array_merge($RPS_TTLARRIVAL,$RPS_TTLEMPLOYEEDELAY);
				$arr1=explode(',',MyHelper::$RPS_TTLSYSTEMOTP);
				$arr2=explode(',',MyHelper::$RPS_TTLEMPLOYEEDELAY_SYSTEMOTP);
				$arr3=explode(',',MyHelper::$RPS_TTLMANUALOTP);
				$arr4=array_merge($arr1,$arr2);
				$arr5=explode(',',MyHelper::$RPS_TTLCABSYSTEMOTP);
				$arr6=array_merge($arr4,$arr3);
				$total_boarded_picked_sts=array_merge($arr6,$arr5);
				$chk_sts=$emp_status=='noshow'?$noshow_status:$total_boarded_picked_sts;
				$RPS_ARRIVAL=MyHelper::$RPS_ARRIVAL;
				$RPS_CREATE=MyHelper::$RPS_CREATE;
				if(in_array($p_chked_sts,$passenger_create_clubing)==true && $emp_status!='noshow')
				{
					$sql="SELECT R.*,conv(bin(R.updatestatus)+bin(".$update_pick_board."),2,10) as upstatus FROM (SELECT ROSTER_PASSENGER_STATUS,ROSTER_ID,
					conv(bin(ROSTER_PASSENGER_STATUS)+bin($RPS_ARRIVAL),2,10) as updatestatus FROM roster_passengers
					WHERE ROSTER_PASSENGER_ID = '$passenger_id') R";
				}
				elseif($p_chked_sts=='' && $emp_status!='noshow')
				{
					
					$sql="SELECT R.*,if(ROSTER_PASSENGER_STATUS IS Null,conv(bin(R.addstatus)+bin($update_pick_board),2,10) ,conv(bin(ROSTER_PASSENGER_STATUS)+bin($RPS_CREATE),2,10)) as upstatus
					FROM (SELECT ROSTER_PASSENGER_STATUS,ROSTER_ID, 
					if(ROSTER_PASSENGER_STATUS IS Null,conv(bin(1)+bin($RPS_ARRIVAL),2,10),conv(bin(ROSTER_PASSENGER_STATUS)+bin($RPS_CREATE),2,10)) as addstatus 
					FROM roster_passengers WHERE ROSTER_PASSENGER_ID = '$passenger_id') R";
				}
				elseif($p_chked_sts=='' && $emp_status=='noshow')
				{
					
					$sql="SELECT R.*,if(ROSTER_PASSENGER_STATUS IS Null,conv(bin(R.addstatus)+bin($update_noshow),2,10) ,conv(bin(ROSTER_PASSENGER_STATUS)+bin($RPS_CREATE),2,10)) as upstatus
					FROM (SELECT ROSTER_PASSENGER_STATUS,ROSTER_ID, 
					if(ROSTER_PASSENGER_STATUS IS Null,conv(bin(0)+bin($RPS_CREATE),2,10),conv(bin(ROSTER_PASSENGER_STATUS)+bin($RPS_CREATE),2,10)) as addstatus 
					FROM roster_passengers WHERE ROSTER_PASSENGER_ID = '$passenger_id') R";
				}
				else if($emp_status=='noshow' && $passenger_type=='D' && in_array($passenger_sts,$total_drop_arrival_delay)) 
				{
					/* Drop (5,7,69,71) */
					$sql="SELECT R.ROSTER_PASSENGER_STATUS,conv(bin(".$RPS_CREATE.")+bin(".$update_noshow."),2,10) as upstatus,R.ROSTER_ID FROM  roster_passengers as R WHERE R.ROSTER_PASSENGER_ID='".$passenger_id."'";
				}
				else
				{
                     $sql = "SELECT R.ROSTER_PASSENGER_STATUS,conv(bin(R.ROSTER_PASSENGER_STATUS)+bin(".$update_sts."),2,10) as upstatus,R.ROSTER_ID FROM roster_passengers as R WHERE ROSTER_PASSENGER_ID='$passenger_id'";
				}
				$roster_Pass_status = DB::select($sql);
				 $upstatus = $roster_Pass_status[0]->upstatus;
				$ROSTER_ID = $roster_Pass_status[0]->ROSTER_ID;
				$previous_status=$roster_Pass_status[0]->ROSTER_PASSENGER_STATUS;
				$table_sts=$this->check_passenger_status($passenger_id);
				$drop_arri_boad_arr=array_merge($RPS_TTLARRIVAL,$RPS_TTLCABDELAY,$RPS_TTLEMPLOYEEDELAY);
				
				DB::update("update driver_billing_summary set UPDATED_AT='".date('Y-m-d H:i:s')."',FIRST_POINT_DATETIME='".date("Y-m-d H:i:s")."',FIRST_POINT_LAT='0',FIRST_POINT_LONG='0' where FIRST_POINT_LAT is NULL and FIRST_POINT_LONG is NULL and  FIRST_POINT_DATETIME is NULL and ROSTER_ID='".$roster_id."'");
				
				if(in_array($upstatus,$chk_sts)==true)
				{
					/* $arr=array("ROSTER_PASSENGER_STATUS" => $upstatus,"REMARKS"=>$remark);
					$result = RosterPassenger::where("ROSTER_PASSENGER_ID", "=", $passenger_id)->update($arr); */
					if($emp_status=='noshow')
					{
						$sql_r="select if(R.PASSENGER_CLUBING_COUNT > 0,'PASSENGER_CLUBING_COUNT','PASSENGER_ALLOT_COUNT') AS decrementfield from rosters R where R.ROSTER_ID='".$ROSTER_ID."'";
						$dec_result=DB::select($sql_r);
						$decrementfield=$dec_result[0]->decrementfield;
						Roster::where([["ROSTER_ID", "=", $ROSTER_ID],["ACTIVE", "=", 1]])->decrement("$decrementfield");
						$update_roster=Roster::where("ROSTER_ID","=",$ROSTER_ID)->increment('PASSENGER_NOSHOW_COUNT', 1);
					}
					else
					{
						if($passenger_type=='D' && (in_array($table_sts,$drop_arri_boad_arr)==true ))
						{
							
						}
						else
						{
							$update_roster=Roster::where("ROSTER_ID","=",$ROSTER_ID)->increment('PASSENGER_ALLOT_IN_ROUT_COUNT', 1);
						}
						//$update_roster=Roster::where("ROSTER_ID","=",$ROSTER_ID)->increment('PASSENGER_ALLOT_IN_ROUT_COUNT', 1);
					}
					if($passenger_type=='P')
					{
						$arr=array("ROSTER_PASSENGER_STATUS" => $upstatus,"REMARKS"=>$remark,"ACTUAL_START_TIME" => $login_date_close_time,"UPDATED_BY"=>$userid);
						$result = RosterPassenger::where("ROSTER_PASSENGER_ID", "=", $passenger_id)->update($arr);
					}
					else
					{
						$arr=array("ROSTER_PASSENGER_STATUS" => $upstatus,"REMARKS"=>$remark,"ACTUAL_END_TIME" => $login_date_close_time,"UPDATED_BY"=>$userid);
						$result = RosterPassenger::where("ROSTER_PASSENGER_ID", "=", $passenger_id)->update($arr);
					}
					$obj2=new CommonFunction();
						$mask_enable = $obj2->GetPropertyValue('CALL MASKING OPTION');
						if($mask_enable=='Y')
						{
							//$mask= new MaskNumberClearController();
							//$mask->Clear_MaskNumber($passenger_id,$branch_id,$roster_id,'Passenger');
						}
					
					
					/* LOG */	
					$passenger_action='';
					$passenger_action=$emp_status=='noshow'?'NOSHOW':'PICKED/BOARDED';
					$date_f=$obj2->date_format_add();
					$log_arr=array("ROSTER_ID"=>$roster_id,"BRANCH_ID"=>$branch_id,"ACTION"=>'ManualTripclose/'.$passenger_action,"PROCESS_DATE"=>$date_f,"CATEGORY"=>'Manual Tripclose',"USER_ID"=>$userid,"UPDATED_STATUS"=>$upstatus,"PREVIOUSED_STATUS"=>$previous_status,"REMARKS"=>$remark,"UPDATE_RosterPassenger_SUCCESS"=>$result,"ROSTER_PASSENGER_ID"=>$passenger_id,"TRIP_TYPE"=>$passenger_type);
					
					//$res=$this->commonFunction->weblogs($log_arr);
					//$obj3->noshow_change_roster_approve_km($roster_id,$passenger_type);
					/* LOG */
				}
				
				//return $this->total_passenger_list($roster_id);


             

                return response([
                'success' => true,
                'status' => 3,
                'message' => $passenger_action." Successfully updated",
                
                
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Roster Passenger action Tripclose  Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ],500);
        }
    }
    public function check_passenger_status($passenger_id)
	{
		$sql2="SELECT ROSTER_PASSENGER_STATUS,ROSTER_ID FROM roster_passengers WHERE ROSTER_PASSENGER_ID='$passenger_id'";
		$check_sts= DB::select($sql2);
		return $checked_sts=$check_sts[0]->ROSTER_PASSENGER_STATUS;
	}
	
    public function total_passenger_list($request): FoundationApplication|Response|ResponseFactory
    {

        try {
            $manual_trip_close_is_enable=true;
            $branch_id=Auth::user()->BRANCH_ID;
            $roster_id=$request->roster_id;
            $RS_ACTIVE = MyHelper::$RS_ACTIVE;
            $RPS_NOSHOW=MyHelper::$RPS_NOSHOW;
            $RP_DROPROUTE=MyHelper::$RP_DROPROUTE;
            $arr1=explode(',',MyHelper::$RPS_TTLSYSTEMOTP);
            //$arr2=explode(',',MyHelper::$RPS_TTLEMPLOYEEDELAY);
            //$arr2=array();
            $arr2=explode(',',MyHelper::$RPS_TTLCABSYSTEMOTP);
            $arr3=explode(',',MyHelper::$RPS_TTLMANUALOTP);
            $arr4=explode(',',MyHelper::$RPS_TTLCABEMPLOYEEDELAY);
            $arr7=explode(',',MyHelper::$RPS_TTLEMPLOYEEDELAY_SYSTEMOTP);
            $RPS_SAFE_DROP_OTP_ARR=explode(',',MyHelper::$RPS_SAFE_DROP_OTP);
            $arr5=array_merge($arr1,$arr2);
            $arr6=array_merge($arr3,$arr4);
            $total_sts1=array_merge($arr5,$arr6);
            $total_sts=array_merge($arr7,$total_sts1,$RPS_SAFE_DROP_OTP_ARR);
            //DB::enableQueryLog();
            $noshow_sts=explode(',',MyHelper::$RPS_TTLNOSHOW);

            $trip_type = DB::table("rosters as R")->select("R.TRIP_TYPE")
						->where("R.ROSTER_ID", "=", $roster_id)->get();
		   // $order = $trip_type[0]->TRIP_TYPE == 'P' ? 'DESC' : 'ASC';

			$order = 'ASC'; 
			if(isset($trip_type[0]) && $trip_type[0]->TRIP_TYPE == 'P'){
				$order = 'DESC';
			}  
            $data=RosterPassenger::query()->select(DB::raw("COUNT(roster_passengers.ROSTER_PASSENGER_ID) as emp_count"),DB::raw("SUM(if(roster_passengers.ROSTER_PASSENGER_STATUS & $RPS_NOSHOW,1,0)) as noshowcnt"),"roster_passengers.ROSTER_PASSENGER_STATUS","roster_passengers.ROSTER_ID","roster_passengers.ROUTE_ORDER","roster_passengers.ROSTER_PASSENGER_ID","roster_passengers.ESTIMATE_START_TIME","roster_passengers.DRIVER_ARRIVAL_TIME","RS.TRIP_APPROVED_KM","RS.ROUTE_ID","RS.TOTAL_KM","RS.TRIP_TYPE","RS.CAB_ID","roster_passengers.ACTUAL_START_TIME","RS.ACTUAL_START_TIME AS driver_start_time","RS.ROSTER_STATUS","RS.ACTUAL_END_TIME AS destination_time","roster_passengers.ACTUAL_END_TIME","L.LOCATION_NAME","roster_passengers.ESTIMATE_END_TIME","roster_passengers.EMPLOYEE_ID","EM.NAME","EM.MOBILE","BR.BRANCH_NAME",DB::raw("if(RS.TRIP_TYPE='P',date(RS.ESTIMATE_END_TIME),date(RS.ESTIMATE_START_TIME)) as ROSTER_TIME"),DB::raw("if(RS.TRIP_TYPE='P',RS.ESTIMATE_END_TIME,RS.ESTIMATE_START_TIME) as LOGIN_TIME"))
            ->join('employees as EM',"EM.EMPLOYEES_ID","=","roster_passengers.EMPLOYEE_ID")
            ->join('rosters as RS',"RS.ROSTER_ID","=","roster_passengers.ROSTER_ID")
            ->join('branch as BR',"BR.BRANCH_ID","=","RS.BRANCH_ID")
            ->join('locations as L',"L.LOCATION_ID","=","roster_passengers.LOCATION_ID")
            ->where("roster_passengers.ROSTER_ID","=",$roster_id)->where("RS.BRANCH_ID","=",$branch_id)->where("EM.BRANCH_ID","=",$branch_id)->where("roster_passengers.ACTIVE","=",$RS_ACTIVE)
            ->groupby("roster_passengers.ROSTER_PASSENGER_ID");
           // ->get();
           // print_r($data);exit;

          $filterModel = $request->input('filterModel');
          if ($filterModel) {
              foreach ($filterModel as $field => $filter) {
                  if (isset($filter['filter']) && $filter['filter'] !== '') {
                      $value = $filter['filter'];
                      $type = $filter['type'];

                      switch ($field) {
                          case 'LOCATION_NAME':
                              $data->where('L.LOCATION_NAME', 'like', "%{$value}%");
                              break;
                          case 'NAME':
                              $data->where('EM.NAME', 'like', "%{$value}%");
                              break;
                          case 'MOBILE':
                              $data->where('EM.MOBILE', 'like', "%{$value}%");
                              break;
                      }
                  }
              }
          }


          if ($request->has('orderBy') && !empty($request->input('orderBy'))) {
              $orderBy = $request->input('orderBy');
              $order = $request->input('order', 'asc');
              $data->orderBy($orderBy, $order);
          } else {
              $data->orderBy('roster_passengers.ROUTE_ORDER', $order);
          }
          $perPage = $request->input('per_page', MyHelper::$PAGINATION_PER_PAGE);

          if ($perPage === MyHelper::$PAGINATION_PER_PAGE_ALL) {
              $paginateddata = $data->paginate($data->count());
          } else {
              $perPage = is_numeric($perPage) ? (int)$perPage : MyHelper::$PAGINATION_PER_PAGE;
              $paginateddata = $data->paginate($perPage);
          }

          $paginateddata->getCollection()->transform(function ($dataV) use (&$manual_trip_close_is_enable) {
            $action_button_is_enable = false;
        
          
            if (in_array($dataV->ROSTER_PASSENGER_STATUS, [1, 5]) && $dataV->CAB_ID !== '') {
                $action_button_is_enable = true;
            }
        
            // If the action button is enabled for any passenger, disable manual trip close
            if ($action_button_is_enable) {
                $manual_trip_close_is_enable = false;
            }
        
            $dataV->action_button_is_enable = $action_button_is_enable;
        
            return $dataV;
        });

                return response([
                'success' => true,
                'status' => 3,
                'data' => $paginateddata,
                'manual_trip_close_is_enable' => $manual_trip_close_is_enable,
                
                
        ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Data For Create for Tripclose  Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ],500);
        }
    }

    
	

   
}
