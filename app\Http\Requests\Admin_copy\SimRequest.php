<?php

//namespace App\Http\Requests\Admin;
namespace App\Http\Requests\Admin;

use App\Helpers\CommonFunction;
use Closure;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;

class SimRequest extends FormRequest
{
    protected CommonFunction $commonFunction;

    public function __construct(CommonFunction $commonFunction)
    {
        
    }

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        
        return [
            'SIM_MOBILE_NO' => [
                'required',
                'numeric',
                 Rule::unique('sim', 'SIM_MOBILE_NO')
            ],
            'SIM_SERIAL_NO' => 'required',
            'SIM_PROVIDER' => 'required',
           
        ];
    }

    public function messages(): array
    {
        return [
            'SIM_MOBILE_NO.required' => 'Sim Mobile number field is required',
            'SIM_SERIAL_NO.required' => 'Sim Serial number field is required',
            'SIM_PROVIDER.required' => 'Sim Provider field is required',
        ];
    }
   

    public function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json([
            'success' => false,
            'validation_error' => true,
            'message' => 'Validation errors',
            'data' => $validator->errors()
        ]));
    }

    private function checkDeactivatedSim($branchId): Closure
    {
        return function (string $attribute, mixed $value, Closure $fail) use ($branchId) {
            $deactivatedSim = DB::table("sim")
                ->where("SIM_ID", $value)
                ->where("ACTIVE", 2)
                ->exists();

            if ($deactivatedSim) {
                $fail("Sim already exists and is deactivated.");
            }
        };
    }

   
}
