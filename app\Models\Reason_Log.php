<?php

namespace App\Models;

use App\Helpers\CommonFunction;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Reason_Log extends Model
{
    use HasFactory;

    protected $table = 'reason_log';
    protected $primaryKey = 'LOG_ID';

    //protected $appends = ['passenger_status'];

    public $timestamps = false;

    protected $fillable = [
        'ROSTER_PASSENGER_ID',
        'REASON_ID',
        'CREATE_BY',
        'CREATED_DATE',
        'REMARKS'
    ];
    
}
