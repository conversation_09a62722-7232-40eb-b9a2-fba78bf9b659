<?php
namespace App\Services\Admin;

use App\Helpers\CommonFunction;
use App\Helpers\MyHelper;
use App\Models\Branch;
use Carbon\Carbon;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;


class TransportDataService
{
    protected CommonFunction $commonFunction;
    protected ?Branch $currentBranch = null;

    public function __construct(CommonFunction $commonFunction, RosterUploadService $rosteruploadservice)
    {
        $this->commonFunction = $commonFunction;
    }

    public function Support(): FoundationApplication|Response|ResponseFactory{
        try {
            $branch_id=Auth::user()->BRANCH_ID;
            
            $data = DB::table("support_log as SL")->select("SL.*", "E.NAME","E.id", "E.EMAIL","E.HOME_EDIT_STATUS", "R.SUB_SUPPORT_CATEGORY")
                            ->join("employees as E", 'E.EMPLOYEES_ID', "=", 'SL.EMPLOYEE_ID')
                            ->join("reason_master as R", 'R.REASON_ID', "=", 'SL.REASON_ID')
                            ->whereNotIn("R.CATEGORY", ['Shuttle'])
                            ->where("SL.BRANCH_ID", "=", $branch_id)
                            ->where("E.BRANCH_ID", "=", $branch_id)
                            ->orderBy("SL.PROCESS_TIME", 'DESC')->get();

            foreach($data as $value){
                $value->NAME = $this->commonFunction->AES_DECRYPT($value->NAME, config('app.aes_encrypt_key'));
                $value->EMAIL = $this->commonFunction->AES_DECRYPT($value->EMAIL, config('app.aes_encrypt_key'));
            }

            return response([
                'success' => true,
                'status' => 1,
                'support_list' => $data,
                'message' => 'Support Data Fetch Successfully!',
            ], 200);
        } catch (\Throwable|\Exception $e) {
            DB::rollBack();
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'message' => 'Support Data Fetch Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function PostSuportUpdate($request): FoundationApplication|Response|ResponseFactory
    {
        try {
            $support_id = $request->support_id;
            $emp_id = $request->emp_id;
            $remarks = $request->remarks;
            $id = Auth::user()->id;
            $date = date("Y-m-d H:i:s");

            $arr = array("SUPPORT_ID" => $support_id, "ACTION_REMARKS" => $remarks, "EMPLOYEE_ID" => $emp_id, "CREATED_BY" => $id, "CREATED_DATE" => $date);
            $result = DB::table("shuttle_support_remarks")->insert($arr);

            $update = DB::table("support_log")->where("SUPPORT_ID", "=", $support_id)->update(array("ACTION_STATUS" => 2, "UPDATED_BY" => $id, "UPDATED_DATE" => $date));

            if ($result == 1) {
                return response([
                    'success' => true,
                    'status' => 1,
                    'message' => 'Support Data Inserted Successfully!',
                ], 200);
            }else{
                return response([
                    'success' => true,
                    'status' => 0,
                   'message' => 'Support Data Insertion Failed!',
                ], 200);
            }
        } catch (\Throwable|\Exception $e) {
            DB::rollBack();
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'message' => 'Support Data Fetch Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function SupportRemarks($request): FoundationApplication|Response|ResponseFactory
    {
        try {
            $reopen_support_id = $request->reopen_support_id;

            $result = DB::table("shuttle_support_remarks as S")->select("S.*")->where("SUPPORT_ID", "=", $reopen_support_id)->get();

            if (count($result) > 0) {
                return response([
                   'success' => true,
                   'status' => 1,
                   'support_remarks' => $result,
                   'message' => 'Support Remarks Fetch Successfully!',
                ], 200);
            } else {
                return response([
                   'success' => true,
                   'status' => 0,
                   'message' => 'No Support Remarks Found!',
                ], 200);
            }
        } catch (\Throwable|\Exception $e) {
            DB::rollBack();
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'message' => 'Support Remarks Fetch Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function SupportApproval($request): FoundationApplication|Response|ResponseFactory
    {
        try {
            $employee_auto_id = $request->employee_auto_id;
            $support_emp_id = $request->support_emp_id;
            $id = Auth::user()->id;
            $branch_id = Auth::user()->BRANCH_ID;
            $date = date("Y-m-d H:i:s");
            
            $update = DB::table("support_log")
            //->where("SUPPORT_ID", "=", $support_log_id)
            ->where("EMPLOYEE_ID", "=", $support_emp_id)
            ->where("BRANCH_ID", "=", $branch_id)
            ->update(array("ACTION_STATUS" => 4, "UPDATED_BY" => $id, "UPDATED_DATE" => $date));
            
            $update = DB::table("employees")->where("id", "=",$employee_auto_id )->update(array("HOME_EDIT_STATUS" => 0, "UPDATED_BY" => $id, "updated_at" => $date));
            
            $data = DB::table("employees as E")->select("E.EMPLOYEES_ID","E.id", "E.EMAIL","E.HOME_EDIT_STATUS","E.MOBILE_GCM","E.MOBILE_CATEGORY")
            ->where("E.id", "=", $employee_auto_id)->get();
            if(count($data)>0)
            {
                $EMPLOYEES_ID=$data[0]->EMPLOYEES_ID;
                $MOBILE_GCM=$data[0]->MOBILE_GCM;
                $MOBILE_CATEGORY=$data[0]->MOBILE_CATEGORY;
                
                $HEADING='Home Location Update';
                $MSG='Admin approved to change your home location in ZINGO CORP app go to profile and edit location';
                if($MOBILE_GCM!='')
                {
                    $emp_notification=DB::insert("INSERT INTO `notification` (`branch_id`, `empid_cabid`, `fcm_id`, `heading`, `message`, `category_value`,`app_category`,`created_at`) VALUES ('".$branch_id."','".$EMPLOYEES_ID."','".$MOBILE_GCM."','".$HEADING."','".$MSG."','4','".$MOBILE_CATEGORY."','".date('Y-m-d H:i:s')."')");
                }
            }
            
            if ($update == 1) {
                return response([
                    'success' => true,
                    'status' => 1,
                    'message' => 'Approval Successfully Updated!',
                 ], 200);
            } else {
                return response([
                    'success' => true,
                   'status' => 0,
                   'message' => 'Approval Failed to Update!',
                ], 200);
            }
        } catch (\Throwable|\Exception $e) {
            DB::rollBack();
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'message' => 'Approval Update Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function PostReopenSuportUpdate($request): FoundationApplication|Response|ResponseFactory
    {
        try {
            $reopen_support_id = $request->reopen_support_id;
            $reopen_remarks = $request->reopen_remarks;
            $reopen_emp_id = $request->reopen_emp_id;
            $id = Auth::user()->id;
            $date = date("Y-m-d H:i:s");
            $arr = array("SUPPORT_ID" => $reopen_support_id, "REOPEN_REMARKS" => $reopen_remarks, "EMPLOYEE_ID" => $reopen_emp_id, "CREATED_BY" => $id, "CREATED_DATE" => $date);
            $result = DB::table("shuttle_support_remarks")->insert($arr);
            $update = DB::table("support_log")->where("SUPPORT_ID", "=", $reopen_support_id)->update(array("ACTION_STATUS" => 3, "UPDATED_BY" => $id, "UPDATED_DATE" => $date));
            if ($result == 1) {
                return response([
                    'success' => true,
                    'status' => 1,
                    'message' => 'Support Reopen Successfully Updated!',
                 ], 200);
            } else {
                return response([
                    'success' => true,
                    'status' => 0,
                    'message' => 'Support Reopen Failed to Update!',
                ], 200);
            }

        } catch (\Throwable|\Exception $e) {
            DB::rollBack();
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'message' => 'Post Reopen Update Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}