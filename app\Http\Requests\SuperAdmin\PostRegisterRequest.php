<?php

namespace App\Http\Requests\SuperAdmin;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Contracts\Validation\Validator;

class PostRegisterRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'branch_id' => 'required',
            'vendor_id' => 'required',
            'user_type' => 'required',
            'name' => 'required',
            'email' => ['required', 'email', 'unique:users,email'],
            'password' => ['required','min:8'],
            'password_confirmation' => ['required','same:password'],
        ];
    }

    public function messages(): array
    {
        return [
            'branch_id.required' => 'Branch ID is required.',
            'vendor_id.required' => 'Vendor ID is required.',
            'user_type.required' => 'User type is required.',
            'name.required' => 'Name is required.',
            'email.required' => 'Email is required.',
            'email.email' => 'Please enter a valid email address.',
            'email.unique' => 'Email already exists.',
            'password.required' => 'Password is required.',
            'password.min' => 'Password must be at least 8 characters long.',
            'password_confirmation.required' => 'Confirm Password is required.',
            'password_confirmation.same' => 'Password and Confirm Password do not match.',
        ];
    }

    public function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json([
           'success' => false,
            'validation_error' => true,
           'message' => 'Validation errors',
            'data' => $validator->errors()
        ]));
    }
}
