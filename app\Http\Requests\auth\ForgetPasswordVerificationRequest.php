<?php

namespace App\Http\Requests\auth;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Contracts\Validation\Validator;

class ForgetPasswordVerificationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'mobileno' => ['required', 'regex:/^[0-9]{10}$/','exists:users,name']
        ];
    }

    public function messages(): array
    {
        return [
            'name.required' => 'The mobile number is required.',
            'name.regex'    => 'The mobile number must be exactly 10 digits.',
            'name.exists'   => 'Mobile number not found. Please register for an admin account.',
        ];
    }
	
    public function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json([
            'success' => false,
            'validation_error' => true,
			'status'=> 0,
            'message' => 'Validation errors',
            'data' => $validator->errors()
        ]));
    }
}
