<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;

use App\Services\Admin\VehicleTrackingService;
use App\Http\Requests\Admin\CabStatusVehicleRequest;
use App\Http\Requests\Admin\VehicleTrackingVehicleStatusRequest;

use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class VehicleTrackingController extends Controller
{
    protected VehicleTrackingService $vehicletrackingService;

    public function __construct(VehicleTrackingService $vehicletrackingService)
    {
        $this->vehicletrackingService = $vehicletrackingService;
    }
    
    public function dataForCreate(): FoundationApplication|Response|ResponseFactory
    {
        return $this->vehicletrackingService->dataForCreate();
    }
   
    public function cab_status_vehicle(CabStatusVehicleRequest $request): FoundationApplication|Response|ResponseFactory
    {
        
        return $this->vehicletrackingService->cab_status_vehicle($request);
        
    }
   
    public function vehicle_status(VehicleTrackingVehicleStatusRequest $request): FoundationApplication|Response|ResponseFactory
    {

        return $this->vehicletrackingService->vehicle_status($request);
        
    }
   
 
}
