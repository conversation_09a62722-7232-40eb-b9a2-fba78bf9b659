<?php

namespace App\Http\Controllers\EmployeeApp;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\OtpCreationRequest;
use App\Http\Requests\EmplyeeApp\GcmStoreRequest;
use App\Models\Employee;
use App\Services\Admin\EmployeeService;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class EmployeeAppController extends Controller
{

    protected EmployeeService $employeeService;

    public function __construct(EmployeeService $employeeService)
    {
        $this->employeeService = $employeeService;
    }

    public function gcmStore(GcmStoreRequest $request): JsonResponse
    {
        try {
            DB::beginTransaction();
            $user = Auth::user();
            $employee = Employee::where('user_id', $user->id)->firstOrFail();
            $employee->update(['MOBILE_GCM' => $request->gcm]);
            DB::commit();
            return response()->json([
                'success' => true,
                'message' => 'GCM Stored Successfully',
                'name' => $employee->name_decrypted,
                'email' => $employee->email_decrypted,
                'mobile' => $employee->mobile_decrypted,
            ]);
        } catch (\Throwable $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'GCM Storage Failed',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function otpCreate(OtpCreationRequest $request): Application|Response|ResponseFactory
    {
        return $this->employeeService->otpCreate($request);
    }
}
