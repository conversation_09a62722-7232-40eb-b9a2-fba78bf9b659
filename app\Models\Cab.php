<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Crypt;

class Cab extends Model
{
    use HasFactory;

    protected $table = 'cab';
    protected $primaryKey = 'CAB_ID';
	public $timestamps = false;
	protected $appends = ['CAB_ID_crypt'];
	
	protected $fillable = [
        'VEHICLE_ID',
        'DRIVER_ID',
        'DEVICE_ID',
        'SIM_ID',
        'BRANCH_ID',
        'VENDOR_ID',
        'TARIFF_TYPE',
        'COMPLIANT_STATUS',
        'ATTACHMENT_DATE',
        'AUTO_LOGOUT',
        'ACTIVE',
        'CREATED_BY',
        'created_at',
        'UPDATED_BY',
        'updated_at',
        'CANCELLED_BY',
        'CANCELLED_DATE',
        'REMARKS	',
        'APPROVED_BY',
        'APPROVED_ON',
        'REJECTED_BY',
        'REJECTED_ON',
        'CAB_STATUS',
        'LAST_PROCESS_TIME',
    ];

	public function getCABIDCryptAttribute(): string
    {
        return Crypt::encryptString($this->CAB_ID);
    }
}
