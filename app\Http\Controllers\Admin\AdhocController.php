<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;

use App\Services\Admin\AdhocService;
use App\Http\Requests\Admin\AdhocDateChangeRequest;
use App\Http\Requests\Admin\RequestedAdhocRequest;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class AdhocController extends Controller
{
    protected AdhocService $AdhocService;

    public function __construct(AdhocService $adhocService)
    {
        $this->adhocService = $adhocService;
    }
    
    public function dataForCreate(): FoundationApplication|Response|ResponseFactory
    {
        return $this->adhocService->dataForCreate();
    }
    public function getemplocation(): FoundationApplication|Response|ResponseFactory
    {

        return $this->adhocService->getemplocation();
    }
 
    public function getadhocdatechange(AdhocDateChangeRequest $request): FoundationApplication|Response|ResponseFactory
    {

        return $this->adhocService->getadhocdatechange($request);
    }
    public function getrequestedAdhoc(RequestedAdhocRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->adhocService->getrequestedAdhoc($request);
    }
 
}
