<?php

namespace App\Models;

use App\Helpers\CommonFunction;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class RosterPassenger extends Model
{
    use HasFactory;

    protected $table = 'roster_passengers';
    protected $primaryKey = 'ROSTER_PASSENGER_ID';

    protected $appends = ['passenger_status','tripstatus'];

    public $timestamps = false;

    protected $fillable = [
        'ROSTER_PASSENGER_ID',
        'EMPLOYEE_ID',
        'ROSTER_ID',
        'SHUTTLE_COMPANY_ID',
        'ESTIMATE_START_TIME',
        'DRIVER_ARRIVAL_TIME',
        'ACTUAL_START_TIME',
        'START_LAT',
        'START_LONG',
        'ACTUAL_END_TIME',
        'END_LAT',
        'END_LONG',
        'ACTIVE',
        'ROSTER_PASSENGER_STATUS',
        'ESTIMATE_END_TIME',
        'ROUTE_ORDER',
        'CREATED_BY',
        'UPDATED_BY',
        'UPDATED_FROM',
        'updated_at',
        'REMARKS',
        'PASSENGER_MASK_NUMBER',
        'LOCATION_ID',
        'NOSHOW_TYPE',
        'NOTIFICATION_STATUS',
        'ASSET_MOVEMENT'        
    ];
    protected CommonFunction $commonFunction;

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
        $this->commonFunction = new CommonFunction();
    }
public function getPassengerStatusAttribute(): ?string
{
       return $this->ROSTER_PASSENGER_STATUS ? $this->commonFunction->employee_status($this->ROSTER_PASSENGER_STATUS) : null;
     // return null;
   }
   public function getTripStatusAttribute(): ?string
    {
        return $this->ROSTER_STATUS ? $this->commonFunction->TripStatus($this->ROSTER_STATUS) : null;
	  // return null;
    }
}
