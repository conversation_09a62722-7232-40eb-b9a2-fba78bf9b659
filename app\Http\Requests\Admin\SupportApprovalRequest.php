<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Contracts\Validation\Validator;

class SupportApprovalRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'employee_auto_id' => 'required',
            'support_emp_id' => 'required',
        ];
    }

    public function messages(): array
    {
        return [
            'employee_auto_id.required' => 'Employee ID is required.',
           'support_emp_id.required' => 'Support Employee ID is required.',
        ];
    }

    public function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json([
           'success' => false,
            'validation_error' => true,
           'message' => 'Validation errors',
            'data' => $validator->errors(),
        ]));
    }
}
