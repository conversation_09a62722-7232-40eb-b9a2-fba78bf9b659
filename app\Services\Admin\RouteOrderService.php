<?php

namespace App\Services\Admin;

use App\Helpers\CommonFunction;
use App\Helpers\MyHelper;
use Carbon\Carbon;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use App\Models\Vendor;
use App\Models\Cab_allocation;
use App\Models\Roster;
use App\Models\RosterPassenger;
use App\Models\Cab;
use App\Models\Vehicle;
use App\Models\VehicleModel;
use App\Models\Driver;
use App\Models\property;
use App\Models\Driver_Billing_Summary;
use App\Http\Controllers\MaskNumberClearController;
use App\Http\Controllers\ElasticController;

class RouteOrderService
{
    protected CommonFunction $commonFunction;
    protected ?Branch $currentBranch = null;

    public function __construct(CommonFunction $commonFunction)
    {
        $this->commonFunction = $commonFunction;
    }
	     
    
    public function VendorBaseType($request): FoundationApplication|Response|ResponseFactory
    {
        try {
            $branch_id = Auth::user()->BRANCH_ID;
            $userid = Auth::user()->id;
            $db_name = Auth::user()->dbname;
            $RS_ACTIVE=MyHelper::$RS_ACTIVE;
            $curdate=date('Y-m-d');
            $active=true;
            $authUser = Auth::user();
            $RS_NEWROSTER=MyHelper::$RS_NEWROSTER;
            $RS_TOTALALLOT=MyHelper::$RS_TOTALALLOT;
            $RS_TOTALACCEPT=MyHelper::$RS_TOTALACCEPT;
            $RS_TOTALEXECUTE=MyHelper::$RS_TOTALEXECUTE;

            $selected_type=$request->selected_type;
            $selected_vendor_id=$request->selected_vendor_id;
            $curdate=date("Y-m-d");
           
            if($selected_type=='cab_allot')
                {
                    $result = DB::connection($db_name)->table('rosters as R')
                    ->select('C.CAB_ID', 'R.VENDOR_ID', 'V.VEHICLE_ID', 'V.VEHICLE_REG_NO')
                    ->join('cab as C', 'C.CAB_ID', '=', 'R.CAB_ID')
                    ->join('vehicles as V', 'V.VEHICLE_ID', '=', 'C.VEHICLE_ID')
                    ->where('R.VENDOR_ID', $selected_vendor_id)
                    ->where('R.BRANCH_ID', $branch_id)
                    ->whereRaw('DATE(R.ESTIMATE_END_TIME) = ? OR DATE(R.ESTIMATE_START_TIME) = ?', [$curdate, $curdate])
                    ->where('R.ACTIVE', 1)
                     ->whereIn('R.ROSTER_STATUS', [$RS_TOTALEXECUTE,$RS_TOTALALLOT,$RS_TOTALACCEPT]) // Uncomment if needed
                    ->distinct()
                    ->get();
                    
                }
                else if($selected_type=='cab_not_allot')
                {
                    $buffer_time=MyHelper::$SHOW_ROUTES_BUFFER_TIME;
                    $livetime = date('Y-m-d H:i:s', time() - $buffer_time); 

                    $result = DB::connection($db_name)->table('rosters as R')
                        ->select('R.ROUTE_ID', 'R.ROSTER_ID','R.TRIP_TYPE',DB::raw("IF(R.TRIP_TYPE = 'P', R.ESTIMATE_END_TIME, R.ESTIMATE_START_TIME) AS in_out") )
                        ->where('R.VENDOR_ID', $selected_vendor_id)
                        ->where('R.BRANCH_ID', $branch_id)
                        ->whereIn('R.ROSTER_STATUS', [$RS_TOTALEXECUTE,$RS_TOTALALLOT,$RS_TOTALACCEPT,$RS_NEWROSTER])
                        ->where(function ($query) use ($livetime) {
                            $query->where('R.ESTIMATE_START_TIME', '>=', $livetime)
                                ->orWhere('R.ESTIMATE_END_TIME', '>=', $livetime);
                        })
                        ->where('R.ACTIVE', 1)
                        ->get();
                   
                  
                }


         
        
            return response([
                'success' => true,
                'status' => 3,
                'data' => $result,
                
            ]);
        } catch (\Throwable|\Exception $e) {
            DB::rollBack();
            $this->commonFunction->logException($e);
            $message = $active ? 'Route order status Unsuccessful' : 'RouteOrder  status Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ],500);
        }
    } 
    public function select_cab_route($request): FoundationApplication|Response|ResponseFactory
    {
        try {
            $branch_id = Auth::user()->BRANCH_ID;
            $userid = Auth::user()->id;
            $db_name = Auth::user()->dbname;
            $RS_ACTIVE=MyHelper::$RS_ACTIVE;
            $curdate=date('Y-m-d');
            $active=true;
            $authUser = Auth::user();
            $RS_NEWROSTER=MyHelper::$RS_NEWROSTER;
            $RS_TOTALALLOT=MyHelper::$RS_TOTALALLOT;
            $RS_TOTALACCEPT=MyHelper::$RS_TOTALACCEPT;
            $RS_TOTALEXECUTE=MyHelper::$RS_TOTALEXECUTE;

            $selected_cab_id=$request->selected_cab_id;
            $selected_vendor_id=$request->selected_vendor_id;
            $curdate=date("Y-m-d");
            $buffer_time=MyHelper::$SHOW_ROUTES_BUFFER_TIME;
            $livetime = date('Y-m-d H:i:s', time() - $buffer_time); 

            $result = DB::connection($db_name)->table('rosters as R')
            ->select('R.ROUTE_ID','R.ROSTER_ID','R.TRIP_TYPE',
                DB::raw("IF(R.TRIP_TYPE = 'P', R.ESTIMATE_END_TIME, R.ESTIMATE_START_TIME) AS login_time")  )
            ->where('R.CAB_ID', $selected_cab_id)
            ->where('R.VENDOR_ID', $selected_vendor_id)
            ->where('R.BRANCH_ID', $branch_id)
            ->whereIn('R.ROSTER_STATUS', [$RS_TOTALEXECUTE,$RS_TOTALALLOT,$RS_TOTALACCEPT,$RS_NEWROSTER])
            ->where(function ($query) use ($livetime) {
                $query->where('R.ESTIMATE_START_TIME', '>=', $livetime)
                    ->orWhere('R.ESTIMATE_END_TIME', '>=', $livetime);
            })->get();
                        
        
            return response([
                'success' => true,
                'status' => 3,
                'data' => $result,
                
            ]);
        } catch (\Throwable|\Exception $e) {
            DB::rollBack();
            $this->commonFunction->logException($e);
            $message = $active ? 'Route order result Unsuccessful' : 'RouteOrder  result Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ],500);
        }
    } 




    public function select_cab_roster($request): FoundationApplication|Response|ResponseFactory
    {
        try {
            $branch_id = Auth::user()->BRANCH_ID;
            $userid = Auth::user()->id;
            $db_name = Auth::user()->dbname;
            $RS_ACTIVE=MyHelper::$RS_ACTIVE;
            $curdate=date('Y-m-d');
            $active=true;
           $authUser = Auth::user();
            $selected_roster_id=$request->selected_roster_id;

            $trip_roster=DB::connection($db_name)->table("rosters")->select("TRIP_TYPE")->where("ROSTER_ID","=",$selected_roster_id)->get();
            $trip_type=$trip_roster[0]->TRIP_TYPE;
            $order=$trip_type=='P'?'DESC':'ASC';


            $result = DB::connection($db_name)->table('roster_passengers as RP')->select("RP.ROSTER_ID", "RP.ROSTER_PASSENGER_ID", "RP.ESTIMATE_START_TIME", "RP.ROSTER_PASSENGER_STATUS", "RP.ROUTE_ORDER", "RP.DRIVER_ARRIVAL_TIME", "L.LOCATION_NAME", "BR.BRANCH_NAME", "RP.ESTIMATE_END_TIME", "RP.EMPLOYEE_ID","EM.GENDER", "EM.NAME", "EM.MOBILE")
            ->leftjoin('employees as EM', "EM.EMPLOYEES_ID", "=", "RP.EMPLOYEE_ID")
            ->leftjoin('rosters as RS', "RS.ROSTER_ID", "=", "RP.ROSTER_ID")
            ->join('branch as BR', "BR.BRANCH_ID", "=", "RS.BRANCH_ID")
            ->leftjoin('locations as L', "L.LOCATION_ID", "=", "EM.LOCATION_ID")
            ->where("RP.ROSTER_ID", "=", $selected_roster_id)
            ->where("RP.ACTIVE", "=", $RS_ACTIVE)
            ->where("EM.BRANCH_ID", "=", $branch_id)
            ->orderBy("RP.ROUTE_ORDER", $order)
            ->get();

          
         
            
            return response([
                'success' => true,
                'status' => 3,
                'result' => $result,
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            DB::rollBack();
            $message = $active ? 'Route order Unsuccessful' : 'Route order Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ],500);
        }
    } 
   
    public function change_order($request): FoundationApplication|Response|ResponseFactory
    {
        try {
            $branch_id = Auth::user()->BRANCH_ID;
            $userid = Auth::user()->id;
            $db_name = Auth::user()->dbname;
            $RS_ACTIVE=MyHelper::$RS_ACTIVE;
            $curdate=date('Y-m-d');
            $active=true;
            $authUser = Auth::user();

            $RS_MGENDER = MyHelper::$RS_MGENDER;
            $RS_FGENDER = MyHelper::$RS_FGENDER;
            $RPS_TTLNOSHOW = MyHelper::$RPS_TTLNOSHOW;
            $ESCORT_NEW = MyHelper::$ESCORT_NEW;
            $ESCORT_REMOVE = MyHelper::$ESCORT_REMOVE;
            $RP_DROPROUTE = MyHelper::$RP_DROPROUTE;
            $PR_ESCORT_ENABLE=MyHelper::$PR_ESCORT_ENABLE;
            $PR_ESCORT_START_TIME=MyHelper::$PR_ESCORT_START_TIME;
            $PR_ESCORT_END_TIME=MyHelper::$PR_ESCORT_END_TIME;

            $ROSTER_ID=$request->selected_roster_id;
            $passenger=$request->selected_roster_passenger_id;
            $route_order=$request->route_order;

            $previous_order=DB::connection($db_name)->select("select GROUP_CONCAT(ROUTE_ORDER) as previous_order from roster_passengers where ROSTER_ID='".$ROSTER_ID."' and ACTIVE='".$RS_ACTIVE."' ");
            $previous_route_order=(string)$previous_order[0]->previous_order;
            $update_route_order=array();
            $passenger_id_list='';
            $passenger_update='';
            for ($i = 0; $i < count($route_order); $i++) {
                $passenger_update = RosterPassenger::on($db_name)->where("ROSTER_PASSENGER_ID", "=", $passenger[$i])
                ->update(array("ROUTE_ORDER" => $route_order[$i],"UPDATED_BY"=>$userid));
                $update_route_order[]=$route_order[$i];
                $passenger_id_list.=','.$passenger[$i];
            }
            $check_escort = "SELECT COUNT(*) as ttlcnt,EMPLOYEE_ID FROM route_escorts WHERE ROSTER_ID='" . $ROSTER_ID . "' and STATUS in(1,2,3)";
            $checkescort = DB::connection($db_name)->select($check_escort);
            $cc = count($checkescort);
            $ttlcnt = $cc > 0 ? $checkescort[0]->ttlcnt : '0';
            
            if ($ttlcnt != 0) {
                $ESCORT_EMPLOYEE_ID=$checkescort[0]->EMPLOYEE_ID;
                RouteEscort::on($db_name)->where("ROSTER_ID", "=", $ROSTER_ID)->where("EMPLOYEE_ID", "=", $ESCORT_EMPLOYEE_ID)->update(array("STATUS" => $ESCORT_REMOVE,"ESCORT_ID"=>NULL,"UPDATED_BY"=>$userid));
            }
            
            $escortcheck = DB::connection($db_name)->table('roster_passengers as rp')
            ->join('rosters as R', 'R.ROSTER_ID', '=', 'rp.ROSTER_ID')
            ->join('employees as e', 'e.EMPLOYEES_ID', '=', 'rp.EMPLOYEE_ID')
            ->join('locations as L', 'L.LOCATION_ID', '=', 'rp.LOCATION_ID')
            ->select('R.TRIP_TYPE',DB::raw('IF(R.CAB_ID IS NULL, 0, R.CAB_ID) AS CABID'),'rp.ROSTER_ID','rp.EMPLOYEE_ID','e.BRANCH_ID','e.GENDER', 'rp.ROSTER_PASSENGER_STATUS', 'R.ESTIMATE_END_TIME','R.ESTIMATE_START_TIME', 'rp.LOCATION_ID', 'L.LOCATION_NAME'  )
            ->where('R.BRANCH_ID', $branch_id)
            ->where('e.BRANCH_ID', $branch_id)
            ->where('rp.ROSTER_ID', $ROSTER_ID)
            ->where('rp.ACTIVE', $RS_ACTIVE)
            ->whereNotIn('rp.ROSTER_PASSENGER_STATUS', explode(',', $RPS_TTLNOSHOW))
            ->orderByDesc('rp.ROUTE_ORDER')
            ->limit(1)
            ->get();

            $GENDER = $escortcheck[0]->GENDER;
            $EMPLOYEE_ID = $escortcheck[0]->EMPLOYEE_ID;
            $TRIP_TYPE = $escortcheck[0]->TRIP_TYPE;
            $LOCATION_NAME = $escortcheck[0]->LOCATION_NAME;
            $CABID = $escortcheck[0]->CABID;
            $loc_update_name=$TRIP_TYPE=='P'?'START_LOCATION':'END_LOCATION';
            $update="update rosters set $loc_update_name='".$LOCATION_NAME."' where ROSTER_ID in($ROSTER_ID)";
            $res=DB::connection($db_name)->update($update);
          
            $isEscort = '';
            if(count($escortcheck)>0)
            {
                if($escortcheck[0]->TRIP_TYPE==$RP_DROPROUTE)
                {
                    $is_Escort_Time=date("H:i:s",strtotime($escortcheck[0]->ESTIMATE_START_TIME));
                    //$is_Escort_Time=$escortcheck[0]->ESTIMATE_START_TIME;
                }
                else
                {
                    $is_Escort_Time=date("H:i:s",strtotime($escortcheck[0]->ESTIMATE_END_TIME));
                    //$is_Escort_Time=$escortcheck[0]->ESTIMATE_END_TIME;
                }
                
            }
            
            $propertie = property::on($db_name)->where([['BRANCH_ID', '=', $branch_id], ['ACTIVE', '=', $RS_ACTIVE]])->get();
            for($i=0;$i<count($propertie);$i++)
            {
                $PROPERTIE_NAME = $propertie[$i]->PROPERTIE_NAME;
                $PROPERTIE_VALUE = $propertie[$i]->PROPERTIE_VALUE;
                
                switch ($PROPERTIE_NAME) {
                    case $PR_ESCORT_ENABLE:
                    $PR_ESCORTENABLE = $PROPERTIE_VALUE;
                    break;
                    case $PR_ESCORT_START_TIME:
                    $PR_ESCORTSTART_TIME = $PROPERTIE_VALUE;
                    break;
                    case $PR_ESCORT_END_TIME:
                    $PR_ESCORTEND_TIME = $PROPERTIE_VALUE;
                    break;
                    default:
                    break;
                }
            }
            if ($PR_ESCORTENABLE == 'Y') {
               
                $ret1 = $this->commonFunction->check_time($PR_ESCORTSTART_TIME, $PR_ESCORTEND_TIME, $is_Escort_Time) ? "yes" : "no";
                
                    if ($ret1 == "yes") {
                        $isEscort = 'true';
                    }
            }
           
            $insert_escort='';
            $escort_Roster_ID='';
            $escort_employee_id='';
            if($isEscort == 'true')
            {
                if ($GENDER == $RS_FGENDER) {
                    $checkescort = "SELECT COUNT(*) as ttlcnt FROM route_escorts WHERE ROSTER_ID='$ROSTER_ID' AND EMPLOYEE_ID='$EMPLOYEE_ID' AND STATUS in (" . $ESCORT_NEW . ",3,4) and BRANCH_ID='".$branch_id."'";
                    $checkescorts = DB::connection($db_name)->select($checkescort);
                    $cc = count($checkescorts);
                    $ttlcnts = $cc > 0 ? $checkescorts[0]->ttlcnt : '0';
                    if ($ttlcnts == 0) {
                        $insert_escort = array("BRANCH_ID" => $branch_id, "ROSTER_ID" => $ROSTER_ID, "EMPLOYEE_ID" => $EMPLOYEE_ID, "STATUS" => $ESCORT_NEW,
                        "CREATED_BY" => $userid, "created_at" => date('Y-m-d H:i:s'));
                        RouteEscort::on($db_name)->insert($insert_escort);
                        $escort_employee_id=$EMPLOYEE_ID;
                        $escort_Roster_ID=$ROSTER_ID;
                    }
                }
            }
            /* log */
            $str=implode(',',$update_route_order);
            
            $date_f=$this->commonFunction->date_format_add();
            //$elastic=new ElasticController();
            //$log_arr=array("ROSTER_ID"=>$ROSTER_ID,"BRANCH_ID"=>$BRANCH_ID,"ACTION"=>'Route Order Changed',"PROCESS_DATE"=>$date_f,"CATEGORY"=>'Route Order Change',"USER_ID"=>$userid,"CABID"=>$cab_id,"SELECTED_VENDOR"=>$vendor_id,"PASSENGER_ID"=>$passenger_id_list,"PREVIOUS_ORDER"=>$previous_route_order,"ROUTE_ORDERS"=>$str,"ROSTER_PASSENGER_SUCCESS"=>$passenger_update,"ESCORT_EMPLOYEE_ID"=>$escort_employee_id,"ESCORT_ROSTER_ID"=>$escort_Roster_ID);
            //$ret=$elastic->insertWebLogs($log_arr);
            /* log End */
            
            
            return response([
                'success' => true,
                'status' => 3,
                'message' =>"Successfully route order updated" ,
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            DB::rollBack();
            $message = $active ? 'Route order Unsuccessful' : 'Route order Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ],500);
        }
    } 
   
   
    
    
    /**
     * @throws ConnectionException
     */
    public function dataForCreate(): FoundationApplication|Response|ResponseFactory
    {

        try {
                $user = Auth::user();
                $RS_ACTIVE = MyHelper::$RS_ACTIVE;
                $db_name = Auth::user()->dbname;
                $branch_id = Auth::user()->BRANCH_ID;
                $vendor_id = Auth::user()->vendor_id;
                
                if (Auth::user()->user_type == MyHelper::$ADMIN) {
                    $vendor_list = Vendor::on($db_name)->where('ACTIVE', '=', $RS_ACTIVE)->where("BRANCH_ID", "=", $branch_id)->get();
                } else {
                    $vendor_list = Vendor::on($db_name)->where('ACTIVE', '=', $RS_ACTIVE)->where("VENDOR_ID", "=", $vendor_id)->get();
                }
                
                return response([
                'success' => true,
                'status' => 3,
                'vendor_list' => $vendor_list,
               
                
            ]);
        } catch (\Throwable|\Exception $e) {
            DB::rollBack();
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Data For Create for Route Order  Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ],500);
        }
    }
   
    
	

   
}
