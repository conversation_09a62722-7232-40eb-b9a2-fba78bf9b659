<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Contracts\Validation\Validator;

class AutoRouteClubDataRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'to_id' => 'required',
            'insertdata' => ['required','array'],
            'insertdata.*.from_id' => 'required',
            'insertdata.*.emp_id' => 'required',
        ];
    }

    public function messages()
    {
        return [
            'to_id.required' => 'to_id is required',
            'insertdata.required' => 'Insert data array is required',
            'insertdata.*.from_id.required' => 'from_id is required',
            'insertdata.*.emp_id.required' => 'emp_id is required',
        ];
    }

    public function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json([
           'success' => false,
            'validation_error' => true,
           'message' => 'Validation errors',
            'data' => $validator->errors(),
        ]));
    }
}
