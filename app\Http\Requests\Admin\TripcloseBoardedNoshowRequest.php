<?php

//namespace App\Http\Requests\Admin;
namespace App\Http\Requests\Admin;

use App\Helpers\CommonFunction;
use Closure;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;

class TripcloseBoardedNoshowRequest extends FormRequest
{
    protected CommonFunction $commonFunction;

    public function __construct(CommonFunction $commonFunction)
    {
        
    }

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        
        return [
            'roster_id' => ['required','numeric'],
            'roster_passenger_status' => ['required','numeric'],
            'roster_passenger_id' => ['required','numeric'],
            'employee_login_date' => ['required','date_format:Y-m-d'],
            'closetime' => ['required','date_format:H:i'],
            'employee_status' => ['required','string'],
            'admin_remarks' => ['required','string'],
            'noshow_reason' => ['string','nullable']
        ];
    }

    public function messages(): array
    {
        return [
            'roster_id.required' => 'roster id field is required',
            'roster_passenger_status.required' => 'roster_passenger_status field is required',
            'roster_passenger_id.required' => 'roster_passenger_id field is required',
            'employee_login_date.required' => 'employee_login_date  field is required',
            'closetime.required' => 'closetime  field is required',
            'employee_status.required' => 'employee_status  field is required',
            'admin_remarks.required' => 'admin_remarks  field is required',
        ];
    }
   

    public function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json([
            'success' => false,
            'validation_error' => true,
            'message' => 'Validation errors',
            'data' => $validator->errors()
        ]));
    }
   
}
