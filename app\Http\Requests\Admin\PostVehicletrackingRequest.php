<?php

//namespace App\Http\Requests\Admin;
namespace App\Http\Requests\Admin;

use App\Helpers\CommonFunction;
use Closure;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;

class PostVehicletrackingRequest extends FormRequest
{
    protected CommonFunction $commonFunction;

    public function __construct(CommonFunction $commonFunction)
    {
        
    }

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {   
        return [
                'vendor_id' => ['required','numeric'],
                'cab_id' => ['required','numeric'],
                'trip_type' => ['required','string'],
                'shift_time' => ['required'],
                'gps_time' => ['required'],
            ];
    }

    public function messages(): array
    {
        return [
               'vendor_id.required' => 'Vendor ID field is required',
               'Trip type.required' => 'Trip type field is required',
               'shift time equired' => 'shift time field is required',
               'gps time equired' => 'gps time field is required',
                  
        ];
    }
   

    public function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json([
            'success' => false,
            'validation_error' => true,
            'message' => 'Validation errors',
            'data' => $validator->errors()
        ]));
    }
   
}
