<?php

namespace App\Services\SuperAdmin;


use App\Helpers\CommonFunction;
use App\Helpers\MyHelper;
use App\Models\Branch;
use Carbon\Carbon;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;

class BranchService
{


    protected CommonFunction $commonFunction;


    public function __construct(CommonFunction $commonFunction)
    {
        $this->commonFunction = $commonFunction;
    }


    public function indexBranch(): FoundationApplication|Response|ResponseFactory
    {
        $authUser = Auth::user();
        try {
            $branchs = Branch::select(
                'branch.BRANCH_ID',
                'branch.BRANCH_NAME',
                'branch.LOCATION',
                'branch.LAT',
                "branch.LONG",
                'branch.ACTIVE'
            )
                ->where('branch.ACTIVE', MyHelper::$RS_ACTIVE)
                ->get();

            return response([
                'success' => true,
                'status' => 200,
                'branchs' => $branchs,
            ]);
        } catch (\Throwable | \Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Branch Index Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ]);
        }
    }


    public function storeBranch($request): FoundationApplication|Response|ResponseFactory
    {

        try {

            DB::beginTransaction();
            $auth_user = Auth::user();
            $branchData = $this->prepareBranchData($request, $auth_user, MyHelper::$RS_ACTIVE);
            $branchResult = Branch::create($branchData);
            return response([
                'success' => true,
                'status' => 200,
                'message' => 'Branch Created Successfully',

            ]);
        } catch (\Throwable | \Exception $e) {
            DB::rollBack();
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'validation_controller' => true,
                'status' => 4,
                'message' => 'Branch Created UnSuccessfully',
                'errorM' => $e->getMessage(),
            ]);
        } finally {
            DB::commit();
        }
    }


    private function prepareBranchData($request, $auth_user, $active): array
    {
        $date = Carbon::now();
        return [
            "ORG_ID" => $request->org_name,
            "COMPANY_ID" => $request->company_name,
            "DIVISION_ID" => $request->division_name,
            "BRANCH_NAME" => $request->name,
            "LOCATION" => $request->location,
            "LAT" => $request->lat,
            "LONG" => $request->long,
            "ACTIVE" => $active,
            "CREATED_BY" => $auth_user->id,
            "created_at" => $date->format("Y-m-d H:i:s"),

        ];
    }
 


    public function editBranch($branchAutoIdCrypt): FoundationApplication|Response|ResponseFactory
    {
        $authUser = Auth::user();

        try {
            $branchAutoId = Crypt::decryptString($branchAutoIdCrypt);
            $branch = Branch::where('branch.BRANCH_ID', $branchAutoId)
                ->where('branch.ACTIVE', MyHelper::$RS_ACTIVE)
                ->select(
                    'branch.BRANCH_ID',
                    'branch.NAME',
                    'branch.LOCATION',
                    'branch.LAT',
                    'branch.LONG',

                )->firstOrFail();

            return response([
                'success' => true,
                'status' => 200,
                'employee' => $branch,
            ]);
        } catch (\Throwable | \Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Branch Edit Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ]);
        }
    }



    public function updateBranch($request, $branchAutoIdCrypt): FoundationApplication|Response|ResponseFactory
    {

        try {

            $authUser = Auth::user();

            DB::beginTransaction();
            $branchAutoId = Crypt::decryptString($branchAutoIdCrypt);
            $branch = Branch::where('branch.BRANCH_ID', $branchAutoId)
                ->where('branch.ACTIVE', MyHelper::$RS_ACTIVE)
                ->firstOrFail();

            $branchData = $this->preparBranchDataForUpdate($request, $authUser);
            $branch->update($branchData);

            DB::commit();

            return response([
                'success' => true,
                'status' => 200,
                'message' => 'Branch Updated Successfully',
            ]);
        } catch (\Throwable | \Exception $e) {
            DB::rollBack();
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'message' => 'Branch Update Unsuccessful',
                'status' => 4,
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ]);
        }
    }


    private function preparBranchDataForUpdate($request, $auth_user): array
    {
        $date = Carbon::now();
        return [
            "NAME" => $request->name,
            "LOCATION" => $request->location,
            "LAT" => $request->lat,
            "LONG" => $request->long,
            "UPDATED_BY" => $auth_user->id,
            "updated_at" => $date->format("Y-m-d H:i:s"),
        ];
    }



    public function deleteBranch($request, $branchAutoIdCrypt): FoundationApplication|Response|ResponseFactory
    {
        $authUser = Auth::user();
        $date = Carbon::now();
        try {
            DB::beginTransaction();

            $branchAutoId = Crypt::decryptString($branchAutoIdCrypt);
            $branch = Branch::where('branch.BRANCH_ID', $branchAutoId)
                ->where('branch.ACTIVE', MyHelper::$RS_ACTIVE)
                ->firstOrFail();

            $branch->update([
                'REMARKS' => $request->input('remark'),
                'ACTIVE' => MyHelper::$RS_INACTIVE,
                'UPDATED_BY' => Auth::user()->id,
                'updated_at' => $date->format("Y-m-d H:i:s"),
            ]);

            DB::commit();

            return response([
                'success' => true,
                'status' => 200,
                'message' => 'Branch Deleted Successfully',
            ]);
        } catch (\Throwable | \Exception $e) {
            DB::rollBack();
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Branch Delete Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ]);
        }
    }



    


    public function paginationBranch($request, bool $active = true): FoundationApplication|Response|ResponseFactory
    {
        try {
            $perPage = $request->input('per_page', MyHelper::$PAGINATION_PER_PAGE);
            $branchs = Branch::query()
                ->select(
                    'branch.BRANCH_ID',
                    'branch.BRANCH_NAME',
                    'branch.LOCATION', 
                     'branch.ORG_ID',
                     'branch.COMPANY_ID',
                     'branch.DIVISION_ID',
                     'branch.LAT',
                     'branch.LONG',                  
                )
                ->where('branch.ACTIVE', MyHelper::$RS_ACTIVE) ;

            $filterModel = $request->input('filterModel');
            if ($filterModel) {
                foreach ($filterModel as $field => $filter) {
                    if (isset($filter['filter']) && $filter['filter'] !== '') {
                        $value = $filter['filter'];
                        $type = $filter['type'];

                        switch ($field) {
                            case 'BRANCH_NAME':
                                $branchs->where('branch.BRANCH_NAME', 'like', "%{$value}%");
                                break;
                           
                            case 'LOCATION':
                                $branchs->where('branch.LOCATION', 'like', "%{$value}%");
                                break;
                          
                        }
                    }
                }
            }


            if ($request->has('orderBy') && !empty($request->input('orderBy'))) {
                $orderBy = $request->input('orderBy');
                $order = $request->input('order', 'asc');
                $branchs->orderBy($orderBy, $order);
            } else {
                $branchs->orderBy('branch.created_at', 'desc');
            }


            if ($perPage === MyHelper::$PAGINATION_PER_PAGE_ALL) {
                $paginatedBranchs = $branchs->paginate($branchs->count());
            } else {
                $perPage = is_numeric($perPage) ? (int)$perPage : MyHelper::$PAGINATION_PER_PAGE;
                $paginatedBranchs = $branchs->paginate($perPage);
            }

            return response([
                'success' => true,
                'status' => 200,
                'branchs' => $paginatedBranchs,
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'Branchs Pagination Unsuccessful' : 'Deactivate Branchs Pagination Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ]);
        }
    }


    public function dataForCreateBranch(): FoundationApplication|Response|ResponseFactory
    {

        try {
            $user = Auth::user();
            $rsActive = MyHelper::$RS_ACTIVE;


            $org_List = DB::table('organization')
                ->select('ORGANIZATIONID', 'NAME')
                ->where('ACTIVE', $rsActive)
                ->get();


            $company_list = DB::table('company')
                ->select('COMPANY_ID', 'NAME')
                ->where('ACTIVE', $rsActive)
                ->get();


            $division_list = DB::table('division')
                ->select('DIVISION_ID', 'NAME')
                ->where('ACTIVE', $rsActive)
                ->get();



            return response([
                'success' => true,
                'status' => 200,
                'org_lists' => $org_List,
                'company_lists' => $company_list,
                'division_lists' => $division_list,
            ]);
        } catch (\Throwable | \Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Data For Create for Branch  Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ]);
        }
    }

}
