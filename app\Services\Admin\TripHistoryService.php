<?php

namespace App\Services\Admin;

use App\Helpers\CommonFunction;
use App\Helpers\MyHelper;
use App\Http\Controllers\ElasticController;
use App\Models\Cab_allocation;
use App\Models\Roster;
use App\Models\Vendor;
use Carbon\Carbon;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use DateTime;


class TripHistoryService
{
    protected CommonFunction $commonFunction;
    protected ?Branch $currentBranch = null;

    public function __construct(CommonFunction $commonFunction)
    {
        $this->commonFunction = $commonFunction;
    }

    public function get_roster_shifttime($request): FoundationApplication|Response|ResponseFactory
    {
        try {
            $branch_id = Auth::user()->BRANCH_ID;
            $userid = Auth::user()->id;
            $db_name = Auth::user()->dbname;
            $RS_ACTIVE = MyHelper::$RS_ACTIVE;
            $active = true;
            $authUser = Auth::user();
            $vendorId = $request->vendor_id;
            $trip_type = $request->trip_type;
            $date = carbon::now();
            // $branch_id=48;
            if ($trip_type == 'D') {
                $sql = "select R.ESTIMATE_START_TIME as estimate_time,R.BRANCH_ID from rosters as R where R.ACTIVE='$RS_ACTIVE' and R.BRANCH_ID='$branch_id' and R.TRIP_TYPE='$trip_type' and date(R.ESTIMATE_START_TIME)='" . Carbon::now()->format('Y-m-d') . "' and R.ESTIMATE_START_TIME>=SUBTIME('" . $date . "', '01:00:00') group by R.ESTIMATE_START_TIME  order by R.ESTIMATE_START_TIME";
            } else {
                $sql = "select R.ESTIMATE_END_TIME as estimate_time,R.BRANCH_ID from rosters as R where R.ACTIVE='$RS_ACTIVE' and R.ESTIMATE_END_TIME is not null and R.BRANCH_ID='$branch_id' and R.TRIP_TYPE='$trip_type' and date(R.ESTIMATE_END_TIME)='" . Carbon::now()->format('Y-m-d') . "' and R.ESTIMATE_END_TIME>=SUBTIME('" . $date . "', '01:00:00') group by R.ESTIMATE_END_TIME  order by R.ESTIMATE_END_TIME";
            }
            $roster_time = DB::connection("$db_name")->select($sql);

            return response([
                'success' => true,
                'status' => 3,
                'roster_time' => $roster_time,
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'Vehicle Tracking  status Unsuccessful' : 'Vehicle Tracking status Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ], 500);
        }
    }

    public function date_shifttime($request): FoundationApplication|Response|ResponseFactory
    {
        try {
            $RS_ACTIVE = MyHelper::$RS_ACTIVE;
            $selected_trip_type = $request->selected_trip_type;
            $selected_date = $request->selected_date;
            $branch_id = Auth::user()->BRANCH_ID;
            $RS_TOTAL_AUTOCANCEL = MyHelper::$RS_TOTAL_AUTOCANCEL;
            // $branchId = 18;
            $dbname = Auth::user()->dbname;

            $data = array();
            $date = carbon::now();

            if ($selected_trip_type == 'P') {
                $cond = " and date(R.ESTIMATE_END_TIME)='" . $selected_date . "' and R.TRIP_TYPE='" . $selected_trip_type . "' and R.ROSTER_STATUS not in($RS_TOTAL_AUTOCANCEL)  group by time(R.ESTIMATE_END_TIME) ";
            } else {
                $cond = " and date(R.ESTIMATE_START_TIME)='" . $selected_date . "' and R.TRIP_TYPE='" . $selected_trip_type . "' and R.ROSTER_STATUS not in($RS_TOTAL_AUTOCANCEL) group by time(R.ESTIMATE_START_TIME)  ";
            }

            $sql = "SELECT if(R.TRIP_TYPE='P',time(R.ESTIMATE_END_TIME),time(R.ESTIMATE_START_TIME)) as login_time
                from rosters R 
                where R.BRANCH_ID='" . $branch_id . "' and R.ACTIVE=1  $cond ";
            $result = DB::select($sql);

            return response([
                'success' => true,
                'status' => 3,
                'shift_time' => $result,
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Data For Create for Trip History Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function getfilter_trip_history($request): FoundationApplication|Response|ResponseFactory
    {
        try {
            $RS_ACTIVE = MyHelper::$RS_ACTIVE;
            $selected_trip_type = $request->selected_trip_type;
            $selected_date = $request->selected_date;
            $selected_vendor = $request->selected_vendor;
            $selected_shift_time = $request->selected_shift_time;

            $branch_id = Auth::user()->BRANCH_ID;
            $RS_TOTAL_AUTOCANCEL = MyHelper::$RS_TOTAL_AUTOCANCEL;
            // $branchId = 18;
            $dbname = Auth::user()->dbname;
            $user_type = Auth::user()->user_type;
            $ADMIN = MyHelper::$ADMIN;
            $vendor_condition = $selected_vendor == 'ALL' ? '' : " rosters.VENDOR_ID = '$selected_vendor' ";

            if ($user_type == $ADMIN) {
                $vendorid = '';
            } else {
                $vendorid = " rosters.VENDOR_ID='" . $selected_vendor . "'";
            }
            if ($selected_shift_time != 'ALL') {
                $req_date_time = $selected_date . ' ' . $selected_shift_time;
            }
            if ($selected_trip_type == 'P') {
                if ($selected_shift_time == 'ALL') {

                    $cond = " date(rosters.ESTIMATE_END_TIME)='" . $selected_date . "' and rosters.TRIP_TYPE='" . $selected_trip_type . "' and rosters.ROSTER_STATUS not in($RS_TOTAL_AUTOCANCEL) ";

                } else {
                    $cond = " rosters.ESTIMATE_END_TIME='" . $req_date_time . "' and rosters.TRIP_TYPE='" . $selected_trip_type . "' and rosters.ROSTER_STATUS not in($RS_TOTAL_AUTOCANCEL) ";
                }
            } else if ($selected_trip_type == 'D') {
                if ($selected_shift_time == 'ALL') {
                    $cond = " date(rosters.ESTIMATE_START_TIME)='" . $selected_date . "'  and rosters.TRIP_TYPE='" . $selected_trip_type . "' and rosters.ROSTER_STATUS not in($RS_TOTAL_AUTOCANCEL)";
                } else {
                    $cond = " rosters.ESTIMATE_START_TIME='" . $req_date_time . "' and rosters.TRIP_TYPE='" . $selected_trip_type . "'  and rosters.ROSTER_STATUS not in($RS_TOTAL_AUTOCANCEL) ";
                }
            }

            /* $sql="SELECT R.ROSTER_ID,R.ROSTER_STATUS,R.ROUTE_ID,R.TRIP_TYPE,R.START_LOCATION,R.END_LOCATION,R.CAB_ID,R.PASSENGER_ALLOT_COUNT,R.PASSENGER_ALLOT_IN_ROUT_COUNT,(R.PASSENGER_ALLOT_COUNT+R.PASSENGER_NOSHOW_COUNT+R.PASSENGER_CLUBING_COUNT) as actual_count,R.PASSENGER_NOSHOW_COUNT,if(R.TRIP_TYPE='P',time(R.ESTIMATE_END_TIME),time(R.ESTIMATE_START_TIME)) as login_logout,if(R.TRIP_TYPE='P',R.ESTIMATE_END_TIME,R.ESTIMATE_START_TIME) as login_logout_time,if(R.TRIP_TYPE='P',R.START_LOCATION,R.END_LOCATION) as pickup_drop_loc,V.VEHICLE_REG_NO,D.DRIVERS_NAME,VE.NAME as vendor_name,if(R.ACTUAL_END_TIME is not null,R.ACTUAL_END_TIME,'--') as actual_end_time,D.DRIVER_MOBILE,R.CAB_CAPACITY_COUNT,RP.ESTIMATE_START_TIME from rosters R
            left join roster_passengers RP on RP.ROSTER_ID=R.ROSTER_ID
            left join cab C on C.CAB_ID=R.CAB_ID
            left join vehicles V on V.VEHICLE_ID=C.VEHICLE_ID
            LEFT JOIN drivers D on D.DRIVERS_ID=C.DRIVER_ID
            left join vendors VE ON VE.VENDOR_ID=R.VENDOR_ID
            where R.BRANCH_ID='".$branch_id."' and R.ACTIVE=1 $cond  $vendorid $vendor_condition group by R.ROSTER_ID";
           $result= DB::connection("$dbname")->select($sql); */

            $result = \App\Models\Roster::query()
                ->select([
                    'rosters.ROSTER_ID',
                    'rosters.ROSTER_STATUS',
                    'rosters.ROUTE_ID',
                    'rosters.TRIP_TYPE',
                    'rosters.START_LOCATION',
                    'rosters.END_LOCATION',
                    'rosters.CAB_ID',
                    'rosters.PASSENGER_ALLOT_COUNT',
                    'rosters.PASSENGER_CLUBING_COUNT',
                    'rosters.PASSENGER_ALLOT_IN_ROUT_COUNT',
                    DB::raw('(rosters.PASSENGER_ALLOT_COUNT + rosters.PASSENGER_CLUBING_COUNT) as actual_count'),
                    'rosters.PASSENGER_NOSHOW_COUNT',
                    DB::raw("IF(rosters.TRIP_TYPE='P', TIME(rosters.ESTIMATE_END_TIME), TIME(rosters.ESTIMATE_START_TIME)) as login_logout"),
                    DB::raw("IF(rosters.TRIP_TYPE='P', rosters.ESTIMATE_END_TIME, rosters.ESTIMATE_START_TIME) as login_logout_time"),
                    DB::raw("IF(rosters.TRIP_TYPE='P', rosters.START_LOCATION, rosters.END_LOCATION) as pickup_drop_loc"),
                    'vehicles.VEHICLE_REG_NO',
                    'drivers.DRIVERS_NAME',
                    'vendors.NAME as vendor_name',
                    DB::raw("IF(rosters.ACTUAL_END_TIME IS NOT NULL, rosters.ACTUAL_END_TIME, '--') as actual_end_time"),
                    'drivers.DRIVER_MOBILE',
                    'rosters.CAB_CAPACITY_COUNT',
                    'roster_passengers.ESTIMATE_START_TIME',
                    DB::raw('IF(RE.STATUS IS NULL, 0, 1) AS ESCORTSTATUS'),
                    DB::raw('IF(RE.ESCORT_ID IS NULL, 0, 1) AS ESCORTALLOTSTATUS'),
                    'RE.ROUTE_ESCORT_ID'
                ])
                ->leftJoin('roster_passengers', 'roster_passengers.ROSTER_ID', '=', 'rosters.ROSTER_ID')
                ->leftJoin('cab', 'cab.CAB_ID', '=', 'rosters.CAB_ID')
                ->leftJoin('vehicles', 'vehicles.VEHICLE_ID', '=', 'cab.VEHICLE_ID')
                ->leftJoin('drivers', 'drivers.DRIVERS_ID', '=', 'cab.DRIVER_ID')
                ->leftJoin('vendors', 'vendors.VENDOR_ID', '=', 'rosters.VENDOR_ID')
                ->leftJoin('route_escorts as RE', 'RE.ROSTER_ID', '=', 'rosters.ROSTER_ID')
                ->where('rosters.BRANCH_ID', $branch_id)
                ->where('rosters.ACTIVE', 1)
                ->when($cond, function ($query) use ($cond) {
                    return $query->whereRaw($cond);
                })
                ->when($vendorid, function ($query) use ($vendorid) {
                    return $query->whereRaw($vendorid);
                })
                ->when($vendor_condition, function ($query) use ($vendor_condition) {
                    return $query->whereRaw($vendor_condition);
                })
                ->groupBy('rosters.ROSTER_ID')
                ->get();
				
				 $allowedStatuses = [1,3,9,11,13,15,25,27,29,31,89,91,93,95];
				//  $allowedStatuses = [9, 11, 13, 15, 25, 27, 29, 31];
            
            foreach ($result as $row) {
                $showCabAllotButton = is_null($row->CAB_ID) && in_array($row->ROSTER_STATUS, $allowedStatuses);
                $showResetButton = !is_null($row->CAB_ID) && in_array($row->ROSTER_STATUS, $allowedStatuses);
                $row->showResetButton = $showResetButton;
                $row->showCabAllotButton = $showCabAllotButton;
            }

            return response([
                'success' => true,
                'status' => 3,
                'result' => $result,
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Data For Create for Trip History Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * @throws ConnectionException
     */
    public function dataForCreate(): FoundationApplication|Response|ResponseFactory
    {
        try {
            $user = Auth::user();
            $RS_ACTIVE = MyHelper::$RS_ACTIVE;
            $ADMIN = MyHelper::$ADMIN;
            $branch_id = Auth::user()->BRANCH_ID;
            $vendor_id = Auth::user()->vendor_id;
            $dbname = Auth::user()->dbname;
            $date = carbon::now();
            $from_date = date('Y-m-d');
            $user_type = Auth::user()->user_type;
            $RS_TOTAL_AUTOCANCEL = MyHelper::$RS_TOTAL_AUTOCANCEL;

            if (Auth::user()->user_type == MyHelper::$ADMIN) {
                $vendor_list = Vendor::where('ACTIVE', '=', $RS_ACTIVE)->where('BRANCH_ID', '=', $branch_id)->get();
            } else {
                $vendor_list = Vendor::where('ACTIVE', '=', $RS_ACTIVE)->where('VENDOR_ID', '=', $vendor_id)->get();
            }

            $sql = "select R.ESTIMATE_END_TIME as estimate_time,R.BRANCH_ID from rosters as R where R.ACTIVE=$RS_ACTIVE and R.ESTIMATE_END_TIME is not null and R.BRANCH_ID='$branch_id' and R.TRIP_TYPE='P' and date(R.ESTIMATE_END_TIME)='" . Carbon::now()->format('Y-m-d') . "' and R.ESTIMATE_END_TIME>=SUBTIME('" . $date . "', '01:00:00') group by R.ESTIMATE_END_TIME order by R.ESTIMATE_END_TIME";  //
            $roster_time = DB::connection("$dbname")->select($sql);

            if ($user_type == $ADMIN) {
                $vendorid = '';
            } else {
                $vendorid = "AND R.VENDOR_ID='" . $vendor_id . "'";
            }

            $sql = "SELECT R.ROSTER_ID,R.ROSTER_STATUS,R.ROUTE_ID,R.TRIP_TYPE,R.START_LOCATION,R.END_LOCATION,R.CAB_ID,R.PASSENGER_ALLOT_COUNT,R.PASSENGER_ALLOT_IN_ROUT_COUNT,(R.PASSENGER_ALLOT_COUNT+R.PASSENGER_NOSHOW_COUNT+R.PASSENGER_CLUBING_COUNT) as actual_count,R.PASSENGER_NOSHOW_COUNT,if(R.TRIP_TYPE='P',time(R.ESTIMATE_END_TIME),time(R.ESTIMATE_START_TIME)) as login_logout,if(R.TRIP_TYPE='P',R.ESTIMATE_END_TIME,R.ESTIMATE_START_TIME) as login_logout_time,if(R.TRIP_TYPE='P',R.START_LOCATION,R.END_LOCATION) as pickup_drop_loc,V.VEHICLE_REG_NO,D.DRIVERS_NAME,VE.NAME as vendor_name,if(R.ACTUAL_END_TIME is not null,R.ACTUAL_END_TIME,'--') as actual_end_time,D.DRIVER_MOBILE,R.CAB_CAPACITY_COUNT, IF(RE.STATUS IS NULL, 0, 1) AS ESCORTSTATUS,
            IF(RE.ESCORT_ID IS NULL, 0, 1) AS ESCORTALLOTSTATUS,RE.ROUTE_ESCORT_ID

             from rosters R
                -- inner join roster_passengers RP on RP.ROSTER_ID=R.ROSTER_ID
                left join cab C on C.CAB_ID=R.CAB_ID
                left join vehicles V on V.VEHICLE_ID=C.VEHICLE_ID
                LEFT JOIN drivers D on D.DRIVERS_ID=C.DRIVER_ID
                left join vendors VE ON VE.VENDOR_ID=R.VENDOR_ID
                left join route_escorts RE on RE.ROSTER_ID=R.ROSTER_ID
                
                where R.BRANCH_ID='" . $branch_id . "' and (date(R.ESTIMATE_START_TIME)='" . $from_date . "' or date(R.ESTIMATE_END_TIME)='" . $from_date . "') and R.ACTIVE=1 $vendorid and R.ROSTER_STATUS not in ($RS_TOTAL_AUTOCANCEL)";
            $roster_data = DB::connection("$dbname")->select($sql);

            $category = array(
                array(
                    'value' => 'P',
                    'name' => 'pickup'
                ),
                array(
                    'value' => 'D',
                    'name' => 'Drop'
                )
            );

            return response([
                'success' => true,
                'status' => 3,
                'vendor_list' => $vendor_list,
                'category' => $category,
                'roster_time' => $roster_time,
                'roster_data' => $roster_data,
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Data For Create for Vehicle Tracking GPS Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}
