<?php

namespace App\Services\Admin;

use App\Helpers\CommonFunction;
use App\Helpers\MyHelper;
use Carbon\Carbon;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use App\Models\Device;

class DeviceService
{
    protected CommonFunction $commonFunction;
    protected ?Branch $currentBranch = null;

    public function __construct(CommonFunction $commonFunction)
    {
        $this->commonFunction = $commonFunction;
    }
	
	 public function indexDevice(): FoundationApplication|Response|ResponseFactory
    {
        $authUser = Auth::user();
       

        try 
		{
            $device = Device::select('devices.DEVICE_ID',
                'devices.DEVICE_MODEL',
                'devices.IMEI_NO_1',
                'devices.IMEI_NO_2',
                'devices.ACTIVE',
                'devices.CREATED_DATE'
            )
                ->where('devices.ACTIVE', MyHelper::$RS_ACTIVE)
               //->where('devices.ORG_ID', "=","O.ORGANIZATIONID")
			    ->join("branch", "branch.BRANCH_ID", "=", DB::Raw($authUser->BRANCH_ID))
                ->Join("organization as O", "O.ORGANIZATIONID", "=", "devices.ORG_ID")
                ->get();

            return response([
                'success' => true,
                'status' => 3,
                'devices' => $device,
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Device Index Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ],500);
        }
    }

	

    public function storeDevice($request): FoundationApplication|Response|ResponseFactory
    {

        try {

            DB::beginTransaction();
            $auth_user = Auth::user();

            $deviceData = $this->prepareDeviceData($request, $auth_user, MyHelper::$RS_ACTIVE, $auth_user->id);
          
            $deviceResult = Device::create($deviceData);



            return response([
                'success' => true,
                'status' => 3,
                'message' => 'Device Created Successfully',

            ]);

        } catch (\Throwable|\Exception $e) 
		{
            DB::rollBack();
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'validation_controller' => true,
                'status' => 4,
                'message' => 'Device Created UnSuccessfully',
                'errorM' => $e->getMessage(),
            ],500);
        } finally {
            DB::commit();
        }


    }
	
    /**
     * @throws ConnectionException
     */
    private function prepareDeviceData($request, $auth_user, $active, $userId): array
    {
        $date = Carbon::now();
        $authUser = Auth::user();
        $org_id=$this->commonFunction->getOrgId($authUser->BRANCH_ID);

        return [
            "IMEI_NO_1" => $request->IMEI_NO_1,
            "IMEI_NO_2" => $request->IMEI_NO_2,
            "DEVICE_MODEL" => $request->DEVICE_MODEL,
            "COMPLIANT_STATUS" => $active,
            "SIM_REMARKS" => $request->SIM_REMARKS,
            "ACTIVE" => $active,
            "ORG_ID" => $org_id,
            "CREATED_BY" => $auth_user->id,
            "CREATED_DATE" => $date->format("Y-m-d H:i:s"),
          
        ];
    }

    public function deleteDevice($request, $deviceAutoIdCrypt): FoundationApplication|Response|ResponseFactory
    {
        $authUser = Auth::user();
        $date = Carbon::now();
        try {
            DB::beginTransaction();

            $deviceId = Crypt::decryptString($deviceAutoIdCrypt);

            $device = Device::where('ACTIVE', MyHelper::$RS_ACTIVE)
               // ->where('BRANCH_ID', $authUser->BRANCH_ID)
                ->findOrFail($deviceId);

            $device->update([
                'REMARKS' => $request->input('remark'),
                'ACTIVE' => MyHelper::$RS_INACTIVE,
                'UPDATED_BY' => Auth::user()->id,
                'updated_at' => $date->format("Y-m-d H:i:s"),
            ]);

            DB::commit();

            return response([
                'success' => true,
                'status' => 3,
                'message' => 'Device Deleted Successfully',
            ]);
        } catch (\Throwable|\Exception $e) {
            DB::rollBack();
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Device Delete Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ]);
        }
    }

   

    public function paginationDevice($request, bool $active = true): FoundationApplication|Response|ResponseFactory
    {
        try {
            $perPage = $request->input('per_page', MyHelper::$PAGINATION_PER_PAGE);
            $branch_id = Auth::user()->BRANCH_ID;
            $activeStatus = $active ? MyHelper::$RS_ACTIVE : MyHelper::$RS_INACTIVE;

            $authUser = Auth::user();

          $device = Device::select('devices.DEVICE_ID',
                'devices.DEVICE_MODEL',
                'devices.IMEI_NO_1',
                'devices.IMEI_NO_2',
                'devices.ACTIVE',
                'devices.CREATED_DATE',
                'devices.COMPLIANT_STATUS',
                'users.name AS CREATED_BY',
            )
                ->where('devices.ACTIVE', MyHelper::$RS_ACTIVE)
               //->where('devices.ORG_ID', "=","O.ORGANIZATIONID")
			    ->join("branch", "branch.BRANCH_ID", "=", DB::Raw($authUser->BRANCH_ID))
                ->join("users", "users.id", "=", "devices.CREATED_BY")
                ->Join("organization as O", "O.ORGANIZATIONID", "=", "devices.ORG_ID");
               
            $filterModel = $request->input('filterModel');
            if ($filterModel) {
                foreach ($filterModel as $field => $filter) {
                    if (isset($filter['filter']) && $filter['filter'] !== '') {
                        $value = $filter['filter'];
                        $type = $filter['type'];

                        switch ($field) {
                            case 'DEVICE_MODEL':
                                $sim->where('devices.DEVICE_MODEL', 'like', "%{$value}%");
                                break;
							case 'IMEI_NO_1':
                                $sim->where('devices.IMEI_NO_1', 'like', "%{$value}%");
                                break;
                        }
                    }
                }
            }


            if ($request->has('orderBy') && !empty($request->input('orderBy'))) {
                $orderBy = $request->input('orderBy');
                $order = $request->input('order', 'asc');
                $device->orderBy($orderBy, $order);
            } else {
                $device->orderBy('devices.CREATED_DATE', 'desc');
            }


            if ($perPage === MyHelper::$PAGINATION_PER_PAGE_ALL) {
                $paginatedDevice = $device->paginate($device->count());
            } else {
                $perPage = is_numeric($perPage) ? (int)$perPage : MyHelper::$PAGINATION_PER_PAGE;
                $paginatedDevice = $device->paginate($perPage);
            }

            return response([
                'success' => true,
                'status' => 3,
                'devices' => $paginatedDevice,
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'Device Pagination Unsuccessful' : 'Device Pagination Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ]);
        }
    } 

    public function editDevice($deviceAutoIdCrypt): FoundationApplication|Response|ResponseFactory
    {
        $authUser = Auth::user();

        try {
            $deviceAutoId = Crypt::decryptString($deviceAutoIdCrypt);
            $device = Device::where('devices.ACTIVE', MyHelper::$RS_ACTIVE)
                // ->where('sim.ORG_ID', "=","O.ORGANIZATIONID")
				 ->where('devices.DEVICE_ID', $deviceAutoId)
			   
                ->select(
                    'devices.DEVICE_ID',
                    'devices.IMEI_NO_1',
                    'devices.IMEI_NO_2',
                    'devices.DEVICE_MODEL',
                )
				->join("branch", "branch.BRANCH_ID", "=", DB::Raw($authUser->BRANCH_ID))
                ->Join("organization as O", "O.ORGANIZATIONID", "=", "devices.ORG_ID")
                ->firstOrFail();

            return response([
                'success' => true,
                'status' => 3,
                'devices' => $device,
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Device Edit Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ]);
        }
    }

    public function updateDevice($request, $deviceAutoIdCrypt): FoundationApplication|Response|ResponseFactory
    {
        $authUser = Auth::user();
        try {
            DB::beginTransaction();
            $deviceAutoId = Crypt::decryptString($deviceAutoIdCrypt);
            $device = Device::findOrFail($deviceAutoId);
            $auth_user = Auth::user();

            $deviceData = $this->prepareDeviceDataForUpdate($request, $auth_user, $device);
            $device->update($deviceData);

            DB::commit();


            return response([
                'success' => true,
                'status' => 3,
                'message' => 'Device Updated Successfully',
            ]);

        } catch (\Throwable|\Exception $e) {
            DB::rollBack();
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'message' => 'Device Update Unsuccessful',
                'status' => 4,
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * @throws ConnectionException
     */
    private function prepareDeviceDataForUpdate($request, $auth_user, $device): array
    {
        $date = Carbon::now();

        return [
            "DEVICE_MODEL" => $request->DEVICE_MODEL,
            "IMEI_NO_1" => $request->IMEI_NO_1,
            "IMEI_NO_2" => $request->IMEI_NO_2,
            "REMARKS" => $request->REMARKS,
            "UPDATED_BY" => $auth_user->id,
            "updated_at" => $date->format("Y-m-d H:i:s"),
        ];
    }

   
}
