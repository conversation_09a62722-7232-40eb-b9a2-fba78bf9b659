<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;

use App\Services\Admin\FileDownloadService;
use App\Http\Requests\Admin\FileDownLoadRequest;
use App\Http\Requests\Admin\ASFRequest;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class FileDownloadController extends Controller
{
    protected FileDownloadService $fileDownloadService;

    public function __construct(FileDownloadService $fileDownloadService)
    {
        $this->fileDownloadService = $fileDownloadService;
    }

    public function fetch_UploadFileData(ASFRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->fileDownloadService->fetch_UploadFileData($request);
    }

   public function fetch_UploadFileDetails(FileDownLoadRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->fileDownloadService->fetch_UploadFileDetails($request);
    }
    
}
