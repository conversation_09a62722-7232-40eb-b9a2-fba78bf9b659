<?php

namespace App\Models;

use App\Helpers\CommonFunction;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EmployeesRosterRequest extends Model
{
    use HasFactory;

    protected $table = 'employees_roster_request';
    protected $primaryKey = 'ROSTER_REQ_ID';

    public $timestamps = false;

    protected $fillable = [

        'FILE_ID',
        'BRANCH_ID',
        'EMPLOYEE_ID',
        'GENDER',
        'TRIP_TYPE',
        'START_LOCATION',
        'END_LOCATION',
        'ESTIMATE_START_TIME',
        'NOT_REQ_DATE',
        'NOT_REQ_REASON',
        'ESTIMATE_TIME',
        'ESTIMATE_END_TIME',
        'OLD_ROUTE_ID',
        'VENDOR_NAME',
        'VENDOR_ID',
        'DISTANCE',
        'STATUS',
        'NEW_ROUTE_CREATE',
        'FILE_NAME',
        'ALIAS_LOCATION',
        'ROUTE_ID',
        'CREATED_BY',
        'created_at',
        'UPDATED_BY',
        'updated_at',
        'CANCEL_REMARKS',
        'ADHOC_REASON_ID',
        'ADHOC_OTHER_REASON',
        'CATEGORY',
        'APP_CATEGORY',
        'TEAM_ID',
        'RE_ROUTE_CREATE',
        'CLUB_STATUS',
        'CLUB_DISTANCE',
        'WORK_TYPE',
        'BOOKING_STATUS',
    ];
}
