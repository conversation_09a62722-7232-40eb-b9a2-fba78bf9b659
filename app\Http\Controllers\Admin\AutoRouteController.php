<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\AutoRouteAjaxRequest;
use App\Http\Requests\Admin\AutoRouteClubDataRequest;
use App\Http\Requests\Admin\GetRouteDetailsByRouteIDRequest;
use App\Http\Requests\Admin\NewRouteCreateRequest;
use App\Http\Requests\Admin\MergeAutoRouteRequest;
use App\Http\Requests\Admin\EmpNoshowRouteRequest;
use App\Http\Requests\Admin\GetTimeResultRequest;
use App\Http\Requests\Admin\AdminChangeRosterRequestTimeRequest;
use App\Services\Admin\AutoRouteService;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class AutoRouteController extends Controller
{
    protected AutoRouteService $autorouteservice;

    public function __construct(AutoRouteService $autorouteservice)
    {
        $this->autorouteservice = $autorouteservice;
    }

    public function dataForCreate(): FoundationApplication|Response|ResponseFactory
    {
        return $this->autorouteservice->dataForCreate();
    }

    public function AutoRouteAjax(AutoRouteAjaxRequest $request): ResponseFactory|Application|Response
    {
        return $this->autorouteservice->AutoRouteAjax($request);
    }

    public function AutoRouteClubData(AutoRouteClubDataRequest $request) {
        return $this->autorouteservice->AutoRouteClubData($request);
    }

    public function GetRouteDetailsByRouteID(GetRouteDetailsByRouteIDRequest $request): ResponseFactory|Application|Response
    {
        return $this->autorouteservice->GetRouteDetailsByRouteID($request);
    }

    public function NewRouteCreate(NewRouteCreateRequest $request): ResponseFactory|Application|Response
    {
        return $this->autorouteservice->NewRouteCreate($request);
    }

    public function MergeAutoRoute(MergeAutoRouteRequest $request): ResponseFactory|Application|Response
    {
        return $this->autorouteservice->MergeAutoRoute($request);
    }

    public function SetRouteVendor(Request $request): ResponseFactory|Application|Response
    {
        return $this->autorouteservice->SetRouteVendor($request);
    }

    public function AutoRouteToRoster(Request $request): ResponseFactory|Application|Response
    {
        return $this->autorouteservice->AutoRouteToRoster($request);
    }

    public function AutoRouteDetials(Request $request): ResponseFactory|Application|Response
    {
        return $this->autorouteservice->AutoRouteDetials($request);
    }

    public function VendorAssignRoute(): ResponseFactory|Application|Response
    {
        return $this->autorouteservice->VendorAssignRoute();
    }

    public function VendorListRoute(): ResponseFactory|Application|Response
    {
        return $this->autorouteservice->VendorListRoute();
    }

    public function AutoRouteList(): ResponseFactory|Application|Response
    {
        return $this->autorouteservice->AutoRouteList();
    }

    public function GetTimeRoutes(Request $request): ResponseFactory|Application|Response
    {
        return $this->autorouteservice->GetTimeRoutes($request);
    }

    public function EmpNoshowRoute(EmpNoshowRouteRequest $request): ResponseFactory|Application|Response
    {
        return $this->autorouteservice->EmpNoshowRoute($request);
    }

    public function GetTimeResult(GetTimeResultRequest $request): ResponseFactory|Application|Response
    {
        return $this->autorouteservice->GetTimeResult($request);
    }

    public function AdminChangeRosterRequestTime(AdminChangeRosterRequestTimeRequest $request): ResponseFactory|Application|Response
    {
        return $this->autorouteservice->AdminChangeRosterRequestTime($request);
    }

    public function CheckVendor(Request $request): ResponseFactory|Application|Response
    {
        return $this->autorouteservice->CheckVendor($request);
    }

    public function GetVendorRouteCnt(Request $request): ResponseFactory|Application|Response
    {
        return $this->autorouteservice->GetVendorRouteCnt($request);
    }
}
