<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MaskTransaction extends Model
{
    use HasFactory;

    protected $table = 'MASK_TRANSACTION';
    protected $primaryKey = 'MASKING_ID';

    public $timestamps = false;

    protected $fillable = [
        'BRANCH_ID',
        'ROSTER_ID',
        'ROSTER_PASSENGER_ID',
        'MASK_DID_NUMBER',
        'START_TIME',
        'END_TIME',
        'STATUS',
        'CREATE_DATE',
        'MASK_CLEAR_RESPONSE',
        'UPDATED_AT',
        'AVAILABLITY_STATUS',
    ];
}
