<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\LocationRequest;
use App\Http\Requests\Admin\LocationUpdateRequest;
use App\Services\Admin\LocationService;


use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class LocationController extends Controller
{
    protected LocationService $locationService;

    public function __construct(LocationService $locationService)
    {
        $this->locationService = $locationService;
    }

    public function index(): ResponseFactory|Application|Response
    {
        return $this->locationService->indexLocation();
    }

   public function store(locationRequest $request): ResponseFactory|Application|Response
    {
        return $this->locationService->storelocation($request);
    }
    public function delete(LocationDeleteRequest $request, $locationAutoIdCrypt): FoundationApplication|Response|ResponseFactory
    {
        return $this->locationService->deleteLocation($request, $locationAutoIdCrypt);
    }
	
	 public function edit($locationAutoIdCrypt): Application|Response|ResponseFactory
    {
        return $this->locationService->editLocation($locationAutoIdCrypt);
    }

    public function update(LocationUpdateRequest $request, $locationAutoIdCrypt): Application|Response|ResponseFactory
    {
        return $this->locationService->updateLocation($request, $locationAutoIdCrypt);
    }
    public function activeLocationPagination(Request $request): ResponseFactory|Application|Response
    {
        return $this->locationService->paginationLocation($request);
    }
    public function dataForCreate(): FoundationApplication|Response|ResponseFactory
    {
        return $this->locationService->dataForCreateLocation();
    } 


}
