<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;

use App\Services\Admin\RouteOrderService;

use App\Http\Requests\Admin\VendorCabAllotmentRequest;
use App\Http\Requests\Admin\SelectedCabRouteRequest;
use App\Http\Requests\Admin\SelectedCabRosterRequest;
use App\Http\Requests\Admin\RosterChangeOrderRequest;


use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class RouteOrderController extends Controller
{
    protected RouteOrderService $routeorderService;

    public function __construct(RouteOrderService $routeorderService)
    {
        $this->routeorderService = $routeorderService;
    }
    
    public function dataForCreate(): FoundationApplication|Response|ResponseFactory
    {
        return $this->routeorderService->dataForCreate();
    }
   
    public function VendorBaseType(VendorCabAllotmentRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->routeorderService->VendorBaseType($request);
        
    }
   
    public function select_cab_route(SelectedCabRouteRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->routeorderService->select_cab_route($request);
        
    }
   
    public function select_cab_roster(SelectedCabRosterRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->routeorderService->select_cab_roster($request);
        
    }
   
    public function change_order(RosterChangeOrderRequest $request): FoundationApplication|Response|ResponseFactory
    {
        return $this->routeorderService->change_order($request);
        
    }
   
   
 
}
