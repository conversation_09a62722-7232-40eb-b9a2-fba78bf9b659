<?php

//namespace App\Http\Requests\Admin;
namespace App\Http\Requests\Admin;

use App\Helpers\CommonFunction;
use Closure;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;

class EscortValidateRequest extends FormRequest
{
    
    protected CommonFunction $commonFunction;

    public function __construct(CommonFunction $commonFunction)
    {
        
    }

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {   
        return [
            'escort_routeid' => ['required','numeric'],
            'escort_reasonid' => ['required','numeric'],
            'escort_validate_type' => ['required','string'],
            'escort_remarks' => ['required','string'],
            ];
    }

    public function messages(): array
    {
        return [
            'escort_routeid.required' => 'Escort Route Id field is required',   
            'escort_reasonid.required' => 'Escort Reason is required',
            'escort_validate_type.required' => 'Escort validate type is required',
            'escort_remarks.required' => 'Escort remarks is required'
        ];
    }
   

    public function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json([
            'success' => false,
            'validation_error' => true,
            'message' => 'Validation errors',
            'data' => $validator->errors()
        ]));
    }
   
}
