<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Crypt;

class Device extends Model
{
    use HasFactory;

    protected $table = 'devices';
    protected $primaryKey = 'DEVICE_ID';
    protected $appends = ['DEVICE_ID_crypt'];

    protected $fillable = [
        'IMEI_NO_1',
        'IMEI_NO_2',
        'DEVICE_MODEL',
        'APP_VERSION',
        'MOBILE_GCM',
        'SESSION_ID	',
        'PUBLIC_KEY',
        'PRIVATE_KEY',
        'COMPLIANT_STATUS',
        'ACTIVE',
        'CREATED_BY',
        'CREATED_AT',
        'UPDATED_BY',
        'updated_at',
        'REMARKS',
        'ORG_ID',
        'ANDROID_ID',
    ];



public function getDEVICEIDCryptAttribute(): string
    {
        return Crypt::encryptString($this->DEVICE_ID);
    }
}