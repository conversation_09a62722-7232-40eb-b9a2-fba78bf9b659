<?php

namespace App\Services\Admin;

use App\Helpers\CommonFunction;
use App\Helpers\MyHelper;
use App\Http\Controllers\ElasticController;
use App\Http\Controllers\MaskNumberClearController;
use App\Models\Cab;
use App\Models\Cab_allocation;
use App\Models\Driver;
use App\Models\Driver_Billing_Summary;
use App\Models\property;
use App\Models\Reason_Log;
use App\Models\Roster;
use App\Models\RosterPassenger;
use App\Models\RouteEscorts;
use App\Models\Vehicle;
use App\Models\VehicleModel;
use App\Models\Vendor;
use Carbon\Carbon;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use DateTime;

class VehicleTrackingService
{
    protected CommonFunction $commonFunction;
    protected ?Branch $currentBranch = null;

    public function __construct(CommonFunction $commonFunction)
    {
        $this->commonFunction = $commonFunction;
    }

    public function cab_status_vehicle($request): FoundationApplication|Response|ResponseFactory
    {
        try {
            $branch_id = Auth::user()->BRANCH_ID;
            $userid = Auth::user()->id;
            $db_name = Auth::user()->dbname;
            $RS_ACTIVE = MyHelper::$RS_ACTIVE;
            $active = true;
            $authUser = Auth::user();
            $vendor_id = $request->vendor_id;
            $reason_id = $request->reason_id;
            $cabid = 0;
            $message = '';
            $overspeed_propertie = DB::table('properties')
                ->select('PROPERTIE_VALUE')
                ->where('PROPERTIE_NAME', '=', 'OVERSPEED TRACKING')
                ->where('BRANCH_ID', '=', $branch_id)
                ->get();
            $overspeed = $overspeed_propertie[0]->PROPERTIE_VALUE;
            $gps_track_control = new \App\Http\Controllers\ElasticController;
            if ($reason_id == 'Over Speeds') {
                $getdata = $gps_track_control->getCabLiveOverspeedStatus($branch_id, $vendor_id, $reason_id, $cabid, $overspeed);
            } else {
                $getdata = $gps_track_control->getCabLiveStatus($branch_id, $vendor_id, $reason_id, $cabid);
            }
            if ($getdata == 'No Record') {
                $getdata = array();
                $message = 'No Record';
            }

            return response([
                'success' => true,
                'status' => 3,
                'cab_data' => $getdata,
                'message' => $message,
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            $message = $active ? 'Vehicle Tracking  status Unsuccessful' : 'Vehicle Tracking status Unsuccessful';
            return response([
                'success' => false,
                'status' => 4,
                'message' => $message,
                'validation_controller' => true,
                'error' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ], 500);
        }
    }

    public function vehicle_status($request): FoundationApplication|Response|ResponseFactory
    {
        try {
            $RS_ACTIVE = MyHelper::$RS_ACTIVE;
            $selected_vendor_id = $request->selected_vendor_id;
            $selected_cab_id = $request->selected_cab_id;
            $selected_reason = $request->selected_reason;
            $selected_traffic_mode = $request->selected_traffic_mode;

            $branchId = Auth::user()->BRANCH_ID;

            $gps_track_control = new \App\Http\Controllers\ElasticController;

            switch ($selected_reason) {
                case MyHelper::$ES_STATUS_GPS_OFF:
                    $search = MyHelper::$VT_CASE_GPS_OFF;
                    break;
                case MyHelper::$ES_STATUS_GPS_NOTFIX:
                    $search = MyHelper::$VT_CASE_GPSNOT_FIX;
                    break;
                case MyHelper::$ES_STATUS_GPS_FIX:
                    $search = MyHelper::$VT_CASE_GPS_FIX;
                    break;
                case MyHelper::$ES_STATUS_LOGIN:
                    $search = MyHelper::$VT_CASE_LOGIN;
                    break;
                case MyHelper::$ES_STATUS_LOGOUT:
                    $search = MyHelper::$VT_CASE_LOGOUT;
                    break;
                case MyHelper::$ES_STATUS_VACANT:
                    $search = MyHelper::$VT_CASE_VACANT;
                    break;
                case MyHelper::$ES_STATUS_ONTRIP:
                    $search = MyHelper::$VT_CASE_ONDUTY;
                    break;
                case MyHelper::$ES_STATUS_ONEHOUR_BELOW:
                    $search = MyHelper::$VT_CASE_Idle5min_to_1h;
                    break;
                case MyHelper::$ES_STATUS_SIXHOURS_BELOW:
                    $search = MyHelper::$VT_CASE_Idle_1h_to_6h;
                    break;
                case MyHelper::$ES_STATUS_SIXHOURS_ABOVE:
                    $search = MyHelper::$VT_CASE_Idle_6h_above;
                    break;
                case 'Over Speeds':
                    $search = 'Over Speeds';
                    break;
            }
            if (($selected_reason == '0' && $selected_cab_id == 0)) {
                $result = $gps_track_control->getCabLiveData($branchId, $selected_vendor_id);


            } else {
                // $result = Session::get('getlist');
                $result = $gps_track_control->getCabLiveStatus($branchId, $selected_vendor_id, $selected_reason, $selected_cab_id);
            }

            return response([
                'success' => true,
                'status' => 3,
                'gps_data' => $result,
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Data For Create for Vehicle Tracking Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * @throws ConnectionException
     */
    public function dataForCreate(): FoundationApplication|Response|ResponseFactory
    {
        try {
            $user = Auth::user();
            $RS_ACTIVE = MyHelper::$RS_ACTIVE;

            $branch_id = Auth::user()->BRANCH_ID;
            $vendor_id = Auth::user()->vendor_id;

            if (Auth::user()->user_type == MyHelper::$ADMIN) {
                $vendor_list = Vendor::where('ACTIVE', '=', $RS_ACTIVE)->where('BRANCH_ID', '=', $branch_id)->get();
            } else {
                $vendor_list = Vendor::where('ACTIVE', '=', $RS_ACTIVE)->where('VENDOR_ID', '=', $vendor_id)->get();
            }
            $cab_status = array(
                
                array(
                    'value' => 'GPS Off',
                    'name' => 'GPS Off',
                    'color'=>'#E70779',
                ),
                array(
                    'value' => 'GPS Not Fix',
                    'name' => 'GPS Not Fix',
                    'color'=>'#989494',
                ),
                array(
                    'value' => 'Logout',
                    'name' => 'Logout',
                    'color' => '#E70000'
                ),
                array(
                    'value' => 'Vacant',
                    'name' => 'Vacant',
                    'color' => '#82B322'
                ),
                array(
                    'value' => 'On Duty',
                    'name' => 'On Duty',
                    'color' => '#d135e1'
                ),
                array(
                    'value' => 'GPS Not Fix 5min to 1hour',
                    'name' => 'GPS Not Fix 5min to 1hour',
                    'color' => '#FDE307'
                ),
                array(
                    'value' => 'GPS Not Fix 1hour to 6hour',
                    'name' => 'GPS Not Fix 1hour to 6hour',
                    'color' => '#3366CC'
                ),
                array(
                    'value' => 'GPS Not Fix 6hour above',
                    'name' => 'GPS Not Fix 6hour above',
                    'color' => '#09BAD6'
                ),
                /* array(
                    'value' => 'Over Speeds',
                    'name' => 'Over Speeds'
                ) */
            );

            return response([
                'success' => true,
                'status' => 3,
                'vendor_list' => $vendor_list,
                'cab_status' => $cab_status,
            ]);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Data For Create for Vehicle Tracking Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}
