<?php
namespace App\Http\Requests\Admin;

use App\Helpers\CommonFunction;
use Closure;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;

class VehicleRequest extends FormRequest
{
    protected CommonFunction $commonFunction;

    public function __construct(CommonFunction $commonFunction)
    {
        
    }

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        
        return [
		
            'VEHICLE_MODEL_ID' => 'required',
            'VEHICLE_REG_NO' =>  [
                'required',
                'regex:/[a-zA-Z0-9\s]+/',
                 Rule::unique('vehicles', 'VEHICLE_REG_NO')
            ],
            'VEHICLE_JOIN_DATE' => 'required',
            'REG_DATE' => 'required',
            'PERMIT_EXPIRY' => 'required',
            'POLLUTION_EXPIRY' => 'required',
            'INSURANCE_EXPIRY' => 'required',
            'FC_EXPIRY' => 'required',
            'TAX_EXPIRY' => 'required',
            'MILEAGE_KM' => 'required',
            
        ];
    }
   

    public function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json([
            'success' => false,
            'validation_error' => true,
            'message' => 'Validation errors',
            'data' => $validator->errors()
        ]));
    }

    private function checkDeactivatedVehicle($branchId): Closure
    {
        return function (string $attribute, mixed $value, Closure $fail) use ($branchId) {
            $deactivatedVehicle = DB::table("vehicles")
                ->where("VEHICLE_ID", $value)
                ->where("ACTIVE", 2)
                ->exists();

            if ($deactivatedVehicle) {
                $fail("Vehicle already exists and is deactivated.");
            }
        };
    }

   
}
