<?php

namespace App\Http\Controllers\Auth;

use App\Helpers\CommonFunction;
use App\Helpers\MyHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\LoginRequest;
use App\Http\Requests\Auth\OtpCreationRequest;
use App\Http\Requests\Auth\OtpVerificationRequest;
use App\Models\OtpVerify;
use App\Models\User;
use Carbon\Carbon;
use Exception;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Traits\SmsTrait;

class AuthController extends Controller
{
    use SmsTrait;

    protected CommonFunction $commonFunction;

    public function __construct(CommonFunction $commonFunction)
    {
        $this->commonFunction = $commonFunction;
    }

    public function login(LoginRequest $request): FoundationApplication|Response|ResponseFactory
    {
        try {
            $credentials = $request->only('name', 'password');
            $credentials['active_status'] = MyHelper::$RS_ACTIVE;

            if (Auth::attempt($credentials)) {
                $user = Auth::user();

                $tokenResult = $user->createToken('app');
                $token = $tokenResult->accessToken;

                $selectedUser = $user->only(['name', 'BRANCH_ID','email','user_type']);

                return response([
                    'success' => true,
                    'message' => "Successfully Login",
                    'api_token' => $token,
                    'user' => $selectedUser,
                ], 200);
            }
        } catch (Exception $exception) {
            return response([
                'success' => false,
                'message' => $exception->getMessage()
            ], 500);
        }
        return response([
            'success' => false,
            'message' => 'Invalid Name Or Password'
        ], 401);
    }

    public function employeemobileverification(Request $request): FoundationApplication|Response|ResponseFactory
    {
        try{
            $mob_val =  preg_match('/^[0-9]{10}+$/', $request->name);

            if($mob_val ==1){
                $employee = User::where('users.name', $request->name)
                                ->where('users.active_status', 1)
                                ->first();

                if(isset($employee) && $employee->created_password == NULL){
                    //OTP Section Code Start
                    $message_request = [
                        "user_id" => $employee->id,
                        "branch_id" => $employee->BRANCH_ID,
                        "sms_name" => "emp_login_otp",
                        "content_type" => 1
                    ];

                    $sms_with_content = $this->smscreation(json_encode($message_request));

                    if($sms_with_content->original['success'] == true){
                        $message = urlencode($sms_with_content->original['message']);
                        $mobileno = $request->name;
                        $originator = MyHelper::$PR_SMSTAG; 

                        $urltopost = "https://hp.dial4sms.com/SendSMS/sendmsg.php?uname=ntltaxi&pass=NTL@ind1&send=".$originator."&dest=".$mobileno."&msg=".$message;
                        
                        // $ch = curl_init ($urltopost);
                        // curl_setopt ($ch, CURLOPT_RETURNTRANSFER, true);
                        // $returndata = curl_exec($ch);
                    }
                    return response([
                        'success' => 2,
                        'message' => "Successfully OTP Send",
                        'message_content' => $sms_with_content->original['message'],
                        'Emp_id'=> $employee->id
                    ], 200);
                }else{
                    return response([
                        'success' => 1,
                        'message' => 'Mobile Number and password already exist'
                    ], 401);
                }
            }else{
                return response([
                    'success' => 0,
                    'message' => 'Invalid Mobile Number'
                ], 401);
            }
        }catch(Exception $exception) {
            return response([
                'success' => false,
                'message' => $exception->getMessage()
            ], 500);
        }
    }

    public function employeeotpVerification(Request $request): FoundationApplication|Response|ResponseFactory
    {
        try{
            $mob_val =  preg_match('/^[0-9]{10}+$/', $request->name);

            if($mob_val ==1){
                $employee = User::where('users.name', $request->name)
                                ->where('users.active_status', 1)
                                ->first();
                            
                if(isset($employee) && $employee->created_password!= NULL){
                    $otp = $request->otp;
                    $password_check = strcmp($request->password,$request->confirmed_password);
                    
                    // $decrypt_otp = bcrypt($otp);
                    if($otp == $employee->created_password && $password_check == 0){
                        $encrypt_password = $this->commonFunction->AES_ENCRYPT($request->password, config('app.aes_encrypt_key'));

                        $update_otp = "UPDATE `users` SET `password`='$encrypt_password',`created_password`='$request->password' WHERE id='$employee->id' and active_status=1";
                        DB::update($update_otp);
                        return response([
                            'success' => 1,
                            'message' => 'Password Set Successfully'
                        ], 200);
                    }else{
                        return response([
                            'success' => 2,
                            'message' => 'Invalid OTP'
                        ], 401);
                    }
                    
                }
            }else{
                return response([
                    'success' => 0,
                    'message' => 'Invalid Mobile Number'
                ], 401);
            }
        }catch(Exception $exception) {
            return response([
                'success' => false,
                'message' => $exception->getMessage()
            ], 500);
        }
    }


    public function verifyToken(): FoundationApplication|Response|ResponseFactory
    {
        try {
            $user = Auth::user();
            $selectedUser = $user->only(['name', 'BRANCH_ID', 'email', 'user_type']);

            return response([
                'success' => true,
                'message' => "Successfully Login",
                'name' => $selectedUser['name'],
                'BRANCH_ID' => $selectedUser['BRANCH_ID'],
                'email' => $selectedUser['email'],
                'user_type' => $selectedUser['user_type'],
            ], 200);

        } catch (Exception $exception) {
            return response([
                'success' => false,
                'message' => $exception->getMessage()
            ], 500);
        }
    }

    public function otpCreation(OtpCreationRequest $request): FoundationApplication|Response|ResponseFactory
    {
        try {
            $user = User::where('name', $request->input('mobile'))
                ->where('active_status', MyHelper::$RS_ACTIVE)
                ->first();

            if ($user) {
                $tokenResult = $user->createToken('app');
                $token = $tokenResult->accessToken;
                $tokenId = $tokenResult->token->id;

                $otp = mt_rand(100000, 999999);
                $message = "Your OTP is: $otp";

                OtpVerify::create([
                    'MOBILE_NO' => $request->input('mobile'),
                    'ROSTER_PASSENGER_ID' => 0,
                    'OTP' => $otp,
                    'VERIFIED_STATUS' => '0',
                    'OTP_CATEGORY' => 'EmpLogin',
                    'token_id' => $tokenId,
                    'SMS_RESPONSE' => $message,
                    'request_coming_from' => $request->header('User-Agent'),
                    'CREATED_BY' => $user->id,
                    'CREATED_DATE' => Carbon::now()
                ]);

                $smsSent = $this->sendSms($request->input('mobile'), $message);

                if ($smsSent) {
                    return response([
                        'success' => true,
                        'message' => "OTP created and sent successfully",
                        'token' => $token,
                    ]);
                } else {
                    return response([
                        'success' => false,
                        'message' => "OTP created but failed to send SMS",
                        'token' => $token,
                    ]);
                }
            }
        } catch (Exception $exception) {
            Log::error("Error in otpCreation: " . $exception->getMessage());
            return response([
                'success' => false,
                'message' => $exception->getMessage()
            ]);
        }
        return response([
            'success' => false,
            'message' => 'Invalid mobile number'
        ]);
    }

    public function otpVerification(OtpVerificationRequest $request):FoundationApplication|Response|ResponseFactory
    {

        try {
            $user = Auth::guard('api')->user();
            $tokenId = $user->token()->id;

            $otpRecord = OtpVerify::where('OTP', $request->otp)
                ->where('token_id', $tokenId)
                ->where('VERIFIED_STATUS', '0')
                ->first();

            if (!$otpRecord) {
                return response([
                    'success' => false,
                    'message' => 'Invalid OTP',
                ]);
            }


            $expiryTime = Carbon::parse($otpRecord->CREATED_DATE)->addMinutes(MyHelper::$OTP_EXPIRE_TIME);
            if (Carbon::now()->isAfter($expiryTime)) {
                return response([
                    'success' => false,
                    'message' => 'OTP has expired',
                ]);
            }


            $otpRecord->update([
                'VERIFIED_STATUS' => '1',
                'UPDATED_BY' => Auth::id(),
                'updated_at' => Carbon::now(),
            ]);

            return response([
                'success' => true,
                'message' => 'OTP verified successfully',
            ]);

        } catch (Exception $exception) {
            return response([
                'success' => false,
                'message' => $exception->getMessage()
            ]);
        }
    }

    private function sendSms($mobileNumber, $message): bool
    {
        try {
            Log::info("Sending SMS to: $mobileNumber, Message: $message");
            Log::info("SMS sent successfully to: $mobileNumber");
            return true;
        } catch (Exception $e) {
            Log::error("Failed to send SMS to: $mobileNumber. Error: " . $e->getMessage());
            return false;
        }
    }


    public function logoutAllDevices(): FoundationApplication|Response|ResponseFactory
    {
        $userId = auth()->id();

        DB::table('oauth_access_tokens')
            ->where('user_id', $userId)
            ->delete();

        return response([
            'success' => true,
            'message' => 'Logout successful from all devices',
        ]);
    }

    public function logoutCurrentDevice()
    {

        $user = Auth::guard('api')->user();

        if ($user) {

            $tokenId = $user->token()->id;

            DB::table('oauth_access_tokens')->where('id', $tokenId)->delete();

            return response()->json([
                'success' => true,
                'message' => 'Successfully logged out from the current device',
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Unable to log out. User not found.',
        ], 404);
    }

    public function getotpverify_forget(Request $request){
        $curdate = date('Y-m-d H:i:s');
        $mobileno = $request->mobileno;
        $ENKEY = env('AES_ENCRYPT_KEY');//config('app.aes_encrypt_key'); - env('AES_ENCRYPT_KEY')
        $PR_SMSTAG =  MyHelper::$PR_SMSTAG;
        $obj = new CommonFunction();
        $getmobile = $obj->AES_ENCRYPT($mobileno,$ENKEY);
        $data = DB::select("SELECT BRANCH_ID,`NAME` as empname,MOBILE FROM employees WHERE MOBILE = '$getmobile' and ACTIVE=1");
        if(isset($data)){
            $branchid = $data[0]->BRANCH_ID;
            $ORIGINATOR = $obj->GetPropertyValue($PR_SMSTAG,$branchid);
            $empname = $obj->AES_DECRYPT($data[0]->empname,$ENKEY);
            $EMP_OTP = substr(number_format(time() * rand(), 0, '', ''), 0, 4);
            $bname = 'Mobile';

            $employee = User::where('users.name', $mobileno)
                                ->where('users.active_status', 1)
                                ->first();

            if(isset($employee)){
                //OTP Section Code Start
                $message_request = [
                    "user_id" => $employee->id, //33794, 42
                    "branch_id" => $employee->BRANCH_ID, //18,48 $employee->BRANCH_ID
                    "sms_name" => "emp_forget_password_otp",
                    "content_type" => 1
                ];

                $sms_with_content = $this->smscreation(json_encode($message_request));

                if($sms_with_content->original['success'] == true){
                    $message = urlencode($sms_with_content->original['message']);

                    $urltopost = "https://hp.dial4sms.com/SendSMS/sendmsg.php?uname=ntltaxi&pass=NTL@ind1&send=".$ORIGINATOR."&dest=".$mobileno."&msg=".$message;
        
                    // $ch = curl_init ($urltopost);
                    // curl_setopt ($ch, CURLOPT_RETURNTRANSFER, true);
                    // $returndata = curl_exec($ch); 

                    // $ENCRYPT_OTP =  bcrypt($EMP_OTP);
                    // $inser_otp = "UPDATE `users` SET `password`='$ENCRYPT_OTP',`created_password`='$EMP_OTP'  WHERE name='$mobileno' and active_status=1";
                    // DB::update($inser_otp);

                    return response([
                        'success' => true,
                        'message' => 'OTP Message sent sucessfully',
                        'message_content' => $sms_with_content->original['message']
                    ], 200);

                }else{
                    return response([
                        'success' => false,
                        'message' => 'SMS Template Creation Error'
                    ], 401);
                }
                var_dump($message);die;
            }
        }else{
            return response([
                'success' => false,
                'message' => 'Employee Not Found'
            ], 401);
        } 
    }



}
