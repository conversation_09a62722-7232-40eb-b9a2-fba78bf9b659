<?php
namespace App\Services\Admin;

use App\Helpers\CommonFunction;
use App\Helpers\MyHelper;
use App\Models\Branch;
use Carbon\Carbon;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use App\Models\property;

class AutoRouteService
{
    //
    protected CommonFunction $commonFunction;
    protected  RosterUploadService $rosteruploadservice;
    protected ?Branch $currentBranch = null;

    public function __construct(CommonFunction $commonFunction, RosterUploadService $rosteruploadservice)
    {
        $this->commonFunction = $commonFunction;
        $this->rosteruploadservice = $rosteruploadservice;
    }

    public function dataForCreate(): FoundationApplication|Response|ResponseFactory
    {
        try{
            $gettime = $this->GetarrivedShiftTime();
            $getroutettl = $this->GetAutoRouteCount();
            $allvendors = $this->GetAllVendors();
            $route_employee_totals = [
                                    [
                                        "label" => "Ttl-Routes",
                                        "value" => $getroutettl[0]->tlroute
                                    ],
                                    [
                                        "label" => "Ttl-Emp",
                                        "value" => $getroutettl[0]->tlemp
                                    ]
                                ];

            // foreach ($getroutettl[0] as $key => $value) {
            //     if ($key == 'tlroute' || $key == 'tlemp') {
            //         unset($getroutettl[0]->$key);
            //     }
            // }

            $route_operational_stats = [
                [
                    "single_seat_routes" => "4S",
                    "total_vehicles" => $getroutettl[0]->seater4,
                    "total_passengers" => $getroutettl[0]->seatercount4,
                ],
                [
                    "single_seat_routes" => "5S",
                    "total_vehicles" => $getroutettl[0]->seater5,
                    "total_passengers" => $getroutettl[0]->seatercount5,
                ],
                [
                    "single_seat_routes" => "6S",
                    "total_vehicles" => $getroutettl[0]->seater6,
                    "total_passengers" => $getroutettl[0]->seatercount6,
                ],
                [
                    "single_seat_routes" => "7S",
                    "total_vehicles" => $getroutettl[0]->seater7,
                    "total_passengers" => $getroutettl[0]->seatercount7,
                ],
                [
                    "single_seat_routes" => "8S",
                    "total_vehicles" => $getroutettl[0]->seater8,
                    "total_passengers" => $getroutettl[0]->seatercount8,
                ],
                [
                    "single_seat_routes" => "15S",
                    "total_vehicles" => $getroutettl[0]->seater15,
                    "total_passengers" => $getroutettl[0]->seatercount15,
                ],
                [
                    "single_seat_routes" => "12S",
                    "total_vehicles" => $getroutettl[0]->seater12,
                    "total_passengers" => $getroutettl[0]->seatercount12,
                ]
            ];
            
            // dd($getroutettl);
            
            return response([
                'success' => true,
                'status' => 3,
                'data' => [
                    'gettime' => $gettime,
                    'route_employee_totals' => $route_employee_totals,
                    'route_operational_stats' => $route_operational_stats,
                    'allvendors' => $allvendors
                ],
                'message' => 'Data for Create Successfully',
    
            ],200);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Data For Create for autorouting  Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ],500);
        }
    }

    public function AutoRouteAjax($request){
        try{
            $routeintime = $request->routeintime;
            $routetype = $request->routetype;

            if ($routeintime != '' && $routetype == 'shifttime') {
                $data = explode("/",$routeintime);
                $triptype = $data[0];
                $triptime = $data[1];

                $routedata = $this->GetRouteDataTime($routeintime);
                $routedata_with_employee_details = $this->GetRouteDetails($routedata,$routeintime);
                $gettime = $this->GetarrivedShiftTime();
                $getroutettl = $this->GetAutoRouteCount($triptime);

                if($getroutettl[0]->seater4 != 0){
                    $model4seater = round($getroutettl[0]->seatercount4 / ($getroutettl[0]->seater4 * 4)*100,2);
                }else{
                    $model4seater = 0;
                }
                if($getroutettl[0]->seater6 != 0) {
                    $model6seater = round($getroutettl[0]->seatercount6 / ($getroutettl[0]->seater6 * 8) * 100, 2);
                }else{
                    $model6seater = 0;
                }
                if($getroutettl[0]->seater8 != 0) {
                    $model8seater = round($getroutettl[0]->seatercount8 / ($getroutettl[0]->seater8 * 15) * 100, 2);
                }else{
                    $model8seater = 0;
                }
                if($getroutettl[0]->seater12 != 0) {
                    $model12seater = round($getroutettl[0]->seatercount12 / ($getroutettl[0]->seater12 * 22) * 100, 2);
                }else{
                    $model12seater = 0;
                }

                $route_employee_totals = [
                    [
                        "label" => "Ttl-Routes",
                        "value" => $getroutettl[0]->tlroute
                    ],
                    [
                        "label" => "Ttl-Emp",
                        "value" => $getroutettl[0]->tlemp
                    ]
                ];

                $route_operational_stats = [
                    [
                        "single_seat_routes" => "4S",
                        "total_vehicles" => $getroutettl[0]->seater4,
                        "total_passengers" => $getroutettl[0]->seatercount4,
                        "percentage" => $model4seater,
                    ],
                    [
                        "single_seat_routes" => "5S",
                        "total_vehicles" => $getroutettl[0]->seater5,
                        "total_passengers" => $getroutettl[0]->seatercount5,
                        "percentage" => 0,
                    ],
                    [
                        "single_seat_routes" => "6S",
                        "total_vehicles" => $getroutettl[0]->seater6,
                        "total_passengers" => $getroutettl[0]->seatercount6,
                        "percentage" => $model6seater,
                    ],
                    [
                        "single_seat_routes" => "7S",
                        "total_vehicles" => $getroutettl[0]->seater7,
                        "total_passengers" => $getroutettl[0]->seatercount7,
                        "percentage" => 0,
                    ],
                    [
                        "single_seat_routes" => "8S",
                        "total_vehicles" => $getroutettl[0]->seater8,
                        "total_passengers" => $getroutettl[0]->seatercount8,
                        "percentage" => $model8seater,
                    ],
                    [
                        "single_seat_routes" => "15S",
                        "total_vehicles" => $getroutettl[0]->seater15,
                        "total_passengers" => $getroutettl[0]->seatercount15,
                        "percentage" => 0,
                    ],
                    [
                        "single_seat_routes" => "12S",
                        "total_vehicles" => $getroutettl[0]->seater12,
                        "total_passengers" => $getroutettl[0]->seatercount12,
                        "percentage" => $model12seater,
                    ]
                ];

                $result_data['routedata'] = $routedata_with_employee_details;
                $result_data['gettime'] = $gettime;
                $result_data['route_employee_totals'] = $route_employee_totals;
                $result_data['route_operational_stats'] = $route_operational_stats;
                // $result_data['getroutettl'] = $getroutettl;
                // $result_data['model4seater'] = $model4seater;
                // $result_data['model6seater'] = $model6seater;
                // $result_data['model8seater'] = $model8seater;
                // $result_data['model12seater'] = $model12seater;


                return response([
                    'success' => true,
                    'status' => 3,
                    'routedata' => $routedata_with_employee_details,
                    'gettime' => $gettime,
                    'route_employee_totals' => $route_employee_totals,
                    'route_operational_stats' => $route_operational_stats,
                    // "data" => $result_data,
                    'message' => 'Newroute Created Successfully',
        
                ],200);
            }
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Data For Create for autorouting  Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ],500);
        }
    }

    public function GetRouteDataTime($triptime){
        $data = explode("/",$triptime);
        $triptype = $data[0];
        $triptime = $data[1];
        $branch_id = Auth::user()->BRANCH_ID;
        $DIVISION_ID = $this->commonFunction->getDivisionId($branch_id);
        if($triptype == 'D'){
            $loc = 'ER.END_LOCATION';
        }else{
            $loc = 'ER.START_LOCATION';
        }
		if($branch_id==60)
		{
			$sql="SELECT T.*,COUNT(T.EMPLOYEE_ID) as ttlempcnt FROM (SELECT ER.ROUTE_ID
        ,ER.EMPLOYEE_ID,if(ER.TRIP_TYPE = 'P',ER.START_LOCATION,ER.END_LOCATION) LOCATION
        ,ER.DISTANCE as APPROVED_DISTANCE,ER.VENDOR_NAME
        FROM employees_roster_request ER
				 INNER JOIN employees E ON E.EMPLOYEES_ID = ER.EMPLOYEE_ID and E.BRANCH_ID='$branch_id'
				inner join shuttle_stops S on S.Stops_ID=E.STOPS_ID
				WHERE ER.BRANCH_ID =$branch_id AND ER.ROUTE_ID != 0 AND ER.`STATUS` = 1 AND ER.TRIP_TYPE = '$triptype' AND ER.ESTIMATE_START_TIME = '$triptime' ORDER BY ER.DISTANCE DESC) T GROUP BY T.ROUTE_ID";
	    /*  */
		}
		else{
         $sql = "SELECT T.*,COUNT(T.EMPLOYEE_ID) as ttlempcnt FROM (SELECT ER.ROUTE_ID
        ,ER.EMPLOYEE_ID,if(ER.TRIP_TYPE = 'P',ER.START_LOCATION,ER.END_LOCATION) LOCATION
        ,AP.APPROVED_DISTANCE,ER.VENDOR_NAME
        FROM employees_roster_request ER
        INNER JOIN locations L ON L.LOCATION_NAME = $loc AND L.DIVISION_ID = $DIVISION_ID
        INNER JOIN approve_distances AP ON AP.BRANCH_ID = $branch_id AND AP.LOCATION_ID = L.LOCATION_ID
        WHERE ER.BRANCH_ID = $branch_id AND ER.ROUTE_ID != 0 AND ER.`STATUS` = 1 AND ER.TRIP_TYPE = '$triptype' AND ER.ESTIMATE_START_TIME = '$triptime' 
        ORDER BY AP.APPROVED_DISTANCE DESC) T GROUP BY T.ROUTE_ID";
		}
        return DB::select($sql);
    }

    public function GetarrivedShiftTime()
    {
        $RS_ACTIVE = MyHelper::$RS_ACTIVE;
        $branch_id = Auth::user()->BRANCH_ID;
        $curdate = date('Y-m-d');
        // $curdate = date('2025-01-08'); // Testing purpose
        $sql = "SELECT FILE_NAME,CONCAT(TRIP_TYPE,'/',ESTIMATE_START_TIME) as ESTIMATESTARTTIME FROM employees_roster_request 
        WHERE DATE(ESTIMATE_START_TIME) >= '$curdate' AND ROUTE_ID != 0  AND `STATUS` = 1 AND BRANCH_ID= '$branch_id' GROUP BY ESTIMATE_START_TIME,TRIP_TYPE";
        return DB::select($sql);
    }

    public function GetAutoRouteCount($triptime = 0)
    {
        $branch_id = Auth::user()->BRANCH_ID;
        
        if($triptime == 0){
            $triptime = date('Y-m-d');
            $trip_flag = 0;
        }else{
            $triptime = date('Y-m-d H:i:s', strtotime($triptime));
            $trip_flag = 1;
        }
        
		$sql="SELECT SUM(TR.ttlroute) as tlroute,SUM(TR.totalcount) as tlemp
			,sum(if(TR.totalcount < 4,1,0)) as seater4
			,sum(if(TR.totalcount < 4,TR.totalcount,0)) as seatercount4 
			
			,sum(if(TR.totalcount= 4,1,0)) as seater5
			,sum(if(TR.totalcount =4,TR.totalcount,0)) as seatercount5
			
			,sum(if(TR.totalcount = 5,1,0)) as seater6
			,sum(if(TR.totalcount = 5,TR.totalcount,0)) as seatercount6
			
			,sum(if(TR.totalcount=6,1,0)) as seater7
			,sum(if(TR.totalcount=6,TR.totalcount,0)) as seatercount7
			
			,sum(if(TR.totalcount=7,1,0)) as seater8
			,sum(if(TR.totalcount=7,TR.totalcount,0)) as seatercount8
			
			,sum(if(TR.totalcount >=8 ,1,0)) as seater15
			,sum(if(TR.totalcount >=8,TR.totalcount,0)) as seatercount15
			
			,sum(if(TR.totalcount >= 15,1,0)) as seater12
			,sum(if(TR.totalcount > 15,TR.totalcount,0)) as seatercount12 
			
			FROM (SELECT COUNT(DISTINCT(ROUTE_ID)) as ttlroute,COUNT(EMPLOYEE_ID) as totalcount
			FROM employees_roster_request 
			WHERE BRANCH_ID ='".$branch_id."' AND ROUTE_ID != 0 AND `STATUS` = 1 AND ";
            if($trip_flag == 0){
                $sql = $sql."DATE(ESTIMATE_START_TIME) = '".$triptime;
            }else{
                $sql = $sql."ESTIMATE_START_TIME = '".$triptime;
            }
            $sql = $sql."'  GROUP BY ESTIMATE_START_TIME,ROUTE_ID) TR";
            
        return DB::select($sql);
    }

    public function GetAllVendors()
    {
        $RS_ACTIVE = MyHelper::$RS_ACTIVE;
        $branch_id = Auth::user()->BRANCH_ID;
        $curdate = date('Y-m-d');
        $sql = "SELECT `NAME` as vendorname FROM vendors WHERE BRANCH_ID = $branch_id and ACTIVE = '$RS_ACTIVE'";
        return DB::select($sql);
    }

    public function GetRouteDetails($routedata,$routeintime)
    {
        $data = explode("/",$routeintime);
        $triptype = $data[0];
        $triptime = $data[1];
        $branch_id = Auth::user()->BRANCH_ID;
        
        $routedata_final = [];

        foreach($routedata as $data){
            $sql = "SELECT ER.ROUTE_ID,ER.OLD_ROUTE_ID,ER.ROSTER_REQ_ID,ER.EMPLOYEE_ID,ER.ALIAS_LOCATION
            ,if(ER.TRIP_TYPE = 'P',REPLACE(ER.END_LOCATION,' ',''),REPLACE(ER.START_LOCATION,' ','')) ENDLOCATION 
            ,E.`NAME`,E.GENDER ,
            if(ER.TRIP_TYPE = 'P',ER.START_LOCATION,ER.END_LOCATION) LOCATION
            ,if(ARD.GOOGLE_DISTANCE != '',ARD.GOOGLE_DISTANCE,0) as APPROVED_DISTANCE,time(ER.ESTIMATE_TIME) as estimatetime 
            FROM employees_roster_request ER 
            INNER JOIN employees E ON E.EMPLOYEES_ID = ER.EMPLOYEE_ID AND E.BRANCH_ID = ER.BRANCH_ID
            LEFT JOIN auto_route_distance ARD ON ARD.FROM_LOCATION_ID = REPLACE(if(ER.TRIP_TYPE = 'P',ER.START_LOCATION,ER.END_LOCATION),' ','')
            AND ARD.TO_LOCATION_ID = REPLACE(if(ER.TRIP_TYPE = 'P',ER.END_LOCATION,ER.START_LOCATION),' ','')
            WHERE ER.BRANCH_ID = $branch_id AND ER.ROUTE_ID = '$data->ROUTE_ID' AND ER.TRIP_TYPE = '$triptype' AND 
            ER.ESTIMATE_START_TIME = '$triptime' AND ER.`STATUS` = 1 ORDER BY ARD.GOOGLE_DISTANCE DESC";

            $data->employee_details = DB::select($sql);

            foreach($data->employee_details as $emp_details)
            {
                $emp_details->NAME = $this->commonFunction->AES_DECRYPT($emp_details->NAME, config('app.aes_encrypt_key'));
		        //print_r($emp_details->NAME);
            }


            array_push($routedata_final, $data);
        }

        return $routedata_final;
    }

    public function AutoRouteClubData($request){
        try {
            // $insertdata = $request->insertdata[0];
            $insertdata = $request->insertdata;
            $inserid = $request->to_id;

            $result =$this->SetRouteClubData($inserid,$insertdata);

            // if($result == 1){
                return response([
                    'success' => true,
                    'status' => 1,
                   'message' => 'Route has been successfully updated.',
                ],200);
            // }else{
            //     return response([
            //        'success' => true,
            //        'status' => 0,
            //        'message' => 'Route update Unsuccessful.',
            //     ],400);
            // }
            
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Autorouteclubdata set  Unsuccessful',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ],500);
        }
    }

    public function SetRouteClubData($sourceroute,$clubdatas)
    {
        $id = Auth::user()->id;
        foreach($clubdatas as $clubdata)
        {
            $from_id = $clubdata['from_id'];
            $emp_id = $clubdata['emp_id'];
            $branch_id = Auth::user()->BRANCH_ID;
            $curdate = date('Y-m-d H:i:s');
            $sql = "UPDATE employees_roster_request SET ROUTE_ID='$sourceroute',OLD_ROUTE_ID = '$from_id',UPDATED_BY='$id',updated_at='$curdate' WHERE BRANCH_ID='$branch_id' AND EMPLOYEE_ID='$emp_id' AND ROUTE_ID='$from_id'";
            $queryvalue = DB::update($sql);
        }
        return $queryvalue;
    }

    public function GetRouteDetailsByRouteID($request){
        try {
            $branch_id = Auth::user()->BRANCH_ID;
            $DIVISION_ID = $this->commonFunction->getDivisionId($branch_id);

            $route_id = $request->route_id;
            $login_time = $request->in_time;
            // $list = '';
            // $route_id_A = str_replace("A", "", $route_id);

            // $poly_line = DB::select("SELECT * from polyline_route_path where ROUTE_ID='" . $route_id_A . "'");

            $sql = "SELECT T.* FROM (SELECT ER.ROUTE_ID ,ER.EMPLOYEE_ID,ER.VENDOR_NAME,IF(ER.TRIP_TYPE='P',L.LOCATION_NAME,L1.LOCATION_NAME) AS LOCATION,IF(ER.TRIP_TYPE='P',L.LATITUDE,L1.LATITUDE) AS LATITUDE ,IF(ER.TRIP_TYPE='P',L.LONGITUDE,L1.LONGITUDE) AS LONGITUDE,B.LAT as cmp_lat,B.LONG as cmp_long,B.BRANCH_NAME as company_name ,ER.OLD_ROUTE_ID,ER.CLUB_STATUS,ER.CLUB_DISTANCE FROM employees_roster_request ER 
                    inner join branch B on B.BRANCH_ID=ER.BRANCH_ID 
                    inner join employees E on E.EMPLOYEES_ID=ER.EMPLOYEE_ID and E.BRANCH_ID=$branch_id
                    INNER JOIN locations L ON L.LOCATION_NAME = ER.START_LOCATION AND L.DIVISION_ID =  $DIVISION_ID
                    INNER JOIN locations L1 ON L1.LOCATION_NAME = ER.END_LOCATION AND L1.DIVISION_ID =  $DIVISION_ID
                    INNER JOIN approve_distances AP ON AP.BRANCH_ID = 48 AND AP.LOCATION_ID = L.LOCATION_ID 
                    WHERE ER.BRANCH_ID = $branch_id AND ER.ROUTE_ID = '" . $route_id . "' and ER.ESTIMATE_START_TIME='" . $login_time . "'  AND ER.`STATUS` = 1 ORDER BY E.DISTANCE DESC ) T";



            if($branch_id != 48)
            {
                $sql=" SELECT T.* FROM (SELECT ER.ROUTE_ID,ER.OLD_ROUTE_ID,ER.CLUB_STATUS,ER.EMPLOYEE_ID,if(ER.TRIP_TYPE = 'P',ER.START_LOCATION,ER.END_LOCATION) LOCATION ,AP.APPROVED_DISTANCE,ER.VENDOR_NAME,L.LATITUDE,L.LONGITUDE,B.LAT as cmp_lat,B.LONG as cmp_long, B.BRANCH_NAME as company_name  FROM employees_roster_request ER 
                left join branch B on B.BRANCH_ID=ER.BRANCH_ID left JOIN locations L ON L.LOCATION_NAME = if(ER.TRIP_TYPE = 'P',ER.START_LOCATION,ER.END_LOCATION) AND L.DIVISION_ID = '$DIVISION_ID' 
                left JOIN approve_distances AP ON AP.BRANCH_ID = $branch_id AND AP.LOCATION_ID = L.LOCATION_ID 
                WHERE ER.BRANCH_ID = $branch_id AND ER.ROUTE_ID = '$route_id' and ER.ESTIMATE_START_TIME='$login_time' AND ER.`STATUS` = 1 ORDER BY AP.APPROVED_DISTANCE DESC) T";	
            }

            $data = DB::select($sql);
            
            if (count($data) > 0) {

                $mark2 = array();
                $mark1 = array();

                $cmp_lat = $data[0]->cmp_lat;
                $cmp_long = $data[0]->cmp_long;
                $cmp_name =$data[0]->company_name;
                $cmp_mark = array("lat" => $cmp_lat, "lng" => $cmp_long);

                for ($i = 0; $i < count($data); $i++) {

                    $club_status = '';
                    if ($data[$i]->OLD_ROUTE_ID != '') {
                        $status = "yellow";
                    } else {
                        $status = "red";
                    }

                    if ($data[$i]->CLUB_STATUS == 1) {
                        $status = 'green';
                        $club_status = 'First Level- ' . $data[$i]->CLUB_DISTANCE;
                    } elseif ($data[$i]->CLUB_STATUS == 2) {
                        $status = 'yellow';
                        $club_status = 'Second Level - ' . $data[$i]->CLUB_DISTANCE;
                    }
                    $alat = array("lat" => $data[$i]->LATITUDE, "lng" => $data[$i]->LONGITUDE, "LOCATION" => $data[$i]->LOCATION . "-" . $data[$i]->EMPLOYEE_ID . "-" . $data[$i]->OLD_ROUTE_ID, "OLD_ROUTE_ID" => $data[$i]->OLD_ROUTE_ID, "STATUS" => $status, "description" => $data[$i]->LOCATION . "-" . $data[$i]->EMPLOYEE_ID . "-<br>" . $club_status);

                    array_push($mark2, $alat);
                }
                
                $alat = array("lat" => $cmp_lat, "lng" => $cmp_long, "LOCATION" => $cmp_name, "description" => $cmp_name, "STATUS" => "orange");

                array_push($mark2, $cmp_mark);
                array_push($mark2, $alat);
                $mark1 = array_values(array_unique($mark2, SORT_REGULAR));

                // $list = json_encode($mark1);

                return response([
                    'success' => true,
                    'status' => 1,
                    'employee_location' => $mark1,
                    'message' => 'Route Details fetch has been successfully.',
                ],200);
            } else {
                return response([
                    'success' => true,
                    'status' => 0,
                    'message' => 'Route Details fetch  Unsuccessful.',
                ],400);
            }
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Route Details fetch  Unsuccessful.',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ],500);
        }
    }

    public function NewRouteCreate($request){
        try{
                $tripdatetime = $request->tripdatetime;
                $data = explode("/",$tripdatetime);
                $triptype = $data[0];
                $triptime = $data[1];

                $branch_id = Auth::user()->BRANCH_ID;

                $sql = "SELECT (ROUTE_ID + 1) as ROUTEID FROM employees_roster_request WHERE BRANCH_ID=$branch_id AND TRIP_TYPE='$triptype' 
                AND ESTIMATE_START_TIME = '$triptime' GROUP BY ROUTE_ID ORDER BY ROUTE_ID DESC LIMIT 1";

                $data = DB::select($sql);
                // return $data[0]->ROUTEID;

                if(isset($data[0]->ROUTEID)){
                    return response([
                        'success' => true,
                        'status' => 1,
                        'route_id' => $data[0]->ROUTEID,
                        'message' => 'New Route ID has been created successfully.',
                    ],200);
                }else{
                    return response([
                        'success' => true,
                        'status' => 0,
                        'message' => 'New Route ID creation Unsuccessful.',
                    ],400);
                }

            
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Route Details fetch  Unsuccessful.',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ],500);
        }
    }

    public function MergeAutoRoute($request){
        try{
            $data = explode("/",$request->intimesel);
            $triptype = $data[0];
            $triptime = $data[1];
            $sourcetrip = $request->sourcetrip;
            $desttrip = $request->desttrip;
            $id = Auth::user()->id;
            $branch_id = Auth::user()->BRANCH_ID;
            $curdate = date('Y-m-d H:i:s');
            
            $sql = "UPDATE employees_roster_request SET ROUTE_ID='$desttrip',OLD_ROUTE_ID = '$sourcetrip',UPDATED_BY='$id',updated_at='$curdate' WHERE BRANCH_ID='$branch_id' AND ROUTE_ID='$sourcetrip' AND TRIP_TYPE = '$triptype' AND ESTIMATE_START_TIME = '$triptime'";
            DB::update($sql);

            return response([
                'success' => true,
                'status' => 1,
                'message' => 'Route Merged successfully.',
            ],200);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Merge Route Unsuccessful.',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ],500);
        }
    }

    public function SetRouteVendor($request){
        try {
            $data = explode("/",$request->routeintime);
            $triptype = $data[0];
            $triptime = $data[1];
            $routeids = $request->routeids;
            $vendorsel = $request->vendorsel;
            $id = Auth::user()->id;
            $branch_id = Auth::user()->BRANCH_ID;
            $curdate = date('Y-m-d H:i:s');

            $sql = "UPDATE employees_roster_request SET VENDOR_NAME='$vendorsel',UPDATED_BY='$id',updated_at='$curdate' WHERE BRANCH_ID='$branch_id' AND TRIP_TYPE='$triptype' AND ESTIMATE_START_TIME = '$triptime' AND ROUTE_ID in ($routeids)";
            DB::update($sql);

            return response([
                'success' => true,
                'status' => 1,
                'message' => 'Vendor Route Set successfully.',
            ],200);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Vendor Route Set Unsuccessful.',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ],500);
        }
    }

    public function AutoRouteToRoster($request){
        try {
            $branch_id = Auth::user()->BRANCH_ID;
            $DIVISION_ID = $this->commonFunction->getDivisionId($branch_id);

            $data = explode("/",$request->routeintime);
            $triptype = $data[0];
            $triptime = $data[1];

            $curdatetime = date('Y-m-d H:i:s');
            $RS_ACTIVE = MyHelper::$RS_ACTIVE;
            $id = Auth::user()->id;
            $AES_KEY = env("AES_ENCRYPT_KEY");
            //$locations=$triptype=='P'?'ER.START_LOCATION':'ER.END_LOCATION';
            if($triptype=='P')
            {
                $locations='ER.START_LOCATION';
            }
            else
            {
                $locations='ER.END_LOCATION';
            }

            $emp="SELECT ER.ROUTE_ID,E.EMPLOYEES_ID,ER.BRANCH_ID,E.`NAME` as emp_name,E.MOBILE,E.GENDER,E.PROJECT_NAME
            ,ER.TRIP_TYPE,ER.START_LOCATION,ER.END_LOCATION,ER.ESTIMATE_START_TIME,ER.ESTIMATE_TIME,ER.VENDOR_NAME,L.LOCATION_NAME
            FROM employees_roster_request ER 
            INNER JOIN employees E ON E.EMPLOYEES_ID = ER.EMPLOYEE_ID AND E.BRANCH_ID = ER.BRANCH_ID AND E.ACTIVE = $RS_ACTIVE
            INNER JOIN locations L ON L.LOCATION_ID = E.LOCATION_ID AND L.DIVISION_ID = $DIVISION_ID 
            WHERE ER.BRANCH_ID = $branch_id AND ER.ROUTE_ID != 0 AND ER.`STATUS` = 1 AND ER.TRIP_TYPE = '$triptype' AND ER.ESTIMATE_START_TIME = '$triptime' 
            ORDER BY ER.ROUTE_ID";

            $emp_data=DB::select($emp);

            $insert_file = array("FILE_NAME" => 'AutoRoute', "FILE_SIZE" => 10, "FILE_TYPE" => 'csv', "STATUS" => 0, "CREATED_BY" => $id, "created_at" => date("Y-m-d H:i:s"),"BRANCH_ID"=>$branch_id);
            $last_file_id=DB::table("file_uploads")->insertGetId($insert_file);
        
            $arr = array();
            $arr2 = array();
            // $obj=new CommonController();
            // $obj2=new UploadController();
            
            for($i=0;$i<count($emp_data);$i++)
            {
                $adhoc_route_id=$emp_data[$i]->ROUTE_ID;
                $EMPLOYEES_ID=$emp_data[$i]->EMPLOYEES_ID;
                $EMP_NAME=$this->commonFunction->AES_DECRYPT($emp_data[$i]->emp_name,$AES_KEY);
                $MOBILE=$this->commonFunction->AES_DECRYPT($emp_data[$i]->MOBILE,$AES_KEY);
                $GENDER=$emp_data[$i]->GENDER;
                $PROJECT_NAME=$emp_data[$i]->PROJECT_NAME;
                $ESTIMATE_START_TIME=$emp_data[$i]->ESTIMATE_START_TIME;
                $ESTIMATE_TIME=$emp_data[$i]->ESTIMATE_TIME;
                
                $TRIP_TYPE=$emp_data[$i]->TRIP_TYPE;
                if($TRIP_TYPE=='P')
                {
                    $START_LOCATION=$emp_data[$i]->END_LOCATION;
                    $END_LOCATION=$emp_data[$i]->START_LOCATION;
                }
                else
                {
                    $START_LOCATION=$emp_data[$i]->START_LOCATION;
                    $END_LOCATION=$emp_data[$i]->END_LOCATION;
                }
                $LOCATION_NAME=$emp_data[$i]->LOCATION_NAME;
                $vendor_name=$emp_data[$i]->VENDOR_NAME;
                $arr2[] = "('" . $last_file_id . "','" . $branch_id . "','".$adhoc_route_id."','" . $TRIP_TYPE. "','" . $ESTIMATE_START_TIME . "','".$ESTIMATE_TIME."','".$START_LOCATION."','".$END_LOCATION."','".$EMPLOYEES_ID."','".$EMP_NAME."','".$GENDER."','".$MOBILE."','".$PROJECT_NAME."','".$LOCATION_NAME."','".$vendor_name."','TRIP','1','".$id."','".$curdatetime."')";
            }

            $QUERY2 = "insert into input_datas(FILE_ID,BRANCH_ID,ROUTE_ID,TRIP_TYPE,ESTIMATE_START_TIME,ESTIMATE_TIME,SITE_NAME,LOCATION,EMPLOYEE_ID,EMPLOYEE_NAME,GENDER,EMPLOYEE_MOBILE,PROJECT_NAME,ADDRESS,VENDOR_NAME,TARIFF_TYPE,STATUS,CREATED_BY,created_at) values " . implode(',', $arr2) . "";
            $result = DB::insert($QUERY2);

            // $res=$obj2->InsertRoster();
            $res=$this->rosteruploadservice->InsertRoster();

            $update="update employees_roster_request set STATUS=2 where BRANCH_ID = $branch_id AND `STATUS` = 1 AND TRIP_TYPE = '$triptype' AND ESTIMATE_START_TIME = '$triptime' ";
            $update_res=DB::update($update);
            
            return response([
                'success' => true,
                'status' => 1,
                'message' => 'AutoRouteToRoster successfully.',
            ],200);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'AutoRouteToRoster Unsuccessful.',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ],500);
        }
    }

    public function AutoRouteDetials($request){
        try {
            $data = explode("/",$request->routeintime);
            $triptype = $data[0];
            $triptime = $data[1];
            $branch_id = Auth::user()->BRANCH_ID;
            $DIVISION_ID = $this->commonFunction->getDivisionId($branch_id);
            if($triptype == 'D'){
                $loc = 'ER.END_LOCATION';
            }else{
                $loc = 'ER.START_LOCATION';
            }
            if($branch_id==60)
            {
                $sql="SELECT T.*,COUNT(T.EMPLOYEE_ID) as ttlempcnt FROM (SELECT ER.ROUTE_ID
            ,ER.EMPLOYEE_ID,if(ER.TRIP_TYPE = 'P',ER.START_LOCATION,ER.END_LOCATION) LOCATION
            ,ER.DISTANCE as APPROVED_DISTANCE,ER.VENDOR_NAME
            FROM employees_roster_request ER
                    INNER JOIN employees E ON E.EMPLOYEES_ID = ER.EMPLOYEE_ID and E.BRANCH_ID='$branch_id'
                    inner join shuttle_stops S on S.Stops_ID=E.STOPS_ID
                    WHERE ER.BRANCH_ID =$branch_id AND ER.ROUTE_ID != 0 AND ER.`STATUS` = 1 AND ER.TRIP_TYPE = '$triptype' AND ER.ESTIMATE_START_TIME = '$triptime' ORDER BY ER.DISTANCE DESC) T GROUP BY T.ROUTE_ID";
            /*  */
            }
            else{
            $sql = "SELECT T.*,COUNT(T.EMPLOYEE_ID) as ttlempcnt FROM (SELECT ER.ROUTE_ID
            ,ER.EMPLOYEE_ID,if(ER.TRIP_TYPE = 'P',ER.START_LOCATION,ER.END_LOCATION) LOCATION
            ,AP.APPROVED_DISTANCE,ER.VENDOR_NAME
            FROM employees_roster_request ER
            INNER JOIN locations L ON L.LOCATION_NAME = $loc AND L.DIVISION_ID = $DIVISION_ID
            INNER JOIN approve_distances AP ON AP.BRANCH_ID = $branch_id AND AP.LOCATION_ID = L.LOCATION_ID
            WHERE ER.BRANCH_ID = $branch_id AND ER.ROUTE_ID != 0 AND ER.`STATUS` = 1 AND ER.TRIP_TYPE = '$triptype' AND ER.ESTIMATE_START_TIME = '$triptime' 
            ORDER BY AP.APPROVED_DISTANCE DESC) T GROUP BY T.ROUTE_ID";
            }
            $result =  DB::select($sql);
            return response([
                'success' => true,
                'status' => 1,
                'data' => $result,
                'message' => 'Route Details Fetch successfully.',
            ],200);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Route Details Fetch Unsuccessful.',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ],500);
        }
    }

    public function VendorListRoute() {
        try{
            $branch_id = Auth::user()->BRANCH_ID;

            $curdate = date('Y-m-d');
            
            $sql = "SELECT ROUTE_ID,TRIP_TYPE,ESTIMATE_START_TIME,VENDOR_NAME,updated_at FROM employees_roster_request 
                    WHERE BRANCH_ID='$branch_id' AND ROUTE_ID != 0 AND DATE(ESTIMATE_START_TIME) >= '$curdate' AND `STATUS` = 1  GROUP BY ROUTE_ID,TRIP_TYPE";

            $vendors =  DB::select($sql);
            
            return response([
               'success' => true,
               'status' => 1,
                'data' => $vendors,
               'message' => 'Vendor List Fetch successfully.',
            ],200);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Vendor List Fetch Unsuccessful.',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ],500);
        }
    }

    public function AutoRouteList(){
        try {
            $res = $this->GetAllRoutelist();
            $AES_KEY = env("AES_ENCRYPT_KEY");
            
            $json_arr = json_encode($res);
            $normal_array = json_decode($json_arr, true);
            //print_r($normal_array);exit;
            $results = array();
            for ($i = 0; $i < count($normal_array); $i++) {
                $empname = $normal_array[$i]['empname'];
                $emp_name = $this->commonFunction->AES_DECRYPT($empname,$AES_KEY);
                $mobile = $normal_array[$i]['MOBILE'];
                $emp_mobile = $this->commonFunction->AES_DECRYPT($mobile,$AES_KEY);
                $emp_arr_val = array("empname"=>$emp_name,"MOBILE"=>$emp_mobile);
                $results[$i] = array_replace($normal_array[$i], $emp_arr_val);
            }
            
            return response([
               'success' => true,
               'status' => 1,
                'data' => $results,
               'message' => 'Auto Route List Fetch successfully.',
            ],200);

        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Auto Route List Fetch Unsuccessful.',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ],500);
        }
    }

    private function GetAllRoutelist(){
        $branch_id = Auth::user()->BRANCH_ID;
        $curdate = date('Y-m-d');

		if($branch_id==60){
            $sql = "SELECT ER.ROUTE_ID,ER.EMPLOYEE_ID,E.`NAME` as empname,E.GENDER,E.MOBILE,ER.TRIP_TYPE
                    ,ER.ESTIMATE_START_TIME,if(ER.ESTIMATE_TIME is NULL,'--',time(ER.ESTIMATE_TIME)) as pickdrop
                    ,if(ER.VENDOR_NAME is NULL,'--',ER.VENDOR_NAME) as VENDOR_NAME,ER.created_at
                    ,CASE 
                    WHEN T.empcount <= 4 THEN '4 Seater'
                    WHEN T.empcount > 4 AND T.empcount <= 8 THEN '8 Seater'
                    WHEN T.empcount > 8 AND T.empcount <= 12 THEN '12 Seater'
                    WHEN T.empcount > 12 AND T.empcount <= 15 THEN '15 Seater'
                    WHEN T.empcount > 15 AND T.empcount <= 25 THEN '25 Seater' 
                    ELSE 'BUS' END as vtype,
                    E.PROJECT_NAME,E.ADDRESS,S.Stops_Name as LOCATION_NAME,T.*
                    FROM employees_roster_request ER
                    INNER JOIN employees E ON E.EMPLOYEES_ID = ER.EMPLOYEE_ID and E.BRANCH_ID='$branch_id'
                    INNER JOIN (SELECT ROUTE_ID,ESTIMATE_START_TIME,TRIP_TYPE,COUNT(EMPLOYEE_ID) as empcount,DISTANCE FROM employees_roster_request 
                    WHERE BRANCH_ID='$branch_id' AND `STATUS` = 1 AND DATE(ESTIMATE_START_TIME) = '$curdate' GROUP BY ROUTE_ID,TRIP_TYPE,ESTIMATE_START_TIME order by ROUTE_ID,DISTANCE DESC ) T
                    ON T.ROUTE_ID = ER.ROUTE_ID AND T.TRIP_TYPE = ER.TRIP_TYPE AND T.ESTIMATE_START_TIME = ER.ESTIMATE_START_TIME
                    inner join shuttle_stops S on S.Stops_ID=E.STOPS_ID
                    WHERE ER.BRANCH_ID='$branch_id' AND ER.`STATUS` = 1 AND DATE(ER.ESTIMATE_START_TIME) >= '$curdate' and ER.CATEGORY='auto_roster' order by T.ROUTE_ID";
		}  else {
			$sql = "SELECT ER.ROUTE_ID,ER.EMPLOYEE_ID,E.`NAME` as empname,E.GENDER,E.MOBILE,ER.TRIP_TYPE
                    ,if(ER.TRIP_TYPE = 'P', ER.START_LOCATION,ER.END_LOCATION) AS LOCATION
                    ,ER.ESTIMATE_START_TIME,if(ER.ESTIMATE_TIME is NULL,'--',time(ER.ESTIMATE_TIME)) as pickdrop
                    ,if(ER.VENDOR_NAME is NULL,'--',ER.VENDOR_NAME) as VENDOR_NAME,ER.created_at
                    ,CASE 
                    WHEN T.empcount <= 4 THEN '4 Seater'
                    WHEN T.empcount > 4 AND T.empcount <= 8 THEN '8 Seater'
                    WHEN T.empcount > 8 AND T.empcount <= 12 THEN '12 Seater'
                    WHEN T.empcount > 12 AND T.empcount <= 15 THEN '15 Seater'
                    WHEN T.empcount > 15 AND T.empcount <= 25 THEN '25 Seater' 
                    ELSE 'BUS' END as vtype,
                    E.PROJECT_NAME,E.ADDRESS,L.LOCATION_NAME,T.*
                    FROM employees_roster_request ER
                    INNER JOIN employees E ON E.EMPLOYEES_ID = ER.EMPLOYEE_ID and E.BRANCH_ID='$branch_id'
                    INNER JOIN (SELECT ROUTE_ID,ESTIMATE_START_TIME,TRIP_TYPE,COUNT(EMPLOYEE_ID) as empcount,DISTANCE FROM employees_roster_request 
                    WHERE BRANCH_ID='$branch_id' AND `STATUS` = 1 AND DATE(ESTIMATE_START_TIME) = '$curdate' GROUP BY ROUTE_ID,TRIP_TYPE,ESTIMATE_START_TIME order by DISTANCE ) T
                    ON T.ROUTE_ID = ER.ROUTE_ID AND T.TRIP_TYPE = ER.TRIP_TYPE AND T.ESTIMATE_START_TIME = ER.ESTIMATE_START_TIME
                    inner join locations L on L.LOCATION_ID=E.LOCATION_ID
                    WHERE ER.BRANCH_ID='$branch_id' AND ER.`STATUS` = 1 AND DATE(ER.ESTIMATE_START_TIME) >= '$curdate' and ER.CATEGORY='auto_roster'";
		}
        return DB::select($sql);
    }

    public function VendorAssignRoute() {
        try {
                $gettime = $this->GetarrivedShiftTime();
				$allroutes = $this->GetAllRoutes();
				$allvendors = $this->GetAllVendors();
                return response([
                    'success' => true,
                    'status' => 1,
                    'data' => [
                        'gettime' => $gettime,
                        'allroutes' => $allroutes,
                        'allvendors' => $allvendors
                    ],
                'message' => 'Vendor Assign Route Fetch successfully.',
             ],200);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Vendor Assign Route Fetch Unsuccessful.',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ],500);
        }
    }

    public function GetAllRoutes()
    {
        $branch_id = Auth::user()->BRANCH_ID;
        $curdate = date('Y-m-d');

        $sql = "SELECT FILE_NAME,CONCAT(TRIP_TYPE,'/',ESTIMATE_START_TIME) as ESTIMATESTARTTIME FROM employees_roster_request 
        WHERE DATE(ESTIMATE_START_TIME) >= '$curdate' AND `STATUS` = 1 AND BRANCH_ID= '$branch_id'  GROUP BY ESTIMATE_START_TIME,TRIP_TYPE";

        return DB::select($sql);
    }

    public function GetTimeRoutes($request){
        try {
            $data = explode("/",$request->routeintime);
            $triptype = $data[0];
            $triptime = $data[1];
            $branch_id = Auth::user()->BRANCH_ID;
            $DIVISION_ID = $this->commonFunction->getDivisionId($branch_id);
            if($triptype == 'D'){
                $loc = 'ER.END_LOCATION';
            }else{
                $loc = 'ER.START_LOCATION';
            }
            if($branch_id==60)
            {
                $sql="SELECT T.*,COUNT(T.EMPLOYEE_ID) as ttlempcnt FROM (SELECT ER.ROUTE_ID
            ,ER.EMPLOYEE_ID,if(ER.TRIP_TYPE = 'P',ER.START_LOCATION,ER.END_LOCATION) LOCATION
            ,ER.DISTANCE as APPROVED_DISTANCE,ER.VENDOR_NAME
            FROM employees_roster_request ER
                    INNER JOIN employees E ON E.EMPLOYEES_ID = ER.EMPLOYEE_ID and E.BRANCH_ID='$branch_id'
                    inner join shuttle_stops S on S.Stops_ID=E.STOPS_ID
                    WHERE ER.BRANCH_ID =$branch_id AND ER.ROUTE_ID != 0 AND ER.`STATUS` = 1 AND ER.TRIP_TYPE = '$triptype' AND ER.ESTIMATE_START_TIME = '$triptime' ORDER BY ER.DISTANCE DESC) T GROUP BY T.ROUTE_ID";
            /*  */
            }
            else{
            $sql = "SELECT T.*,COUNT(T.EMPLOYEE_ID) as ttlempcnt FROM (SELECT ER.ROUTE_ID
            ,ER.EMPLOYEE_ID,if(ER.TRIP_TYPE = 'P',ER.START_LOCATION,ER.END_LOCATION) LOCATION
            ,AP.APPROVED_DISTANCE,ER.VENDOR_NAME
            FROM employees_roster_request ER
            INNER JOIN locations L ON L.LOCATION_NAME = $loc AND L.DIVISION_ID = $DIVISION_ID
            INNER JOIN approve_distances AP ON AP.BRANCH_ID = $branch_id AND AP.LOCATION_ID = L.LOCATION_ID
            WHERE ER.BRANCH_ID = $branch_id AND ER.ROUTE_ID != 0 AND ER.`STATUS` = 1 AND ER.TRIP_TYPE = '$triptype' AND ER.ESTIMATE_START_TIME = '$triptime' 
            ORDER BY AP.APPROVED_DISTANCE DESC) T GROUP BY T.ROUTE_ID";
            }

            $data_val = DB::select($sql);
            return response([
                'success' => true,
                'status' => 1,
                'data' => $data_val,
                'message' => 'Get Time Route Fetch successfully.',
             ],200);
            
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Get Time Route Fetch Unsuccessful.',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ],500);
        }
    }

    public function EmpNoshowRoute($request)
    {
        try {
            $roster_req_id = $request->roster_req_id;
            $sql = "UPDATE `employees_roster_request` SET `STATUS`='6' WHERE (`ROSTER_REQ_ID`='".$roster_req_id."')";
            $data = DB::select($sql);

            return response([
                'success' => true,
                'status' => 1,
                'message' => 'Noshow Update successfully.',
             ],200);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
                'success' => false,
                'status' => 4,
                'message' => 'Noshow Unsuccessful.',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ],500);
        }
    }

    public function GetTimeResult($request)
    {
        try {
            $roster_req_time = $request->login_time;
            $data = explode("/",$roster_req_time);
            $triptype = $data[0];
            $triptime = $data[1];
            $branch_id = Auth::user()->BRANCH_ID;
            
            $sql = "SELECT ER.ROSTER_REQ_ID,ER.EMPLOYEE_ID,ER.TRIP_TYPE,ER.ROUTE_ID,ER.ALIAS_LOCATION,E.GENDER ,
            if(ER.TRIP_TYPE = 'P',ER.START_LOCATION,ER.END_LOCATION) LOCATION
            ,ER.ESTIMATE_START_TIME,ER.ESTIMATE_TIME as estimatetime,ER.MANUAL_EDIT as manualeditstatus
            FROM employees_roster_request ER 
            INNER JOIN employees E ON E.EMPLOYEES_ID = ER.EMPLOYEE_ID AND E.BRANCH_ID = ER.BRANCH_ID
            LEFT JOIN auto_route_distance ARD ON ARD.FROM_LOCATION_ID = REPLACE(if(ER.TRIP_TYPE = 'P',ER.START_LOCATION,ER.END_LOCATION),' ','')
            AND ARD.TO_LOCATION_ID = REPLACE(if(ER.TRIP_TYPE = 'P',ER.END_LOCATION,ER.START_LOCATION),' ','')
            WHERE ER.BRANCH_ID = $branch_id AND ER.TRIP_TYPE = '$triptype' AND 
            ER.ESTIMATE_START_TIME = '$triptime' AND ER.`STATUS` = 1 ORDER BY ER.ROUTE_ID ASC";           
	    $data = DB::select($sql);

            return response([
                'success' => true,
                'status' => 1,
                'employee_details' => $data,
                'message' => 'Get Time Result Fetch successfully.',
            ],200);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
               'success' => false,
               'status' => 4,
               'message' => 'Get Time Result Fetch Unsuccessful.',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ],500);
        }
    }

    public function AdminChangeRosterRequestTime($request)
    {
        try {
            $roster_req_id = $request->roster_req_id;
            $new_time = $request->passenger_pickup_time;
            $edit_status = "EDITED";
            $sql = "UPDATE `employees_roster_request` SET `ESTIMATE_TIME`='$new_time', `MANUAL_EDIT`='$edit_status' WHERE (`ROSTER_REQ_ID`='".$roster_req_id."')";
            $data = DB::select($sql);

            return response([
               'success' => true,
               'status' => 1,
               'message' => 'Time Update successfully.',
            ],200);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
               'success' => false,
               'status' => 4,
               'message' => 'Admin Change Roster Request Time Update Unsuccessful.',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ],500);
        }
    }

    public function CheckVendor($request)
    {
        try {
            $triptime = $request->routeintime;
            $data = explode("/",$triptime);
            $triptype = $data[0];
            $triptime = $data[1];
            $branch_id = Auth::user()->BRANCH_ID;

            $sql = "SELECT ROUTE_ID,VENDOR_NAME FROM employees_roster_request WHERE BRANCH_ID=$branch_id and `STATUS` = 1 AND
                    TRIP_TYPE = '$triptype' AND ESTIMATE_START_TIME = '$triptime' AND FILE_NAME is NOT NULL AND VENDOR_NAME is NULL GROUP BY ROUTE_ID";
            $data = DB::select($sql);

            //return count($data);
            return response([
                'status' => true,
                'value' => count($data),
                'message' => 'Check Vendor Successfully.'
            ],200);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
               'success' => false,
               'status' => 4,
               'message' => 'Check Vendor Unsuccessful.',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ],500);
        }
    }

    public function GetVendorRouteCnt($request)
    {
        try {
            $branch_id = Auth::user()->BRANCH_ID;

            //$vendorname =  $request->vendorname;
            $allvendors = $this->GetAllVendors();
            $routeintime =  $request->routeintime;

            $data = explode("/",$routeintime);
            $triptype = $data[0];
            $triptime = $data[1];

            $result = [];
            foreach($allvendors as $allvendor){
                $vendorname = $allvendor->vendorname;
                $sql = "SELECT COUNT(ROUTE_ID) as ttlcnt FROM employees_roster_request WHERE BRANCH_ID=$branch_id AND `STATUS`=1 AND VENDOR_NAME = '$vendorname' AND TRIP_TYPE = '$triptype' AND ESTIMATE_START_TIME = '$triptime' GROUP BY ROUTE_ID";
                $data = DB::select($sql);
		
		        $data_val = [];
		        $data_val['Label'] = $allvendor->vendorname;
		        $data_val['value'] = count($data);

		        array_push($result, $data_val);
                //$result[$allvendor->vendorname] = count($data);
            }

            //return count($data);
            return response([
                'status' => true,
                'value' => $result,
                'message' => 'Get Vendor Route Count Successfully.'
            ],200);
        } catch (\Throwable|\Exception $e) {
            $this->commonFunction->logException($e);
            return response([
               'success' => false,
               'status' => 4,
               'message' => 'Get Vendor Route Count Unsuccessful.',
                'validation_controller' => true,
                'error' => $e->getMessage(),
            ],500);
        }
    }
}