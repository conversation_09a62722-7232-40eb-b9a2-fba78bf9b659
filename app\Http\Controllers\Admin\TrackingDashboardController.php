<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\TrackingDashboardRequest;
use App\Http\Requests\Admin\TripDetailsRequest;
use App\Http\Requests\Admin\TripReasonRequest;
use App\Http\Requests\Admin\RosterVendorChangeRequest;
use App\Http\Requests\Admin\RosterResetRequest;
use App\Http\Requests\Admin\RosterNoshowRequest;
use App\Http\Requests\Admin\RosterEmpClubRequest;
use App\Http\Requests\Admin\InsertClubbingRequest;
use App\Http\Requests\Admin\OverspeedRemarksRequest;
use App\Http\Requests\Admin\PanicActionRequest;
use App\Http\Requests\Admin\EscortAssignRequest;
use App\Http\Requests\Admin\EscortResetRequest;
use App\Http\Requests\Admin\EscortValidateRequest;
//use App\Http\Requests\Admin\RosterCaballotRequest;
use App\Http\Requests\Admin\RosterCabAllotRequest;
use App\Http\Requests\Admin\SafeDropRemarksRequest;
use App\Http\Requests\Admin\UpdateASFRemarksRequest;
use App\Http\Requests\Admin\DeviationRemarksUpdateRequest;
use App\Http\Requests\Admin\BreakdownReasonRequest;


use App\Services\Admin\TrackingDashboardService;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application;
use Illuminate\Http\Response;
use Illuminate\Http\Request;

class TrackingDashboardController extends Controller
{
    protected TrackingDashboardService $trackingService;

    public function __construct(TrackingDashboardService $trackingService)
    {
        $this->trackingService = $trackingService;
    }


    public function trackingDashboard(TrackingDashboardRequest $request): Application|Response|ResponseFactory
    {
        return $this->trackingService->pagination_tracking_dashboard($request);
    }
	public function dashboard_count(): Application|Response|ResponseFactory
    {
        return $this->trackingService->dashboard_count();
    }
	public function roster_details(TripDetailsRequest $request): Application|Response|ResponseFactory
    {
       
        return $this->trackingService->roster_details($request);
    }
	public function get_reason_details(TripReasonRequest $request): Application|Response|ResponseFactory
    {
       
         return $this->trackingService->get_reason_details($request);
    }
	public function roster_vendor_change(RosterVendorChangeRequest $request): Application|Response|ResponseFactory
    {
        
         return $this->trackingService->roster_vendor_change($request);
    }
	public function roster_reset(RosterResetRequest $request): Application|Response|ResponseFactory
    {
         return $this->trackingService->roster_reset($request);
    }
	public function roster_noshow(RosterNoshowRequest $request): Application|Response|ResponseFactory
    {
         return $this->trackingService->roster_noshow($request);
    }
	public function empclubmob(RosterEmpClubRequest $request): Application|Response|ResponseFactory
    {
         return $this->trackingService->empclubmob($request);
    }
	public function club_emp_details(Request $request): Application|Response|ResponseFactory
    {
         return $this->trackingService->club_emp_details($request);

    }
	public function cab_allot_list(Request $request): Application|Response|ResponseFactory
    {
         return $this->trackingService->cab_allot_list($request);

    }
	public function rostercaballot(RosterCabAllotRequest $request): Application|Response|ResponseFactory
    {
       
        return $this->trackingService->rostercaballot($request);

    }
	public function breakdownreason(BreakdownReasonRequest $request): Application|Response|ResponseFactory
    {
       
        return $this->trackingService->breakdownreason($request);

    }
	public function insert_clubdata(InsertClubbingRequest $request): Application|Response|ResponseFactory
    {
         return $this->trackingService->insert_clubdata($request);

    }

	public function dataForCreate(): Application|Response|ResponseFactory
    {
         return $this->trackingService->dataForCreate();

    }
	public function escort_details(): Application|Response|ResponseFactory
    {
         return $this->trackingService->escort_details();

    }
	public function escort_assign(EscortAssignRequest $request): Application|Response|ResponseFactory
    {
         return $this->trackingService->escort_assign($request);

    }
	public function escort_reset(EscortResetRequest $request): Application|Response|ResponseFactory
    {
         return $this->trackingService->escort_reset($request);

    }
	public function speedcount_details(Request $request): Application|Response|ResponseFactory
    {
         return $this->trackingService->speedcount_details($request);

    }
	public function overspeedremarks(OverspeedRemarksRequest $request): Application|Response|ResponseFactory
    {
         return $this->trackingService->overspeedremarks($request);

    }
	public function panic_action_update(PanicActionRequest $request): Application|Response|ResponseFactory
    {
         return $this->trackingService->panic_action_update($request);

    }
	public function update_safedropremarks(SafeDropRemarksRequest $request): Application|Response|ResponseFactory
    {
         return $this->trackingService->update_safedropremarks($request);

    }
	public function update_asfremarks(UpdateASFRemarksRequest $request): Application|Response|ResponseFactory
    {
         return $this->trackingService->update_asfremarks($request);

    }
	public function deviation_remarks_update(DeviationRemarksUpdateRequest $request): Application|Response|ResponseFactory
    {
         return $this->trackingService->deviation_remarks_update($request);

    }
	public function escort_validate(EscortValidateRequest $request): Application|Response|ResponseFactory
    {
         return $this->trackingService->escort_validate($request);

    }
}
