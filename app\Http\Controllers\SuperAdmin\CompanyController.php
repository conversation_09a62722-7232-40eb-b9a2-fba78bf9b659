<?php

namespace App\Http\Controllers\SuperAdmin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Services\SuperAdmin\CompanyService;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Application as FoundationApplication;
use Illuminate\Http\Response;
use App\Http\Requests\SuperAdmin\CompanyDeleteRequest;
use App\Http\Requests\SuperAdmin\CompanyRequest;

class CompanyController extends Controller
{
    protected CompanyService $companyService;

    public function __construct(CompanyService $companyService)
    {
        $this->companyService = $companyService;
    }


    public function index(): FoundationApplication|Response|ResponseFactory
    {
        return $this->companyService->indexCompany();
    }


    public function store(CompanyRequest $request): ResponseFactory|Application|Response
    {
        return $this->companyService->storeCompany($request);
    }


    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    public function edit($companyAutoIdCrypt): Application|Response|ResponseFactory
    {
        return $this->companyService->editCompany($companyAutoIdCrypt);
    }

    public function update(CompanyRequest $request, $companyAutoIdCrypt): Application|Response|ResponseFactory
    {
        return $this->companyService->updateCompany($request, $companyAutoIdCrypt);
    }


    public function delete(CompanyDeleteRequest $request, $companyAutoIdCrypt): FoundationApplication|Response|ResponseFactory
    {
        return $this->companyService->deleteCompany($request, $companyAutoIdCrypt);
    }

    public function companylistpagination(Request $request): ResponseFactory|Application|Response
    {
            
        return $this->companyService->paginationCompany($request);
    }
    
}
