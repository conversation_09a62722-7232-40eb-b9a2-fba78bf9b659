<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Contracts\Validation\Validator;

class GpsActionListRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            "route_no" => "required",
            "cab_no" => "required",
            "vendor_id" => "required",
            "datepicker" => "required",
        ];
    }

    public function messages(): array
    {
        return [
            "route_no.required" => "Route number is required",
            "cab_no.required" => "Cab number is required",
            "vendor_id.required" => "Vendor ID is required",
            "datepicker.required" => "Datepicker is required",
        ];
    }

    public function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json([
            'success' => false,
            'validation_error' => true,
           'message' => 'Validation errors',
            'data' => $validator->errors(),
        ]));
    }
}
